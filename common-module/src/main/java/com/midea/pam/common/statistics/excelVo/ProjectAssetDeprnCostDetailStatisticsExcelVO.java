package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;

/**
 * @program: common-module
 * @description: 资产折旧成本统计
 * @author:ygx
 * @create:2025-01-22 16:52
 **/
@Getter
@Setter
public class ProjectAssetDeprnCostDetailStatisticsExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer num;

    @Excel(name = "资产编号", width = 30)
    private String assetNumber;

    @Excel(name = "资产说明", width = 50)
    private String description;

    @Excel(name = "折旧期间", width = 20)
    private String periodName;

    @Excel(name = "折旧金额", width = 30)
    private BigDecimal deprnAmount;

    @Excel(name = "项目编号", width = 25)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "项目类型", width = 30)
    private String projectType;

    @Excel(name = "入账状态", width = 15, replace = {"未入账_0", "入账中_1", "已入账_2", " _null"})
    private Integer status;

    @Excel(name = "币种")
    private String currency;

    @Excel(name = "业务实体", width = 30)
    private String ouName;
}
