package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ReportProjectDimension extends LongIdEntity implements Serializable {
    private Long id;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private Long unitId;

    private String unitName;

    private String divisionCode;

    private String divisionName;

    private Long type;

    private String typeName;

    private Integer status;

    private String priceType;

    private BigDecimal amount;

    private BigDecimal budgetCost;

    private String currency;

    private Long reportId;

    private Long executeId;

    private String managerName;

    private Long managerId;

    private String contractCode;

    private Date startDate;

    private Date endDate;

    private Long ouId;

    private String ouName;

    private BigDecimal incomeAmount;

    private BigDecimal costAmount;

    private BigDecimal currentIncomeTotalAmount;

    private BigDecimal currentCostActual;

    private BigDecimal innerLaborCost;

    private BigDecimal feeCost;

    private BigDecimal cumulativeIncomePercent;

    private BigDecimal attainmentIncomePercent;

    private BigDecimal budgetOfPercent;

    private BigDecimal planProfit;

    private BigDecimal actualProfit;

    private BigDecimal profitExecutionComparison;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode == null ? null : divisionCode.trim();
    }

    public String getDivisionName() {
        return divisionName;
    }

    public void setDivisionName(String divisionName) {
        this.divisionName = divisionName == null ? null : divisionName.trim();
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName == null ? null : typeName.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getPriceType() {
        return priceType;
    }

    public void setPriceType(String priceType) {
        this.priceType = priceType == null ? null : priceType.trim();
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getBudgetCost() {
        return budgetCost;
    }

    public void setBudgetCost(BigDecimal budgetCost) {
        this.budgetCost = budgetCost;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName == null ? null : managerName.trim();
    }

    public Long getManagerId() {
        return managerId;
    }

    public void setManagerId(Long managerId) {
        this.managerId = managerId;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode == null ? null : contractCode.trim();
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public BigDecimal getIncomeAmount() {
        return incomeAmount;
    }

    public void setIncomeAmount(BigDecimal incomeAmount) {
        this.incomeAmount = incomeAmount;
    }

    public BigDecimal getCostAmount() {
        return costAmount;
    }

    public void setCostAmount(BigDecimal costAmount) {
        this.costAmount = costAmount;
    }

    public BigDecimal getCurrentIncomeTotalAmount() {
        return currentIncomeTotalAmount;
    }

    public void setCurrentIncomeTotalAmount(BigDecimal currentIncomeTotalAmount) {
        this.currentIncomeTotalAmount = currentIncomeTotalAmount;
    }

    public BigDecimal getCurrentCostActual() {
        return currentCostActual;
    }

    public void setCurrentCostActual(BigDecimal currentCostActual) {
        this.currentCostActual = currentCostActual;
    }

    public BigDecimal getInnerLaborCost() {
        return innerLaborCost;
    }

    public void setInnerLaborCost(BigDecimal innerLaborCost) {
        this.innerLaborCost = innerLaborCost;
    }

    public BigDecimal getFeeCost() {
        return feeCost;
    }

    public void setFeeCost(BigDecimal feeCost) {
        this.feeCost = feeCost;
    }

    public BigDecimal getCumulativeIncomePercent() {
        return cumulativeIncomePercent;
    }

    public void setCumulativeIncomePercent(BigDecimal cumulativeIncomePercent) {
        this.cumulativeIncomePercent = cumulativeIncomePercent;
    }

    public BigDecimal getAttainmentIncomePercent() {
        return attainmentIncomePercent;
    }

    public void setAttainmentIncomePercent(BigDecimal attainmentIncomePercent) {
        this.attainmentIncomePercent = attainmentIncomePercent;
    }

    public BigDecimal getBudgetOfPercent() {
        return budgetOfPercent;
    }

    public void setBudgetOfPercent(BigDecimal budgetOfPercent) {
        this.budgetOfPercent = budgetOfPercent;
    }

    public BigDecimal getPlanProfit() {
        return planProfit;
    }

    public void setPlanProfit(BigDecimal planProfit) {
        this.planProfit = planProfit;
    }

    public BigDecimal getActualProfit() {
        return actualProfit;
    }

    public void setActualProfit(BigDecimal actualProfit) {
        this.actualProfit = actualProfit;
    }

    public BigDecimal getProfitExecutionComparison() {
        return profitExecutionComparison;
    }

    public void setProfitExecutionComparison(BigDecimal profitExecutionComparison) {
        this.profitExecutionComparison = profitExecutionComparison;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", unitId=").append(unitId);
        sb.append(", unitName=").append(unitName);
        sb.append(", divisionCode=").append(divisionCode);
        sb.append(", divisionName=").append(divisionName);
        sb.append(", type=").append(type);
        sb.append(", typeName=").append(typeName);
        sb.append(", status=").append(status);
        sb.append(", priceType=").append(priceType);
        sb.append(", amount=").append(amount);
        sb.append(", budgetCost=").append(budgetCost);
        sb.append(", currency=").append(currency);
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", managerName=").append(managerName);
        sb.append(", managerId=").append(managerId);
        sb.append(", contractCode=").append(contractCode);
        sb.append(", startDate=").append(startDate);
        sb.append(", endDate=").append(endDate);
        sb.append(", ouId=").append(ouId);
        sb.append(", ouName=").append(ouName);
        sb.append(", incomeAmount=").append(incomeAmount);
        sb.append(", costAmount=").append(costAmount);
        sb.append(", currentIncomeTotalAmount=").append(currentIncomeTotalAmount);
        sb.append(", currentCostActual=").append(currentCostActual);
        sb.append(", innerLaborCost=").append(innerLaborCost);
        sb.append(", feeCost=").append(feeCost);
        sb.append(", cumulativeIncomePercent=").append(cumulativeIncomePercent);
        sb.append(", attainmentIncomePercent=").append(attainmentIncomePercent);
        sb.append(", budgetOfPercent=").append(budgetOfPercent);
        sb.append(", planProfit=").append(planProfit);
        sb.append(", actualProfit=").append(actualProfit);
        sb.append(", profitExecutionComparison=").append(profitExecutionComparison);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}