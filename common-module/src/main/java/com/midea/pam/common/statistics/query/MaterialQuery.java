package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;


/**
 * 创建时间 2020-5-21
 *
 * <AUTHOR>
 */
@ApiModel(value = "MaterialQuery", description = "物料查询参数")
public class MaterialQuery {

    /**
     * 默认起始页
     */
    public static final int DEFAULT_PAGE_NUM = 1;
    /**
     * 默认分页大小
     */
    public static final int DEFAULT_PAGE_SIZE = 10;

    @ApiModelProperty(value = "ERP物料编码， 不为空时，只查ERP物料编码匹配此值的物料")
    private String itemCode;

    @ApiModelProperty(value = "PAM物料编码， 不为空时，只查PAM物料编码匹配此值的物料")
    private String pamCode;

    @ApiModelProperty(value = "物料描述， 不为空时，只查物料描述编码匹配此值的物料")
    private String itemInfo;

    @ApiModelProperty(value = "用户物料类型列表， 不为空时，只查物料类型属于此列表的物料")
    private List<String> itemTypes;

    @ApiModelProperty(value = "物料状态列表， 不为空时，只查物料状态属于此列表的物料")
    private List<String> itemStatuses;

    //@ApiModelProperty(value = "物料大类ID列表， 不为空时，只查物料大类ID属于此列表的物料")
    //private List<Long> materialClassificationIds;

    @ApiModelProperty(value = "物料大类名称列表， 不为空时，只查物料大类名称属于此列表的物料")
    private List<String> materialClassificationNames;

    @ApiModelProperty(value = "物料小类ID列表， 不为空时，只查物料小类ID属于此列表的物料")
    private List<Long> materialTypeIds;

    @ApiModelProperty(value = "物料中类ID列表， 不为空时，只查物料中类ID属于此列表的物料")
    private List<String> codingMiddleclassNames;

    @ApiModelProperty(value = "物料名称， 不为空时，只查物料名称匹配此值的物料")
    private String name;

    @ApiModelProperty(value = "物料型号/规格/图号， 不为空时，只查物料型号/规格/图号匹配此值的物料")
    private String model;

    @ApiModelProperty(value = "物料品牌， 不为空时，只查物料品牌匹配此值的物料")
    private String brand;

    @ApiModelProperty(value = "加工件分类列表， 不为空时，只查物料加工件分类属于此列表的物料")
    private List<String> machiningPartTypes;

    @ApiModelProperty(value = "物料材质， 不为空时，只查物料材质匹配此值的物料")
    private String material;

    @ApiModelProperty(value = "单位重量（Kg)， 不为空时，只查物料单位重量匹配此值的物料")
    private String unitWeight;

    @ApiModelProperty(value = "材质处理， 不为空时，只查物料材质处理匹配此值的物料")
    private String materialProcessing;

    @ApiModelProperty(value = "库存组织ID列表， 不为空时，只查物料库存组织ID属于此列表的物料")
    private List<Long> organizationIds;

    @ApiModelProperty(value = "业务实体ID")
    private List<Long> ouIds;

    @ApiModelProperty(value = "创建日期开始范围， 不为空时，只查物料创建日期大于或等于此值的物料")
    private Date createDateFrom;

    @ApiModelProperty(value = "创建日期结束范围， 不为空时，只查物料创建日期小于或等于此值的物料")
    private Date createDateTo;

    @ApiModelProperty(value = "创建人名称， 不为空时，只查物料创建人名称匹配此值的物料")
    private String creatorName;

    @ApiModelProperty(value = "更新日期开始范围， 不为空时，只查物料更新日期大于或等于此值的物料")
    private Date updateDateFrom;

    @ApiModelProperty(value = "更新日期结束范围， 不为空时，只查物料更新日期小于或等于此值的物料")
    private Date updateDateTo;

    @ApiModelProperty(value = "更新人名称， 不为空时，只查物料创建人名称匹配此值的物料")
    private String updaterName;

    @ApiModelProperty(value = "页码， 不为空时，只查物料创建人名称匹配此值的物料")
    private Integer pageNum;

    @ApiModelProperty(value = "页大小， 不为空时，只查物料创建人名称匹配此值的物料")
    private Integer pageSize;

    private Boolean delistFlag;

    @ApiModelProperty(value = "接受子库存")
    private String recevingSubinventory;

    @ApiModelProperty(value = "货架")
    private String shelves;

    @ApiModelProperty(value = "配套人员")
    private String sourcer;

    private Long minimumOrderQuantity;

    private Long safetyStockQuantity;

    private List<Boolean> delistFlags;

    private Boolean pamCodeIsNotNull;

    @ApiModelProperty(value = "分页的第一个物料ID")
    private Long firstMaterialId;

    @ApiModelProperty("是否是本组织物料")
    private boolean currentOrganizationIs = false;

    @ApiModelProperty("选中数据的库存组织")
    private Long organizationId;

    @ApiModelProperty("物料分类")
    private String materialClassification;

    @ApiModelProperty(value = "图号")
    private String figureNumber;

    @ApiModelProperty(value = "品牌商物料编码")
    private String brandMaterialCode;

    @ApiModelProperty(value = "备注")
    private String remark;

    private List<String> pamCodeList;


    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode;
    }

    public String getItemInfo() {
        return itemInfo;
    }

    public void setItemInfo(String itemInfo) {
        this.itemInfo = itemInfo;
    }

    public List<String> getItemTypes() {
        return itemTypes;
    }

    public void setItemTypes(List<String> itemTypes) {
        this.itemTypes = itemTypes;
    }

    public List<String> getItemStatuses() {
        return itemStatuses;
    }

    public void setItemStatuses(List<String> itemStatuses) {
        this.itemStatuses = itemStatuses;
    }

    public List<String> getMaterialClassificationNames() {
        return materialClassificationNames;
    }

    public void setMaterialClassificationNames(List<String> materialClassificationNames) {
        this.materialClassificationNames = materialClassificationNames;
    }

    public List<Long> getMaterialTypeIds() {
        return materialTypeIds;
    }

    public void setMaterialTypeIds(List<Long> materialTypeIds) {
        this.materialTypeIds = materialTypeIds;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public List<String> getMachiningPartTypes() {
        return machiningPartTypes;
    }

    public void setMachiningPartTypes(List<String> machiningPartTypes) {
        this.machiningPartTypes = machiningPartTypes;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public String getUnitWeight() {
        return unitWeight;
    }

    public void setUnitWeight(String unitWeight) {
        this.unitWeight = unitWeight;
    }

    public String getMaterialProcessing() {
        return materialProcessing;
    }

    public void setMaterialProcessing(String materialProcessing) {
        this.materialProcessing = materialProcessing;
    }

    public List<Long> getOrganizationIds() {
        return organizationIds;
    }

    public void setOrganizationIds(List<Long> organizationIds) {
        this.organizationIds = organizationIds;
    }

    public Date getCreateDateFrom() {
        return createDateFrom;
    }

    public void setCreateDateFrom(Date createDateFrom) {
        this.createDateFrom = createDateFrom;
    }

    public Date getCreateDateTo() {
        return createDateTo;
    }

    public void setCreateDateTo(Date createDateTo) {
        this.createDateTo = createDateTo;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getUpdateDateFrom() {
        return updateDateFrom;
    }

    public void setUpdateDateFrom(Date updateDateFrom) {
        this.updateDateFrom = updateDateFrom;
    }

    public Date getUpdateDateTo() {
        return updateDateTo;
    }

    public void setUpdateDateTo(Date updateDateTo) {
        this.updateDateTo = updateDateTo;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public Integer getPageNum() {
        if (pageNum == null)
            pageNum = DEFAULT_PAGE_NUM;

        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        if (pageSize == null)
            pageSize = DEFAULT_PAGE_SIZE;
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Boolean getDelistFlag() {
        return delistFlag;
    }

    public void setDelistFlag(Boolean delistFlag) {
        this.delistFlag = delistFlag;
    }

    public List<Long> getOuIds() {
        return ouIds;
    }

    public void setOuIds(List<Long> ouIds) {
        this.ouIds = ouIds;
    }

    public String getRecevingSubinventory() {
        return recevingSubinventory;
    }

    public void setRecevingSubinventory(String recevingSubinventory) {
        this.recevingSubinventory = recevingSubinventory;
    }

    public String getShelves() {
        return shelves;
    }

    public void setShelves(String shelves) {
        this.shelves = shelves;
    }

    public String getSourcer() {
        return sourcer;
    }

    public void setSourcer(String sourcer) {
        this.sourcer = sourcer;
    }

    public Long getMinimumOrderQuantity() {
        return minimumOrderQuantity;
    }

    public void setMinimumOrderQuantity(Long minimumOrderQuantity) {
        this.minimumOrderQuantity = minimumOrderQuantity;
    }

    public Long getSafetyStockQuantity() {
        return safetyStockQuantity;
    }

    public void setSafetyStockQuantity(Long safetyStockQuantity) {
        this.safetyStockQuantity = safetyStockQuantity;
    }

    public List<String> getCodingMiddleclassNames() {
        return codingMiddleclassNames;
    }

    public void setCodingMiddleclassNames(List<String> codingMiddleclassNames) {
        this.codingMiddleclassNames = codingMiddleclassNames;
    }

    public List<Boolean> getDelistFlags() {
        return delistFlags;
    }

    public void setDelistFlags(List<Boolean> delistFlags) {
        this.delistFlags = delistFlags;
    }

    public Boolean getPamCodeIsNotNull() {
        return pamCodeIsNotNull;
    }

    public void setPamCodeIsNotNull(Boolean pamCodeIsNotNull) {
        this.pamCodeIsNotNull = pamCodeIsNotNull;
    }

    public Long getFirstMaterialId() {
        return firstMaterialId;
    }

    public void setFirstMaterialId(Long firstMaterialId) {
        this.firstMaterialId = firstMaterialId;
    }

    public boolean isCurrentOrganizationIs() {
        return currentOrganizationIs;
    }

    public void setCurrentOrganizationIs(boolean currentOrganizationIs) {
        this.currentOrganizationIs = currentOrganizationIs;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public String getMaterialClassification() {
        return materialClassification;
    }

    public void setMaterialClassification(String materialClassification) {
        this.materialClassification = materialClassification;
    }

    public String getFigureNumber() {
        return figureNumber;
    }

    public void setFigureNumber(String figureNumber) {
        this.figureNumber = figureNumber;
    }

    public String getBrandMaterialCode() {
        return brandMaterialCode;
    }

    public void setBrandMaterialCode(String brandMaterialCode) {
        this.brandMaterialCode = brandMaterialCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<String> getPamCodeList() {
        return pamCodeList;
    }

    public void setPamCodeList(List<String> pamCodeList) {
        this.pamCodeList = pamCodeList;
    }

    @Override
    public String toString() {
        return "MaterialQuery{" +
                "itemCode='" + itemCode + '\'' +
                ", pamCode='" + pamCode + '\'' +
                ", itemInfo='" + itemInfo + '\'' +
                ", itemTypes=" + itemTypes +
                ", itemStatuses=" + itemStatuses +
                ", materialClassificationNames=" + materialClassificationNames +
                ", materialTypeIds=" + materialTypeIds +
                ", codingMiddleclassNames=" + codingMiddleclassNames +
                ", name='" + name + '\'' +
                ", model='" + model + '\'' +
                ", brand='" + brand + '\'' +
                ", machiningPartTypes=" + machiningPartTypes +
                ", material='" + material + '\'' +
                ", unitWeight='" + unitWeight + '\'' +
                ", materialProcessing='" + materialProcessing + '\'' +
                ", organizationIds=" + organizationIds +
                ", ouIds=" + ouIds +
                ", createDateFrom=" + createDateFrom +
                ", createDateTo=" + createDateTo +
                ", creatorName='" + creatorName + '\'' +
                ", updateDateFrom=" + updateDateFrom +
                ", updateDateTo=" + updateDateTo +
                ", updaterName='" + updaterName + '\'' +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", delistFlag=" + delistFlag +
                ", recevingSubinventory='" + recevingSubinventory + '\'' +
                ", shelves='" + shelves + '\'' +
                ", sourcer='" + sourcer + '\'' +
                ", minimumOrderQuantity=" + minimumOrderQuantity +
                ", safetyStockQuantity=" + safetyStockQuantity +
                ", delistFlags=" + delistFlags +
                ", pamCodeIsNotNull=" + pamCodeIsNotNull +
                ", firstMaterialId=" + firstMaterialId +
                ", currentOrganizationIs=" + currentOrganizationIs +
                ", organizationId=" + organizationId +
                ", materialClassification='" + materialClassification + '\'' +
                ", figureNumber='" + figureNumber + '\'' +
                ", brandMaterialCode='" + brandMaterialCode + '\'' +
                ", remark='" + remark + '\'' +
                ", pamCodeList=" + pamCodeList +
                '}';
    }
}
