package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportPurchaseProgressDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportPurchaseProgressDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNull() {
            addCriterion("project_type is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNotNull() {
            addCriterion("project_type is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeEqualTo(String value) {
            addCriterion("project_type =", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotEqualTo(String value) {
            addCriterion("project_type <>", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThan(String value) {
            addCriterion("project_type >", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThanOrEqualTo(String value) {
            addCriterion("project_type >=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThan(String value) {
            addCriterion("project_type <", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThanOrEqualTo(String value) {
            addCriterion("project_type <=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLike(String value) {
            addCriterion("project_type like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotLike(String value) {
            addCriterion("project_type not like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIn(List<String> values) {
            addCriterion("project_type in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotIn(List<String> values) {
            addCriterion("project_type not in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeBetween(String value1, String value2) {
            addCriterion("project_type between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotBetween(String value1, String value2) {
            addCriterion("project_type not between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectSortIsNull() {
            addCriterion("project_sort is null");
            return (Criteria) this;
        }

        public Criteria andProjectSortIsNotNull() {
            addCriterion("project_sort is not null");
            return (Criteria) this;
        }

        public Criteria andProjectSortEqualTo(String value) {
            addCriterion("project_sort =", value, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortNotEqualTo(String value) {
            addCriterion("project_sort <>", value, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortGreaterThan(String value) {
            addCriterion("project_sort >", value, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortGreaterThanOrEqualTo(String value) {
            addCriterion("project_sort >=", value, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortLessThan(String value) {
            addCriterion("project_sort <", value, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortLessThanOrEqualTo(String value) {
            addCriterion("project_sort <=", value, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortLike(String value) {
            addCriterion("project_sort like", value, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortNotLike(String value) {
            addCriterion("project_sort not like", value, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortIn(List<String> values) {
            addCriterion("project_sort in", values, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortNotIn(List<String> values) {
            addCriterion("project_sort not in", values, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortBetween(String value1, String value2) {
            addCriterion("project_sort between", value1, value2, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortNotBetween(String value1, String value2) {
            addCriterion("project_sort not between", value1, value2, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNull() {
            addCriterion("project_manager is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNotNull() {
            addCriterion("project_manager is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerEqualTo(String value) {
            addCriterion("project_manager =", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotEqualTo(String value) {
            addCriterion("project_manager <>", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThan(String value) {
            addCriterion("project_manager >", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThanOrEqualTo(String value) {
            addCriterion("project_manager >=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThan(String value) {
            addCriterion("project_manager <", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThanOrEqualTo(String value) {
            addCriterion("project_manager <=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLike(String value) {
            addCriterion("project_manager like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotLike(String value) {
            addCriterion("project_manager not like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIn(List<String> values) {
            addCriterion("project_manager in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotIn(List<String> values) {
            addCriterion("project_manager not in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerBetween(String value1, String value2) {
            addCriterion("project_manager between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotBetween(String value1, String value2) {
            addCriterion("project_manager not between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdIsNull() {
            addCriterion("material_purchase_requirement_id is null");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdIsNotNull() {
            addCriterion("material_purchase_requirement_id is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdEqualTo(Long value) {
            addCriterion("material_purchase_requirement_id =", value, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdNotEqualTo(Long value) {
            addCriterion("material_purchase_requirement_id <>", value, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdGreaterThan(Long value) {
            addCriterion("material_purchase_requirement_id >", value, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdGreaterThanOrEqualTo(Long value) {
            addCriterion("material_purchase_requirement_id >=", value, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdLessThan(Long value) {
            addCriterion("material_purchase_requirement_id <", value, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdLessThanOrEqualTo(Long value) {
            addCriterion("material_purchase_requirement_id <=", value, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdIn(List<Long> values) {
            addCriterion("material_purchase_requirement_id in", values, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdNotIn(List<Long> values) {
            addCriterion("material_purchase_requirement_id not in", values, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdBetween(Long value1, Long value2) {
            addCriterion("material_purchase_requirement_id between", value1, value2, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdNotBetween(Long value1, Long value2) {
            addCriterion("material_purchase_requirement_id not between", value1, value2, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeIsNull() {
            addCriterion("requirement_type is null");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeIsNotNull() {
            addCriterion("requirement_type is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeEqualTo(Integer value) {
            addCriterion("requirement_type =", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeNotEqualTo(Integer value) {
            addCriterion("requirement_type <>", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeGreaterThan(Integer value) {
            addCriterion("requirement_type >", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("requirement_type >=", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeLessThan(Integer value) {
            addCriterion("requirement_type <", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeLessThanOrEqualTo(Integer value) {
            addCriterion("requirement_type <=", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeIn(List<Integer> values) {
            addCriterion("requirement_type in", values, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeNotIn(List<Integer> values) {
            addCriterion("requirement_type not in", values, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeBetween(Integer value1, Integer value2) {
            addCriterion("requirement_type between", value1, value2, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("requirement_type not between", value1, value2, "requirementType");
            return (Criteria) this;
        }

        public Criteria andCodeIdIsNull() {
            addCriterion("code_id is null");
            return (Criteria) this;
        }

        public Criteria andCodeIdIsNotNull() {
            addCriterion("code_id is not null");
            return (Criteria) this;
        }

        public Criteria andCodeIdEqualTo(Long value) {
            addCriterion("code_id =", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdNotEqualTo(Long value) {
            addCriterion("code_id <>", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdGreaterThan(Long value) {
            addCriterion("code_id >", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("code_id >=", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdLessThan(Long value) {
            addCriterion("code_id <", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdLessThanOrEqualTo(Long value) {
            addCriterion("code_id <=", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdIn(List<Long> values) {
            addCriterion("code_id in", values, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdNotIn(List<Long> values) {
            addCriterion("code_id not in", values, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdBetween(Long value1, Long value2) {
            addCriterion("code_id between", value1, value2, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdNotBetween(Long value1, Long value2) {
            addCriterion("code_id not between", value1, value2, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andLineNumberIsNull() {
            addCriterion("line_number is null");
            return (Criteria) this;
        }

        public Criteria andLineNumberIsNotNull() {
            addCriterion("line_number is not null");
            return (Criteria) this;
        }

        public Criteria andLineNumberEqualTo(Integer value) {
            addCriterion("line_number =", value, "lineNumber");
            return (Criteria) this;
        }

        public Criteria andLineNumberNotEqualTo(Integer value) {
            addCriterion("line_number <>", value, "lineNumber");
            return (Criteria) this;
        }

        public Criteria andLineNumberGreaterThan(Integer value) {
            addCriterion("line_number >", value, "lineNumber");
            return (Criteria) this;
        }

        public Criteria andLineNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("line_number >=", value, "lineNumber");
            return (Criteria) this;
        }

        public Criteria andLineNumberLessThan(Integer value) {
            addCriterion("line_number <", value, "lineNumber");
            return (Criteria) this;
        }

        public Criteria andLineNumberLessThanOrEqualTo(Integer value) {
            addCriterion("line_number <=", value, "lineNumber");
            return (Criteria) this;
        }

        public Criteria andLineNumberIn(List<Integer> values) {
            addCriterion("line_number in", values, "lineNumber");
            return (Criteria) this;
        }

        public Criteria andLineNumberNotIn(List<Integer> values) {
            addCriterion("line_number not in", values, "lineNumber");
            return (Criteria) this;
        }

        public Criteria andLineNumberBetween(Integer value1, Integer value2) {
            addCriterion("line_number between", value1, value2, "lineNumber");
            return (Criteria) this;
        }

        public Criteria andLineNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("line_number not between", value1, value2, "lineNumber");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNull() {
            addCriterion("pam_code is null");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNotNull() {
            addCriterion("pam_code is not null");
            return (Criteria) this;
        }

        public Criteria andPamCodeEqualTo(String value) {
            addCriterion("pam_code =", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotEqualTo(String value) {
            addCriterion("pam_code <>", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThan(String value) {
            addCriterion("pam_code >", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("pam_code >=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThan(String value) {
            addCriterion("pam_code <", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThanOrEqualTo(String value) {
            addCriterion("pam_code <=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLike(String value) {
            addCriterion("pam_code like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotLike(String value) {
            addCriterion("pam_code not like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeIn(List<String> values) {
            addCriterion("pam_code in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotIn(List<String> values) {
            addCriterion("pam_code not in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeBetween(String value1, String value2) {
            addCriterion("pam_code between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotBetween(String value1, String value2) {
            addCriterion("pam_code not between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNull() {
            addCriterion("erp_code is null");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNotNull() {
            addCriterion("erp_code is not null");
            return (Criteria) this;
        }

        public Criteria andErpCodeEqualTo(String value) {
            addCriterion("erp_code =", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotEqualTo(String value) {
            addCriterion("erp_code <>", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThan(String value) {
            addCriterion("erp_code >", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("erp_code >=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThan(String value) {
            addCriterion("erp_code <", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThanOrEqualTo(String value) {
            addCriterion("erp_code <=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLike(String value) {
            addCriterion("erp_code like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotLike(String value) {
            addCriterion("erp_code not like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIn(List<String> values) {
            addCriterion("erp_code in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotIn(List<String> values) {
            addCriterion("erp_code not in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeBetween(String value1, String value2) {
            addCriterion("erp_code between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotBetween(String value1, String value2) {
            addCriterion("erp_code not between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNull() {
            addCriterion("materiel_descr is null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNotNull() {
            addCriterion("materiel_descr is not null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrEqualTo(String value) {
            addCriterion("materiel_descr =", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotEqualTo(String value) {
            addCriterion("materiel_descr <>", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThan(String value) {
            addCriterion("materiel_descr >", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThanOrEqualTo(String value) {
            addCriterion("materiel_descr >=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThan(String value) {
            addCriterion("materiel_descr <", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThanOrEqualTo(String value) {
            addCriterion("materiel_descr <=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLike(String value) {
            addCriterion("materiel_descr like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotLike(String value) {
            addCriterion("materiel_descr not like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIn(List<String> values) {
            addCriterion("materiel_descr in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotIn(List<String> values) {
            addCriterion("materiel_descr not in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrBetween(String value1, String value2) {
            addCriterion("materiel_descr between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotBetween(String value1, String value2) {
            addCriterion("materiel_descr not between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andPoBuyerIsNull() {
            addCriterion("po_buyer is null");
            return (Criteria) this;
        }

        public Criteria andPoBuyerIsNotNull() {
            addCriterion("po_buyer is not null");
            return (Criteria) this;
        }

        public Criteria andPoBuyerEqualTo(String value) {
            addCriterion("po_buyer =", value, "poBuyer");
            return (Criteria) this;
        }

        public Criteria andPoBuyerNotEqualTo(String value) {
            addCriterion("po_buyer <>", value, "poBuyer");
            return (Criteria) this;
        }

        public Criteria andPoBuyerGreaterThan(String value) {
            addCriterion("po_buyer >", value, "poBuyer");
            return (Criteria) this;
        }

        public Criteria andPoBuyerGreaterThanOrEqualTo(String value) {
            addCriterion("po_buyer >=", value, "poBuyer");
            return (Criteria) this;
        }

        public Criteria andPoBuyerLessThan(String value) {
            addCriterion("po_buyer <", value, "poBuyer");
            return (Criteria) this;
        }

        public Criteria andPoBuyerLessThanOrEqualTo(String value) {
            addCriterion("po_buyer <=", value, "poBuyer");
            return (Criteria) this;
        }

        public Criteria andPoBuyerLike(String value) {
            addCriterion("po_buyer like", value, "poBuyer");
            return (Criteria) this;
        }

        public Criteria andPoBuyerNotLike(String value) {
            addCriterion("po_buyer not like", value, "poBuyer");
            return (Criteria) this;
        }

        public Criteria andPoBuyerIn(List<String> values) {
            addCriterion("po_buyer in", values, "poBuyer");
            return (Criteria) this;
        }

        public Criteria andPoBuyerNotIn(List<String> values) {
            addCriterion("po_buyer not in", values, "poBuyer");
            return (Criteria) this;
        }

        public Criteria andPoBuyerBetween(String value1, String value2) {
            addCriterion("po_buyer between", value1, value2, "poBuyer");
            return (Criteria) this;
        }

        public Criteria andPoBuyerNotBetween(String value1, String value2) {
            addCriterion("po_buyer not between", value1, value2, "poBuyer");
            return (Criteria) this;
        }

        public Criteria andDemandDateIsNull() {
            addCriterion("demand_date is null");
            return (Criteria) this;
        }

        public Criteria andDemandDateIsNotNull() {
            addCriterion("demand_date is not null");
            return (Criteria) this;
        }

        public Criteria andDemandDateEqualTo(Date value) {
            addCriterion("demand_date =", value, "demandDate");
            return (Criteria) this;
        }

        public Criteria andDemandDateNotEqualTo(Date value) {
            addCriterion("demand_date <>", value, "demandDate");
            return (Criteria) this;
        }

        public Criteria andDemandDateGreaterThan(Date value) {
            addCriterion("demand_date >", value, "demandDate");
            return (Criteria) this;
        }

        public Criteria andDemandDateGreaterThanOrEqualTo(Date value) {
            addCriterion("demand_date >=", value, "demandDate");
            return (Criteria) this;
        }

        public Criteria andDemandDateLessThan(Date value) {
            addCriterion("demand_date <", value, "demandDate");
            return (Criteria) this;
        }

        public Criteria andDemandDateLessThanOrEqualTo(Date value) {
            addCriterion("demand_date <=", value, "demandDate");
            return (Criteria) this;
        }

        public Criteria andDemandDateIn(List<Date> values) {
            addCriterion("demand_date in", values, "demandDate");
            return (Criteria) this;
        }

        public Criteria andDemandDateNotIn(List<Date> values) {
            addCriterion("demand_date not in", values, "demandDate");
            return (Criteria) this;
        }

        public Criteria andDemandDateBetween(Date value1, Date value2) {
            addCriterion("demand_date between", value1, value2, "demandDate");
            return (Criteria) this;
        }

        public Criteria andDemandDateNotBetween(Date value1, Date value2) {
            addCriterion("demand_date not between", value1, value2, "demandDate");
            return (Criteria) this;
        }

        public Criteria andPoDeliveryIsNull() {
            addCriterion("po_delivery is null");
            return (Criteria) this;
        }

        public Criteria andPoDeliveryIsNotNull() {
            addCriterion("po_delivery is not null");
            return (Criteria) this;
        }

        public Criteria andPoDeliveryEqualTo(Date value) {
            addCriterion("po_delivery =", value, "poDelivery");
            return (Criteria) this;
        }

        public Criteria andPoDeliveryNotEqualTo(Date value) {
            addCriterion("po_delivery <>", value, "poDelivery");
            return (Criteria) this;
        }

        public Criteria andPoDeliveryGreaterThan(Date value) {
            addCriterion("po_delivery >", value, "poDelivery");
            return (Criteria) this;
        }

        public Criteria andPoDeliveryGreaterThanOrEqualTo(Date value) {
            addCriterion("po_delivery >=", value, "poDelivery");
            return (Criteria) this;
        }

        public Criteria andPoDeliveryLessThan(Date value) {
            addCriterion("po_delivery <", value, "poDelivery");
            return (Criteria) this;
        }

        public Criteria andPoDeliveryLessThanOrEqualTo(Date value) {
            addCriterion("po_delivery <=", value, "poDelivery");
            return (Criteria) this;
        }

        public Criteria andPoDeliveryIn(List<Date> values) {
            addCriterion("po_delivery in", values, "poDelivery");
            return (Criteria) this;
        }

        public Criteria andPoDeliveryNotIn(List<Date> values) {
            addCriterion("po_delivery not in", values, "poDelivery");
            return (Criteria) this;
        }

        public Criteria andPoDeliveryBetween(Date value1, Date value2) {
            addCriterion("po_delivery between", value1, value2, "poDelivery");
            return (Criteria) this;
        }

        public Criteria andPoDeliveryNotBetween(Date value1, Date value2) {
            addCriterion("po_delivery not between", value1, value2, "poDelivery");
            return (Criteria) this;
        }

        public Criteria andPoTrackIsNull() {
            addCriterion("po_track is null");
            return (Criteria) this;
        }

        public Criteria andPoTrackIsNotNull() {
            addCriterion("po_track is not null");
            return (Criteria) this;
        }

        public Criteria andPoTrackEqualTo(Date value) {
            addCriterion("po_track =", value, "poTrack");
            return (Criteria) this;
        }

        public Criteria andPoTrackNotEqualTo(Date value) {
            addCriterion("po_track <>", value, "poTrack");
            return (Criteria) this;
        }

        public Criteria andPoTrackGreaterThan(Date value) {
            addCriterion("po_track >", value, "poTrack");
            return (Criteria) this;
        }

        public Criteria andPoTrackGreaterThanOrEqualTo(Date value) {
            addCriterion("po_track >=", value, "poTrack");
            return (Criteria) this;
        }

        public Criteria andPoTrackLessThan(Date value) {
            addCriterion("po_track <", value, "poTrack");
            return (Criteria) this;
        }

        public Criteria andPoTrackLessThanOrEqualTo(Date value) {
            addCriterion("po_track <=", value, "poTrack");
            return (Criteria) this;
        }

        public Criteria andPoTrackIn(List<Date> values) {
            addCriterion("po_track in", values, "poTrack");
            return (Criteria) this;
        }

        public Criteria andPoTrackNotIn(List<Date> values) {
            addCriterion("po_track not in", values, "poTrack");
            return (Criteria) this;
        }

        public Criteria andPoTrackBetween(Date value1, Date value2) {
            addCriterion("po_track between", value1, value2, "poTrack");
            return (Criteria) this;
        }

        public Criteria andPoTrackNotBetween(Date value1, Date value2) {
            addCriterion("po_track not between", value1, value2, "poTrack");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveIsNull() {
            addCriterion("newest_po_receive is null");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveIsNotNull() {
            addCriterion("newest_po_receive is not null");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveEqualTo(String value) {
            addCriterion("newest_po_receive =", value, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveNotEqualTo(String value) {
            addCriterion("newest_po_receive <>", value, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveGreaterThan(String value) {
            addCriterion("newest_po_receive >", value, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveGreaterThanOrEqualTo(String value) {
            addCriterion("newest_po_receive >=", value, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveLessThan(String value) {
            addCriterion("newest_po_receive <", value, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveLessThanOrEqualTo(String value) {
            addCriterion("newest_po_receive <=", value, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveLike(String value) {
            addCriterion("newest_po_receive like", value, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveNotLike(String value) {
            addCriterion("newest_po_receive not like", value, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveIn(List<String> values) {
            addCriterion("newest_po_receive in", values, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveNotIn(List<String> values) {
            addCriterion("newest_po_receive not in", values, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveBetween(String value1, String value2) {
            addCriterion("newest_po_receive between", value1, value2, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveNotBetween(String value1, String value2) {
            addCriterion("newest_po_receive not between", value1, value2, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingIsNull() {
            addCriterion("newest_po_warehousing is null");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingIsNotNull() {
            addCriterion("newest_po_warehousing is not null");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingEqualTo(String value) {
            addCriterion("newest_po_warehousing =", value, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingNotEqualTo(String value) {
            addCriterion("newest_po_warehousing <>", value, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingGreaterThan(String value) {
            addCriterion("newest_po_warehousing >", value, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingGreaterThanOrEqualTo(String value) {
            addCriterion("newest_po_warehousing >=", value, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingLessThan(String value) {
            addCriterion("newest_po_warehousing <", value, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingLessThanOrEqualTo(String value) {
            addCriterion("newest_po_warehousing <=", value, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingLike(String value) {
            addCriterion("newest_po_warehousing like", value, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingNotLike(String value) {
            addCriterion("newest_po_warehousing not like", value, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingIn(List<String> values) {
            addCriterion("newest_po_warehousing in", values, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingNotIn(List<String> values) {
            addCriterion("newest_po_warehousing not in", values, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingBetween(String value1, String value2) {
            addCriterion("newest_po_warehousing between", value1, value2, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingNotBetween(String value1, String value2) {
            addCriterion("newest_po_warehousing not between", value1, value2, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(BigDecimal value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(BigDecimal value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(BigDecimal value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(BigDecimal value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<BigDecimal> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<BigDecimal> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andCancelQuantityIsNull() {
            addCriterion("cancel_quantity is null");
            return (Criteria) this;
        }

        public Criteria andCancelQuantityIsNotNull() {
            addCriterion("cancel_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andCancelQuantityEqualTo(BigDecimal value) {
            addCriterion("cancel_quantity =", value, "cancelQuantity");
            return (Criteria) this;
        }

        public Criteria andCancelQuantityNotEqualTo(BigDecimal value) {
            addCriterion("cancel_quantity <>", value, "cancelQuantity");
            return (Criteria) this;
        }

        public Criteria andCancelQuantityGreaterThan(BigDecimal value) {
            addCriterion("cancel_quantity >", value, "cancelQuantity");
            return (Criteria) this;
        }

        public Criteria andCancelQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cancel_quantity >=", value, "cancelQuantity");
            return (Criteria) this;
        }

        public Criteria andCancelQuantityLessThan(BigDecimal value) {
            addCriterion("cancel_quantity <", value, "cancelQuantity");
            return (Criteria) this;
        }

        public Criteria andCancelQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cancel_quantity <=", value, "cancelQuantity");
            return (Criteria) this;
        }

        public Criteria andCancelQuantityIn(List<BigDecimal> values) {
            addCriterion("cancel_quantity in", values, "cancelQuantity");
            return (Criteria) this;
        }

        public Criteria andCancelQuantityNotIn(List<BigDecimal> values) {
            addCriterion("cancel_quantity not in", values, "cancelQuantity");
            return (Criteria) this;
        }

        public Criteria andCancelQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cancel_quantity between", value1, value2, "cancelQuantity");
            return (Criteria) this;
        }

        public Criteria andCancelQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cancel_quantity not between", value1, value2, "cancelQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityIsNull() {
            addCriterion("receive_quantity is null");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityIsNotNull() {
            addCriterion("receive_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityEqualTo(BigDecimal value) {
            addCriterion("receive_quantity =", value, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityNotEqualTo(BigDecimal value) {
            addCriterion("receive_quantity <>", value, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityGreaterThan(BigDecimal value) {
            addCriterion("receive_quantity >", value, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("receive_quantity >=", value, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityLessThan(BigDecimal value) {
            addCriterion("receive_quantity <", value, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("receive_quantity <=", value, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityIn(List<BigDecimal> values) {
            addCriterion("receive_quantity in", values, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityNotIn(List<BigDecimal> values) {
            addCriterion("receive_quantity not in", values, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("receive_quantity between", value1, value2, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("receive_quantity not between", value1, value2, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutWarehousingQuantityIsNull() {
            addCriterion("checkout_warehousing_quantity is null");
            return (Criteria) this;
        }

        public Criteria andCheckoutWarehousingQuantityIsNotNull() {
            addCriterion("checkout_warehousing_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andCheckoutWarehousingQuantityEqualTo(BigDecimal value) {
            addCriterion("checkout_warehousing_quantity =", value, "checkoutWarehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutWarehousingQuantityNotEqualTo(BigDecimal value) {
            addCriterion("checkout_warehousing_quantity <>", value, "checkoutWarehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutWarehousingQuantityGreaterThan(BigDecimal value) {
            addCriterion("checkout_warehousing_quantity >", value, "checkoutWarehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutWarehousingQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("checkout_warehousing_quantity >=", value, "checkoutWarehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutWarehousingQuantityLessThan(BigDecimal value) {
            addCriterion("checkout_warehousing_quantity <", value, "checkoutWarehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutWarehousingQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("checkout_warehousing_quantity <=", value, "checkoutWarehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutWarehousingQuantityIn(List<BigDecimal> values) {
            addCriterion("checkout_warehousing_quantity in", values, "checkoutWarehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutWarehousingQuantityNotIn(List<BigDecimal> values) {
            addCriterion("checkout_warehousing_quantity not in", values, "checkoutWarehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutWarehousingQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("checkout_warehousing_quantity between", value1, value2, "checkoutWarehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutWarehousingQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("checkout_warehousing_quantity not between", value1, value2, "checkoutWarehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityIsNull() {
            addCriterion("warehousing_quantity is null");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityIsNotNull() {
            addCriterion("warehousing_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityEqualTo(BigDecimal value) {
            addCriterion("warehousing_quantity =", value, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityNotEqualTo(BigDecimal value) {
            addCriterion("warehousing_quantity <>", value, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityGreaterThan(BigDecimal value) {
            addCriterion("warehousing_quantity >", value, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("warehousing_quantity >=", value, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityLessThan(BigDecimal value) {
            addCriterion("warehousing_quantity <", value, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("warehousing_quantity <=", value, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityIn(List<BigDecimal> values) {
            addCriterion("warehousing_quantity in", values, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityNotIn(List<BigDecimal> values) {
            addCriterion("warehousing_quantity not in", values, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("warehousing_quantity between", value1, value2, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("warehousing_quantity not between", value1, value2, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityIsNull() {
            addCriterion("not_delivery_quantity is null");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityIsNotNull() {
            addCriterion("not_delivery_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityEqualTo(BigDecimal value) {
            addCriterion("not_delivery_quantity =", value, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityNotEqualTo(BigDecimal value) {
            addCriterion("not_delivery_quantity <>", value, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityGreaterThan(BigDecimal value) {
            addCriterion("not_delivery_quantity >", value, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("not_delivery_quantity >=", value, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityLessThan(BigDecimal value) {
            addCriterion("not_delivery_quantity <", value, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("not_delivery_quantity <=", value, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityIn(List<BigDecimal> values) {
            addCriterion("not_delivery_quantity in", values, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityNotIn(List<BigDecimal> values) {
            addCriterion("not_delivery_quantity not in", values, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("not_delivery_quantity between", value1, value2, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("not_delivery_quantity not between", value1, value2, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutQuantityIsNull() {
            addCriterion("checkout_quantity is null");
            return (Criteria) this;
        }

        public Criteria andCheckoutQuantityIsNotNull() {
            addCriterion("checkout_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andCheckoutQuantityEqualTo(BigDecimal value) {
            addCriterion("checkout_quantity =", value, "checkoutQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutQuantityNotEqualTo(BigDecimal value) {
            addCriterion("checkout_quantity <>", value, "checkoutQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutQuantityGreaterThan(BigDecimal value) {
            addCriterion("checkout_quantity >", value, "checkoutQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("checkout_quantity >=", value, "checkoutQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutQuantityLessThan(BigDecimal value) {
            addCriterion("checkout_quantity <", value, "checkoutQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("checkout_quantity <=", value, "checkoutQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutQuantityIn(List<BigDecimal> values) {
            addCriterion("checkout_quantity in", values, "checkoutQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutQuantityNotIn(List<BigDecimal> values) {
            addCriterion("checkout_quantity not in", values, "checkoutQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("checkout_quantity between", value1, value2, "checkoutQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckoutQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("checkout_quantity not between", value1, value2, "checkoutQuantity");
            return (Criteria) this;
        }

        public Criteria andPoArrivalIsNull() {
            addCriterion("po_arrival is null");
            return (Criteria) this;
        }

        public Criteria andPoArrivalIsNotNull() {
            addCriterion("po_arrival is not null");
            return (Criteria) this;
        }

        public Criteria andPoArrivalEqualTo(Integer value) {
            addCriterion("po_arrival =", value, "poArrival");
            return (Criteria) this;
        }

        public Criteria andPoArrivalNotEqualTo(Integer value) {
            addCriterion("po_arrival <>", value, "poArrival");
            return (Criteria) this;
        }

        public Criteria andPoArrivalGreaterThan(Integer value) {
            addCriterion("po_arrival >", value, "poArrival");
            return (Criteria) this;
        }

        public Criteria andPoArrivalGreaterThanOrEqualTo(Integer value) {
            addCriterion("po_arrival >=", value, "poArrival");
            return (Criteria) this;
        }

        public Criteria andPoArrivalLessThan(Integer value) {
            addCriterion("po_arrival <", value, "poArrival");
            return (Criteria) this;
        }

        public Criteria andPoArrivalLessThanOrEqualTo(Integer value) {
            addCriterion("po_arrival <=", value, "poArrival");
            return (Criteria) this;
        }

        public Criteria andPoArrivalIn(List<Integer> values) {
            addCriterion("po_arrival in", values, "poArrival");
            return (Criteria) this;
        }

        public Criteria andPoArrivalNotIn(List<Integer> values) {
            addCriterion("po_arrival not in", values, "poArrival");
            return (Criteria) this;
        }

        public Criteria andPoArrivalBetween(Integer value1, Integer value2) {
            addCriterion("po_arrival between", value1, value2, "poArrival");
            return (Criteria) this;
        }

        public Criteria andPoArrivalNotBetween(Integer value1, Integer value2) {
            addCriterion("po_arrival not between", value1, value2, "poArrival");
            return (Criteria) this;
        }

        public Criteria andPoStatusIsNull() {
            addCriterion("po_status is null");
            return (Criteria) this;
        }

        public Criteria andPoStatusIsNotNull() {
            addCriterion("po_status is not null");
            return (Criteria) this;
        }

        public Criteria andPoStatusEqualTo(Integer value) {
            addCriterion("po_status =", value, "poStatus");
            return (Criteria) this;
        }

        public Criteria andPoStatusNotEqualTo(Integer value) {
            addCriterion("po_status <>", value, "poStatus");
            return (Criteria) this;
        }

        public Criteria andPoStatusGreaterThan(Integer value) {
            addCriterion("po_status >", value, "poStatus");
            return (Criteria) this;
        }

        public Criteria andPoStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("po_status >=", value, "poStatus");
            return (Criteria) this;
        }

        public Criteria andPoStatusLessThan(Integer value) {
            addCriterion("po_status <", value, "poStatus");
            return (Criteria) this;
        }

        public Criteria andPoStatusLessThanOrEqualTo(Integer value) {
            addCriterion("po_status <=", value, "poStatus");
            return (Criteria) this;
        }

        public Criteria andPoStatusIn(List<Integer> values) {
            addCriterion("po_status in", values, "poStatus");
            return (Criteria) this;
        }

        public Criteria andPoStatusNotIn(List<Integer> values) {
            addCriterion("po_status not in", values, "poStatus");
            return (Criteria) this;
        }

        public Criteria andPoStatusBetween(Integer value1, Integer value2) {
            addCriterion("po_status between", value1, value2, "poStatus");
            return (Criteria) this;
        }

        public Criteria andPoStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("po_status not between", value1, value2, "poStatus");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityIsNull() {
            addCriterion("invoicing_quantity is null");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityIsNotNull() {
            addCriterion("invoicing_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityEqualTo(BigDecimal value) {
            addCriterion("invoicing_quantity =", value, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityNotEqualTo(BigDecimal value) {
            addCriterion("invoicing_quantity <>", value, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityGreaterThan(BigDecimal value) {
            addCriterion("invoicing_quantity >", value, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("invoicing_quantity >=", value, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityLessThan(BigDecimal value) {
            addCriterion("invoicing_quantity <", value, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("invoicing_quantity <=", value, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityIn(List<BigDecimal> values) {
            addCriterion("invoicing_quantity in", values, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityNotIn(List<BigDecimal> values) {
            addCriterion("invoicing_quantity not in", values, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invoicing_quantity between", value1, value2, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invoicing_quantity not between", value1, value2, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityIsNull() {
            addCriterion("not_invoicing_quantity is null");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityIsNotNull() {
            addCriterion("not_invoicing_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityEqualTo(BigDecimal value) {
            addCriterion("not_invoicing_quantity =", value, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityNotEqualTo(BigDecimal value) {
            addCriterion("not_invoicing_quantity <>", value, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityGreaterThan(BigDecimal value) {
            addCriterion("not_invoicing_quantity >", value, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("not_invoicing_quantity >=", value, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityLessThan(BigDecimal value) {
            addCriterion("not_invoicing_quantity <", value, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("not_invoicing_quantity <=", value, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityIn(List<BigDecimal> values) {
            addCriterion("not_invoicing_quantity in", values, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityNotIn(List<BigDecimal> values) {
            addCriterion("not_invoicing_quantity not in", values, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("not_invoicing_quantity between", value1, value2, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("not_invoicing_quantity not between", value1, value2, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andVendorNumIsNull() {
            addCriterion("vendor_num is null");
            return (Criteria) this;
        }

        public Criteria andVendorNumIsNotNull() {
            addCriterion("vendor_num is not null");
            return (Criteria) this;
        }

        public Criteria andVendorNumEqualTo(String value) {
            addCriterion("vendor_num =", value, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumNotEqualTo(String value) {
            addCriterion("vendor_num <>", value, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumGreaterThan(String value) {
            addCriterion("vendor_num >", value, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_num >=", value, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumLessThan(String value) {
            addCriterion("vendor_num <", value, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumLessThanOrEqualTo(String value) {
            addCriterion("vendor_num <=", value, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumLike(String value) {
            addCriterion("vendor_num like", value, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumNotLike(String value) {
            addCriterion("vendor_num not like", value, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumIn(List<String> values) {
            addCriterion("vendor_num in", values, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumNotIn(List<String> values) {
            addCriterion("vendor_num not in", values, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumBetween(String value1, String value2) {
            addCriterion("vendor_num between", value1, value2, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumNotBetween(String value1, String value2) {
            addCriterion("vendor_num not between", value1, value2, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNull() {
            addCriterion("vendor_name is null");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNotNull() {
            addCriterion("vendor_name is not null");
            return (Criteria) this;
        }

        public Criteria andVendorNameEqualTo(String value) {
            addCriterion("vendor_name =", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotEqualTo(String value) {
            addCriterion("vendor_name <>", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThan(String value) {
            addCriterion("vendor_name >", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_name >=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThan(String value) {
            addCriterion("vendor_name <", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThanOrEqualTo(String value) {
            addCriterion("vendor_name <=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLike(String value) {
            addCriterion("vendor_name like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotLike(String value) {
            addCriterion("vendor_name not like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameIn(List<String> values) {
            addCriterion("vendor_name in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotIn(List<String> values) {
            addCriterion("vendor_name not in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameBetween(String value1, String value2) {
            addCriterion("vendor_name between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotBetween(String value1, String value2) {
            addCriterion("vendor_name not between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeIsNull() {
            addCriterion("vendor_site_code is null");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeIsNotNull() {
            addCriterion("vendor_site_code is not null");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeEqualTo(String value) {
            addCriterion("vendor_site_code =", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotEqualTo(String value) {
            addCriterion("vendor_site_code <>", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeGreaterThan(String value) {
            addCriterion("vendor_site_code >", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_site_code >=", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeLessThan(String value) {
            addCriterion("vendor_site_code <", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeLessThanOrEqualTo(String value) {
            addCriterion("vendor_site_code <=", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeLike(String value) {
            addCriterion("vendor_site_code like", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotLike(String value) {
            addCriterion("vendor_site_code not like", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeIn(List<String> values) {
            addCriterion("vendor_site_code in", values, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotIn(List<String> values) {
            addCriterion("vendor_site_code not in", values, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeBetween(String value1, String value2) {
            addCriterion("vendor_site_code between", value1, value2, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotBetween(String value1, String value2) {
            addCriterion("vendor_site_code not between", value1, value2, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNull() {
            addCriterion("wbs_summary_code is null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNotNull() {
            addCriterion("wbs_summary_code is not null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeEqualTo(String value) {
            addCriterion("wbs_summary_code =", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotEqualTo(String value) {
            addCriterion("wbs_summary_code <>", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThan(String value) {
            addCriterion("wbs_summary_code >", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code >=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThan(String value) {
            addCriterion("wbs_summary_code <", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code <=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLike(String value) {
            addCriterion("wbs_summary_code like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotLike(String value) {
            addCriterion("wbs_summary_code not like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIn(List<String> values) {
            addCriterion("wbs_summary_code in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotIn(List<String> values) {
            addCriterion("wbs_summary_code not in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeBetween(String value1, String value2) {
            addCriterion("wbs_summary_code between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotBetween(String value1, String value2) {
            addCriterion("wbs_summary_code not between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNull() {
            addCriterion("activity_code is null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNotNull() {
            addCriterion("activity_code is not null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeEqualTo(String value) {
            addCriterion("activity_code =", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotEqualTo(String value) {
            addCriterion("activity_code <>", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThan(String value) {
            addCriterion("activity_code >", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("activity_code >=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThan(String value) {
            addCriterion("activity_code <", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThanOrEqualTo(String value) {
            addCriterion("activity_code <=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLike(String value) {
            addCriterion("activity_code like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotLike(String value) {
            addCriterion("activity_code not like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIn(List<String> values) {
            addCriterion("activity_code in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotIn(List<String> values) {
            addCriterion("activity_code not in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeBetween(String value1, String value2) {
            addCriterion("activity_code between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotBetween(String value1, String value2) {
            addCriterion("activity_code not between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIsNull() {
            addCriterion("requirement_code is null");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIsNotNull() {
            addCriterion("requirement_code is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeEqualTo(String value) {
            addCriterion("requirement_code =", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotEqualTo(String value) {
            addCriterion("requirement_code <>", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeGreaterThan(String value) {
            addCriterion("requirement_code >", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeGreaterThanOrEqualTo(String value) {
            addCriterion("requirement_code >=", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLessThan(String value) {
            addCriterion("requirement_code <", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLessThanOrEqualTo(String value) {
            addCriterion("requirement_code <=", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLike(String value) {
            addCriterion("requirement_code like", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotLike(String value) {
            addCriterion("requirement_code not like", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIn(List<String> values) {
            addCriterion("requirement_code in", values, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotIn(List<String> values) {
            addCriterion("requirement_code not in", values, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeBetween(String value1, String value2) {
            addCriterion("requirement_code between", value1, value2, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotBetween(String value1, String value2) {
            addCriterion("requirement_code not between", value1, value2, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}