package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "采购合同应计负债账龄明细报表")
public class ReportPurchaseContractDebtAges extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "业务实体")
    private String ouName;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "采购合同号")
    private String code;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "供应商地点")
    private String vendorSiteCode;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "合同汇率")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "合同开票不含税金额（原币）")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "合同开票不含税金额（本位币）")
    private BigDecimal standardInvoiceAmount;

    @ApiModelProperty(value = "合同进展金额（原币）")
    private BigDecimal progressAmount;

    @ApiModelProperty(value = "合同进展金额（本位币）")
    private BigDecimal standardProgressAmount;

    @ApiModelProperty(value = "合同进展未开票金额（原币）")
    private BigDecimal remainAmount;

    @ApiModelProperty(value = "合同进展未开票金额（本位币）")
    private BigDecimal standardRemainAmount;

    @ApiModelProperty(value = "合同进展未开票金额（重估后）")
    private BigDecimal evaluateRemainAmount;

    @ApiModelProperty(value = "规则值拼接")
    private String ruleValue;

    @ApiModelProperty(value = "规则范围拼接")
    private String ruleScope;

    @ApiModelProperty(value = "规则名称拼接")
    private String ruleName;

    @ApiModelProperty(value = "删除标志")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode == null ? null : vendorCode.trim();
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName == null ? null : vendorName.trim();
    }

    public String getVendorSiteCode() {
        return vendorSiteCode;
    }

    public void setVendorSiteCode(String vendorSiteCode) {
        this.vendorSiteCode = vendorSiteCode == null ? null : vendorSiteCode.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public BigDecimal getStandardInvoiceAmount() {
        return standardInvoiceAmount;
    }

    public void setStandardInvoiceAmount(BigDecimal standardInvoiceAmount) {
        this.standardInvoiceAmount = standardInvoiceAmount;
    }

    public BigDecimal getProgressAmount() {
        return progressAmount;
    }

    public void setProgressAmount(BigDecimal progressAmount) {
        this.progressAmount = progressAmount;
    }

    public BigDecimal getStandardProgressAmount() {
        return standardProgressAmount;
    }

    public void setStandardProgressAmount(BigDecimal standardProgressAmount) {
        this.standardProgressAmount = standardProgressAmount;
    }

    public BigDecimal getRemainAmount() {
        return remainAmount;
    }

    public void setRemainAmount(BigDecimal remainAmount) {
        this.remainAmount = remainAmount;
    }

    public BigDecimal getStandardRemainAmount() {
        return standardRemainAmount;
    }

    public void setStandardRemainAmount(BigDecimal standardRemainAmount) {
        this.standardRemainAmount = standardRemainAmount;
    }

    public BigDecimal getEvaluateRemainAmount() {
        return evaluateRemainAmount;
    }

    public void setEvaluateRemainAmount(BigDecimal evaluateRemainAmount) {
        this.evaluateRemainAmount = evaluateRemainAmount;
    }

    public String getRuleValue() {
        return ruleValue;
    }

    public void setRuleValue(String ruleValue) {
        this.ruleValue = ruleValue == null ? null : ruleValue.trim();
    }

    public String getRuleScope() {
        return ruleScope;
    }

    public void setRuleScope(String ruleScope) {
        this.ruleScope = ruleScope == null ? null : ruleScope.trim();
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName == null ? null : ruleName.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", ouName=").append(ouName);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", code=").append(code);
        sb.append(", vendorCode=").append(vendorCode);
        sb.append(", vendorName=").append(vendorName);
        sb.append(", vendorSiteCode=").append(vendorSiteCode);
        sb.append(", currency=").append(currency);
        sb.append(", conversionRate=").append(conversionRate);
        sb.append(", invoiceAmount=").append(invoiceAmount);
        sb.append(", standardInvoiceAmount=").append(standardInvoiceAmount);
        sb.append(", progressAmount=").append(progressAmount);
        sb.append(", standardProgressAmount=").append(standardProgressAmount);
        sb.append(", remainAmount=").append(remainAmount);
        sb.append(", standardRemainAmount=").append(standardRemainAmount);
        sb.append(", evaluateRemainAmount=").append(evaluateRemainAmount);
        sb.append(", ruleValue=").append(ruleValue);
        sb.append(", ruleScope=").append(ruleScope);
        sb.append(", ruleName=").append(ruleName);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}