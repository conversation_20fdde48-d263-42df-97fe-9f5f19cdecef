package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

/**
 * Description
 * Created by l<PERSON>qing
 * Date 2021/11/5 16:57
 */
public class ReportProjectMilepostExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer number;

    @Excel(name = "项目编号", width = 15)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "项目类型", width = 30)
    private String projectType;

    @Excel(name = "业务分类", width = 30)
    private String unitName;

    @Excel(name = "阶段类型", width = 30)
    private String projectMilepostName;

    private Date baseStartTime;

    private Date baseEndTime;

    private Date startTime;

    private Date endTime;

    private Date actualStartTime;

    private Date actualEndTime;

    @Excel(name = "分类", width = 30)
    private String classification;

    @Excel(name = "开始时间", width = 30)
    private String classificationStartTimeStr;

    @Excel(name = "结束时间", width = 30)
    private String classificationEndTimeStr;

    @Excel(name = "天数", width = 30)
    private String classificationDateCountStr;

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getProjectMilepostName() {
        return projectMilepostName;
    }

    public void setProjectMilepostName(String projectMilepostName) {
        this.projectMilepostName = projectMilepostName;
    }

    public Date getBaseStartTime() {
        return baseStartTime;
    }

    public void setBaseStartTime(Date baseStartTime) {
        this.baseStartTime = baseStartTime;
    }

    public Date getBaseEndTime() {
        return baseEndTime;
    }

    public void setBaseEndTime(Date baseEndTime) {
        this.baseEndTime = baseEndTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getActualStartTime() {
        return actualStartTime;
    }

    public void setActualStartTime(Date actualStartTime) {
        this.actualStartTime = actualStartTime;
    }

    public Date getActualEndTime() {
        return actualEndTime;
    }

    public void setActualEndTime(Date actualEndTime) {
        this.actualEndTime = actualEndTime;
    }

    public String getClassification() {
        return classification;
    }

    public void setClassification(String classification) {
        this.classification = classification;
    }

    public String getClassificationStartTimeStr() {
        return classificationStartTimeStr;
    }

    public void setClassificationStartTimeStr(String classificationStartTimeStr) {
        this.classificationStartTimeStr = classificationStartTimeStr;
    }

    public String getClassificationEndTimeStr() {
        return classificationEndTimeStr;
    }

    public void setClassificationEndTimeStr(String classificationEndTimeStr) {
        this.classificationEndTimeStr = classificationEndTimeStr;
    }

    public String getClassificationDateCountStr() {
        return classificationDateCountStr;
    }

    public void setClassificationDateCountStr(String classificationDateCountStr) {
        this.classificationDateCountStr = classificationDateCountStr;
    }
}
