package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "物料历史价格表")
public class MaterialHistoryPrice extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "PAM编码")
    private String pamCode;

    @ApiModelProperty(value = "ERP编码")
    private String itemCode;

    @ApiModelProperty(value = "物料描述")
    private String itemInfo;

    @ApiModelProperty(value = "基本计量单位")
    private String unit;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "下单日期(采购合同预算行/采购订单订单行的创建时间)")
    private Date orderDate;

    @ApiModelProperty(value = "单据号(采购合同编号/采购订单编号)")
    private String code;

    @ApiModelProperty(value = "物料大类")
    private String materialClassification;

    @ApiModelProperty(value = "物料中类")
    private String codingMiddleclass;

    @ApiModelProperty(value = "物料小类")
    private String materialType;

    @ApiModelProperty(value = "物料名称")
    private String name;

    @ApiModelProperty(value = "型号/规格")
    private String model;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "图号")
    private String figureNumber;

    @ApiModelProperty(value = "图纸版本号")
    private String chartVersion;

    @ApiModelProperty(value = "加工分类")
    private String machiningPartType;

    @ApiModelProperty(value = "材质")
    private String material;

    @ApiModelProperty(value = "单位重量(Kg)")
    private BigDecimal unitWeight;

    @ApiModelProperty(value = "表面处理")
    private String materialProcessing;

    @ApiModelProperty(value = "品牌商物料编码")
    private String brandMaterialCode;

    @ApiModelProperty(value = "来源：1-采购合同 2-采购订单")
    private Integer source;

    @ApiModelProperty(value = "业务实体id")
    private Long ouId;

    @ApiModelProperty(value = "删除标志")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode == null ? null : pamCode.trim();
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode == null ? null : itemCode.trim();
    }

    public String getItemInfo() {
        return itemInfo;
    }

    public void setItemInfo(String itemInfo) {
        this.itemInfo = itemInfo == null ? null : itemInfo.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public Date getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getMaterialClassification() {
        return materialClassification;
    }

    public void setMaterialClassification(String materialClassification) {
        this.materialClassification = materialClassification == null ? null : materialClassification.trim();
    }

    public String getCodingMiddleclass() {
        return codingMiddleclass;
    }

    public void setCodingMiddleclass(String codingMiddleclass) {
        this.codingMiddleclass = codingMiddleclass == null ? null : codingMiddleclass.trim();
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType == null ? null : materialType.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public String getFigureNumber() {
        return figureNumber;
    }

    public void setFigureNumber(String figureNumber) {
        this.figureNumber = figureNumber == null ? null : figureNumber.trim();
    }

    public String getChartVersion() {
        return chartVersion;
    }

    public void setChartVersion(String chartVersion) {
        this.chartVersion = chartVersion == null ? null : chartVersion.trim();
    }

    public String getMachiningPartType() {
        return machiningPartType;
    }

    public void setMachiningPartType(String machiningPartType) {
        this.machiningPartType = machiningPartType == null ? null : machiningPartType.trim();
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material == null ? null : material.trim();
    }

    public BigDecimal getUnitWeight() {
        return unitWeight;
    }

    public void setUnitWeight(BigDecimal unitWeight) {
        this.unitWeight = unitWeight;
    }

    public String getMaterialProcessing() {
        return materialProcessing;
    }

    public void setMaterialProcessing(String materialProcessing) {
        this.materialProcessing = materialProcessing == null ? null : materialProcessing.trim();
    }

    public String getBrandMaterialCode() {
        return brandMaterialCode;
    }

    public void setBrandMaterialCode(String brandMaterialCode) {
        this.brandMaterialCode = brandMaterialCode == null ? null : brandMaterialCode.trim();
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", pamCode=").append(pamCode);
        sb.append(", itemCode=").append(itemCode);
        sb.append(", itemInfo=").append(itemInfo);
        sb.append(", unit=").append(unit);
        sb.append(", price=").append(price);
        sb.append(", currency=").append(currency);
        sb.append(", orderDate=").append(orderDate);
        sb.append(", code=").append(code);
        sb.append(", materialClassification=").append(materialClassification);
        sb.append(", codingMiddleclass=").append(codingMiddleclass);
        sb.append(", materialType=").append(materialType);
        sb.append(", name=").append(name);
        sb.append(", model=").append(model);
        sb.append(", brand=").append(brand);
        sb.append(", figureNumber=").append(figureNumber);
        sb.append(", chartVersion=").append(chartVersion);
        sb.append(", machiningPartType=").append(machiningPartType);
        sb.append(", material=").append(material);
        sb.append(", unitWeight=").append(unitWeight);
        sb.append(", materialProcessing=").append(materialProcessing);
        sb.append(", brandMaterialCode=").append(brandMaterialCode);
        sb.append(", source=").append(source);
        sb.append(", ouId=").append(ouId);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}