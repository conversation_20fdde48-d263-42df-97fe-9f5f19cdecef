package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "销售合同接单报表")
public class ReportContractProcess extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "接单年度")
    private String processYear;

    @ApiModelProperty(value = "接单月份")
    private String processMonth;

    @ApiModelProperty(value = "接单类型 新增(1), 变更(2)")
    private Integer processType;

    @ApiModelProperty(value = "是否框架合同")
    private Integer frameFlag;

    @ApiModelProperty(value = "订单日期")
    private Date processTime;

    @ApiModelProperty(value = "PAM合同编号")
    private String code;

    @ApiModelProperty(value = "法务合同号")
    private String legalContractNum;

    @ApiModelProperty(value = "框架合同号")
    private String frameCode;

    @ApiModelProperty(value = "合同名称")
    private String name;

    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "是否内部客户:1-内部客户;0-外部客户")
    private Integer customerType;

    @ApiModelProperty(value = "不含税金额")
    private BigDecimal excludingTaxAmount;

    @ApiModelProperty(value = "币种编码")
    private String currency;

    @ApiModelProperty(value = "汇率")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "业务分类（销售部门）")
    private String unitName;

    @ApiModelProperty(value = "销售经理-初始")
    private String oldSalesManager;

    @ApiModelProperty(value = "销售经理-当前")
    private String newSalesManager;

    @ApiModelProperty(value = "项目号")
    private String projectCode;

    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    @ApiModelProperty(value = "业务实体")
    private String ouName;

    @ApiModelProperty(value = "业务模式（项目类型）")
    private String businessTypeName;

    @ApiModelProperty(value = "是否删除")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getProcessYear() {
        return processYear;
    }

    public void setProcessYear(String processYear) {
        this.processYear = processYear == null ? null : processYear.trim();
    }

    public String getProcessMonth() {
        return processMonth;
    }

    public void setProcessMonth(String processMonth) {
        this.processMonth = processMonth == null ? null : processMonth.trim();
    }

    public Integer getProcessType() {
        return processType;
    }

    public void setProcessType(Integer processType) {
        this.processType = processType;
    }

    public Integer getFrameFlag() {
        return frameFlag;
    }

    public void setFrameFlag(Integer frameFlag) {
        this.frameFlag = frameFlag;
    }

    public Date getProcessTime() {
        return processTime;
    }

    public void setProcessTime(Date processTime) {
        this.processTime = processTime;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getLegalContractNum() {
        return legalContractNum;
    }

    public void setLegalContractNum(String legalContractNum) {
        this.legalContractNum = legalContractNum == null ? null : legalContractNum.trim();
    }

    public String getFrameCode() {
        return frameCode;
    }

    public void setFrameCode(String frameCode) {
        this.frameCode = frameCode == null ? null : frameCode.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode == null ? null : customerCode.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public BigDecimal getExcludingTaxAmount() {
        return excludingTaxAmount;
    }

    public void setExcludingTaxAmount(BigDecimal excludingTaxAmount) {
        this.excludingTaxAmount = excludingTaxAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public String getOldSalesManager() {
        return oldSalesManager;
    }

    public void setOldSalesManager(String oldSalesManager) {
        this.oldSalesManager = oldSalesManager == null ? null : oldSalesManager.trim();
    }

    public String getNewSalesManager() {
        return newSalesManager;
    }

    public void setNewSalesManager(String newSalesManager) {
        this.newSalesManager = newSalesManager == null ? null : newSalesManager.trim();
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public String getBusinessTypeName() {
        return businessTypeName;
    }

    public void setBusinessTypeName(String businessTypeName) {
        this.businessTypeName = businessTypeName == null ? null : businessTypeName.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", processYear=").append(processYear);
        sb.append(", processMonth=").append(processMonth);
        sb.append(", processType=").append(processType);
        sb.append(", frameFlag=").append(frameFlag);
        sb.append(", processTime=").append(processTime);
        sb.append(", code=").append(code);
        sb.append(", legalContractNum=").append(legalContractNum);
        sb.append(", frameCode=").append(frameCode);
        sb.append(", name=").append(name);
        sb.append(", customerCode=").append(customerCode);
        sb.append(", customerName=").append(customerName);
        sb.append(", customerType=").append(customerType);
        sb.append(", excludingTaxAmount=").append(excludingTaxAmount);
        sb.append(", currency=").append(currency);
        sb.append(", conversionRate=").append(conversionRate);
        sb.append(", unitName=").append(unitName);
        sb.append(", oldSalesManager=").append(oldSalesManager);
        sb.append(", newSalesManager=").append(newSalesManager);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", ouName=").append(ouName);
        sb.append(", businessTypeName=").append(businessTypeName);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}