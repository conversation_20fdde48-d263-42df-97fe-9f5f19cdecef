package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;

public class ReportUnconfirmedIncome extends LongIdEntity implements Serializable {
    private Long id;

    private Long executeId;

    private Long contractId;

    private String contractCode;

    private String contractName;

    private String orderYear;

    private String orderMonth;

    private Long unitId;

    private String unitName;

    private Long customerId;

    private String customerCode;

    private String customerName;

    private String customerType;

    private BigDecimal initialPeriodOrderAmount;

    private BigDecimal hisYearConfirmedIncome;

    private BigDecimal initialPeriodOb;

    private BigDecimal curYearOrderAmount;

    private BigDecimal curYearConfirmedIncome;

    private BigDecimal remainingOrderAmount;

    private String contractStatus;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private String projectTypeName;

    private String projectStatus;

    private Long ouId;

    private String ouName;

    private BigDecimal totalConfirmedIncomeAmount;

    private BigDecimal incomeAchivementRate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode == null ? null : contractCode.trim();
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName == null ? null : contractName.trim();
    }

    public String getOrderYear() {
        return orderYear;
    }

    public void setOrderYear(String orderYear) {
        this.orderYear = orderYear == null ? null : orderYear.trim();
    }

    public String getOrderMonth() {
        return orderMonth;
    }

    public void setOrderMonth(String orderMonth) {
        this.orderMonth = orderMonth == null ? null : orderMonth.trim();
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode == null ? null : customerCode.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType == null ? null : customerType.trim();
    }

    public BigDecimal getInitialPeriodOrderAmount() {
        return initialPeriodOrderAmount;
    }

    public void setInitialPeriodOrderAmount(BigDecimal initialPeriodOrderAmount) {
        this.initialPeriodOrderAmount = initialPeriodOrderAmount;
    }

    public BigDecimal getHisYearConfirmedIncome() {
        return hisYearConfirmedIncome;
    }

    public void setHisYearConfirmedIncome(BigDecimal hisYearConfirmedIncome) {
        this.hisYearConfirmedIncome = hisYearConfirmedIncome;
    }

    public BigDecimal getInitialPeriodOb() {
        return initialPeriodOb;
    }

    public void setInitialPeriodOb(BigDecimal initialPeriodOb) {
        this.initialPeriodOb = initialPeriodOb;
    }

    public BigDecimal getCurYearOrderAmount() {
        return curYearOrderAmount;
    }

    public void setCurYearOrderAmount(BigDecimal curYearOrderAmount) {
        this.curYearOrderAmount = curYearOrderAmount;
    }

    public BigDecimal getCurYearConfirmedIncome() {
        return curYearConfirmedIncome;
    }

    public void setCurYearConfirmedIncome(BigDecimal curYearConfirmedIncome) {
        this.curYearConfirmedIncome = curYearConfirmedIncome;
    }

    public BigDecimal getRemainingOrderAmount() {
        return remainingOrderAmount;
    }

    public void setRemainingOrderAmount(BigDecimal remainingOrderAmount) {
        this.remainingOrderAmount = remainingOrderAmount;
    }

    public String getContractStatus() {
        return contractStatus;
    }

    public void setContractStatus(String contractStatus) {
        this.contractStatus = contractStatus == null ? null : contractStatus.trim();
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectTypeName() {
        return projectTypeName;
    }

    public void setProjectTypeName(String projectTypeName) {
        this.projectTypeName = projectTypeName == null ? null : projectTypeName.trim();
    }

    public String getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(String projectStatus) {
        this.projectStatus = projectStatus == null ? null : projectStatus.trim();
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public BigDecimal getTotalConfirmedIncomeAmount() {
        return totalConfirmedIncomeAmount;
    }

    public void setTotalConfirmedIncomeAmount(BigDecimal totalConfirmedIncomeAmount) {
        this.totalConfirmedIncomeAmount = totalConfirmedIncomeAmount;
    }

    public BigDecimal getIncomeAchivementRate() {
        return incomeAchivementRate;
    }

    public void setIncomeAchivementRate(BigDecimal incomeAchivementRate) {
        this.incomeAchivementRate = incomeAchivementRate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", executeId=").append(executeId);
        sb.append(", contractId=").append(contractId);
        sb.append(", contractCode=").append(contractCode);
        sb.append(", contractName=").append(contractName);
        sb.append(", orderYear=").append(orderYear);
        sb.append(", orderMonth=").append(orderMonth);
        sb.append(", unitId=").append(unitId);
        sb.append(", unitName=").append(unitName);
        sb.append(", customerId=").append(customerId);
        sb.append(", customerCode=").append(customerCode);
        sb.append(", customerName=").append(customerName);
        sb.append(", customerType=").append(customerType);
        sb.append(", initialPeriodOrderAmount=").append(initialPeriodOrderAmount);
        sb.append(", hisYearConfirmedIncome=").append(hisYearConfirmedIncome);
        sb.append(", initialPeriodOb=").append(initialPeriodOb);
        sb.append(", curYearOrderAmount=").append(curYearOrderAmount);
        sb.append(", curYearConfirmedIncome=").append(curYearConfirmedIncome);
        sb.append(", remainingOrderAmount=").append(remainingOrderAmount);
        sb.append(", contractStatus=").append(contractStatus);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectTypeName=").append(projectTypeName);
        sb.append(", projectStatus=").append(projectStatus);
        sb.append(", ouId=").append(ouId);
        sb.append(", ouName=").append(ouName);
        sb.append(", totalConfirmedIncomeAmount=").append(totalConfirmedIncomeAmount);
        sb.append(", incomeAchivementRate=").append(incomeAchivementRate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}