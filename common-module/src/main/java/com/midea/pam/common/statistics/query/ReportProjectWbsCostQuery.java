package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "ReportProjectProcessProfitQuery", description = "柔性项目成本明细表")
public class ReportProjectWbsCostQuery {

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    @ApiModelProperty(value = "报表ID")
    private Long reportId;

    private Long personal;

    private Long companyId;


    private Long createBy;

    @ApiModelProperty(value = "销售部门id列表")
    private List<Long> unitIdList;
    @ApiModelProperty(value = "业务实体id列表")
    private List<Long> ouIdList;

    @ApiModelProperty(value = "业务模式id列表")
    private List<Long> projectTypeList;

    @ApiModelProperty(value = "项目状态")
    private List<Integer> projectStatusList;

    @ApiModelProperty(value = "项目经理")
    private List<String> projectManageMipList;

    //1）项目成本汇总   2）开票回款信息    3）里程碑信息    4）需求预算明细   5）在途成本明细
    @ApiModelProperty(value = "导出内容")
    private List<Integer> exportContentList;



}
