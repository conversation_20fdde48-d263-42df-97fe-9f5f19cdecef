package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectCostHumanItemRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectCostHumanItemRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andSummaryIdIsNull() {
            addCriterion("summary_id is null");
            return (Criteria) this;
        }

        public Criteria andSummaryIdIsNotNull() {
            addCriterion("summary_id is not null");
            return (Criteria) this;
        }

        public Criteria andSummaryIdEqualTo(Long value) {
            addCriterion("summary_id =", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdNotEqualTo(Long value) {
            addCriterion("summary_id <>", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdGreaterThan(Long value) {
            addCriterion("summary_id >", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("summary_id >=", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdLessThan(Long value) {
            addCriterion("summary_id <", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdLessThanOrEqualTo(Long value) {
            addCriterion("summary_id <=", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdIn(List<Long> values) {
            addCriterion("summary_id in", values, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdNotIn(List<Long> values) {
            addCriterion("summary_id not in", values, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdBetween(Long value1, Long value2) {
            addCriterion("summary_id between", value1, value2, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdNotBetween(Long value1, Long value2) {
            addCriterion("summary_id not between", value1, value2, "summaryId");
            return (Criteria) this;
        }

        public Criteria andItemNameIsNull() {
            addCriterion("item_name is null");
            return (Criteria) this;
        }

        public Criteria andItemNameIsNotNull() {
            addCriterion("item_name is not null");
            return (Criteria) this;
        }

        public Criteria andItemNameEqualTo(String value) {
            addCriterion("item_name =", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotEqualTo(String value) {
            addCriterion("item_name <>", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameGreaterThan(String value) {
            addCriterion("item_name >", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameGreaterThanOrEqualTo(String value) {
            addCriterion("item_name >=", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLessThan(String value) {
            addCriterion("item_name <", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLessThanOrEqualTo(String value) {
            addCriterion("item_name <=", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLike(String value) {
            addCriterion("item_name like", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotLike(String value) {
            addCriterion("item_name not like", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameIn(List<String> values) {
            addCriterion("item_name in", values, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotIn(List<String> values) {
            addCriterion("item_name not in", values, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameBetween(String value1, String value2) {
            addCriterion("item_name between", value1, value2, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotBetween(String value1, String value2) {
            addCriterion("item_name not between", value1, value2, "itemName");
            return (Criteria) this;
        }

        public Criteria andAuditCostIsNull() {
            addCriterion("audit_cost is null");
            return (Criteria) this;
        }

        public Criteria andAuditCostIsNotNull() {
            addCriterion("audit_cost is not null");
            return (Criteria) this;
        }

        public Criteria andAuditCostEqualTo(BigDecimal value) {
            addCriterion("audit_cost =", value, "auditCost");
            return (Criteria) this;
        }

        public Criteria andAuditCostNotEqualTo(BigDecimal value) {
            addCriterion("audit_cost <>", value, "auditCost");
            return (Criteria) this;
        }

        public Criteria andAuditCostGreaterThan(BigDecimal value) {
            addCriterion("audit_cost >", value, "auditCost");
            return (Criteria) this;
        }

        public Criteria andAuditCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("audit_cost >=", value, "auditCost");
            return (Criteria) this;
        }

        public Criteria andAuditCostLessThan(BigDecimal value) {
            addCriterion("audit_cost <", value, "auditCost");
            return (Criteria) this;
        }

        public Criteria andAuditCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("audit_cost <=", value, "auditCost");
            return (Criteria) this;
        }

        public Criteria andAuditCostIn(List<BigDecimal> values) {
            addCriterion("audit_cost in", values, "auditCost");
            return (Criteria) this;
        }

        public Criteria andAuditCostNotIn(List<BigDecimal> values) {
            addCriterion("audit_cost not in", values, "auditCost");
            return (Criteria) this;
        }

        public Criteria andAuditCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("audit_cost between", value1, value2, "auditCost");
            return (Criteria) this;
        }

        public Criteria andAuditCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("audit_cost not between", value1, value2, "auditCost");
            return (Criteria) this;
        }

        public Criteria andAuditWorkingHoursIsNull() {
            addCriterion("audit_working_hours is null");
            return (Criteria) this;
        }

        public Criteria andAuditWorkingHoursIsNotNull() {
            addCriterion("audit_working_hours is not null");
            return (Criteria) this;
        }

        public Criteria andAuditWorkingHoursEqualTo(BigDecimal value) {
            addCriterion("audit_working_hours =", value, "auditWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditWorkingHoursNotEqualTo(BigDecimal value) {
            addCriterion("audit_working_hours <>", value, "auditWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditWorkingHoursGreaterThan(BigDecimal value) {
            addCriterion("audit_working_hours >", value, "auditWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditWorkingHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("audit_working_hours >=", value, "auditWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditWorkingHoursLessThan(BigDecimal value) {
            addCriterion("audit_working_hours <", value, "auditWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditWorkingHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("audit_working_hours <=", value, "auditWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditWorkingHoursIn(List<BigDecimal> values) {
            addCriterion("audit_working_hours in", values, "auditWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditWorkingHoursNotIn(List<BigDecimal> values) {
            addCriterion("audit_working_hours not in", values, "auditWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditWorkingHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("audit_working_hours between", value1, value2, "auditWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditWorkingHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("audit_working_hours not between", value1, value2, "auditWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditingCostIsNull() {
            addCriterion("auditing_cost is null");
            return (Criteria) this;
        }

        public Criteria andAuditingCostIsNotNull() {
            addCriterion("auditing_cost is not null");
            return (Criteria) this;
        }

        public Criteria andAuditingCostEqualTo(BigDecimal value) {
            addCriterion("auditing_cost =", value, "auditingCost");
            return (Criteria) this;
        }

        public Criteria andAuditingCostNotEqualTo(BigDecimal value) {
            addCriterion("auditing_cost <>", value, "auditingCost");
            return (Criteria) this;
        }

        public Criteria andAuditingCostGreaterThan(BigDecimal value) {
            addCriterion("auditing_cost >", value, "auditingCost");
            return (Criteria) this;
        }

        public Criteria andAuditingCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("auditing_cost >=", value, "auditingCost");
            return (Criteria) this;
        }

        public Criteria andAuditingCostLessThan(BigDecimal value) {
            addCriterion("auditing_cost <", value, "auditingCost");
            return (Criteria) this;
        }

        public Criteria andAuditingCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("auditing_cost <=", value, "auditingCost");
            return (Criteria) this;
        }

        public Criteria andAuditingCostIn(List<BigDecimal> values) {
            addCriterion("auditing_cost in", values, "auditingCost");
            return (Criteria) this;
        }

        public Criteria andAuditingCostNotIn(List<BigDecimal> values) {
            addCriterion("auditing_cost not in", values, "auditingCost");
            return (Criteria) this;
        }

        public Criteria andAuditingCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("auditing_cost between", value1, value2, "auditingCost");
            return (Criteria) this;
        }

        public Criteria andAuditingCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("auditing_cost not between", value1, value2, "auditingCost");
            return (Criteria) this;
        }

        public Criteria andAuditingWorkingHoursIsNull() {
            addCriterion("auditing_working_hours is null");
            return (Criteria) this;
        }

        public Criteria andAuditingWorkingHoursIsNotNull() {
            addCriterion("auditing_working_hours is not null");
            return (Criteria) this;
        }

        public Criteria andAuditingWorkingHoursEqualTo(BigDecimal value) {
            addCriterion("auditing_working_hours =", value, "auditingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditingWorkingHoursNotEqualTo(BigDecimal value) {
            addCriterion("auditing_working_hours <>", value, "auditingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditingWorkingHoursGreaterThan(BigDecimal value) {
            addCriterion("auditing_working_hours >", value, "auditingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditingWorkingHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("auditing_working_hours >=", value, "auditingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditingWorkingHoursLessThan(BigDecimal value) {
            addCriterion("auditing_working_hours <", value, "auditingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditingWorkingHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("auditing_working_hours <=", value, "auditingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditingWorkingHoursIn(List<BigDecimal> values) {
            addCriterion("auditing_working_hours in", values, "auditingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditingWorkingHoursNotIn(List<BigDecimal> values) {
            addCriterion("auditing_working_hours not in", values, "auditingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditingWorkingHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("auditing_working_hours between", value1, value2, "auditingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andAuditingWorkingHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("auditing_working_hours not between", value1, value2, "auditingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andTotalCostIsNull() {
            addCriterion("total_cost is null");
            return (Criteria) this;
        }

        public Criteria andTotalCostIsNotNull() {
            addCriterion("total_cost is not null");
            return (Criteria) this;
        }

        public Criteria andTotalCostEqualTo(BigDecimal value) {
            addCriterion("total_cost =", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostNotEqualTo(BigDecimal value) {
            addCriterion("total_cost <>", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostGreaterThan(BigDecimal value) {
            addCriterion("total_cost >", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_cost >=", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostLessThan(BigDecimal value) {
            addCriterion("total_cost <", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_cost <=", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostIn(List<BigDecimal> values) {
            addCriterion("total_cost in", values, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostNotIn(List<BigDecimal> values) {
            addCriterion("total_cost not in", values, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_cost between", value1, value2, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_cost not between", value1, value2, "totalCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostIsNull() {
            addCriterion("outer_labor_cost is null");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostIsNotNull() {
            addCriterion("outer_labor_cost is not null");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostEqualTo(BigDecimal value) {
            addCriterion("outer_labor_cost =", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostNotEqualTo(BigDecimal value) {
            addCriterion("outer_labor_cost <>", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostGreaterThan(BigDecimal value) {
            addCriterion("outer_labor_cost >", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("outer_labor_cost >=", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostLessThan(BigDecimal value) {
            addCriterion("outer_labor_cost <", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("outer_labor_cost <=", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostIn(List<BigDecimal> values) {
            addCriterion("outer_labor_cost in", values, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostNotIn(List<BigDecimal> values) {
            addCriterion("outer_labor_cost not in", values, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outer_labor_cost between", value1, value2, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outer_labor_cost not between", value1, value2, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostIsNull() {
            addCriterion("inner_labor_cost is null");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostIsNotNull() {
            addCriterion("inner_labor_cost is not null");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost =", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNotEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost <>", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostGreaterThan(BigDecimal value) {
            addCriterion("inner_labor_cost >", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost >=", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostLessThan(BigDecimal value) {
            addCriterion("inner_labor_cost <", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost <=", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostIn(List<BigDecimal> values) {
            addCriterion("inner_labor_cost in", values, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNotIn(List<BigDecimal> values) {
            addCriterion("inner_labor_cost not in", values, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_labor_cost between", value1, value2, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_labor_cost not between", value1, value2, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHoursIsNull() {
            addCriterion("total_working_hours is null");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHoursIsNotNull() {
            addCriterion("total_working_hours is not null");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHoursEqualTo(BigDecimal value) {
            addCriterion("total_working_hours =", value, "totalWorkingHours");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHoursNotEqualTo(BigDecimal value) {
            addCriterion("total_working_hours <>", value, "totalWorkingHours");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHoursGreaterThan(BigDecimal value) {
            addCriterion("total_working_hours >", value, "totalWorkingHours");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_working_hours >=", value, "totalWorkingHours");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHoursLessThan(BigDecimal value) {
            addCriterion("total_working_hours <", value, "totalWorkingHours");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_working_hours <=", value, "totalWorkingHours");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHoursIn(List<BigDecimal> values) {
            addCriterion("total_working_hours in", values, "totalWorkingHours");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHoursNotIn(List<BigDecimal> values) {
            addCriterion("total_working_hours not in", values, "totalWorkingHours");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_working_hours between", value1, value2, "totalWorkingHours");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_working_hours not between", value1, value2, "totalWorkingHours");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}