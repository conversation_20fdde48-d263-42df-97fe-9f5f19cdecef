package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: common-module
 * @description: 实际成本归集-费用成本明细导出实体
 * @author:zhongpeng
 * @create:2020-03-24 16:52
 **/
@Setter
@Getter
public class FeeCostDetailExcelVO {
    @Excel(name = "序号", width = 10)
    private Integer num;

    @Excel(name = "归集日期", width = 20, format = "yyyy-MM-dd")
    private Date collectionDate;

    @Excel(name = "成本发生日期", width = 20, format = "yyyy-MM-dd")
    private Date costDate;

    @Excel(name = "结转状态", width = 20, replace = {"未结转_0", "已结转_1", "未结转_null"})
    private Integer carryStatus;

    @Excel(name = "结转期间", width = 20)
    private String glPeriod;

    @Excel(name = "项目编号", width = 30)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "费用类型", width = 30)
    private String type;

    @Excel(name = "单据编号", width = 30)
    private String orderCode;

    @Excel(name = "单据入账日期", width = 30, format = "yyyy-MM-dd")
    private Date glDate;

    @Excel(name = "供应商名称", width = 30)
    private String vendorName; //暂时没有数据

    @Excel(name = "供应商编码", width = 30)
    private String vendorCode; //暂时没有数据

    @Excel(name = "供应商地点", width = 30)
    private String vendorSiteCode; //暂时没有数据

    @Excel(name = "发票币种", width = 30)
    private String invoiceCurrency;

    @Excel(name = "发票金额(不含税)", width = 30)
    private BigDecimal invoiceAmount;

    @Excel(name = "本位币", width = 20)
    private String localCurrency;

    @Excel(name = "本位币金额(不含税)", width = 30)
    private BigDecimal localCurrencyAmount;

    @Excel(name = "业务分类", width = 30)
    private String typeName;

    @Excel(name = "业务实体", width = 30)
    private String ouName;

    @Excel(name = "WBS号", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项编码", width = 20)
    private String activityCode;

}
