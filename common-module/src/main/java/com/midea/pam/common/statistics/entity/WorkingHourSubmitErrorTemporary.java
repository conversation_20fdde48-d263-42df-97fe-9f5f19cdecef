package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class WorkingHourSubmitErrorTemporary extends LongIdEntity implements Serializable {
    private Long id;

    private Long workingHourId;

    private String projectCode;

    private Long projectId;

    private String projectName;

    private String userMip;

    private String userName;

    private Date applyDate;

    private Date createAt;

    private Integer status;

    private BigDecimal applyWorkingHours;

    private Long bizUnitId;

    private Long ouId;

    private String operatingUnitName;

    private String erpMessages;

    private Integer invoiceApplyFlag;

    private Integer costCollectionFlag;

    private Boolean deletedFlag;

    private String lever;

    private String laborCostType;

    private BigDecimal actualCostMoney;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWorkingHourId() {
        return workingHourId;
    }

    public void setWorkingHourId(Long workingHourId) {
        this.workingHourId = workingHourId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getUserMip() {
        return userMip;
    }

    public void setUserMip(String userMip) {
        this.userMip = userMip == null ? null : userMip.trim();
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getApplyWorkingHours() {
        return applyWorkingHours;
    }

    public void setApplyWorkingHours(BigDecimal applyWorkingHours) {
        this.applyWorkingHours = applyWorkingHours;
    }

    public Long getBizUnitId() {
        return bizUnitId;
    }

    public void setBizUnitId(Long bizUnitId) {
        this.bizUnitId = bizUnitId;
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getOperatingUnitName() {
        return operatingUnitName;
    }

    public void setOperatingUnitName(String operatingUnitName) {
        this.operatingUnitName = operatingUnitName == null ? null : operatingUnitName.trim();
    }

    public String getErpMessages() {
        return erpMessages;
    }

    public void setErpMessages(String erpMessages) {
        this.erpMessages = erpMessages == null ? null : erpMessages.trim();
    }

    public Integer getInvoiceApplyFlag() {
        return invoiceApplyFlag;
    }

    public void setInvoiceApplyFlag(Integer invoiceApplyFlag) {
        this.invoiceApplyFlag = invoiceApplyFlag;
    }

    public Integer getCostCollectionFlag() {
        return costCollectionFlag;
    }

    public void setCostCollectionFlag(Integer costCollectionFlag) {
        this.costCollectionFlag = costCollectionFlag;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public String getLever() {
        return lever;
    }

    public void setLever(String lever) {
        this.lever = lever == null ? null : lever.trim();
    }

    public String getLaborCostType() {
        return laborCostType;
    }

    public void setLaborCostType(String laborCostType) {
        this.laborCostType = laborCostType == null ? null : laborCostType.trim();
    }

    public BigDecimal getActualCostMoney() {
        return actualCostMoney;
    }

    public void setActualCostMoney(BigDecimal actualCostMoney) {
        this.actualCostMoney = actualCostMoney;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", workingHourId=").append(workingHourId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectName=").append(projectName);
        sb.append(", userMip=").append(userMip);
        sb.append(", userName=").append(userName);
        sb.append(", applyDate=").append(applyDate);
        sb.append(", createAt=").append(createAt);
        sb.append(", status=").append(status);
        sb.append(", applyWorkingHours=").append(applyWorkingHours);
        sb.append(", bizUnitId=").append(bizUnitId);
        sb.append(", ouId=").append(ouId);
        sb.append(", operatingUnitName=").append(operatingUnitName);
        sb.append(", erpMessages=").append(erpMessages);
        sb.append(", invoiceApplyFlag=").append(invoiceApplyFlag);
        sb.append(", costCollectionFlag=").append(costCollectionFlag);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", lever=").append(lever);
        sb.append(", laborCostType=").append(laborCostType);
        sb.append(", actualCostMoney=").append(actualCostMoney);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}