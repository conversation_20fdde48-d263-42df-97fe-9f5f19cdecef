package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "物料预算明细报表")
public class MaterialBudgetDetail extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表ID")
    private Long reportId;

    @ApiModelProperty(value = "执行ID")
    private Long executeId;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "业务实体ID")
    private Long ouId;

    @ApiModelProperty(value = "序号")
    private String num;

    @ApiModelProperty(value = "BOOM层级(0-4)")
    private Integer boomLevel;

    @ApiModelProperty(value = "集成外包")
    private String ext;

    @ApiModelProperty(value = "物料类型")
    private String materielType;

    @ApiModelProperty(value = "物料描述")
    private String materielDescr;

    @ApiModelProperty(value = "PAM物料编码")
    private String pamCode;

    @ApiModelProperty(value = "ERP物料编码")
    private String erpCode;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "单套数量")
    private BigDecimal number;

    @ApiModelProperty(value = "采购需求总数")
    private BigDecimal totalNum;

    @ApiModelProperty(value = "已发布需求数量")
    private BigDecimal requirementNum;

    @ApiModelProperty(value = "已下采购订单")
    private BigDecimal purchaseOrderPlaced;

    @ApiModelProperty(value = "库存")
    private BigDecimal inventoryQuantity;

    @ApiModelProperty(value = "已领料")
    private BigDecimal materialReceived;

    @ApiModelProperty(value = "未下单")
    private BigDecimal notOrdered;

    @ApiModelProperty(value = "一揽子价格/估价单价")
    private BigDecimal packagePriceEstimateUnitPrice;

    @ApiModelProperty(value = "采购单价")
    private BigDecimal purchaseUnitPrice;

    @ApiModelProperty(value = "领料平均单位成本")
    private BigDecimal averageUnitCostMaterial;

    @ApiModelProperty(value = "预计待发生成本")
    private BigDecimal expectedPendingCost;

    @ApiModelProperty(value = "采购总成本")
    private BigDecimal totalPurchaseCost;

    @ApiModelProperty(value = "领料总成本")
    private BigDecimal totalMaterialCost;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "物料到货日期")
    private Date deliveryTime;

    @ApiModelProperty(value = "型号/规格/图号")
    private String model;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num == null ? null : num.trim();
    }

    public Integer getBoomLevel() {
        return boomLevel;
    }

    public void setBoomLevel(Integer boomLevel) {
        this.boomLevel = boomLevel;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext == null ? null : ext.trim();
    }

    public String getMaterielType() {
        return materielType;
    }

    public void setMaterielType(String materielType) {
        this.materielType = materielType == null ? null : materielType.trim();
    }

    public String getMaterielDescr() {
        return materielDescr;
    }

    public void setMaterielDescr(String materielDescr) {
        this.materielDescr = materielDescr == null ? null : materielDescr.trim();
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode == null ? null : pamCode.trim();
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode == null ? null : erpCode.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public BigDecimal getNumber() {
        return number;
    }

    public void setNumber(BigDecimal number) {
        this.number = number;
    }

    public BigDecimal getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(BigDecimal totalNum) {
        this.totalNum = totalNum;
    }

    public BigDecimal getRequirementNum() {
        return requirementNum;
    }

    public void setRequirementNum(BigDecimal requirementNum) {
        this.requirementNum = requirementNum;
    }

    public BigDecimal getPurchaseOrderPlaced() {
        return purchaseOrderPlaced;
    }

    public void setPurchaseOrderPlaced(BigDecimal purchaseOrderPlaced) {
        this.purchaseOrderPlaced = purchaseOrderPlaced;
    }

    public BigDecimal getInventoryQuantity() {
        return inventoryQuantity;
    }

    public void setInventoryQuantity(BigDecimal inventoryQuantity) {
        this.inventoryQuantity = inventoryQuantity;
    }

    public BigDecimal getMaterialReceived() {
        return materialReceived;
    }

    public void setMaterialReceived(BigDecimal materialReceived) {
        this.materialReceived = materialReceived;
    }

    public BigDecimal getNotOrdered() {
        return notOrdered;
    }

    public void setNotOrdered(BigDecimal notOrdered) {
        this.notOrdered = notOrdered;
    }

    public BigDecimal getPackagePriceEstimateUnitPrice() {
        return packagePriceEstimateUnitPrice;
    }

    public void setPackagePriceEstimateUnitPrice(BigDecimal packagePriceEstimateUnitPrice) {
        this.packagePriceEstimateUnitPrice = packagePriceEstimateUnitPrice;
    }

    public BigDecimal getPurchaseUnitPrice() {
        return purchaseUnitPrice;
    }

    public void setPurchaseUnitPrice(BigDecimal purchaseUnitPrice) {
        this.purchaseUnitPrice = purchaseUnitPrice;
    }

    public BigDecimal getAverageUnitCostMaterial() {
        return averageUnitCostMaterial;
    }

    public void setAverageUnitCostMaterial(BigDecimal averageUnitCostMaterial) {
        this.averageUnitCostMaterial = averageUnitCostMaterial;
    }

    public BigDecimal getExpectedPendingCost() {
        return expectedPendingCost;
    }

    public void setExpectedPendingCost(BigDecimal expectedPendingCost) {
        this.expectedPendingCost = expectedPendingCost;
    }

    public BigDecimal getTotalPurchaseCost() {
        return totalPurchaseCost;
    }

    public void setTotalPurchaseCost(BigDecimal totalPurchaseCost) {
        this.totalPurchaseCost = totalPurchaseCost;
    }

    public BigDecimal getTotalMaterialCost() {
        return totalMaterialCost;
    }

    public void setTotalMaterialCost(BigDecimal totalMaterialCost) {
        this.totalMaterialCost = totalMaterialCost;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", ouId=").append(ouId);
        sb.append(", num=").append(num);
        sb.append(", boomLevel=").append(boomLevel);
        sb.append(", ext=").append(ext);
        sb.append(", materielType=").append(materielType);
        sb.append(", materielDescr=").append(materielDescr);
        sb.append(", pamCode=").append(pamCode);
        sb.append(", erpCode=").append(erpCode);
        sb.append(", name=").append(name);
        sb.append(", unit=").append(unit);
        sb.append(", number=").append(number);
        sb.append(", totalNum=").append(totalNum);
        sb.append(", requirementNum=").append(requirementNum);
        sb.append(", purchaseOrderPlaced=").append(purchaseOrderPlaced);
        sb.append(", inventoryQuantity=").append(inventoryQuantity);
        sb.append(", materialReceived=").append(materialReceived);
        sb.append(", notOrdered=").append(notOrdered);
        sb.append(", packagePriceEstimateUnitPrice=").append(packagePriceEstimateUnitPrice);
        sb.append(", purchaseUnitPrice=").append(purchaseUnitPrice);
        sb.append(", averageUnitCostMaterial=").append(averageUnitCostMaterial);
        sb.append(", expectedPendingCost=").append(expectedPendingCost);
        sb.append(", totalPurchaseCost=").append(totalPurchaseCost);
        sb.append(", totalMaterialCost=").append(totalMaterialCost);
        sb.append(", remark=").append(remark);
        sb.append(", deliveryTime=").append(deliveryTime);
        sb.append(", model=").append(model);
        sb.append(", brand=").append(brand);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}