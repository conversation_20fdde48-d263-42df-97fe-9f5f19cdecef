package com.midea.pam.common.statistics.excelVo;


import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022-11-24
 * @description 需求预算-审批中的人力点工需求单据
 */
@Getter
@Setter
public class HroRequirementBudgetApprovalExcelVO {

    @Excel(name = "序号", width = 5)
    private Integer number;

    @Excel(name = "WBS", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项", width = 15)
    private String activityCode;

    @Excel(name = "人力点工需求单据编号", width = 25)
    private String requirementCode;

    @Excel(name = "角色", width = 25)
    private String roleName;

    @Excel(name = "总预算（需求预算）", width = 15)
    private BigDecimal budgetOccupiedAmount;

}
