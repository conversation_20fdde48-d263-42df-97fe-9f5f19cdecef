package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class WorkingHourCollectionErrorTemporary extends LongIdEntity implements Serializable {
    private Long id;

    private Long costCollectionId;

    private Date collectionDate;

    private Date costDate;

    private Integer carryStatus;

    private String glPeriod;

    private String projectCode;

    private Long projectId;

    private String projectName;

    private Long projectType;

    private String currency;

    private BigDecimal materialActualCost;

    private BigDecimal materialOutsourceCost;

    private BigDecimal innerLaborCost;

    private BigDecimal outerLaborCost;

    private BigDecimal materialDifferenceCost;

    private BigDecimal feeCost;

    private Long bizUnitId;

    private Long ouId;

    private String ouName;

    private String erpMessages;

    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCostCollectionId() {
        return costCollectionId;
    }

    public void setCostCollectionId(Long costCollectionId) {
        this.costCollectionId = costCollectionId;
    }

    public Date getCollectionDate() {
        return collectionDate;
    }

    public void setCollectionDate(Date collectionDate) {
        this.collectionDate = collectionDate;
    }

    public Date getCostDate() {
        return costDate;
    }

    public void setCostDate(Date costDate) {
        this.costDate = costDate;
    }

    public Integer getCarryStatus() {
        return carryStatus;
    }

    public void setCarryStatus(Integer carryStatus) {
        this.carryStatus = carryStatus;
    }

    public String getGlPeriod() {
        return glPeriod;
    }

    public void setGlPeriod(String glPeriod) {
        this.glPeriod = glPeriod == null ? null : glPeriod.trim();
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public Long getProjectType() {
        return projectType;
    }

    public void setProjectType(Long projectType) {
        this.projectType = projectType;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public BigDecimal getMaterialActualCost() {
        return materialActualCost;
    }

    public void setMaterialActualCost(BigDecimal materialActualCost) {
        this.materialActualCost = materialActualCost;
    }

    public BigDecimal getMaterialOutsourceCost() {
        return materialOutsourceCost;
    }

    public void setMaterialOutsourceCost(BigDecimal materialOutsourceCost) {
        this.materialOutsourceCost = materialOutsourceCost;
    }

    public BigDecimal getInnerLaborCost() {
        return innerLaborCost;
    }

    public void setInnerLaborCost(BigDecimal innerLaborCost) {
        this.innerLaborCost = innerLaborCost;
    }

    public BigDecimal getOuterLaborCost() {
        return outerLaborCost;
    }

    public void setOuterLaborCost(BigDecimal outerLaborCost) {
        this.outerLaborCost = outerLaborCost;
    }

    public BigDecimal getMaterialDifferenceCost() {
        return materialDifferenceCost;
    }

    public void setMaterialDifferenceCost(BigDecimal materialDifferenceCost) {
        this.materialDifferenceCost = materialDifferenceCost;
    }

    public BigDecimal getFeeCost() {
        return feeCost;
    }

    public void setFeeCost(BigDecimal feeCost) {
        this.feeCost = feeCost;
    }

    public Long getBizUnitId() {
        return bizUnitId;
    }

    public void setBizUnitId(Long bizUnitId) {
        this.bizUnitId = bizUnitId;
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public String getErpMessages() {
        return erpMessages;
    }

    public void setErpMessages(String erpMessages) {
        this.erpMessages = erpMessages == null ? null : erpMessages.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", costCollectionId=").append(costCollectionId);
        sb.append(", collectionDate=").append(collectionDate);
        sb.append(", costDate=").append(costDate);
        sb.append(", carryStatus=").append(carryStatus);
        sb.append(", glPeriod=").append(glPeriod);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectType=").append(projectType);
        sb.append(", currency=").append(currency);
        sb.append(", materialActualCost=").append(materialActualCost);
        sb.append(", materialOutsourceCost=").append(materialOutsourceCost);
        sb.append(", innerLaborCost=").append(innerLaborCost);
        sb.append(", outerLaborCost=").append(outerLaborCost);
        sb.append(", materialDifferenceCost=").append(materialDifferenceCost);
        sb.append(", feeCost=").append(feeCost);
        sb.append(", bizUnitId=").append(bizUnitId);
        sb.append(", ouId=").append(ouId);
        sb.append(", ouName=").append(ouName);
        sb.append(", erpMessages=").append(erpMessages);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}