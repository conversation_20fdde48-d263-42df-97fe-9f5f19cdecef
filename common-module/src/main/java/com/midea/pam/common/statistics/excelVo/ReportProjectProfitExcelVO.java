package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;

/**
 *
 **/
public class ReportProjectProfitExcelVO {
    @Excel(name = "序号",width = 10)
    private Integer num;

    @Excel(name = "业务分类",width = 20)
    private String unitName;

    @Excel(name = "事业部",width = 20)
    private String divisionName;

    @Excel(name = "项目编号",width = 20)
    private String projectCode;

    @Excel(name = "项目属性",width = 15, replace = {"内部_1", "外部_2", "研发_3", "_null"})
    private String priceType;

    @Excel(name = "项目名称",width = 20)
    private String projectName;

    @Excel(name = "项目经理",width = 20)
    private String managerName;

    @Excel(name = "项目状态",width = 20,replace = {"审批驳回_-2", "财务驳回_-1", "草稿_0", "审批中_3", "项目进行中_4", "项目变更中_9", "结项_10",
            "预立项转正驳回_-3", "预立项审批驳回_-4", "审批撤回_11", "预立项审批撤回_13", "作废_12", "预立项转正审批中_7", "预立项转正审批撤回_14","终止_16", "_null"})
    private String status;

    @Excel(name = "是否预立项",width = 15, replace = {"否_0", "是_1", "_null"})
    private String previewFlag;

    @Excel(name = "项目类型",width = 15)
    private String typeName;

    @Excel(name = "项目金额",width = 20)
    private BigDecimal amount;

    @Excel(name = "报价成本",width = 20, replace = {"-_null"})
    private BigDecimal quoteCostTotal;

    @Excel(name = "项目预算变更次数",width = 20)
    private Integer budgetChangeNum;

    @Excel(name = "项目预算",width = 20)
    private BigDecimal budgetCost;

    @Excel(name = "已发生成本",width = 20)
    private BigDecimal incurredCost;

    @Excel(name = "已结转成本",width = 20)
    private BigDecimal confirmedCostTotalAmount;

    @Excel(name = "已结转收入",width = 20)
    private BigDecimal confirmedIncomeTotalAmount;

    @Excel(name = "收入进度",width = 20, suffix = "%")
    private BigDecimal confirmedIncomeRatio;

    @Excel(name = "已结转毛利",width = 20)
    private BigDecimal confirmedGrossProfitAmount;

    @Excel(name = "毛利率",width = 20, suffix = "%")
    private BigDecimal confirmedGrossProfitRatio;

    @Excel(name = "子合同编号",width = 20)
    private String childContractCode;

    @Excel(name = "子合同名称",width = 20)
    private String childContractName;

    @Excel(name = "子合同状态",width = 20, replace = {"待归档_0", "草稿_1", "审核中_2", "驳回_3", "待生效_4", "生效_5", "冻结_6", "失效_7", "已结束_8", "作废_9", "变更中_10", "合同终止_11", "终止审批中_12"})
    private Byte childContractStatus;

    @Excel(name = "子合同金额（含税）",width = 20)
    private BigDecimal childContractAmount;

    @Excel(name = "已开票（含税）",width = 20)
    private BigDecimal childContractActualInvoiceAmountWithtax;

    @Excel(name = "剩余开票（含税）",width = 20)
    private BigDecimal childContractRemainInvoiceAmountWithtax;

    @Excel(name = "开票率",width = 20, suffix = "%")
    private BigDecimal childContractActualInvoiceRatio;

    @Excel(name = "回款金额（含税）",width = 20)
    private BigDecimal childContractActualReceiptAmountWithtax;

    @Excel(name = "剩余回款（含税）",width = 20)
    private BigDecimal childContractRemainReceiptAmountWithtax;

    @Excel(name = "回款率",width = 20, suffix = "%")
    private BigDecimal childContractActualReceiptRatio;

    @Excel(name = "已开票未回款",width = 20)
    private BigDecimal childContractInvoiceSubReceiptAmountWithtax;

    @Excel(name = "主合同编号",width = 20)
    private String parentContractCode;

    @Excel(name = "主合同状态",width = 20, replace = {"待归档_0", "草稿_1", "审核中_2", "驳回_3", "待生效_4", "生效_5", "冻结_6", "失效_7", "已结束_8", "作废_9", "变更中_10", "合同终止_11", "终止审批中_12"})
    private Byte parentContractStatus;

    @Excel(name = "主合同金额(含税)",width = 20)
    private BigDecimal parentContractAmount;

    @Excel(name = "主合同回款金额",width = 20)
    private BigDecimal parentContractActualReceiptAmountWithtax;

    @Excel(name = "客户名称",width = 20)
    private String customerName;

    @Excel(name = "客户属性",width = 20, replace = {"内部客户_1",  "外部客户_0", "_null"})
    private String customerIsInner;

    // 报价成本明细
    @Excel(name = "物料预算",width = 20, replace = {"_null"})
    private BigDecimal quoteCostHardwareWithoutTax;

    @Excel(name = "人力预算",width = 20, replace = {"_null"})
    private BigDecimal quoteCostLabourWithoutTax;

    @Excel(name = "差旅费预算",width = 20, replace = {"_null"})
    private BigDecimal quoteCostTravelWithoutTax;

    @Excel(name = "非差旅费预算",width = 20, replace = {"_null"})
    private BigDecimal quoteCostOtherWithoutTax;

    // 项目预算
    @Excel(name = "物料预算",width = 20)
    private BigDecimal projectBudgetHardwareWithoutTax;

    @Excel(name = "人力预算",width = 20)
    private BigDecimal projectBudgetLabourWithoutTax;

    @Excel(name = "差旅费预算",width = 20)
    private BigDecimal projectBudgetTravelWithoutTax;

    @Excel(name = "非差旅费预算",width = 20)
    private BigDecimal projectBudgetOtherWithoutTax;

    // 已发生成本
    @Excel(name = "物料成本",width = 20)
    private BigDecimal incurredCostHardware;

    @Excel(name = "人力成本",width = 20)
    private BigDecimal incurredCostLabour;

    @Excel(name = "差旅成本",width = 20)
    private BigDecimal incurredCostTravel;

    @Excel(name = "非差旅成本",width = 20)
    private BigDecimal incurredCostOther;

    // 已结转成本
    @Excel(name = "物料成本",width = 20)
    private BigDecimal confirmedCostHardware;

    @Excel(name = "人力成本",width = 20)
    private BigDecimal confirmedCostLabour;

    @Excel(name = "差旅成本",width = 20)
    private BigDecimal confirmedCostTravel;

    @Excel(name = "非差旅成本",width = 20)
    private BigDecimal confirmedCostOther;

    @Excel(name = "战略投入项目",width = 20, replace = {"是_1",  "否_0", "_null"})
    private Byte isObjectiveProject;

    @Excel(name = "项目分级",width = 20, replace = {"A级_1",  "B级_2", "C级_3", "_null"})
    private Byte projectLevel;

    @Excel(name = "业务实体",width = 20)
    private String ouname;

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getDivisionName() {
        return divisionName;
    }

    public void setDivisionName(String divisionName) {
        this.divisionName = divisionName;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getPriceType() {
        return priceType;
    }

    public void setPriceType(String priceType) {
        this.priceType = priceType;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPreviewFlag() {
        return previewFlag;
    }

    public void setPreviewFlag(String previewFlag) {
        this.previewFlag = previewFlag;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getQuoteCostTotal() {
        return quoteCostTotal;
    }

    public void setQuoteCostTotal(BigDecimal quoteCostTotal) {
        this.quoteCostTotal = quoteCostTotal;
    }

    public Integer getBudgetChangeNum() {
        return budgetChangeNum;
    }

    public void setBudgetChangeNum(Integer budgetChangeNum) {
        this.budgetChangeNum = budgetChangeNum;
    }

    public BigDecimal getBudgetCost() {
        return budgetCost;
    }

    public void setBudgetCost(BigDecimal budgetCost) {
        this.budgetCost = budgetCost;
    }

    public BigDecimal getIncurredCost() {
        return incurredCost;
    }

    public void setIncurredCost(BigDecimal incurredCost) {
        this.incurredCost = incurredCost;
    }

    public BigDecimal getConfirmedCostTotalAmount() {
        return confirmedCostTotalAmount;
    }

    public void setConfirmedCostTotalAmount(BigDecimal confirmedCostTotalAmount) {
        this.confirmedCostTotalAmount = confirmedCostTotalAmount;
    }

    public BigDecimal getConfirmedIncomeTotalAmount() {
        return confirmedIncomeTotalAmount;
    }

    public void setConfirmedIncomeTotalAmount(BigDecimal confirmedIncomeTotalAmount) {
        this.confirmedIncomeTotalAmount = confirmedIncomeTotalAmount;
    }

    public BigDecimal getConfirmedIncomeRatio() {
        return confirmedIncomeRatio;
    }

    public void setConfirmedIncomeRatio(BigDecimal confirmedIncomeRatio) {
        this.confirmedIncomeRatio = confirmedIncomeRatio;
    }

    public BigDecimal getConfirmedGrossProfitAmount() {
        return confirmedGrossProfitAmount;
    }

    public void setConfirmedGrossProfitAmount(BigDecimal confirmedGrossProfitAmount) {
        this.confirmedGrossProfitAmount = confirmedGrossProfitAmount;
    }

    public BigDecimal getConfirmedGrossProfitRatio() {
        return confirmedGrossProfitRatio;
    }

    public void setConfirmedGrossProfitRatio(BigDecimal confirmedGrossProfitRatio) {
        this.confirmedGrossProfitRatio = confirmedGrossProfitRatio;
    }

    public String getChildContractCode() {
        return childContractCode;
    }

    public void setChildContractCode(String childContractCode) {
        this.childContractCode = childContractCode;
    }

    public String getChildContractName() {
        return childContractName;
    }

    public void setChildContractName(String childContractName) {
        this.childContractName = childContractName;
    }

    public Byte getChildContractStatus() {
        return childContractStatus;
    }

    public void setChildContractStatus(Byte childContractStatus) {
        this.childContractStatus = childContractStatus;
    }

    public BigDecimal getChildContractAmount() {
        return childContractAmount;
    }

    public void setChildContractAmount(BigDecimal childContractAmount) {
        this.childContractAmount = childContractAmount;
    }

    public BigDecimal getChildContractActualInvoiceAmountWithtax() {
        return childContractActualInvoiceAmountWithtax;
    }

    public void setChildContractActualInvoiceAmountWithtax(BigDecimal childContractActualInvoiceAmountWithtax) {
        this.childContractActualInvoiceAmountWithtax = childContractActualInvoiceAmountWithtax;
    }

    public BigDecimal getChildContractRemainInvoiceAmountWithtax() {
        return childContractRemainInvoiceAmountWithtax;
    }

    public void setChildContractRemainInvoiceAmountWithtax(BigDecimal childContractRemainInvoiceAmountWithtax) {
        this.childContractRemainInvoiceAmountWithtax = childContractRemainInvoiceAmountWithtax;
    }

    public BigDecimal getChildContractActualInvoiceRatio() {
        return childContractActualInvoiceRatio;
    }

    public void setChildContractActualInvoiceRatio(BigDecimal childContractActualInvoiceRatio) {
        this.childContractActualInvoiceRatio = childContractActualInvoiceRatio;
    }

    public BigDecimal getChildContractActualReceiptAmountWithtax() {
        return childContractActualReceiptAmountWithtax;
    }

    public void setChildContractActualReceiptAmountWithtax(BigDecimal childContractActualReceiptAmountWithtax) {
        this.childContractActualReceiptAmountWithtax = childContractActualReceiptAmountWithtax;
    }

    public BigDecimal getChildContractRemainReceiptAmountWithtax() {
        return childContractRemainReceiptAmountWithtax;
    }

    public void setChildContractRemainReceiptAmountWithtax(BigDecimal childContractRemainReceiptAmountWithtax) {
        this.childContractRemainReceiptAmountWithtax = childContractRemainReceiptAmountWithtax;
    }

    public BigDecimal getChildContractActualReceiptRatio() {
        return childContractActualReceiptRatio;
    }

    public void setChildContractActualReceiptRatio(BigDecimal childContractActualReceiptRatio) {
        this.childContractActualReceiptRatio = childContractActualReceiptRatio;
    }

    public BigDecimal getChildContractInvoiceSubReceiptAmountWithtax() {
        return childContractInvoiceSubReceiptAmountWithtax;
    }

    public void setChildContractInvoiceSubReceiptAmountWithtax(BigDecimal childContractInvoiceSubReceiptAmountWithtax) {
        this.childContractInvoiceSubReceiptAmountWithtax = childContractInvoiceSubReceiptAmountWithtax;
    }

    public String getParentContractCode() {
        return parentContractCode;
    }

    public void setParentContractCode(String parentContractCode) {
        this.parentContractCode = parentContractCode;
    }

    public Byte getParentContractStatus() {
        return parentContractStatus;
    }

    public void setParentContractStatus(Byte parentContractStatus) {
        this.parentContractStatus = parentContractStatus;
    }

    public BigDecimal getParentContractAmount() {
        return parentContractAmount;
    }

    public void setParentContractAmount(BigDecimal parentContractAmount) {
        this.parentContractAmount = parentContractAmount;
    }

    public BigDecimal getParentContractActualReceiptAmountWithtax() {
        return parentContractActualReceiptAmountWithtax;
    }

    public void setParentContractActualReceiptAmountWithtax(BigDecimal parentContractActualReceiptAmountWithtax) {
        this.parentContractActualReceiptAmountWithtax = parentContractActualReceiptAmountWithtax;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerIsInner() {
        return customerIsInner;
    }

    public void setCustomerIsInner(String customerIsInner) {
        this.customerIsInner = customerIsInner;
    }

    public BigDecimal getQuoteCostHardwareWithoutTax() {
        return quoteCostHardwareWithoutTax;
    }

    public void setQuoteCostHardwareWithoutTax(BigDecimal quoteCostHardwareWithoutTax) {
        this.quoteCostHardwareWithoutTax = quoteCostHardwareWithoutTax;
    }

    public BigDecimal getQuoteCostLabourWithoutTax() {
        return quoteCostLabourWithoutTax;
    }

    public void setQuoteCostLabourWithoutTax(BigDecimal quoteCostLabourWithoutTax) {
        this.quoteCostLabourWithoutTax = quoteCostLabourWithoutTax;
    }

    public BigDecimal getQuoteCostTravelWithoutTax() {
        return quoteCostTravelWithoutTax;
    }

    public void setQuoteCostTravelWithoutTax(BigDecimal quoteCostTravelWithoutTax) {
        this.quoteCostTravelWithoutTax = quoteCostTravelWithoutTax;
    }

    public BigDecimal getQuoteCostOtherWithoutTax() {
        return quoteCostOtherWithoutTax;
    }

    public void setQuoteCostOtherWithoutTax(BigDecimal quoteCostOtherWithoutTax) {
        this.quoteCostOtherWithoutTax = quoteCostOtherWithoutTax;
    }

    public BigDecimal getProjectBudgetHardwareWithoutTax() {
        return projectBudgetHardwareWithoutTax;
    }

    public void setProjectBudgetHardwareWithoutTax(BigDecimal projectBudgetHardwareWithoutTax) {
        this.projectBudgetHardwareWithoutTax = projectBudgetHardwareWithoutTax;
    }

    public BigDecimal getProjectBudgetLabourWithoutTax() {
        return projectBudgetLabourWithoutTax;
    }

    public void setProjectBudgetLabourWithoutTax(BigDecimal projectBudgetLabourWithoutTax) {
        this.projectBudgetLabourWithoutTax = projectBudgetLabourWithoutTax;
    }

    public BigDecimal getProjectBudgetTravelWithoutTax() {
        return projectBudgetTravelWithoutTax;
    }

    public void setProjectBudgetTravelWithoutTax(BigDecimal projectBudgetTravelWithoutTax) {
        this.projectBudgetTravelWithoutTax = projectBudgetTravelWithoutTax;
    }

    public BigDecimal getProjectBudgetOtherWithoutTax() {
        return projectBudgetOtherWithoutTax;
    }

    public void setProjectBudgetOtherWithoutTax(BigDecimal projectBudgetOtherWithoutTax) {
        this.projectBudgetOtherWithoutTax = projectBudgetOtherWithoutTax;
    }

    public BigDecimal getIncurredCostHardware() {
        return incurredCostHardware;
    }

    public void setIncurredCostHardware(BigDecimal incurredCostHardware) {
        this.incurredCostHardware = incurredCostHardware;
    }

    public BigDecimal getIncurredCostLabour() {
        return incurredCostLabour;
    }

    public void setIncurredCostLabour(BigDecimal incurredCostLabour) {
        this.incurredCostLabour = incurredCostLabour;
    }

    public BigDecimal getIncurredCostTravel() {
        return incurredCostTravel;
    }

    public void setIncurredCostTravel(BigDecimal incurredCostTravel) {
        this.incurredCostTravel = incurredCostTravel;
    }

    public BigDecimal getIncurredCostOther() {
        return incurredCostOther;
    }

    public void setIncurredCostOther(BigDecimal incurredCostOther) {
        this.incurredCostOther = incurredCostOther;
    }

    public BigDecimal getConfirmedCostHardware() {
        return confirmedCostHardware;
    }

    public void setConfirmedCostHardware(BigDecimal confirmedCostHardware) {
        this.confirmedCostHardware = confirmedCostHardware;
    }

    public BigDecimal getConfirmedCostLabour() {
        return confirmedCostLabour;
    }

    public void setConfirmedCostLabour(BigDecimal confirmedCostLabour) {
        this.confirmedCostLabour = confirmedCostLabour;
    }

    public BigDecimal getConfirmedCostTravel() {
        return confirmedCostTravel;
    }

    public void setConfirmedCostTravel(BigDecimal confirmedCostTravel) {
        this.confirmedCostTravel = confirmedCostTravel;
    }

    public BigDecimal getConfirmedCostOther() {
        return confirmedCostOther;
    }

    public void setConfirmedCostOther(BigDecimal confirmedCostOther) {
        this.confirmedCostOther = confirmedCostOther;
    }

    public Byte getIsObjectiveProject() {
        return isObjectiveProject;
    }

    public void setIsObjectiveProject(Byte isObjectiveProject) {
        this.isObjectiveProject = isObjectiveProject;
    }

    public Byte getProjectLevel() {
        return projectLevel;
    }

    public void setProjectLevel(Byte projectLevel) {
        this.projectLevel = projectLevel;
    }

    public String getOuname() {
        return ouname;
    }

    public void setOuname(String ouname) {
        this.ouname = ouname;
    }
}
