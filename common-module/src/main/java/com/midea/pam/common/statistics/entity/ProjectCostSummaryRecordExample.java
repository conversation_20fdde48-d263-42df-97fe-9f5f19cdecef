package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectCostSummaryRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectCostSummaryRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBatchNumIsNull() {
            addCriterion("batch_num is null");
            return (Criteria) this;
        }

        public Criteria andBatchNumIsNotNull() {
            addCriterion("batch_num is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNumEqualTo(Long value) {
            addCriterion("batch_num =", value, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumNotEqualTo(Long value) {
            addCriterion("batch_num <>", value, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumGreaterThan(Long value) {
            addCriterion("batch_num >", value, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumGreaterThanOrEqualTo(Long value) {
            addCriterion("batch_num >=", value, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumLessThan(Long value) {
            addCriterion("batch_num <", value, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumLessThanOrEqualTo(Long value) {
            addCriterion("batch_num <=", value, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumIn(List<Long> values) {
            addCriterion("batch_num in", values, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumNotIn(List<Long> values) {
            addCriterion("batch_num not in", values, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumBetween(Long value1, Long value2) {
            addCriterion("batch_num between", value1, value2, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumNotBetween(Long value1, Long value2) {
            addCriterion("batch_num not between", value1, value2, "batchNum");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNull() {
            addCriterion("project_status is null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNotNull() {
            addCriterion("project_status is not null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusEqualTo(Integer value) {
            addCriterion("project_status =", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotEqualTo(Integer value) {
            addCriterion("project_status <>", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThan(Integer value) {
            addCriterion("project_status >", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("project_status >=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThan(Integer value) {
            addCriterion("project_status <", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThanOrEqualTo(Integer value) {
            addCriterion("project_status <=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIn(List<Integer> values) {
            addCriterion("project_status in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotIn(List<Integer> values) {
            addCriterion("project_status not in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusBetween(Integer value1, Integer value2) {
            addCriterion("project_status between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("project_status not between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountIsNull() {
            addCriterion("confirmed_income_total_amount is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountIsNotNull() {
            addCriterion("confirmed_income_total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountEqualTo(BigDecimal value) {
            addCriterion("confirmed_income_total_amount =", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_income_total_amount <>", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("confirmed_income_total_amount >", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_income_total_amount >=", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountLessThan(BigDecimal value) {
            addCriterion("confirmed_income_total_amount <", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_income_total_amount <=", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountIn(List<BigDecimal> values) {
            addCriterion("confirmed_income_total_amount in", values, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_income_total_amount not in", values, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_income_total_amount between", value1, value2, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_income_total_amount not between", value1, value2, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountIsNull() {
            addCriterion("standard_confirmed_income_total_amount is null");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountIsNotNull() {
            addCriterion("standard_confirmed_income_total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountEqualTo(BigDecimal value) {
            addCriterion("standard_confirmed_income_total_amount =", value, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("standard_confirmed_income_total_amount <>", value, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("standard_confirmed_income_total_amount >", value, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_confirmed_income_total_amount >=", value, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountLessThan(BigDecimal value) {
            addCriterion("standard_confirmed_income_total_amount <", value, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_confirmed_income_total_amount <=", value, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountIn(List<BigDecimal> values) {
            addCriterion("standard_confirmed_income_total_amount in", values, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("standard_confirmed_income_total_amount not in", values, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_confirmed_income_total_amount between", value1, value2, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_confirmed_income_total_amount not between", value1, value2, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountIsNull() {
            addCriterion("confirmed_cost_total_amount is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountIsNotNull() {
            addCriterion("confirmed_cost_total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount =", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount <>", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount >", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount >=", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountLessThan(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount <", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount <=", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountIn(List<BigDecimal> values) {
            addCriterion("confirmed_cost_total_amount in", values, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_cost_total_amount not in", values, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_cost_total_amount between", value1, value2, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_cost_total_amount not between", value1, value2, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioIsNull() {
            addCriterion("confirmed_gross_profit_ratio is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioIsNotNull() {
            addCriterion("confirmed_gross_profit_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioEqualTo(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio =", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio <>", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioGreaterThan(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio >", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio >=", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioLessThan(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio <", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio <=", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioIn(List<BigDecimal> values) {
            addCriterion("confirmed_gross_profit_ratio in", values, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_gross_profit_ratio not in", values, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_gross_profit_ratio between", value1, value2, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_gross_profit_ratio not between", value1, value2, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountIsNull() {
            addCriterion("confirmed_exchange_amount is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountIsNotNull() {
            addCriterion("confirmed_exchange_amount is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountEqualTo(BigDecimal value) {
            addCriterion("confirmed_exchange_amount =", value, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_exchange_amount <>", value, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountGreaterThan(BigDecimal value) {
            addCriterion("confirmed_exchange_amount >", value, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_exchange_amount >=", value, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountLessThan(BigDecimal value) {
            addCriterion("confirmed_exchange_amount <", value, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_exchange_amount <=", value, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountIn(List<BigDecimal> values) {
            addCriterion("confirmed_exchange_amount in", values, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_exchange_amount not in", values, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_exchange_amount between", value1, value2, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_exchange_amount not between", value1, value2, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetIsNull() {
            addCriterion("project_budget is null");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetIsNotNull() {
            addCriterion("project_budget is not null");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetEqualTo(BigDecimal value) {
            addCriterion("project_budget =", value, "projectBudget");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetNotEqualTo(BigDecimal value) {
            addCriterion("project_budget <>", value, "projectBudget");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetGreaterThan(BigDecimal value) {
            addCriterion("project_budget >", value, "projectBudget");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_budget >=", value, "projectBudget");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetLessThan(BigDecimal value) {
            addCriterion("project_budget <", value, "projectBudget");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_budget <=", value, "projectBudget");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetIn(List<BigDecimal> values) {
            addCriterion("project_budget in", values, "projectBudget");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetNotIn(List<BigDecimal> values) {
            addCriterion("project_budget not in", values, "projectBudget");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_budget between", value1, value2, "projectBudget");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_budget not between", value1, value2, "projectBudget");
            return (Criteria) this;
        }

        public Criteria andProjectAmountIsNull() {
            addCriterion("project_amount is null");
            return (Criteria) this;
        }

        public Criteria andProjectAmountIsNotNull() {
            addCriterion("project_amount is not null");
            return (Criteria) this;
        }

        public Criteria andProjectAmountEqualTo(BigDecimal value) {
            addCriterion("project_amount =", value, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountNotEqualTo(BigDecimal value) {
            addCriterion("project_amount <>", value, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountGreaterThan(BigDecimal value) {
            addCriterion("project_amount >", value, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_amount >=", value, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountLessThan(BigDecimal value) {
            addCriterion("project_amount <", value, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_amount <=", value, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountIn(List<BigDecimal> values) {
            addCriterion("project_amount in", values, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountNotIn(List<BigDecimal> values) {
            addCriterion("project_amount not in", values, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_amount between", value1, value2, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_amount not between", value1, value2, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountIsNull() {
            addCriterion("project_contract_standard_amount is null");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountIsNotNull() {
            addCriterion("project_contract_standard_amount is not null");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountEqualTo(BigDecimal value) {
            addCriterion("project_contract_standard_amount =", value, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountNotEqualTo(BigDecimal value) {
            addCriterion("project_contract_standard_amount <>", value, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountGreaterThan(BigDecimal value) {
            addCriterion("project_contract_standard_amount >", value, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_contract_standard_amount >=", value, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountLessThan(BigDecimal value) {
            addCriterion("project_contract_standard_amount <", value, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_contract_standard_amount <=", value, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountIn(List<BigDecimal> values) {
            addCriterion("project_contract_standard_amount in", values, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountNotIn(List<BigDecimal> values) {
            addCriterion("project_contract_standard_amount not in", values, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_contract_standard_amount between", value1, value2, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_contract_standard_amount not between", value1, value2, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andIncurredCostIsNull() {
            addCriterion("incurred_cost is null");
            return (Criteria) this;
        }

        public Criteria andIncurredCostIsNotNull() {
            addCriterion("incurred_cost is not null");
            return (Criteria) this;
        }

        public Criteria andIncurredCostEqualTo(BigDecimal value) {
            addCriterion("incurred_cost =", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostNotEqualTo(BigDecimal value) {
            addCriterion("incurred_cost <>", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostGreaterThan(BigDecimal value) {
            addCriterion("incurred_cost >", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_cost >=", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLessThan(BigDecimal value) {
            addCriterion("incurred_cost <", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_cost <=", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostIn(List<BigDecimal> values) {
            addCriterion("incurred_cost in", values, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostNotIn(List<BigDecimal> values) {
            addCriterion("incurred_cost not in", values, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_cost between", value1, value2, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_cost not between", value1, value2, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetIsNull() {
            addCriterion("remainder_budget is null");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetIsNotNull() {
            addCriterion("remainder_budget is not null");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetEqualTo(BigDecimal value) {
            addCriterion("remainder_budget =", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetNotEqualTo(BigDecimal value) {
            addCriterion("remainder_budget <>", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetGreaterThan(BigDecimal value) {
            addCriterion("remainder_budget >", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("remainder_budget >=", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetLessThan(BigDecimal value) {
            addCriterion("remainder_budget <", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("remainder_budget <=", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetIn(List<BigDecimal> values) {
            addCriterion("remainder_budget in", values, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetNotIn(List<BigDecimal> values) {
            addCriterion("remainder_budget not in", values, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("remainder_budget between", value1, value2, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("remainder_budget not between", value1, value2, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostIsNull() {
            addCriterion("current_target_cost is null");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostIsNotNull() {
            addCriterion("current_target_cost is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostEqualTo(BigDecimal value) {
            addCriterion("current_target_cost =", value, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostNotEqualTo(BigDecimal value) {
            addCriterion("current_target_cost <>", value, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostGreaterThan(BigDecimal value) {
            addCriterion("current_target_cost >", value, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("current_target_cost >=", value, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostLessThan(BigDecimal value) {
            addCriterion("current_target_cost <", value, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("current_target_cost <=", value, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostIn(List<BigDecimal> values) {
            addCriterion("current_target_cost in", values, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostNotIn(List<BigDecimal> values) {
            addCriterion("current_target_cost not in", values, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_target_cost between", value1, value2, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_target_cost not between", value1, value2, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andWinTotalCostIsNull() {
            addCriterion("win_total_cost is null");
            return (Criteria) this;
        }

        public Criteria andWinTotalCostIsNotNull() {
            addCriterion("win_total_cost is not null");
            return (Criteria) this;
        }

        public Criteria andWinTotalCostEqualTo(BigDecimal value) {
            addCriterion("win_total_cost =", value, "winTotalCost");
            return (Criteria) this;
        }

        public Criteria andWinTotalCostNotEqualTo(BigDecimal value) {
            addCriterion("win_total_cost <>", value, "winTotalCost");
            return (Criteria) this;
        }

        public Criteria andWinTotalCostGreaterThan(BigDecimal value) {
            addCriterion("win_total_cost >", value, "winTotalCost");
            return (Criteria) this;
        }

        public Criteria andWinTotalCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("win_total_cost >=", value, "winTotalCost");
            return (Criteria) this;
        }

        public Criteria andWinTotalCostLessThan(BigDecimal value) {
            addCriterion("win_total_cost <", value, "winTotalCost");
            return (Criteria) this;
        }

        public Criteria andWinTotalCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("win_total_cost <=", value, "winTotalCost");
            return (Criteria) this;
        }

        public Criteria andWinTotalCostIn(List<BigDecimal> values) {
            addCriterion("win_total_cost in", values, "winTotalCost");
            return (Criteria) this;
        }

        public Criteria andWinTotalCostNotIn(List<BigDecimal> values) {
            addCriterion("win_total_cost not in", values, "winTotalCost");
            return (Criteria) this;
        }

        public Criteria andWinTotalCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("win_total_cost between", value1, value2, "winTotalCost");
            return (Criteria) this;
        }

        public Criteria andWinTotalCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("win_total_cost not between", value1, value2, "winTotalCost");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitAmountIsNull() {
            addCriterion("win_grass_profit_amount is null");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitAmountIsNotNull() {
            addCriterion("win_grass_profit_amount is not null");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitAmountEqualTo(BigDecimal value) {
            addCriterion("win_grass_profit_amount =", value, "winGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitAmountNotEqualTo(BigDecimal value) {
            addCriterion("win_grass_profit_amount <>", value, "winGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitAmountGreaterThan(BigDecimal value) {
            addCriterion("win_grass_profit_amount >", value, "winGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("win_grass_profit_amount >=", value, "winGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitAmountLessThan(BigDecimal value) {
            addCriterion("win_grass_profit_amount <", value, "winGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("win_grass_profit_amount <=", value, "winGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitAmountIn(List<BigDecimal> values) {
            addCriterion("win_grass_profit_amount in", values, "winGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitAmountNotIn(List<BigDecimal> values) {
            addCriterion("win_grass_profit_amount not in", values, "winGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("win_grass_profit_amount between", value1, value2, "winGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("win_grass_profit_amount not between", value1, value2, "winGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitRatioIsNull() {
            addCriterion("win_grass_profit_ratio is null");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitRatioIsNotNull() {
            addCriterion("win_grass_profit_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitRatioEqualTo(BigDecimal value) {
            addCriterion("win_grass_profit_ratio =", value, "winGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitRatioNotEqualTo(BigDecimal value) {
            addCriterion("win_grass_profit_ratio <>", value, "winGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitRatioGreaterThan(BigDecimal value) {
            addCriterion("win_grass_profit_ratio >", value, "winGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("win_grass_profit_ratio >=", value, "winGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitRatioLessThan(BigDecimal value) {
            addCriterion("win_grass_profit_ratio <", value, "winGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("win_grass_profit_ratio <=", value, "winGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitRatioIn(List<BigDecimal> values) {
            addCriterion("win_grass_profit_ratio in", values, "winGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitRatioNotIn(List<BigDecimal> values) {
            addCriterion("win_grass_profit_ratio not in", values, "winGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("win_grass_profit_ratio between", value1, value2, "winGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andWinGrassProfitRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("win_grass_profit_ratio not between", value1, value2, "winGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentTotalCostIsNull() {
            addCriterion("current_total_cost is null");
            return (Criteria) this;
        }

        public Criteria andCurrentTotalCostIsNotNull() {
            addCriterion("current_total_cost is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentTotalCostEqualTo(BigDecimal value) {
            addCriterion("current_total_cost =", value, "currentTotalCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTotalCostNotEqualTo(BigDecimal value) {
            addCriterion("current_total_cost <>", value, "currentTotalCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTotalCostGreaterThan(BigDecimal value) {
            addCriterion("current_total_cost >", value, "currentTotalCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTotalCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("current_total_cost >=", value, "currentTotalCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTotalCostLessThan(BigDecimal value) {
            addCriterion("current_total_cost <", value, "currentTotalCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTotalCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("current_total_cost <=", value, "currentTotalCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTotalCostIn(List<BigDecimal> values) {
            addCriterion("current_total_cost in", values, "currentTotalCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTotalCostNotIn(List<BigDecimal> values) {
            addCriterion("current_total_cost not in", values, "currentTotalCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTotalCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_total_cost between", value1, value2, "currentTotalCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTotalCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_total_cost not between", value1, value2, "currentTotalCost");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitAmountIsNull() {
            addCriterion("current_grass_profit_amount is null");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitAmountIsNotNull() {
            addCriterion("current_grass_profit_amount is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitAmountEqualTo(BigDecimal value) {
            addCriterion("current_grass_profit_amount =", value, "currentGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitAmountNotEqualTo(BigDecimal value) {
            addCriterion("current_grass_profit_amount <>", value, "currentGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitAmountGreaterThan(BigDecimal value) {
            addCriterion("current_grass_profit_amount >", value, "currentGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("current_grass_profit_amount >=", value, "currentGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitAmountLessThan(BigDecimal value) {
            addCriterion("current_grass_profit_amount <", value, "currentGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("current_grass_profit_amount <=", value, "currentGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitAmountIn(List<BigDecimal> values) {
            addCriterion("current_grass_profit_amount in", values, "currentGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitAmountNotIn(List<BigDecimal> values) {
            addCriterion("current_grass_profit_amount not in", values, "currentGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_grass_profit_amount between", value1, value2, "currentGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_grass_profit_amount not between", value1, value2, "currentGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitRatioIsNull() {
            addCriterion("current_grass_profit_ratio is null");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitRatioIsNotNull() {
            addCriterion("current_grass_profit_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitRatioEqualTo(BigDecimal value) {
            addCriterion("current_grass_profit_ratio =", value, "currentGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitRatioNotEqualTo(BigDecimal value) {
            addCriterion("current_grass_profit_ratio <>", value, "currentGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitRatioGreaterThan(BigDecimal value) {
            addCriterion("current_grass_profit_ratio >", value, "currentGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("current_grass_profit_ratio >=", value, "currentGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitRatioLessThan(BigDecimal value) {
            addCriterion("current_grass_profit_ratio <", value, "currentGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("current_grass_profit_ratio <=", value, "currentGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitRatioIn(List<BigDecimal> values) {
            addCriterion("current_grass_profit_ratio in", values, "currentGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitRatioNotIn(List<BigDecimal> values) {
            addCriterion("current_grass_profit_ratio not in", values, "currentGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_grass_profit_ratio between", value1, value2, "currentGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentGrassProfitRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_grass_profit_ratio not between", value1, value2, "currentGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitRatioIsNull() {
            addCriterion("target_budget_grass_profit_ratio is null");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitRatioIsNotNull() {
            addCriterion("target_budget_grass_profit_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitRatioEqualTo(BigDecimal value) {
            addCriterion("target_budget_grass_profit_ratio =", value, "targetBudgetGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitRatioNotEqualTo(BigDecimal value) {
            addCriterion("target_budget_grass_profit_ratio <>", value, "targetBudgetGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitRatioGreaterThan(BigDecimal value) {
            addCriterion("target_budget_grass_profit_ratio >", value, "targetBudgetGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("target_budget_grass_profit_ratio >=", value, "targetBudgetGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitRatioLessThan(BigDecimal value) {
            addCriterion("target_budget_grass_profit_ratio <", value, "targetBudgetGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("target_budget_grass_profit_ratio <=", value, "targetBudgetGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitRatioIn(List<BigDecimal> values) {
            addCriterion("target_budget_grass_profit_ratio in", values, "targetBudgetGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitRatioNotIn(List<BigDecimal> values) {
            addCriterion("target_budget_grass_profit_ratio not in", values, "targetBudgetGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("target_budget_grass_profit_ratio between", value1, value2, "targetBudgetGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("target_budget_grass_profit_ratio not between", value1, value2, "targetBudgetGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitAmountIsNull() {
            addCriterion("target_budget_grass_profit_amount is null");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitAmountIsNotNull() {
            addCriterion("target_budget_grass_profit_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitAmountEqualTo(BigDecimal value) {
            addCriterion("target_budget_grass_profit_amount =", value, "targetBudgetGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitAmountNotEqualTo(BigDecimal value) {
            addCriterion("target_budget_grass_profit_amount <>", value, "targetBudgetGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitAmountGreaterThan(BigDecimal value) {
            addCriterion("target_budget_grass_profit_amount >", value, "targetBudgetGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("target_budget_grass_profit_amount >=", value, "targetBudgetGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitAmountLessThan(BigDecimal value) {
            addCriterion("target_budget_grass_profit_amount <", value, "targetBudgetGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("target_budget_grass_profit_amount <=", value, "targetBudgetGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitAmountIn(List<BigDecimal> values) {
            addCriterion("target_budget_grass_profit_amount in", values, "targetBudgetGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitAmountNotIn(List<BigDecimal> values) {
            addCriterion("target_budget_grass_profit_amount not in", values, "targetBudgetGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("target_budget_grass_profit_amount between", value1, value2, "targetBudgetGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetBudgetGrassProfitAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("target_budget_grass_profit_amount not between", value1, value2, "targetBudgetGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioIsNull() {
            addCriterion("target_cost_grass_profit_ratio is null");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioIsNotNull() {
            addCriterion("target_cost_grass_profit_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioEqualTo(BigDecimal value) {
            addCriterion("target_cost_grass_profit_ratio =", value, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioNotEqualTo(BigDecimal value) {
            addCriterion("target_cost_grass_profit_ratio <>", value, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioGreaterThan(BigDecimal value) {
            addCriterion("target_cost_grass_profit_ratio >", value, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("target_cost_grass_profit_ratio >=", value, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioLessThan(BigDecimal value) {
            addCriterion("target_cost_grass_profit_ratio <", value, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("target_cost_grass_profit_ratio <=", value, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioIn(List<BigDecimal> values) {
            addCriterion("target_cost_grass_profit_ratio in", values, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioNotIn(List<BigDecimal> values) {
            addCriterion("target_cost_grass_profit_ratio not in", values, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("target_cost_grass_profit_ratio between", value1, value2, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("target_cost_grass_profit_ratio not between", value1, value2, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitAmountIsNull() {
            addCriterion("target_cost_grass_profit_amount is null");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitAmountIsNotNull() {
            addCriterion("target_cost_grass_profit_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitAmountEqualTo(BigDecimal value) {
            addCriterion("target_cost_grass_profit_amount =", value, "targetCostGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitAmountNotEqualTo(BigDecimal value) {
            addCriterion("target_cost_grass_profit_amount <>", value, "targetCostGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitAmountGreaterThan(BigDecimal value) {
            addCriterion("target_cost_grass_profit_amount >", value, "targetCostGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("target_cost_grass_profit_amount >=", value, "targetCostGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitAmountLessThan(BigDecimal value) {
            addCriterion("target_cost_grass_profit_amount <", value, "targetCostGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("target_cost_grass_profit_amount <=", value, "targetCostGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitAmountIn(List<BigDecimal> values) {
            addCriterion("target_cost_grass_profit_amount in", values, "targetCostGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitAmountNotIn(List<BigDecimal> values) {
            addCriterion("target_cost_grass_profit_amount not in", values, "targetCostGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("target_cost_grass_profit_amount between", value1, value2, "targetCostGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("target_cost_grass_profit_amount not between", value1, value2, "targetCostGrassProfitAmount");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}