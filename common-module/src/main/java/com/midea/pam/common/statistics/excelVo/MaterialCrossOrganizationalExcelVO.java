package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

/**
 * @description 物料导出模版
 */
@Getter
@Setter
public class MaterialCrossOrganizationalExcelVO {

    private Long id;

    @Excel(name = "序号", width = 10)
    private int num;

    @Excel(name = "PAM编码", width = 15, replace = {"-_null"})
    private String pamCode;

    @Excel(name = "ERP编码", width = 15, replace = {"-_null"})
    private String itemCode;

    @Excel(name = "物料描述", width = 15, replace = {"-_null"})
    private String itemInfo;

    @Excel(name = "基本计量单位", width = 15, replace = {"-_null"})
    private String unit;

    @Excel(name = "物料大类", width = 15, replace = {"-_null"})
    private String materialClassification;

    @Excel(name = "物料中类", width = 15, replace = {"-_null"})
    private String codingMiddleclass;

    @Excel(name = "物料小类", width = 15, replace = {"-_null"})
    private String materialType;

    @Excel(name = "名称", width = 15, replace = {"-_null"})
    private String name;

    @Excel(name = "品牌", width = 15, replace = {"-_null"})
    private String brand;

    @Excel(name = "型号规格", width = 15, replace = {"-_null"})
    private String model;

    @Excel(name = "图号", width = 15, replace = {"-_null"})
    private String figureNumber;

    @Excel(name = "创建人", width = 15, replace = {"-_null"})
    private String creatorName;

    @Excel(name = "创建日期", width = 15, format = "yyyy-MM-dd", replace = {"-_null"})
    private Date create_at;

    @Excel(name = "最后更新人", width = 15, replace = {"-_null"})
    private String updaterName;

    @Excel(name = "最后更新日期", width = 15, format = "yyyy-MM-dd", replace = {"-_null"})
    private Date update_at;

    @Excel(name = "是否待退市物料", width = 20, replace = {"否_false", "是_true", "_null"})
    private Boolean delistFlag;

    @Excel(name = "库存组织名称", width = 15, replace = {"-_null"})
    private String organizationName;
}
