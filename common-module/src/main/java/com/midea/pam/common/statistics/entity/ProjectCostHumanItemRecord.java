package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProjectCostHumanItemRecord extends LongIdEntity implements Serializable {
    private Long id;

    private Long executeId;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private Long summaryId;

    private String itemName;

    private BigDecimal auditCost;

    private BigDecimal auditWorkingHours;

    private BigDecimal auditingCost;

    private BigDecimal auditingWorkingHours;

    private BigDecimal totalCost;

    private BigDecimal outerLaborCost;

    private BigDecimal innerLaborCost;

    private BigDecimal totalWorkingHours;

    private Boolean deletedFlag;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public Long getSummaryId() {
        return summaryId;
    }

    public void setSummaryId(Long summaryId) {
        this.summaryId = summaryId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }

    public BigDecimal getAuditCost() {
        return auditCost;
    }

    public void setAuditCost(BigDecimal auditCost) {
        this.auditCost = auditCost;
    }

    public BigDecimal getAuditWorkingHours() {
        return auditWorkingHours;
    }

    public void setAuditWorkingHours(BigDecimal auditWorkingHours) {
        this.auditWorkingHours = auditWorkingHours;
    }

    public BigDecimal getAuditingCost() {
        return auditingCost;
    }

    public void setAuditingCost(BigDecimal auditingCost) {
        this.auditingCost = auditingCost;
    }

    public BigDecimal getAuditingWorkingHours() {
        return auditingWorkingHours;
    }

    public void setAuditingWorkingHours(BigDecimal auditingWorkingHours) {
        this.auditingWorkingHours = auditingWorkingHours;
    }

    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    public BigDecimal getOuterLaborCost() {
        return outerLaborCost;
    }

    public void setOuterLaborCost(BigDecimal outerLaborCost) {
        this.outerLaborCost = outerLaborCost;
    }

    public BigDecimal getInnerLaborCost() {
        return innerLaborCost;
    }

    public void setInnerLaborCost(BigDecimal innerLaborCost) {
        this.innerLaborCost = innerLaborCost;
    }

    public BigDecimal getTotalWorkingHours() {
        return totalWorkingHours;
    }

    public void setTotalWorkingHours(BigDecimal totalWorkingHours) {
        this.totalWorkingHours = totalWorkingHours;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", summaryId=").append(summaryId);
        sb.append(", itemName=").append(itemName);
        sb.append(", auditCost=").append(auditCost);
        sb.append(", auditWorkingHours=").append(auditWorkingHours);
        sb.append(", auditingCost=").append(auditingCost);
        sb.append(", auditingWorkingHours=").append(auditingWorkingHours);
        sb.append(", totalCost=").append(totalCost);
        sb.append(", outerLaborCost=").append(outerLaborCost);
        sb.append(", innerLaborCost=").append(innerLaborCost);
        sb.append(", totalWorkingHours=").append(totalWorkingHours);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}