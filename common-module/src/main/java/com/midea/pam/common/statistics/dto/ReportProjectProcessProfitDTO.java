package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.ReportProjectProcessProfit;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class ReportProjectProcessProfitDTO extends ReportProjectProcessProfit {

    private Integer number;

    private Long contractId;

    private Long projectId;

    private String invoicePlanDetailIdStr;

    private Date invoicePlanDetailDate;

    private Date contractStartTime;

    private Date milePostStartTime;

    private Date milePostEndTime;

    private Date milePostActualStartTime;

    private Date milePostActualEndTime;

    private Date contractFilingDate;

    private BigDecimal feeCostSummary;
}