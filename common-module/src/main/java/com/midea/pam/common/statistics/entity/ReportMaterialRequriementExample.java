package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportMaterialRequriementExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportMaterialRequriementExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeIsNull() {
            addCriterion("ticket_task_code is null");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeIsNotNull() {
            addCriterion("ticket_task_code is not null");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeEqualTo(String value) {
            addCriterion("ticket_task_code =", value, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeNotEqualTo(String value) {
            addCriterion("ticket_task_code <>", value, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeGreaterThan(String value) {
            addCriterion("ticket_task_code >", value, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ticket_task_code >=", value, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeLessThan(String value) {
            addCriterion("ticket_task_code <", value, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeLessThanOrEqualTo(String value) {
            addCriterion("ticket_task_code <=", value, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeLike(String value) {
            addCriterion("ticket_task_code like", value, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeNotLike(String value) {
            addCriterion("ticket_task_code not like", value, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeIn(List<String> values) {
            addCriterion("ticket_task_code in", values, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeNotIn(List<String> values) {
            addCriterion("ticket_task_code not in", values, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeBetween(String value1, String value2) {
            addCriterion("ticket_task_code between", value1, value2, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeNotBetween(String value1, String value2) {
            addCriterion("ticket_task_code not between", value1, value2, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andTicketPlanTimeIsNull() {
            addCriterion("ticket_plan_time is null");
            return (Criteria) this;
        }

        public Criteria andTicketPlanTimeIsNotNull() {
            addCriterion("ticket_plan_time is not null");
            return (Criteria) this;
        }

        public Criteria andTicketPlanTimeEqualTo(Date value) {
            addCriterion("ticket_plan_time =", value, "ticketPlanTime");
            return (Criteria) this;
        }

        public Criteria andTicketPlanTimeNotEqualTo(Date value) {
            addCriterion("ticket_plan_time <>", value, "ticketPlanTime");
            return (Criteria) this;
        }

        public Criteria andTicketPlanTimeGreaterThan(Date value) {
            addCriterion("ticket_plan_time >", value, "ticketPlanTime");
            return (Criteria) this;
        }

        public Criteria andTicketPlanTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ticket_plan_time >=", value, "ticketPlanTime");
            return (Criteria) this;
        }

        public Criteria andTicketPlanTimeLessThan(Date value) {
            addCriterion("ticket_plan_time <", value, "ticketPlanTime");
            return (Criteria) this;
        }

        public Criteria andTicketPlanTimeLessThanOrEqualTo(Date value) {
            addCriterion("ticket_plan_time <=", value, "ticketPlanTime");
            return (Criteria) this;
        }

        public Criteria andTicketPlanTimeIn(List<Date> values) {
            addCriterion("ticket_plan_time in", values, "ticketPlanTime");
            return (Criteria) this;
        }

        public Criteria andTicketPlanTimeNotIn(List<Date> values) {
            addCriterion("ticket_plan_time not in", values, "ticketPlanTime");
            return (Criteria) this;
        }

        public Criteria andTicketPlanTimeBetween(Date value1, Date value2) {
            addCriterion("ticket_plan_time between", value1, value2, "ticketPlanTime");
            return (Criteria) this;
        }

        public Criteria andTicketPlanTimeNotBetween(Date value1, Date value2) {
            addCriterion("ticket_plan_time not between", value1, value2, "ticketPlanTime");
            return (Criteria) this;
        }

        public Criteria andTheassemblyCodeIsNull() {
            addCriterion("theassembly_code is null");
            return (Criteria) this;
        }

        public Criteria andTheassemblyCodeIsNotNull() {
            addCriterion("theassembly_code is not null");
            return (Criteria) this;
        }

        public Criteria andTheassemblyCodeEqualTo(String value) {
            addCriterion("theassembly_code =", value, "theassemblyCode");
            return (Criteria) this;
        }

        public Criteria andTheassemblyCodeNotEqualTo(String value) {
            addCriterion("theassembly_code <>", value, "theassemblyCode");
            return (Criteria) this;
        }

        public Criteria andTheassemblyCodeGreaterThan(String value) {
            addCriterion("theassembly_code >", value, "theassemblyCode");
            return (Criteria) this;
        }

        public Criteria andTheassemblyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("theassembly_code >=", value, "theassemblyCode");
            return (Criteria) this;
        }

        public Criteria andTheassemblyCodeLessThan(String value) {
            addCriterion("theassembly_code <", value, "theassemblyCode");
            return (Criteria) this;
        }

        public Criteria andTheassemblyCodeLessThanOrEqualTo(String value) {
            addCriterion("theassembly_code <=", value, "theassemblyCode");
            return (Criteria) this;
        }

        public Criteria andTheassemblyCodeLike(String value) {
            addCriterion("theassembly_code like", value, "theassemblyCode");
            return (Criteria) this;
        }

        public Criteria andTheassemblyCodeNotLike(String value) {
            addCriterion("theassembly_code not like", value, "theassemblyCode");
            return (Criteria) this;
        }

        public Criteria andTheassemblyCodeIn(List<String> values) {
            addCriterion("theassembly_code in", values, "theassemblyCode");
            return (Criteria) this;
        }

        public Criteria andTheassemblyCodeNotIn(List<String> values) {
            addCriterion("theassembly_code not in", values, "theassemblyCode");
            return (Criteria) this;
        }

        public Criteria andTheassemblyCodeBetween(String value1, String value2) {
            addCriterion("theassembly_code between", value1, value2, "theassemblyCode");
            return (Criteria) this;
        }

        public Criteria andTheassemblyCodeNotBetween(String value1, String value2) {
            addCriterion("theassembly_code not between", value1, value2, "theassemblyCode");
            return (Criteria) this;
        }

        public Criteria andTheassemblyDesIsNull() {
            addCriterion("theassembly_des is null");
            return (Criteria) this;
        }

        public Criteria andTheassemblyDesIsNotNull() {
            addCriterion("theassembly_des is not null");
            return (Criteria) this;
        }

        public Criteria andTheassemblyDesEqualTo(String value) {
            addCriterion("theassembly_des =", value, "theassemblyDes");
            return (Criteria) this;
        }

        public Criteria andTheassemblyDesNotEqualTo(String value) {
            addCriterion("theassembly_des <>", value, "theassemblyDes");
            return (Criteria) this;
        }

        public Criteria andTheassemblyDesGreaterThan(String value) {
            addCriterion("theassembly_des >", value, "theassemblyDes");
            return (Criteria) this;
        }

        public Criteria andTheassemblyDesGreaterThanOrEqualTo(String value) {
            addCriterion("theassembly_des >=", value, "theassemblyDes");
            return (Criteria) this;
        }

        public Criteria andTheassemblyDesLessThan(String value) {
            addCriterion("theassembly_des <", value, "theassemblyDes");
            return (Criteria) this;
        }

        public Criteria andTheassemblyDesLessThanOrEqualTo(String value) {
            addCriterion("theassembly_des <=", value, "theassemblyDes");
            return (Criteria) this;
        }

        public Criteria andTheassemblyDesLike(String value) {
            addCriterion("theassembly_des like", value, "theassemblyDes");
            return (Criteria) this;
        }

        public Criteria andTheassemblyDesNotLike(String value) {
            addCriterion("theassembly_des not like", value, "theassemblyDes");
            return (Criteria) this;
        }

        public Criteria andTheassemblyDesIn(List<String> values) {
            addCriterion("theassembly_des in", values, "theassemblyDes");
            return (Criteria) this;
        }

        public Criteria andTheassemblyDesNotIn(List<String> values) {
            addCriterion("theassembly_des not in", values, "theassemblyDes");
            return (Criteria) this;
        }

        public Criteria andTheassemblyDesBetween(String value1, String value2) {
            addCriterion("theassembly_des between", value1, value2, "theassemblyDes");
            return (Criteria) this;
        }

        public Criteria andTheassemblyDesNotBetween(String value1, String value2) {
            addCriterion("theassembly_des not between", value1, value2, "theassemblyDes");
            return (Criteria) this;
        }

        public Criteria andTheassemblyUnitIsNull() {
            addCriterion("theassembly_unit is null");
            return (Criteria) this;
        }

        public Criteria andTheassemblyUnitIsNotNull() {
            addCriterion("theassembly_unit is not null");
            return (Criteria) this;
        }

        public Criteria andTheassemblyUnitEqualTo(String value) {
            addCriterion("theassembly_unit =", value, "theassemblyUnit");
            return (Criteria) this;
        }

        public Criteria andTheassemblyUnitNotEqualTo(String value) {
            addCriterion("theassembly_unit <>", value, "theassemblyUnit");
            return (Criteria) this;
        }

        public Criteria andTheassemblyUnitGreaterThan(String value) {
            addCriterion("theassembly_unit >", value, "theassemblyUnit");
            return (Criteria) this;
        }

        public Criteria andTheassemblyUnitGreaterThanOrEqualTo(String value) {
            addCriterion("theassembly_unit >=", value, "theassemblyUnit");
            return (Criteria) this;
        }

        public Criteria andTheassemblyUnitLessThan(String value) {
            addCriterion("theassembly_unit <", value, "theassemblyUnit");
            return (Criteria) this;
        }

        public Criteria andTheassemblyUnitLessThanOrEqualTo(String value) {
            addCriterion("theassembly_unit <=", value, "theassemblyUnit");
            return (Criteria) this;
        }

        public Criteria andTheassemblyUnitLike(String value) {
            addCriterion("theassembly_unit like", value, "theassemblyUnit");
            return (Criteria) this;
        }

        public Criteria andTheassemblyUnitNotLike(String value) {
            addCriterion("theassembly_unit not like", value, "theassemblyUnit");
            return (Criteria) this;
        }

        public Criteria andTheassemblyUnitIn(List<String> values) {
            addCriterion("theassembly_unit in", values, "theassemblyUnit");
            return (Criteria) this;
        }

        public Criteria andTheassemblyUnitNotIn(List<String> values) {
            addCriterion("theassembly_unit not in", values, "theassemblyUnit");
            return (Criteria) this;
        }

        public Criteria andTheassemblyUnitBetween(String value1, String value2) {
            addCriterion("theassembly_unit between", value1, value2, "theassemblyUnit");
            return (Criteria) this;
        }

        public Criteria andTheassemblyUnitNotBetween(String value1, String value2) {
            addCriterion("theassembly_unit not between", value1, value2, "theassemblyUnit");
            return (Criteria) this;
        }

        public Criteria andModuleCodeIsNull() {
            addCriterion("module_code is null");
            return (Criteria) this;
        }

        public Criteria andModuleCodeIsNotNull() {
            addCriterion("module_code is not null");
            return (Criteria) this;
        }

        public Criteria andModuleCodeEqualTo(String value) {
            addCriterion("module_code =", value, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeNotEqualTo(String value) {
            addCriterion("module_code <>", value, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeGreaterThan(String value) {
            addCriterion("module_code >", value, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeGreaterThanOrEqualTo(String value) {
            addCriterion("module_code >=", value, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeLessThan(String value) {
            addCriterion("module_code <", value, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeLessThanOrEqualTo(String value) {
            addCriterion("module_code <=", value, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeLike(String value) {
            addCriterion("module_code like", value, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeNotLike(String value) {
            addCriterion("module_code not like", value, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeIn(List<String> values) {
            addCriterion("module_code in", values, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeNotIn(List<String> values) {
            addCriterion("module_code not in", values, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeBetween(String value1, String value2) {
            addCriterion("module_code between", value1, value2, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleCodeNotBetween(String value1, String value2) {
            addCriterion("module_code not between", value1, value2, "moduleCode");
            return (Criteria) this;
        }

        public Criteria andModuleNameIsNull() {
            addCriterion("module_name is null");
            return (Criteria) this;
        }

        public Criteria andModuleNameIsNotNull() {
            addCriterion("module_name is not null");
            return (Criteria) this;
        }

        public Criteria andModuleNameEqualTo(String value) {
            addCriterion("module_name =", value, "moduleName");
            return (Criteria) this;
        }

        public Criteria andModuleNameNotEqualTo(String value) {
            addCriterion("module_name <>", value, "moduleName");
            return (Criteria) this;
        }

        public Criteria andModuleNameGreaterThan(String value) {
            addCriterion("module_name >", value, "moduleName");
            return (Criteria) this;
        }

        public Criteria andModuleNameGreaterThanOrEqualTo(String value) {
            addCriterion("module_name >=", value, "moduleName");
            return (Criteria) this;
        }

        public Criteria andModuleNameLessThan(String value) {
            addCriterion("module_name <", value, "moduleName");
            return (Criteria) this;
        }

        public Criteria andModuleNameLessThanOrEqualTo(String value) {
            addCriterion("module_name <=", value, "moduleName");
            return (Criteria) this;
        }

        public Criteria andModuleNameLike(String value) {
            addCriterion("module_name like", value, "moduleName");
            return (Criteria) this;
        }

        public Criteria andModuleNameNotLike(String value) {
            addCriterion("module_name not like", value, "moduleName");
            return (Criteria) this;
        }

        public Criteria andModuleNameIn(List<String> values) {
            addCriterion("module_name in", values, "moduleName");
            return (Criteria) this;
        }

        public Criteria andModuleNameNotIn(List<String> values) {
            addCriterion("module_name not in", values, "moduleName");
            return (Criteria) this;
        }

        public Criteria andModuleNameBetween(String value1, String value2) {
            addCriterion("module_name between", value1, value2, "moduleName");
            return (Criteria) this;
        }

        public Criteria andModuleNameNotBetween(String value1, String value2) {
            addCriterion("module_name not between", value1, value2, "moduleName");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIsNull() {
            addCriterion("task_status is null");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIsNotNull() {
            addCriterion("task_status is not null");
            return (Criteria) this;
        }

        public Criteria andTaskStatusEqualTo(Integer value) {
            addCriterion("task_status =", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotEqualTo(Integer value) {
            addCriterion("task_status <>", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusGreaterThan(Integer value) {
            addCriterion("task_status >", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("task_status >=", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusLessThan(Integer value) {
            addCriterion("task_status <", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusLessThanOrEqualTo(Integer value) {
            addCriterion("task_status <=", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIn(List<Integer> values) {
            addCriterion("task_status in", values, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotIn(List<Integer> values) {
            addCriterion("task_status not in", values, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusBetween(Integer value1, Integer value2) {
            addCriterion("task_status between", value1, value2, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("task_status not between", value1, value2, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andPlanNumIsNull() {
            addCriterion("plan_num is null");
            return (Criteria) this;
        }

        public Criteria andPlanNumIsNotNull() {
            addCriterion("plan_num is not null");
            return (Criteria) this;
        }

        public Criteria andPlanNumEqualTo(BigDecimal value) {
            addCriterion("plan_num =", value, "planNum");
            return (Criteria) this;
        }

        public Criteria andPlanNumNotEqualTo(BigDecimal value) {
            addCriterion("plan_num <>", value, "planNum");
            return (Criteria) this;
        }

        public Criteria andPlanNumGreaterThan(BigDecimal value) {
            addCriterion("plan_num >", value, "planNum");
            return (Criteria) this;
        }

        public Criteria andPlanNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("plan_num >=", value, "planNum");
            return (Criteria) this;
        }

        public Criteria andPlanNumLessThan(BigDecimal value) {
            addCriterion("plan_num <", value, "planNum");
            return (Criteria) this;
        }

        public Criteria andPlanNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("plan_num <=", value, "planNum");
            return (Criteria) this;
        }

        public Criteria andPlanNumIn(List<BigDecimal> values) {
            addCriterion("plan_num in", values, "planNum");
            return (Criteria) this;
        }

        public Criteria andPlanNumNotIn(List<BigDecimal> values) {
            addCriterion("plan_num not in", values, "planNum");
            return (Criteria) this;
        }

        public Criteria andPlanNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("plan_num between", value1, value2, "planNum");
            return (Criteria) this;
        }

        public Criteria andPlanNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("plan_num not between", value1, value2, "planNum");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNull() {
            addCriterion("erp_code is null");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNotNull() {
            addCriterion("erp_code is not null");
            return (Criteria) this;
        }

        public Criteria andErpCodeEqualTo(String value) {
            addCriterion("erp_code =", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotEqualTo(String value) {
            addCriterion("erp_code <>", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThan(String value) {
            addCriterion("erp_code >", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("erp_code >=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThan(String value) {
            addCriterion("erp_code <", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThanOrEqualTo(String value) {
            addCriterion("erp_code <=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLike(String value) {
            addCriterion("erp_code like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotLike(String value) {
            addCriterion("erp_code not like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIn(List<String> values) {
            addCriterion("erp_code in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotIn(List<String> values) {
            addCriterion("erp_code not in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeBetween(String value1, String value2) {
            addCriterion("erp_code between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotBetween(String value1, String value2) {
            addCriterion("erp_code not between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNull() {
            addCriterion("materiel_descr is null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNotNull() {
            addCriterion("materiel_descr is not null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrEqualTo(String value) {
            addCriterion("materiel_descr =", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotEqualTo(String value) {
            addCriterion("materiel_descr <>", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThan(String value) {
            addCriterion("materiel_descr >", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThanOrEqualTo(String value) {
            addCriterion("materiel_descr >=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThan(String value) {
            addCriterion("materiel_descr <", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThanOrEqualTo(String value) {
            addCriterion("materiel_descr <=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLike(String value) {
            addCriterion("materiel_descr like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotLike(String value) {
            addCriterion("materiel_descr not like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIn(List<String> values) {
            addCriterion("materiel_descr in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotIn(List<String> values) {
            addCriterion("materiel_descr not in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrBetween(String value1, String value2) {
            addCriterion("materiel_descr between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotBetween(String value1, String value2) {
            addCriterion("materiel_descr not between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitIsNull() {
            addCriterion("material_unit is null");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitIsNotNull() {
            addCriterion("material_unit is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitEqualTo(String value) {
            addCriterion("material_unit =", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotEqualTo(String value) {
            addCriterion("material_unit <>", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitGreaterThan(String value) {
            addCriterion("material_unit >", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitGreaterThanOrEqualTo(String value) {
            addCriterion("material_unit >=", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLessThan(String value) {
            addCriterion("material_unit <", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLessThanOrEqualTo(String value) {
            addCriterion("material_unit <=", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLike(String value) {
            addCriterion("material_unit like", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotLike(String value) {
            addCriterion("material_unit not like", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitIn(List<String> values) {
            addCriterion("material_unit in", values, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotIn(List<String> values) {
            addCriterion("material_unit not in", values, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitBetween(String value1, String value2) {
            addCriterion("material_unit between", value1, value2, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotBetween(String value1, String value2) {
            addCriterion("material_unit not between", value1, value2, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeIsNull() {
            addCriterion("subinventory_code is null");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeIsNotNull() {
            addCriterion("subinventory_code is not null");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeEqualTo(String value) {
            addCriterion("subinventory_code =", value, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeNotEqualTo(String value) {
            addCriterion("subinventory_code <>", value, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeGreaterThan(String value) {
            addCriterion("subinventory_code >", value, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("subinventory_code >=", value, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeLessThan(String value) {
            addCriterion("subinventory_code <", value, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeLessThanOrEqualTo(String value) {
            addCriterion("subinventory_code <=", value, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeLike(String value) {
            addCriterion("subinventory_code like", value, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeNotLike(String value) {
            addCriterion("subinventory_code not like", value, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeIn(List<String> values) {
            addCriterion("subinventory_code in", values, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeNotIn(List<String> values) {
            addCriterion("subinventory_code not in", values, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeBetween(String value1, String value2) {
            addCriterion("subinventory_code between", value1, value2, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeNotBetween(String value1, String value2) {
            addCriterion("subinventory_code not between", value1, value2, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andTaskRequirementNumIsNull() {
            addCriterion("task_requirement_num is null");
            return (Criteria) this;
        }

        public Criteria andTaskRequirementNumIsNotNull() {
            addCriterion("task_requirement_num is not null");
            return (Criteria) this;
        }

        public Criteria andTaskRequirementNumEqualTo(BigDecimal value) {
            addCriterion("task_requirement_num =", value, "taskRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequirementNumNotEqualTo(BigDecimal value) {
            addCriterion("task_requirement_num <>", value, "taskRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequirementNumGreaterThan(BigDecimal value) {
            addCriterion("task_requirement_num >", value, "taskRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequirementNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("task_requirement_num >=", value, "taskRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequirementNumLessThan(BigDecimal value) {
            addCriterion("task_requirement_num <", value, "taskRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequirementNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("task_requirement_num <=", value, "taskRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequirementNumIn(List<BigDecimal> values) {
            addCriterion("task_requirement_num in", values, "taskRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequirementNumNotIn(List<BigDecimal> values) {
            addCriterion("task_requirement_num not in", values, "taskRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequirementNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("task_requirement_num between", value1, value2, "taskRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequirementNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("task_requirement_num not between", value1, value2, "taskRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequirementNumIsNull() {
            addCriterion("total_requirement_num is null");
            return (Criteria) this;
        }

        public Criteria andTotalRequirementNumIsNotNull() {
            addCriterion("total_requirement_num is not null");
            return (Criteria) this;
        }

        public Criteria andTotalRequirementNumEqualTo(BigDecimal value) {
            addCriterion("total_requirement_num =", value, "totalRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequirementNumNotEqualTo(BigDecimal value) {
            addCriterion("total_requirement_num <>", value, "totalRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequirementNumGreaterThan(BigDecimal value) {
            addCriterion("total_requirement_num >", value, "totalRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequirementNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_requirement_num >=", value, "totalRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequirementNumLessThan(BigDecimal value) {
            addCriterion("total_requirement_num <", value, "totalRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequirementNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_requirement_num <=", value, "totalRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequirementNumIn(List<BigDecimal> values) {
            addCriterion("total_requirement_num in", values, "totalRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequirementNumNotIn(List<BigDecimal> values) {
            addCriterion("total_requirement_num not in", values, "totalRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequirementNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_requirement_num between", value1, value2, "totalRequirementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequirementNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_requirement_num not between", value1, value2, "totalRequirementNum");
            return (Criteria) this;
        }

        public Criteria andIssuedMaterialNumIsNull() {
            addCriterion("issued_material_num is null");
            return (Criteria) this;
        }

        public Criteria andIssuedMaterialNumIsNotNull() {
            addCriterion("issued_material_num is not null");
            return (Criteria) this;
        }

        public Criteria andIssuedMaterialNumEqualTo(BigDecimal value) {
            addCriterion("issued_material_num =", value, "issuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andIssuedMaterialNumNotEqualTo(BigDecimal value) {
            addCriterion("issued_material_num <>", value, "issuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andIssuedMaterialNumGreaterThan(BigDecimal value) {
            addCriterion("issued_material_num >", value, "issuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andIssuedMaterialNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("issued_material_num >=", value, "issuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andIssuedMaterialNumLessThan(BigDecimal value) {
            addCriterion("issued_material_num <", value, "issuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andIssuedMaterialNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("issued_material_num <=", value, "issuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andIssuedMaterialNumIn(List<BigDecimal> values) {
            addCriterion("issued_material_num in", values, "issuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andIssuedMaterialNumNotIn(List<BigDecimal> values) {
            addCriterion("issued_material_num not in", values, "issuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andIssuedMaterialNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("issued_material_num between", value1, value2, "issuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andIssuedMaterialNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("issued_material_num not between", value1, value2, "issuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andUnissuedMaterialNumIsNull() {
            addCriterion("unissued_material_num is null");
            return (Criteria) this;
        }

        public Criteria andUnissuedMaterialNumIsNotNull() {
            addCriterion("unissued_material_num is not null");
            return (Criteria) this;
        }

        public Criteria andUnissuedMaterialNumEqualTo(BigDecimal value) {
            addCriterion("unissued_material_num =", value, "unissuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andUnissuedMaterialNumNotEqualTo(BigDecimal value) {
            addCriterion("unissued_material_num <>", value, "unissuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andUnissuedMaterialNumGreaterThan(BigDecimal value) {
            addCriterion("unissued_material_num >", value, "unissuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andUnissuedMaterialNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("unissued_material_num >=", value, "unissuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andUnissuedMaterialNumLessThan(BigDecimal value) {
            addCriterion("unissued_material_num <", value, "unissuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andUnissuedMaterialNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("unissued_material_num <=", value, "unissuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andUnissuedMaterialNumIn(List<BigDecimal> values) {
            addCriterion("unissued_material_num in", values, "unissuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andUnissuedMaterialNumNotIn(List<BigDecimal> values) {
            addCriterion("unissued_material_num not in", values, "unissuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andUnissuedMaterialNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unissued_material_num between", value1, value2, "unissuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andUnissuedMaterialNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unissued_material_num not between", value1, value2, "unissuedMaterialNum");
            return (Criteria) this;
        }

        public Criteria andStorageCurrentNumIsNull() {
            addCriterion("storage_current_num is null");
            return (Criteria) this;
        }

        public Criteria andStorageCurrentNumIsNotNull() {
            addCriterion("storage_current_num is not null");
            return (Criteria) this;
        }

        public Criteria andStorageCurrentNumEqualTo(BigDecimal value) {
            addCriterion("storage_current_num =", value, "storageCurrentNum");
            return (Criteria) this;
        }

        public Criteria andStorageCurrentNumNotEqualTo(BigDecimal value) {
            addCriterion("storage_current_num <>", value, "storageCurrentNum");
            return (Criteria) this;
        }

        public Criteria andStorageCurrentNumGreaterThan(BigDecimal value) {
            addCriterion("storage_current_num >", value, "storageCurrentNum");
            return (Criteria) this;
        }

        public Criteria andStorageCurrentNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("storage_current_num >=", value, "storageCurrentNum");
            return (Criteria) this;
        }

        public Criteria andStorageCurrentNumLessThan(BigDecimal value) {
            addCriterion("storage_current_num <", value, "storageCurrentNum");
            return (Criteria) this;
        }

        public Criteria andStorageCurrentNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("storage_current_num <=", value, "storageCurrentNum");
            return (Criteria) this;
        }

        public Criteria andStorageCurrentNumIn(List<BigDecimal> values) {
            addCriterion("storage_current_num in", values, "storageCurrentNum");
            return (Criteria) this;
        }

        public Criteria andStorageCurrentNumNotIn(List<BigDecimal> values) {
            addCriterion("storage_current_num not in", values, "storageCurrentNum");
            return (Criteria) this;
        }

        public Criteria andStorageCurrentNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("storage_current_num between", value1, value2, "storageCurrentNum");
            return (Criteria) this;
        }

        public Criteria andStorageCurrentNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("storage_current_num not between", value1, value2, "storageCurrentNum");
            return (Criteria) this;
        }

        public Criteria andAvailableNumIsNull() {
            addCriterion("available_num is null");
            return (Criteria) this;
        }

        public Criteria andAvailableNumIsNotNull() {
            addCriterion("available_num is not null");
            return (Criteria) this;
        }

        public Criteria andAvailableNumEqualTo(BigDecimal value) {
            addCriterion("available_num =", value, "availableNum");
            return (Criteria) this;
        }

        public Criteria andAvailableNumNotEqualTo(BigDecimal value) {
            addCriterion("available_num <>", value, "availableNum");
            return (Criteria) this;
        }

        public Criteria andAvailableNumGreaterThan(BigDecimal value) {
            addCriterion("available_num >", value, "availableNum");
            return (Criteria) this;
        }

        public Criteria andAvailableNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("available_num >=", value, "availableNum");
            return (Criteria) this;
        }

        public Criteria andAvailableNumLessThan(BigDecimal value) {
            addCriterion("available_num <", value, "availableNum");
            return (Criteria) this;
        }

        public Criteria andAvailableNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("available_num <=", value, "availableNum");
            return (Criteria) this;
        }

        public Criteria andAvailableNumIn(List<BigDecimal> values) {
            addCriterion("available_num in", values, "availableNum");
            return (Criteria) this;
        }

        public Criteria andAvailableNumNotIn(List<BigDecimal> values) {
            addCriterion("available_num not in", values, "availableNum");
            return (Criteria) this;
        }

        public Criteria andAvailableNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("available_num between", value1, value2, "availableNum");
            return (Criteria) this;
        }

        public Criteria andAvailableNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("available_num not between", value1, value2, "availableNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequriementNumIsNull() {
            addCriterion("task_requriement_num is null");
            return (Criteria) this;
        }

        public Criteria andTaskRequriementNumIsNotNull() {
            addCriterion("task_requriement_num is not null");
            return (Criteria) this;
        }

        public Criteria andTaskRequriementNumEqualTo(BigDecimal value) {
            addCriterion("task_requriement_num =", value, "taskRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequriementNumNotEqualTo(BigDecimal value) {
            addCriterion("task_requriement_num <>", value, "taskRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequriementNumGreaterThan(BigDecimal value) {
            addCriterion("task_requriement_num >", value, "taskRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequriementNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("task_requriement_num >=", value, "taskRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequriementNumLessThan(BigDecimal value) {
            addCriterion("task_requriement_num <", value, "taskRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequriementNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("task_requriement_num <=", value, "taskRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequriementNumIn(List<BigDecimal> values) {
            addCriterion("task_requriement_num in", values, "taskRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequriementNumNotIn(List<BigDecimal> values) {
            addCriterion("task_requriement_num not in", values, "taskRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequriementNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("task_requriement_num between", value1, value2, "taskRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTaskRequriementNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("task_requriement_num not between", value1, value2, "taskRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequriementNumIsNull() {
            addCriterion("total_requriement_num is null");
            return (Criteria) this;
        }

        public Criteria andTotalRequriementNumIsNotNull() {
            addCriterion("total_requriement_num is not null");
            return (Criteria) this;
        }

        public Criteria andTotalRequriementNumEqualTo(BigDecimal value) {
            addCriterion("total_requriement_num =", value, "totalRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequriementNumNotEqualTo(BigDecimal value) {
            addCriterion("total_requriement_num <>", value, "totalRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequriementNumGreaterThan(BigDecimal value) {
            addCriterion("total_requriement_num >", value, "totalRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequriementNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_requriement_num >=", value, "totalRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequriementNumLessThan(BigDecimal value) {
            addCriterion("total_requriement_num <", value, "totalRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequriementNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_requriement_num <=", value, "totalRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequriementNumIn(List<BigDecimal> values) {
            addCriterion("total_requriement_num in", values, "totalRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequriementNumNotIn(List<BigDecimal> values) {
            addCriterion("total_requriement_num not in", values, "totalRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequriementNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_requriement_num between", value1, value2, "totalRequriementNum");
            return (Criteria) this;
        }

        public Criteria andTotalRequriementNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_requriement_num not between", value1, value2, "totalRequriementNum");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoTotalNumIsNull() {
            addCriterion("delivered_po_total_num is null");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoTotalNumIsNotNull() {
            addCriterion("delivered_po_total_num is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoTotalNumEqualTo(BigDecimal value) {
            addCriterion("delivered_po_total_num =", value, "deliveredPoTotalNum");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoTotalNumNotEqualTo(BigDecimal value) {
            addCriterion("delivered_po_total_num <>", value, "deliveredPoTotalNum");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoTotalNumGreaterThan(BigDecimal value) {
            addCriterion("delivered_po_total_num >", value, "deliveredPoTotalNum");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoTotalNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("delivered_po_total_num >=", value, "deliveredPoTotalNum");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoTotalNumLessThan(BigDecimal value) {
            addCriterion("delivered_po_total_num <", value, "deliveredPoTotalNum");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoTotalNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("delivered_po_total_num <=", value, "deliveredPoTotalNum");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoTotalNumIn(List<BigDecimal> values) {
            addCriterion("delivered_po_total_num in", values, "deliveredPoTotalNum");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoTotalNumNotIn(List<BigDecimal> values) {
            addCriterion("delivered_po_total_num not in", values, "deliveredPoTotalNum");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoTotalNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("delivered_po_total_num between", value1, value2, "deliveredPoTotalNum");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoTotalNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("delivered_po_total_num not between", value1, value2, "deliveredPoTotalNum");
            return (Criteria) this;
        }

        public Criteria andNeedByDateIsNull() {
            addCriterion("need_by_date is null");
            return (Criteria) this;
        }

        public Criteria andNeedByDateIsNotNull() {
            addCriterion("need_by_date is not null");
            return (Criteria) this;
        }

        public Criteria andNeedByDateEqualTo(String value) {
            addCriterion("need_by_date =", value, "needByDate");
            return (Criteria) this;
        }

        public Criteria andNeedByDateNotEqualTo(String value) {
            addCriterion("need_by_date <>", value, "needByDate");
            return (Criteria) this;
        }

        public Criteria andNeedByDateGreaterThan(String value) {
            addCriterion("need_by_date >", value, "needByDate");
            return (Criteria) this;
        }

        public Criteria andNeedByDateGreaterThanOrEqualTo(String value) {
            addCriterion("need_by_date >=", value, "needByDate");
            return (Criteria) this;
        }

        public Criteria andNeedByDateLessThan(String value) {
            addCriterion("need_by_date <", value, "needByDate");
            return (Criteria) this;
        }

        public Criteria andNeedByDateLessThanOrEqualTo(String value) {
            addCriterion("need_by_date <=", value, "needByDate");
            return (Criteria) this;
        }

        public Criteria andNeedByDateLike(String value) {
            addCriterion("need_by_date like", value, "needByDate");
            return (Criteria) this;
        }

        public Criteria andNeedByDateNotLike(String value) {
            addCriterion("need_by_date not like", value, "needByDate");
            return (Criteria) this;
        }

        public Criteria andNeedByDateIn(List<String> values) {
            addCriterion("need_by_date in", values, "needByDate");
            return (Criteria) this;
        }

        public Criteria andNeedByDateNotIn(List<String> values) {
            addCriterion("need_by_date not in", values, "needByDate");
            return (Criteria) this;
        }

        public Criteria andNeedByDateBetween(String value1, String value2) {
            addCriterion("need_by_date between", value1, value2, "needByDate");
            return (Criteria) this;
        }

        public Criteria andNeedByDateNotBetween(String value1, String value2) {
            addCriterion("need_by_date not between", value1, value2, "needByDate");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoDetailIsNull() {
            addCriterion("delivered_po_detail is null");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoDetailIsNotNull() {
            addCriterion("delivered_po_detail is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoDetailEqualTo(String value) {
            addCriterion("delivered_po_detail =", value, "deliveredPoDetail");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoDetailNotEqualTo(String value) {
            addCriterion("delivered_po_detail <>", value, "deliveredPoDetail");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoDetailGreaterThan(String value) {
            addCriterion("delivered_po_detail >", value, "deliveredPoDetail");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoDetailGreaterThanOrEqualTo(String value) {
            addCriterion("delivered_po_detail >=", value, "deliveredPoDetail");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoDetailLessThan(String value) {
            addCriterion("delivered_po_detail <", value, "deliveredPoDetail");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoDetailLessThanOrEqualTo(String value) {
            addCriterion("delivered_po_detail <=", value, "deliveredPoDetail");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoDetailLike(String value) {
            addCriterion("delivered_po_detail like", value, "deliveredPoDetail");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoDetailNotLike(String value) {
            addCriterion("delivered_po_detail not like", value, "deliveredPoDetail");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoDetailIn(List<String> values) {
            addCriterion("delivered_po_detail in", values, "deliveredPoDetail");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoDetailNotIn(List<String> values) {
            addCriterion("delivered_po_detail not in", values, "deliveredPoDetail");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoDetailBetween(String value1, String value2) {
            addCriterion("delivered_po_detail between", value1, value2, "deliveredPoDetail");
            return (Criteria) this;
        }

        public Criteria andDeliveredPoDetailNotBetween(String value1, String value2) {
            addCriterion("delivered_po_detail not between", value1, value2, "deliveredPoDetail");
            return (Criteria) this;
        }

        public Criteria andBuyerIsNull() {
            addCriterion("buyer is null");
            return (Criteria) this;
        }

        public Criteria andBuyerIsNotNull() {
            addCriterion("buyer is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerEqualTo(String value) {
            addCriterion("buyer =", value, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerNotEqualTo(String value) {
            addCriterion("buyer <>", value, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerGreaterThan(String value) {
            addCriterion("buyer >", value, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerGreaterThanOrEqualTo(String value) {
            addCriterion("buyer >=", value, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerLessThan(String value) {
            addCriterion("buyer <", value, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerLessThanOrEqualTo(String value) {
            addCriterion("buyer <=", value, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerLike(String value) {
            addCriterion("buyer like", value, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerNotLike(String value) {
            addCriterion("buyer not like", value, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerIn(List<String> values) {
            addCriterion("buyer in", values, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerNotIn(List<String> values) {
            addCriterion("buyer not in", values, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerBetween(String value1, String value2) {
            addCriterion("buyer between", value1, value2, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerNotBetween(String value1, String value2) {
            addCriterion("buyer not between", value1, value2, "buyer");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryIsNull() {
            addCriterion("material_category is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryIsNotNull() {
            addCriterion("material_category is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryEqualTo(String value) {
            addCriterion("material_category =", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryNotEqualTo(String value) {
            addCriterion("material_category <>", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryGreaterThan(String value) {
            addCriterion("material_category >", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("material_category >=", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryLessThan(String value) {
            addCriterion("material_category <", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryLessThanOrEqualTo(String value) {
            addCriterion("material_category <=", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryLike(String value) {
            addCriterion("material_category like", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryNotLike(String value) {
            addCriterion("material_category not like", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryIn(List<String> values) {
            addCriterion("material_category in", values, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryNotIn(List<String> values) {
            addCriterion("material_category not in", values, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryBetween(String value1, String value2) {
            addCriterion("material_category between", value1, value2, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryNotBetween(String value1, String value2) {
            addCriterion("material_category not between", value1, value2, "materialCategory");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}