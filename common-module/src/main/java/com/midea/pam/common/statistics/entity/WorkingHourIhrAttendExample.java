package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WorkingHourIhrAttendExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WorkingHourIhrAttendExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andUserMipNameIsNull() {
            addCriterion("user_mip_name is null");
            return (Criteria) this;
        }

        public Criteria andUserMipNameIsNotNull() {
            addCriterion("user_mip_name is not null");
            return (Criteria) this;
        }

        public Criteria andUserMipNameEqualTo(String value) {
            addCriterion("user_mip_name =", value, "userMipName");
            return (Criteria) this;
        }

        public Criteria andUserMipNameNotEqualTo(String value) {
            addCriterion("user_mip_name <>", value, "userMipName");
            return (Criteria) this;
        }

        public Criteria andUserMipNameGreaterThan(String value) {
            addCriterion("user_mip_name >", value, "userMipName");
            return (Criteria) this;
        }

        public Criteria andUserMipNameGreaterThanOrEqualTo(String value) {
            addCriterion("user_mip_name >=", value, "userMipName");
            return (Criteria) this;
        }

        public Criteria andUserMipNameLessThan(String value) {
            addCriterion("user_mip_name <", value, "userMipName");
            return (Criteria) this;
        }

        public Criteria andUserMipNameLessThanOrEqualTo(String value) {
            addCriterion("user_mip_name <=", value, "userMipName");
            return (Criteria) this;
        }

        public Criteria andUserMipNameLike(String value) {
            addCriterion("user_mip_name like", value, "userMipName");
            return (Criteria) this;
        }

        public Criteria andUserMipNameNotLike(String value) {
            addCriterion("user_mip_name not like", value, "userMipName");
            return (Criteria) this;
        }

        public Criteria andUserMipNameIn(List<String> values) {
            addCriterion("user_mip_name in", values, "userMipName");
            return (Criteria) this;
        }

        public Criteria andUserMipNameNotIn(List<String> values) {
            addCriterion("user_mip_name not in", values, "userMipName");
            return (Criteria) this;
        }

        public Criteria andUserMipNameBetween(String value1, String value2) {
            addCriterion("user_mip_name between", value1, value2, "userMipName");
            return (Criteria) this;
        }

        public Criteria andUserMipNameNotBetween(String value1, String value2) {
            addCriterion("user_mip_name not between", value1, value2, "userMipName");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserMipIsNull() {
            addCriterion("user_mip is null");
            return (Criteria) this;
        }

        public Criteria andUserMipIsNotNull() {
            addCriterion("user_mip is not null");
            return (Criteria) this;
        }

        public Criteria andUserMipEqualTo(String value) {
            addCriterion("user_mip =", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipNotEqualTo(String value) {
            addCriterion("user_mip <>", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipGreaterThan(String value) {
            addCriterion("user_mip >", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipGreaterThanOrEqualTo(String value) {
            addCriterion("user_mip >=", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipLessThan(String value) {
            addCriterion("user_mip <", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipLessThanOrEqualTo(String value) {
            addCriterion("user_mip <=", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipLike(String value) {
            addCriterion("user_mip like", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipNotLike(String value) {
            addCriterion("user_mip not like", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipIn(List<String> values) {
            addCriterion("user_mip in", values, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipNotIn(List<String> values) {
            addCriterion("user_mip not in", values, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipBetween(String value1, String value2) {
            addCriterion("user_mip between", value1, value2, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipNotBetween(String value1, String value2) {
            addCriterion("user_mip not between", value1, value2, "userMip");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNull() {
            addCriterion("org_id is null");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNotNull() {
            addCriterion("org_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrgIdEqualTo(Long value) {
            addCriterion("org_id =", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotEqualTo(Long value) {
            addCriterion("org_id <>", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThan(Long value) {
            addCriterion("org_id >", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("org_id >=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThan(Long value) {
            addCriterion("org_id <", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("org_id <=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdIn(List<Long> values) {
            addCriterion("org_id in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotIn(List<Long> values) {
            addCriterion("org_id not in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdBetween(Long value1, Long value2) {
            addCriterion("org_id between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("org_id not between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgNameIsNull() {
            addCriterion("org_name is null");
            return (Criteria) this;
        }

        public Criteria andOrgNameIsNotNull() {
            addCriterion("org_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrgNameEqualTo(String value) {
            addCriterion("org_name =", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotEqualTo(String value) {
            addCriterion("org_name <>", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThan(String value) {
            addCriterion("org_name >", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("org_name >=", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThan(String value) {
            addCriterion("org_name <", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThanOrEqualTo(String value) {
            addCriterion("org_name <=", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLike(String value) {
            addCriterion("org_name like", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotLike(String value) {
            addCriterion("org_name not like", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameIn(List<String> values) {
            addCriterion("org_name in", values, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotIn(List<String> values) {
            addCriterion("org_name not in", values, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameBetween(String value1, String value2) {
            addCriterion("org_name between", value1, value2, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotBetween(String value1, String value2) {
            addCriterion("org_name not between", value1, value2, "orgName");
            return (Criteria) this;
        }

        public Criteria andApplyOrgIsNull() {
            addCriterion("apply_org is null");
            return (Criteria) this;
        }

        public Criteria andApplyOrgIsNotNull() {
            addCriterion("apply_org is not null");
            return (Criteria) this;
        }

        public Criteria andApplyOrgEqualTo(String value) {
            addCriterion("apply_org =", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNotEqualTo(String value) {
            addCriterion("apply_org <>", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgGreaterThan(String value) {
            addCriterion("apply_org >", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgGreaterThanOrEqualTo(String value) {
            addCriterion("apply_org >=", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgLessThan(String value) {
            addCriterion("apply_org <", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgLessThanOrEqualTo(String value) {
            addCriterion("apply_org <=", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgLike(String value) {
            addCriterion("apply_org like", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNotLike(String value) {
            addCriterion("apply_org not like", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgIn(List<String> values) {
            addCriterion("apply_org in", values, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNotIn(List<String> values) {
            addCriterion("apply_org not in", values, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgBetween(String value1, String value2) {
            addCriterion("apply_org between", value1, value2, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNotBetween(String value1, String value2) {
            addCriterion("apply_org not between", value1, value2, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNull() {
            addCriterion("apply_date is null");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNotNull() {
            addCriterion("apply_date is not null");
            return (Criteria) this;
        }

        public Criteria andApplyDateEqualTo(Date value) {
            addCriterion("apply_date =", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotEqualTo(Date value) {
            addCriterion("apply_date <>", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThan(Date value) {
            addCriterion("apply_date >", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThanOrEqualTo(Date value) {
            addCriterion("apply_date >=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThan(Date value) {
            addCriterion("apply_date <", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThanOrEqualTo(Date value) {
            addCriterion("apply_date <=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateIn(List<Date> values) {
            addCriterion("apply_date in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotIn(List<Date> values) {
            addCriterion("apply_date not in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateBetween(Date value1, Date value2) {
            addCriterion("apply_date between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotBetween(Date value1, Date value2) {
            addCriterion("apply_date not between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursIsNull() {
            addCriterion("ihr_attend_hours is null");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursIsNotNull() {
            addCriterion("ihr_attend_hours is not null");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursEqualTo(BigDecimal value) {
            addCriterion("ihr_attend_hours =", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursNotEqualTo(BigDecimal value) {
            addCriterion("ihr_attend_hours <>", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursGreaterThan(BigDecimal value) {
            addCriterion("ihr_attend_hours >", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ihr_attend_hours >=", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursLessThan(BigDecimal value) {
            addCriterion("ihr_attend_hours <", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ihr_attend_hours <=", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursIn(List<BigDecimal> values) {
            addCriterion("ihr_attend_hours in", values, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursNotIn(List<BigDecimal> values) {
            addCriterion("ihr_attend_hours not in", values, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ihr_attend_hours between", value1, value2, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ihr_attend_hours not between", value1, value2, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursIsNull() {
            addCriterion("actual_working_hours is null");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursIsNotNull() {
            addCriterion("actual_working_hours is not null");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursEqualTo(BigDecimal value) {
            addCriterion("actual_working_hours =", value, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursNotEqualTo(BigDecimal value) {
            addCriterion("actual_working_hours <>", value, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursGreaterThan(BigDecimal value) {
            addCriterion("actual_working_hours >", value, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_working_hours >=", value, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursLessThan(BigDecimal value) {
            addCriterion("actual_working_hours <", value, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_working_hours <=", value, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursIn(List<BigDecimal> values) {
            addCriterion("actual_working_hours in", values, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursNotIn(List<BigDecimal> values) {
            addCriterion("actual_working_hours not in", values, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_working_hours between", value1, value2, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_working_hours not between", value1, value2, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andPendingWorkingHoursIsNull() {
            addCriterion("pending_working_hours is null");
            return (Criteria) this;
        }

        public Criteria andPendingWorkingHoursIsNotNull() {
            addCriterion("pending_working_hours is not null");
            return (Criteria) this;
        }

        public Criteria andPendingWorkingHoursEqualTo(BigDecimal value) {
            addCriterion("pending_working_hours =", value, "pendingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andPendingWorkingHoursNotEqualTo(BigDecimal value) {
            addCriterion("pending_working_hours <>", value, "pendingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andPendingWorkingHoursGreaterThan(BigDecimal value) {
            addCriterion("pending_working_hours >", value, "pendingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andPendingWorkingHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("pending_working_hours >=", value, "pendingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andPendingWorkingHoursLessThan(BigDecimal value) {
            addCriterion("pending_working_hours <", value, "pendingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andPendingWorkingHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("pending_working_hours <=", value, "pendingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andPendingWorkingHoursIn(List<BigDecimal> values) {
            addCriterion("pending_working_hours in", values, "pendingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andPendingWorkingHoursNotIn(List<BigDecimal> values) {
            addCriterion("pending_working_hours not in", values, "pendingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andPendingWorkingHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pending_working_hours between", value1, value2, "pendingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andPendingWorkingHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pending_working_hours not between", value1, value2, "pendingWorkingHours");
            return (Criteria) this;
        }

        public Criteria andHoursDifferentIsNull() {
            addCriterion("hours_different is null");
            return (Criteria) this;
        }

        public Criteria andHoursDifferentIsNotNull() {
            addCriterion("hours_different is not null");
            return (Criteria) this;
        }

        public Criteria andHoursDifferentEqualTo(BigDecimal value) {
            addCriterion("hours_different =", value, "hoursDifferent");
            return (Criteria) this;
        }

        public Criteria andHoursDifferentNotEqualTo(BigDecimal value) {
            addCriterion("hours_different <>", value, "hoursDifferent");
            return (Criteria) this;
        }

        public Criteria andHoursDifferentGreaterThan(BigDecimal value) {
            addCriterion("hours_different >", value, "hoursDifferent");
            return (Criteria) this;
        }

        public Criteria andHoursDifferentGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("hours_different >=", value, "hoursDifferent");
            return (Criteria) this;
        }

        public Criteria andHoursDifferentLessThan(BigDecimal value) {
            addCriterion("hours_different <", value, "hoursDifferent");
            return (Criteria) this;
        }

        public Criteria andHoursDifferentLessThanOrEqualTo(BigDecimal value) {
            addCriterion("hours_different <=", value, "hoursDifferent");
            return (Criteria) this;
        }

        public Criteria andHoursDifferentIn(List<BigDecimal> values) {
            addCriterion("hours_different in", values, "hoursDifferent");
            return (Criteria) this;
        }

        public Criteria andHoursDifferentNotIn(List<BigDecimal> values) {
            addCriterion("hours_different not in", values, "hoursDifferent");
            return (Criteria) this;
        }

        public Criteria andHoursDifferentBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("hours_different between", value1, value2, "hoursDifferent");
            return (Criteria) this;
        }

        public Criteria andHoursDifferentNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("hours_different not between", value1, value2, "hoursDifferent");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdIsNull() {
            addCriterion("parent_unit_id is null");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdIsNotNull() {
            addCriterion("parent_unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdEqualTo(Long value) {
            addCriterion("parent_unit_id =", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdNotEqualTo(Long value) {
            addCriterion("parent_unit_id <>", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdGreaterThan(Long value) {
            addCriterion("parent_unit_id >", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("parent_unit_id >=", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdLessThan(Long value) {
            addCriterion("parent_unit_id <", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("parent_unit_id <=", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdIn(List<Long> values) {
            addCriterion("parent_unit_id in", values, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdNotIn(List<Long> values) {
            addCriterion("parent_unit_id not in", values, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdBetween(Long value1, Long value2) {
            addCriterion("parent_unit_id between", value1, value2, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("parent_unit_id not between", value1, value2, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitNameIsNull() {
            addCriterion("parent_unit_name is null");
            return (Criteria) this;
        }

        public Criteria andParentUnitNameIsNotNull() {
            addCriterion("parent_unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andParentUnitNameEqualTo(String value) {
            addCriterion("parent_unit_name =", value, "parentUnitName");
            return (Criteria) this;
        }

        public Criteria andParentUnitNameNotEqualTo(String value) {
            addCriterion("parent_unit_name <>", value, "parentUnitName");
            return (Criteria) this;
        }

        public Criteria andParentUnitNameGreaterThan(String value) {
            addCriterion("parent_unit_name >", value, "parentUnitName");
            return (Criteria) this;
        }

        public Criteria andParentUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("parent_unit_name >=", value, "parentUnitName");
            return (Criteria) this;
        }

        public Criteria andParentUnitNameLessThan(String value) {
            addCriterion("parent_unit_name <", value, "parentUnitName");
            return (Criteria) this;
        }

        public Criteria andParentUnitNameLessThanOrEqualTo(String value) {
            addCriterion("parent_unit_name <=", value, "parentUnitName");
            return (Criteria) this;
        }

        public Criteria andParentUnitNameLike(String value) {
            addCriterion("parent_unit_name like", value, "parentUnitName");
            return (Criteria) this;
        }

        public Criteria andParentUnitNameNotLike(String value) {
            addCriterion("parent_unit_name not like", value, "parentUnitName");
            return (Criteria) this;
        }

        public Criteria andParentUnitNameIn(List<String> values) {
            addCriterion("parent_unit_name in", values, "parentUnitName");
            return (Criteria) this;
        }

        public Criteria andParentUnitNameNotIn(List<String> values) {
            addCriterion("parent_unit_name not in", values, "parentUnitName");
            return (Criteria) this;
        }

        public Criteria andParentUnitNameBetween(String value1, String value2) {
            addCriterion("parent_unit_name between", value1, value2, "parentUnitName");
            return (Criteria) this;
        }

        public Criteria andParentUnitNameNotBetween(String value1, String value2) {
            addCriterion("parent_unit_name not between", value1, value2, "parentUnitName");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}