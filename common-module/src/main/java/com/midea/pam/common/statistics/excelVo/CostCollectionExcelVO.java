package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;

/**
 * @program: common-module
 * @description: 工时成本统计汇总导出实体类
 * @author:zhongpeng
 * @create:2020-03-20 17:27
 **/
public class CostCollectionExcelVO {
    @Excel(name = "序号", width = 10)
    private Integer num;

    @Excel(name = "审批月份", width = 20)
    private String approveMonth;

    @Excel(name = "出勤月份", width = 20)
    private String applyMonth;

    @Excel(name = "项目编号", width = 30)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "项目类型", width = 30)
    private String projectType;

    @Excel(name = "内部工时(H)", width = 20)
    private BigDecimal innerWorkingHour;

    @Excel(name = "外部工时(H)", width = 20)
    private BigDecimal outerWorkingHour;

    @Excel(name = "工时合计(H)", width = 20)
    private BigDecimal totalWorkingHour;

    @Excel(name = "内部工时成本", width = 20)
    private BigDecimal innerLaborCost;

    @Excel(name = "外部工时成本", width = 20)
    private BigDecimal outerLaborCost;

    @Excel(name = "工时成本合计", width = 20)
    private BigDecimal totalLaborCost;

    @Excel(name = "跨单位工时成本", width = 20)
    private BigDecimal crossUnitLaborCost;

    @Excel(name = "入账状态", width = 20, replace = {"未入账_0", "入账中_1", "已入账_2", " _null"})
    private Integer costCollectionStatus;

    @Excel(name = "币种", width = 20)
    private String currency;

    @Excel(name = "业务分类", width = 30)
    private String typeName;

    @Excel(name = "业务实体", width = 30)
    private String ouName;

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getApplyMonth() {
        return applyMonth;
    }

    public void setApplyMonth(String applyMonth) {
        this.applyMonth = applyMonth;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public BigDecimal getInnerWorkingHour() {
        return innerWorkingHour;
    }

    public void setInnerWorkingHour(BigDecimal innerWorkingHour) {
        this.innerWorkingHour = innerWorkingHour;
    }

    public BigDecimal getOuterWorkingHour() {
        return outerWorkingHour;
    }

    public void setOuterWorkingHour(BigDecimal outerWorkingHour) {
        this.outerWorkingHour = outerWorkingHour;
    }

    public BigDecimal getTotalWorkingHour() {
        return totalWorkingHour;
    }

    public void setTotalWorkingHour(BigDecimal totalWorkingHour) {
        this.totalWorkingHour = totalWorkingHour;
    }

    public BigDecimal getInnerLaborCost() {
        return innerLaborCost;
    }

    public void setInnerLaborCost(BigDecimal innerLaborCost) {
        this.innerLaborCost = innerLaborCost;
    }

    public BigDecimal getOuterLaborCost() {
        return outerLaborCost;
    }

    public void setOuterLaborCost(BigDecimal outerLaborCost) {
        this.outerLaborCost = outerLaborCost;
    }

    public BigDecimal getTotalLaborCost() {
        return totalLaborCost;
    }

    public void setTotalLaborCost(BigDecimal totalLaborCost) {
        this.totalLaborCost = totalLaborCost;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName;
    }

    public Integer getCostCollectionStatus() {
        return costCollectionStatus;
    }

    public void setCostCollectionStatus(Integer costCollectionStatus) {
        this.costCollectionStatus = costCollectionStatus;
    }

    public BigDecimal getCrossUnitLaborCost() {
        return crossUnitLaborCost;
    }

    public void setCrossUnitLaborCost(BigDecimal crossUnitLaborCost) {
        this.crossUnitLaborCost = crossUnitLaborCost;
    }

    public String getApproveMonth() {
        return approveMonth;
    }

    public void setApproveMonth(String approveMonth) {
        this.approveMonth = approveMonth;
    }
}
