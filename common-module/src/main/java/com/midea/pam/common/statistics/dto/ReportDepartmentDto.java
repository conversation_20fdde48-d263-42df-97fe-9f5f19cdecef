package com.midea.pam.common.statistics.dto;

import java.io.Serializable;

public class ReportDepartmentDto implements Serializable {
    private Long unitId;

    private String unitName;

    private Long owner;

    private String ownerName;

    private Long manager;

    private String managerName;

    private String preSalerName;

    private Long preSaler;

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Long getOwner() {
        return owner;
    }

    public void setOwner(Long owner) {
        this.owner = owner;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Long getManager() {
        return manager;
    }

    public void setManager(Long manager) {
        this.manager = manager;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public String getPreSalerName() {
        return preSalerName;
    }

    public void setPreSalerName(String preSalerName) {
        this.preSalerName = preSalerName;
    }

    public Long getPreSaler() {
        return preSaler;
    }

    public void setPreSaler(Long preSaler) {
        this.preSaler = preSaler;
    }
}