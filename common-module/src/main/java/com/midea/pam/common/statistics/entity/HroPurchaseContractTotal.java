package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目成本人力点工采购合同对账统计表")
public class HroPurchaseContractTotal extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "人力点工需求id")
    private Long hroRequirementId;

    @ApiModelProperty(value = "人力点工需求单据编号")
    private String requirementCode;

    @ApiModelProperty(value = "人力点工角色名称")
    private String roleName;

    @ApiModelProperty(value = "采购合同编号")
    private String purchaseContractCode;

    @ApiModelProperty(value = "采购合同名称")
    private String purchaseContractName;

    @ApiModelProperty(value = "总价（不含税）-本位币")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "已对账工时金额（不含税）")
    private BigDecimal billMhAmount;

    @ApiModelProperty(value = "已对账费用（含税）")
    private BigDecimal billCostAmount;

    @ApiModelProperty(value = "点工采购合同分配金额-已对账金额")
    private BigDecimal surplusAmount;

    @ApiModelProperty(value = "类型 1-在途、2-已完成")
    private Integer budgetType;

    @ApiModelProperty(value = "wbs编码")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "合同创建时间")
    private Date contractCreateTime;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "物料PAM编码")
    private String pamCode;

    @ApiModelProperty(value = "合同对账单对账时间")
    private Date dataTime;

    @ApiModelProperty(value = "合同对账单创建时间")
    private Date billCreateTime;

    private static final long serialVersionUID = 1L;

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public Long getHroRequirementId() {
        return hroRequirementId;
    }

    public void setHroRequirementId(Long hroRequirementId) {
        this.hroRequirementId = hroRequirementId;
    }

    public String getRequirementCode() {
        return requirementCode;
    }

    public void setRequirementCode(String requirementCode) {
        this.requirementCode = requirementCode == null ? null : requirementCode.trim();
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName == null ? null : roleName.trim();
    }

    public String getPurchaseContractCode() {
        return purchaseContractCode;
    }

    public void setPurchaseContractCode(String purchaseContractCode) {
        this.purchaseContractCode = purchaseContractCode == null ? null : purchaseContractCode.trim();
    }

    public String getPurchaseContractName() {
        return purchaseContractName;
    }

    public void setPurchaseContractName(String purchaseContractName) {
        this.purchaseContractName = purchaseContractName == null ? null : purchaseContractName.trim();
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getBillMhAmount() {
        return billMhAmount;
    }

    public void setBillMhAmount(BigDecimal billMhAmount) {
        this.billMhAmount = billMhAmount;
    }

    public BigDecimal getBillCostAmount() {
        return billCostAmount;
    }

    public void setBillCostAmount(BigDecimal billCostAmount) {
        this.billCostAmount = billCostAmount;
    }

    public BigDecimal getSurplusAmount() {
        return surplusAmount;
    }

    public void setSurplusAmount(BigDecimal surplusAmount) {
        this.surplusAmount = surplusAmount;
    }

    public Integer getBudgetType() {
        return budgetType;
    }

    public void setBudgetType(Integer budgetType) {
        this.budgetType = budgetType;
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public Date getContractCreateTime() {
        return contractCreateTime;
    }

    public void setContractCreateTime(Date contractCreateTime) {
        this.contractCreateTime = contractCreateTime;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName == null ? null : vendorName.trim();
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode == null ? null : pamCode.trim();
    }

    public Date getDataTime() {
        return dataTime;
    }

    public void setDataTime(Date dataTime) {
        this.dataTime = dataTime;
    }

    public Date getBillCreateTime() {
        return billCreateTime;
    }

    public void setBillCreateTime(Date billCreateTime) {
        this.billCreateTime = billCreateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", hroRequirementId=").append(hroRequirementId);
        sb.append(", requirementCode=").append(requirementCode);
        sb.append(", roleName=").append(roleName);
        sb.append(", purchaseContractCode=").append(purchaseContractCode);
        sb.append(", purchaseContractName=").append(purchaseContractName);
        sb.append(", totalAmount=").append(totalAmount);
        sb.append(", billMhAmount=").append(billMhAmount);
        sb.append(", billCostAmount=").append(billCostAmount);
        sb.append(", surplusAmount=").append(surplusAmount);
        sb.append(", budgetType=").append(budgetType);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", contractCreateTime=").append(contractCreateTime);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", vendorName=").append(vendorName);
        sb.append(", pamCode=").append(pamCode);
        sb.append(", dataTime=").append(dataTime);
        sb.append(", billCreateTime=").append(billCreateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}