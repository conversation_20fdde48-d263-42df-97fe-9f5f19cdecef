package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ReportReceivableReceiptClaim extends LongIdEntity implements Serializable {
    private Long id;

    private Long reportId;

    private Long executeId;

    private String projectCode;

    private String projectName;

    private String projectManager;

    private String salesManager;

    private String code;

    private String name;

    private String parentCode;

    private String parentName;

    private String unitName;

    private String projectType;

    private String customerCode;

    private String customerName;

    private Integer customerType;

    private BigDecimal invoiceAmount;

    private String receiptPlanDetailCode;

    private Integer receiptPlanDetailNum;

    private Date receiptPlanDetailDate;

    private BigDecimal receiptPlanDetailAmount;

    private String requirement;

    private String invoiceApplyDetailsCode;

    private String milestoneName;

    private BigDecimal actualAmount;

    private BigDecimal expireAmount;

    private String receiptConfigurationType;

    private String receiptConfigurationRule;

    private Date receiptDateStart;

    private BigDecimal days;

    private Date receiptDateEnd;

    private BigDecimal ageAmount0;

    private BigDecimal ageAmount1;

    private BigDecimal ageAmount2;

    private BigDecimal ageAmount3;

    private BigDecimal ageAmount4;

    private BigDecimal ageAmount5;

    private BigDecimal ageAmount6;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public String getSalesManager() {
        return salesManager;
    }

    public void setSalesManager(String salesManager) {
        this.salesManager = salesManager == null ? null : salesManager.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode == null ? null : parentCode.trim();
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName == null ? null : parentName.trim();
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType == null ? null : projectType.trim();
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode == null ? null : customerCode.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public String getReceiptPlanDetailCode() {
        return receiptPlanDetailCode;
    }

    public void setReceiptPlanDetailCode(String receiptPlanDetailCode) {
        this.receiptPlanDetailCode = receiptPlanDetailCode == null ? null : receiptPlanDetailCode.trim();
    }

    public Integer getReceiptPlanDetailNum() {
        return receiptPlanDetailNum;
    }

    public void setReceiptPlanDetailNum(Integer receiptPlanDetailNum) {
        this.receiptPlanDetailNum = receiptPlanDetailNum;
    }

    public Date getReceiptPlanDetailDate() {
        return receiptPlanDetailDate;
    }

    public void setReceiptPlanDetailDate(Date receiptPlanDetailDate) {
        this.receiptPlanDetailDate = receiptPlanDetailDate;
    }

    public BigDecimal getReceiptPlanDetailAmount() {
        return receiptPlanDetailAmount;
    }

    public void setReceiptPlanDetailAmount(BigDecimal receiptPlanDetailAmount) {
        this.receiptPlanDetailAmount = receiptPlanDetailAmount;
    }

    public String getRequirement() {
        return requirement;
    }

    public void setRequirement(String requirement) {
        this.requirement = requirement == null ? null : requirement.trim();
    }

    public String getInvoiceApplyDetailsCode() {
        return invoiceApplyDetailsCode;
    }

    public void setInvoiceApplyDetailsCode(String invoiceApplyDetailsCode) {
        this.invoiceApplyDetailsCode = invoiceApplyDetailsCode == null ? null : invoiceApplyDetailsCode.trim();
    }

    public String getMilestoneName() {
        return milestoneName;
    }

    public void setMilestoneName(String milestoneName) {
        this.milestoneName = milestoneName == null ? null : milestoneName.trim();
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public BigDecimal getExpireAmount() {
        return expireAmount;
    }

    public void setExpireAmount(BigDecimal expireAmount) {
        this.expireAmount = expireAmount;
    }

    public String getReceiptConfigurationType() {
        return receiptConfigurationType;
    }

    public void setReceiptConfigurationType(String receiptConfigurationType) {
        this.receiptConfigurationType = receiptConfigurationType == null ? null : receiptConfigurationType.trim();
    }

    public String getReceiptConfigurationRule() {
        return receiptConfigurationRule;
    }

    public void setReceiptConfigurationRule(String receiptConfigurationRule) {
        this.receiptConfigurationRule = receiptConfigurationRule == null ? null : receiptConfigurationRule.trim();
    }

    public Date getReceiptDateStart() {
        return receiptDateStart;
    }

    public void setReceiptDateStart(Date receiptDateStart) {
        this.receiptDateStart = receiptDateStart;
    }

    public BigDecimal getDays() {
        return days;
    }

    public void setDays(BigDecimal days) {
        this.days = days;
    }

    public Date getReceiptDateEnd() {
        return receiptDateEnd;
    }

    public void setReceiptDateEnd(Date receiptDateEnd) {
        this.receiptDateEnd = receiptDateEnd;
    }

    public BigDecimal getAgeAmount0() {
        return ageAmount0;
    }

    public void setAgeAmount0(BigDecimal ageAmount0) {
        this.ageAmount0 = ageAmount0;
    }

    public BigDecimal getAgeAmount1() {
        return ageAmount1;
    }

    public void setAgeAmount1(BigDecimal ageAmount1) {
        this.ageAmount1 = ageAmount1;
    }

    public BigDecimal getAgeAmount2() {
        return ageAmount2;
    }

    public void setAgeAmount2(BigDecimal ageAmount2) {
        this.ageAmount2 = ageAmount2;
    }

    public BigDecimal getAgeAmount3() {
        return ageAmount3;
    }

    public void setAgeAmount3(BigDecimal ageAmount3) {
        this.ageAmount3 = ageAmount3;
    }

    public BigDecimal getAgeAmount4() {
        return ageAmount4;
    }

    public void setAgeAmount4(BigDecimal ageAmount4) {
        this.ageAmount4 = ageAmount4;
    }

    public BigDecimal getAgeAmount5() {
        return ageAmount5;
    }

    public void setAgeAmount5(BigDecimal ageAmount5) {
        this.ageAmount5 = ageAmount5;
    }

    public BigDecimal getAgeAmount6() {
        return ageAmount6;
    }

    public void setAgeAmount6(BigDecimal ageAmount6) {
        this.ageAmount6 = ageAmount6;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", salesManager=").append(salesManager);
        sb.append(", code=").append(code);
        sb.append(", name=").append(name);
        sb.append(", parentCode=").append(parentCode);
        sb.append(", parentName=").append(parentName);
        sb.append(", unitName=").append(unitName);
        sb.append(", projectType=").append(projectType);
        sb.append(", customerCode=").append(customerCode);
        sb.append(", customerName=").append(customerName);
        sb.append(", customerType=").append(customerType);
        sb.append(", invoiceAmount=").append(invoiceAmount);
        sb.append(", receiptPlanDetailCode=").append(receiptPlanDetailCode);
        sb.append(", receiptPlanDetailNum=").append(receiptPlanDetailNum);
        sb.append(", receiptPlanDetailDate=").append(receiptPlanDetailDate);
        sb.append(", receiptPlanDetailAmount=").append(receiptPlanDetailAmount);
        sb.append(", requirement=").append(requirement);
        sb.append(", invoiceApplyDetailsCode=").append(invoiceApplyDetailsCode);
        sb.append(", milestoneName=").append(milestoneName);
        sb.append(", actualAmount=").append(actualAmount);
        sb.append(", expireAmount=").append(expireAmount);
        sb.append(", receiptConfigurationType=").append(receiptConfigurationType);
        sb.append(", receiptConfigurationRule=").append(receiptConfigurationRule);
        sb.append(", receiptDateStart=").append(receiptDateStart);
        sb.append(", days=").append(days);
        sb.append(", receiptDateEnd=").append(receiptDateEnd);
        sb.append(", ageAmount0=").append(ageAmount0);
        sb.append(", ageAmount1=").append(ageAmount1);
        sb.append(", ageAmount2=").append(ageAmount2);
        sb.append(", ageAmount3=").append(ageAmount3);
        sb.append(", ageAmount4=").append(ageAmount4);
        sb.append(", ageAmount5=").append(ageAmount5);
        sb.append(", ageAmount6=").append(ageAmount6);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}