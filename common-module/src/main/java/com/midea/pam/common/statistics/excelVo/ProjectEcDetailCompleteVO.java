package com.midea.pam.common.statistics.excelVo;


import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-7-25
 * @description 在途成本-费用报销（EC单）已申请未报销
 */
@Getter
@Setter
public class ProjectEcDetailCompleteVO {

    @Excel(name = "序号", width = 5)
    private Integer number;

    @Excel(name = "WBS", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项", width = 15)
    private String activityCode;

    @Excel(name = "EC单号", width = 20)
    private String feeApplyCode;

    @Excel(name = "审核通过并已入账金额", width = 15)
    private BigDecimal amount;

    @Excel(name = "入账日期", width = 20, format = "yyyy-MM-dd")
    private Date dataTime;

}
