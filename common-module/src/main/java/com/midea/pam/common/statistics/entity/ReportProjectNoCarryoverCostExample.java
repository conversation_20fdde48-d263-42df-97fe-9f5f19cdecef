package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportProjectNoCarryoverCostExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportProjectNoCarryoverCostExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNull() {
            addCriterion("project_manager is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNotNull() {
            addCriterion("project_manager is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerEqualTo(String value) {
            addCriterion("project_manager =", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotEqualTo(String value) {
            addCriterion("project_manager <>", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThan(String value) {
            addCriterion("project_manager >", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThanOrEqualTo(String value) {
            addCriterion("project_manager >=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThan(String value) {
            addCriterion("project_manager <", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThanOrEqualTo(String value) {
            addCriterion("project_manager <=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLike(String value) {
            addCriterion("project_manager like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotLike(String value) {
            addCriterion("project_manager not like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIn(List<String> values) {
            addCriterion("project_manager in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotIn(List<String> values) {
            addCriterion("project_manager not in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerBetween(String value1, String value2) {
            addCriterion("project_manager between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotBetween(String value1, String value2) {
            addCriterion("project_manager not between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNull() {
            addCriterion("project_status is null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNotNull() {
            addCriterion("project_status is not null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusEqualTo(Integer value) {
            addCriterion("project_status =", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotEqualTo(Integer value) {
            addCriterion("project_status <>", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThan(Integer value) {
            addCriterion("project_status >", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("project_status >=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThan(Integer value) {
            addCriterion("project_status <", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThanOrEqualTo(Integer value) {
            addCriterion("project_status <=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIn(List<Integer> values) {
            addCriterion("project_status in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotIn(List<Integer> values) {
            addCriterion("project_status not in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusBetween(Integer value1, Integer value2) {
            addCriterion("project_status between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("project_status not between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNull() {
            addCriterion("project_type is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNotNull() {
            addCriterion("project_type is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeEqualTo(String value) {
            addCriterion("project_type =", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotEqualTo(String value) {
            addCriterion("project_type <>", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThan(String value) {
            addCriterion("project_type >", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThanOrEqualTo(String value) {
            addCriterion("project_type >=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThan(String value) {
            addCriterion("project_type <", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThanOrEqualTo(String value) {
            addCriterion("project_type <=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLike(String value) {
            addCriterion("project_type like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotLike(String value) {
            addCriterion("project_type not like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIn(List<String> values) {
            addCriterion("project_type in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotIn(List<String> values) {
            addCriterion("project_type not in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeBetween(String value1, String value2) {
            addCriterion("project_type between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotBetween(String value1, String value2) {
            addCriterion("project_type not between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNull() {
            addCriterion("customer_code is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNotNull() {
            addCriterion("customer_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeEqualTo(String value) {
            addCriterion("customer_code =", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotEqualTo(String value) {
            addCriterion("customer_code <>", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThan(String value) {
            addCriterion("customer_code >", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("customer_code >=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThan(String value) {
            addCriterion("customer_code <", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThanOrEqualTo(String value) {
            addCriterion("customer_code <=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLike(String value) {
            addCriterion("customer_code like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotLike(String value) {
            addCriterion("customer_code not like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIn(List<String> values) {
            addCriterion("customer_code in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotIn(List<String> values) {
            addCriterion("customer_code not in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBetween(String value1, String value2) {
            addCriterion("customer_code between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotBetween(String value1, String value2) {
            addCriterion("customer_code not between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNull() {
            addCriterion("unit_name is null");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNotNull() {
            addCriterion("unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andUnitNameEqualTo(String value) {
            addCriterion("unit_name =", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotEqualTo(String value) {
            addCriterion("unit_name <>", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThan(String value) {
            addCriterion("unit_name >", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("unit_name >=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThan(String value) {
            addCriterion("unit_name <", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThanOrEqualTo(String value) {
            addCriterion("unit_name <=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLike(String value) {
            addCriterion("unit_name like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotLike(String value) {
            addCriterion("unit_name not like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIn(List<String> values) {
            addCriterion("unit_name in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotIn(List<String> values) {
            addCriterion("unit_name not in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameBetween(String value1, String value2) {
            addCriterion("unit_name between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotBetween(String value1, String value2) {
            addCriterion("unit_name not between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCollectIsNull() {
            addCriterion("material_actual_cost_collect is null");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCollectIsNotNull() {
            addCriterion("material_actual_cost_collect is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCollectEqualTo(BigDecimal value) {
            addCriterion("material_actual_cost_collect =", value, "materialActualCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCollectNotEqualTo(BigDecimal value) {
            addCriterion("material_actual_cost_collect <>", value, "materialActualCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCollectGreaterThan(BigDecimal value) {
            addCriterion("material_actual_cost_collect >", value, "materialActualCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCollectGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_actual_cost_collect >=", value, "materialActualCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCollectLessThan(BigDecimal value) {
            addCriterion("material_actual_cost_collect <", value, "materialActualCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCollectLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_actual_cost_collect <=", value, "materialActualCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCollectIn(List<BigDecimal> values) {
            addCriterion("material_actual_cost_collect in", values, "materialActualCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCollectNotIn(List<BigDecimal> values) {
            addCriterion("material_actual_cost_collect not in", values, "materialActualCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCollectBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_actual_cost_collect between", value1, value2, "materialActualCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCollectNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_actual_cost_collect not between", value1, value2, "materialActualCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCarryoverIsNull() {
            addCriterion("material_actual_cost_carryover is null");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCarryoverIsNotNull() {
            addCriterion("material_actual_cost_carryover is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCarryoverEqualTo(BigDecimal value) {
            addCriterion("material_actual_cost_carryover =", value, "materialActualCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCarryoverNotEqualTo(BigDecimal value) {
            addCriterion("material_actual_cost_carryover <>", value, "materialActualCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCarryoverGreaterThan(BigDecimal value) {
            addCriterion("material_actual_cost_carryover >", value, "materialActualCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCarryoverGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_actual_cost_carryover >=", value, "materialActualCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCarryoverLessThan(BigDecimal value) {
            addCriterion("material_actual_cost_carryover <", value, "materialActualCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCarryoverLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_actual_cost_carryover <=", value, "materialActualCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCarryoverIn(List<BigDecimal> values) {
            addCriterion("material_actual_cost_carryover in", values, "materialActualCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCarryoverNotIn(List<BigDecimal> values) {
            addCriterion("material_actual_cost_carryover not in", values, "materialActualCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCarryoverBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_actual_cost_carryover between", value1, value2, "materialActualCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostCarryoverNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_actual_cost_carryover not between", value1, value2, "materialActualCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostNoCarryoverIsNull() {
            addCriterion("material_actual_cost_no_carryover is null");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostNoCarryoverIsNotNull() {
            addCriterion("material_actual_cost_no_carryover is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostNoCarryoverEqualTo(BigDecimal value) {
            addCriterion("material_actual_cost_no_carryover =", value, "materialActualCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostNoCarryoverNotEqualTo(BigDecimal value) {
            addCriterion("material_actual_cost_no_carryover <>", value, "materialActualCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostNoCarryoverGreaterThan(BigDecimal value) {
            addCriterion("material_actual_cost_no_carryover >", value, "materialActualCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostNoCarryoverGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_actual_cost_no_carryover >=", value, "materialActualCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostNoCarryoverLessThan(BigDecimal value) {
            addCriterion("material_actual_cost_no_carryover <", value, "materialActualCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostNoCarryoverLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_actual_cost_no_carryover <=", value, "materialActualCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostNoCarryoverIn(List<BigDecimal> values) {
            addCriterion("material_actual_cost_no_carryover in", values, "materialActualCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostNoCarryoverNotIn(List<BigDecimal> values) {
            addCriterion("material_actual_cost_no_carryover not in", values, "materialActualCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostNoCarryoverBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_actual_cost_no_carryover between", value1, value2, "materialActualCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostNoCarryoverNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_actual_cost_no_carryover not between", value1, value2, "materialActualCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCollectIsNull() {
            addCriterion("material_outsource_cost_collect is null");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCollectIsNotNull() {
            addCriterion("material_outsource_cost_collect is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCollectEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost_collect =", value, "materialOutsourceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCollectNotEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost_collect <>", value, "materialOutsourceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCollectGreaterThan(BigDecimal value) {
            addCriterion("material_outsource_cost_collect >", value, "materialOutsourceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCollectGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost_collect >=", value, "materialOutsourceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCollectLessThan(BigDecimal value) {
            addCriterion("material_outsource_cost_collect <", value, "materialOutsourceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCollectLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost_collect <=", value, "materialOutsourceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCollectIn(List<BigDecimal> values) {
            addCriterion("material_outsource_cost_collect in", values, "materialOutsourceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCollectNotIn(List<BigDecimal> values) {
            addCriterion("material_outsource_cost_collect not in", values, "materialOutsourceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCollectBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_outsource_cost_collect between", value1, value2, "materialOutsourceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCollectNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_outsource_cost_collect not between", value1, value2, "materialOutsourceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCarryoverIsNull() {
            addCriterion("material_outsource_cost_carryover is null");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCarryoverIsNotNull() {
            addCriterion("material_outsource_cost_carryover is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCarryoverEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost_carryover =", value, "materialOutsourceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCarryoverNotEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost_carryover <>", value, "materialOutsourceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCarryoverGreaterThan(BigDecimal value) {
            addCriterion("material_outsource_cost_carryover >", value, "materialOutsourceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCarryoverGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost_carryover >=", value, "materialOutsourceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCarryoverLessThan(BigDecimal value) {
            addCriterion("material_outsource_cost_carryover <", value, "materialOutsourceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCarryoverLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost_carryover <=", value, "materialOutsourceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCarryoverIn(List<BigDecimal> values) {
            addCriterion("material_outsource_cost_carryover in", values, "materialOutsourceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCarryoverNotIn(List<BigDecimal> values) {
            addCriterion("material_outsource_cost_carryover not in", values, "materialOutsourceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCarryoverBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_outsource_cost_carryover between", value1, value2, "materialOutsourceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostCarryoverNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_outsource_cost_carryover not between", value1, value2, "materialOutsourceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNoCarryoverIsNull() {
            addCriterion("material_outsource_cost_no_carryover is null");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNoCarryoverIsNotNull() {
            addCriterion("material_outsource_cost_no_carryover is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNoCarryoverEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost_no_carryover =", value, "materialOutsourceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNoCarryoverNotEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost_no_carryover <>", value, "materialOutsourceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNoCarryoverGreaterThan(BigDecimal value) {
            addCriterion("material_outsource_cost_no_carryover >", value, "materialOutsourceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNoCarryoverGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost_no_carryover >=", value, "materialOutsourceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNoCarryoverLessThan(BigDecimal value) {
            addCriterion("material_outsource_cost_no_carryover <", value, "materialOutsourceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNoCarryoverLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost_no_carryover <=", value, "materialOutsourceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNoCarryoverIn(List<BigDecimal> values) {
            addCriterion("material_outsource_cost_no_carryover in", values, "materialOutsourceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNoCarryoverNotIn(List<BigDecimal> values) {
            addCriterion("material_outsource_cost_no_carryover not in", values, "materialOutsourceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNoCarryoverBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_outsource_cost_no_carryover between", value1, value2, "materialOutsourceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNoCarryoverNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_outsource_cost_no_carryover not between", value1, value2, "materialOutsourceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCollectIsNull() {
            addCriterion("material_difference_cost_collect is null");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCollectIsNotNull() {
            addCriterion("material_difference_cost_collect is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCollectEqualTo(BigDecimal value) {
            addCriterion("material_difference_cost_collect =", value, "materialDifferenceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCollectNotEqualTo(BigDecimal value) {
            addCriterion("material_difference_cost_collect <>", value, "materialDifferenceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCollectGreaterThan(BigDecimal value) {
            addCriterion("material_difference_cost_collect >", value, "materialDifferenceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCollectGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_difference_cost_collect >=", value, "materialDifferenceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCollectLessThan(BigDecimal value) {
            addCriterion("material_difference_cost_collect <", value, "materialDifferenceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCollectLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_difference_cost_collect <=", value, "materialDifferenceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCollectIn(List<BigDecimal> values) {
            addCriterion("material_difference_cost_collect in", values, "materialDifferenceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCollectNotIn(List<BigDecimal> values) {
            addCriterion("material_difference_cost_collect not in", values, "materialDifferenceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCollectBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_difference_cost_collect between", value1, value2, "materialDifferenceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCollectNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_difference_cost_collect not between", value1, value2, "materialDifferenceCostCollect");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCarryoverIsNull() {
            addCriterion("material_difference_cost_carryover is null");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCarryoverIsNotNull() {
            addCriterion("material_difference_cost_carryover is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCarryoverEqualTo(BigDecimal value) {
            addCriterion("material_difference_cost_carryover =", value, "materialDifferenceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCarryoverNotEqualTo(BigDecimal value) {
            addCriterion("material_difference_cost_carryover <>", value, "materialDifferenceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCarryoverGreaterThan(BigDecimal value) {
            addCriterion("material_difference_cost_carryover >", value, "materialDifferenceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCarryoverGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_difference_cost_carryover >=", value, "materialDifferenceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCarryoverLessThan(BigDecimal value) {
            addCriterion("material_difference_cost_carryover <", value, "materialDifferenceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCarryoverLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_difference_cost_carryover <=", value, "materialDifferenceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCarryoverIn(List<BigDecimal> values) {
            addCriterion("material_difference_cost_carryover in", values, "materialDifferenceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCarryoverNotIn(List<BigDecimal> values) {
            addCriterion("material_difference_cost_carryover not in", values, "materialDifferenceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCarryoverBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_difference_cost_carryover between", value1, value2, "materialDifferenceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostCarryoverNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_difference_cost_carryover not between", value1, value2, "materialDifferenceCostCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostNoCarryoverIsNull() {
            addCriterion("material_difference_cost_no_carryover is null");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostNoCarryoverIsNotNull() {
            addCriterion("material_difference_cost_no_carryover is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostNoCarryoverEqualTo(BigDecimal value) {
            addCriterion("material_difference_cost_no_carryover =", value, "materialDifferenceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostNoCarryoverNotEqualTo(BigDecimal value) {
            addCriterion("material_difference_cost_no_carryover <>", value, "materialDifferenceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostNoCarryoverGreaterThan(BigDecimal value) {
            addCriterion("material_difference_cost_no_carryover >", value, "materialDifferenceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostNoCarryoverGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_difference_cost_no_carryover >=", value, "materialDifferenceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostNoCarryoverLessThan(BigDecimal value) {
            addCriterion("material_difference_cost_no_carryover <", value, "materialDifferenceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostNoCarryoverLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_difference_cost_no_carryover <=", value, "materialDifferenceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostNoCarryoverIn(List<BigDecimal> values) {
            addCriterion("material_difference_cost_no_carryover in", values, "materialDifferenceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostNoCarryoverNotIn(List<BigDecimal> values) {
            addCriterion("material_difference_cost_no_carryover not in", values, "materialDifferenceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostNoCarryoverBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_difference_cost_no_carryover between", value1, value2, "materialDifferenceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostNoCarryoverNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_difference_cost_no_carryover not between", value1, value2, "materialDifferenceCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCollectIsNull() {
            addCriterion("inner_labor_cost_collect is null");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCollectIsNotNull() {
            addCriterion("inner_labor_cost_collect is not null");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCollectEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost_collect =", value, "innerLaborCostCollect");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCollectNotEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost_collect <>", value, "innerLaborCostCollect");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCollectGreaterThan(BigDecimal value) {
            addCriterion("inner_labor_cost_collect >", value, "innerLaborCostCollect");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCollectGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost_collect >=", value, "innerLaborCostCollect");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCollectLessThan(BigDecimal value) {
            addCriterion("inner_labor_cost_collect <", value, "innerLaborCostCollect");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCollectLessThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost_collect <=", value, "innerLaborCostCollect");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCollectIn(List<BigDecimal> values) {
            addCriterion("inner_labor_cost_collect in", values, "innerLaborCostCollect");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCollectNotIn(List<BigDecimal> values) {
            addCriterion("inner_labor_cost_collect not in", values, "innerLaborCostCollect");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCollectBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_labor_cost_collect between", value1, value2, "innerLaborCostCollect");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCollectNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_labor_cost_collect not between", value1, value2, "innerLaborCostCollect");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCarryoverIsNull() {
            addCriterion("inner_labor_cost_carryover is null");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCarryoverIsNotNull() {
            addCriterion("inner_labor_cost_carryover is not null");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCarryoverEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost_carryover =", value, "innerLaborCostCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCarryoverNotEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost_carryover <>", value, "innerLaborCostCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCarryoverGreaterThan(BigDecimal value) {
            addCriterion("inner_labor_cost_carryover >", value, "innerLaborCostCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCarryoverGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost_carryover >=", value, "innerLaborCostCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCarryoverLessThan(BigDecimal value) {
            addCriterion("inner_labor_cost_carryover <", value, "innerLaborCostCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCarryoverLessThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost_carryover <=", value, "innerLaborCostCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCarryoverIn(List<BigDecimal> values) {
            addCriterion("inner_labor_cost_carryover in", values, "innerLaborCostCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCarryoverNotIn(List<BigDecimal> values) {
            addCriterion("inner_labor_cost_carryover not in", values, "innerLaborCostCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCarryoverBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_labor_cost_carryover between", value1, value2, "innerLaborCostCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostCarryoverNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_labor_cost_carryover not between", value1, value2, "innerLaborCostCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoAccountIsNull() {
            addCriterion("inner_labor_cost_no_account is null");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoAccountIsNotNull() {
            addCriterion("inner_labor_cost_no_account is not null");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoAccountEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost_no_account =", value, "innerLaborCostNoAccount");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoAccountNotEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost_no_account <>", value, "innerLaborCostNoAccount");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoAccountGreaterThan(BigDecimal value) {
            addCriterion("inner_labor_cost_no_account >", value, "innerLaborCostNoAccount");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoAccountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost_no_account >=", value, "innerLaborCostNoAccount");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoAccountLessThan(BigDecimal value) {
            addCriterion("inner_labor_cost_no_account <", value, "innerLaborCostNoAccount");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoAccountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost_no_account <=", value, "innerLaborCostNoAccount");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoAccountIn(List<BigDecimal> values) {
            addCriterion("inner_labor_cost_no_account in", values, "innerLaborCostNoAccount");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoAccountNotIn(List<BigDecimal> values) {
            addCriterion("inner_labor_cost_no_account not in", values, "innerLaborCostNoAccount");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoAccountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_labor_cost_no_account between", value1, value2, "innerLaborCostNoAccount");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoAccountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_labor_cost_no_account not between", value1, value2, "innerLaborCostNoAccount");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoCarryoverIsNull() {
            addCriterion("inner_labor_cost_no_carryover is null");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoCarryoverIsNotNull() {
            addCriterion("inner_labor_cost_no_carryover is not null");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoCarryoverEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost_no_carryover =", value, "innerLaborCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoCarryoverNotEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost_no_carryover <>", value, "innerLaborCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoCarryoverGreaterThan(BigDecimal value) {
            addCriterion("inner_labor_cost_no_carryover >", value, "innerLaborCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoCarryoverGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost_no_carryover >=", value, "innerLaborCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoCarryoverLessThan(BigDecimal value) {
            addCriterion("inner_labor_cost_no_carryover <", value, "innerLaborCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoCarryoverLessThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost_no_carryover <=", value, "innerLaborCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoCarryoverIn(List<BigDecimal> values) {
            addCriterion("inner_labor_cost_no_carryover in", values, "innerLaborCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoCarryoverNotIn(List<BigDecimal> values) {
            addCriterion("inner_labor_cost_no_carryover not in", values, "innerLaborCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoCarryoverBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_labor_cost_no_carryover between", value1, value2, "innerLaborCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNoCarryoverNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_labor_cost_no_carryover not between", value1, value2, "innerLaborCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostCollectIsNull() {
            addCriterion("fee_cost_collect is null");
            return (Criteria) this;
        }

        public Criteria andFeeCostCollectIsNotNull() {
            addCriterion("fee_cost_collect is not null");
            return (Criteria) this;
        }

        public Criteria andFeeCostCollectEqualTo(BigDecimal value) {
            addCriterion("fee_cost_collect =", value, "feeCostCollect");
            return (Criteria) this;
        }

        public Criteria andFeeCostCollectNotEqualTo(BigDecimal value) {
            addCriterion("fee_cost_collect <>", value, "feeCostCollect");
            return (Criteria) this;
        }

        public Criteria andFeeCostCollectGreaterThan(BigDecimal value) {
            addCriterion("fee_cost_collect >", value, "feeCostCollect");
            return (Criteria) this;
        }

        public Criteria andFeeCostCollectGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("fee_cost_collect >=", value, "feeCostCollect");
            return (Criteria) this;
        }

        public Criteria andFeeCostCollectLessThan(BigDecimal value) {
            addCriterion("fee_cost_collect <", value, "feeCostCollect");
            return (Criteria) this;
        }

        public Criteria andFeeCostCollectLessThanOrEqualTo(BigDecimal value) {
            addCriterion("fee_cost_collect <=", value, "feeCostCollect");
            return (Criteria) this;
        }

        public Criteria andFeeCostCollectIn(List<BigDecimal> values) {
            addCriterion("fee_cost_collect in", values, "feeCostCollect");
            return (Criteria) this;
        }

        public Criteria andFeeCostCollectNotIn(List<BigDecimal> values) {
            addCriterion("fee_cost_collect not in", values, "feeCostCollect");
            return (Criteria) this;
        }

        public Criteria andFeeCostCollectBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee_cost_collect between", value1, value2, "feeCostCollect");
            return (Criteria) this;
        }

        public Criteria andFeeCostCollectNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee_cost_collect not between", value1, value2, "feeCostCollect");
            return (Criteria) this;
        }

        public Criteria andFeeCostCarryoverIsNull() {
            addCriterion("fee_cost_carryover is null");
            return (Criteria) this;
        }

        public Criteria andFeeCostCarryoverIsNotNull() {
            addCriterion("fee_cost_carryover is not null");
            return (Criteria) this;
        }

        public Criteria andFeeCostCarryoverEqualTo(BigDecimal value) {
            addCriterion("fee_cost_carryover =", value, "feeCostCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostCarryoverNotEqualTo(BigDecimal value) {
            addCriterion("fee_cost_carryover <>", value, "feeCostCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostCarryoverGreaterThan(BigDecimal value) {
            addCriterion("fee_cost_carryover >", value, "feeCostCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostCarryoverGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("fee_cost_carryover >=", value, "feeCostCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostCarryoverLessThan(BigDecimal value) {
            addCriterion("fee_cost_carryover <", value, "feeCostCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostCarryoverLessThanOrEqualTo(BigDecimal value) {
            addCriterion("fee_cost_carryover <=", value, "feeCostCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostCarryoverIn(List<BigDecimal> values) {
            addCriterion("fee_cost_carryover in", values, "feeCostCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostCarryoverNotIn(List<BigDecimal> values) {
            addCriterion("fee_cost_carryover not in", values, "feeCostCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostCarryoverBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee_cost_carryover between", value1, value2, "feeCostCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostCarryoverNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee_cost_carryover not between", value1, value2, "feeCostCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostNoCarryoverIsNull() {
            addCriterion("fee_cost_no_carryover is null");
            return (Criteria) this;
        }

        public Criteria andFeeCostNoCarryoverIsNotNull() {
            addCriterion("fee_cost_no_carryover is not null");
            return (Criteria) this;
        }

        public Criteria andFeeCostNoCarryoverEqualTo(BigDecimal value) {
            addCriterion("fee_cost_no_carryover =", value, "feeCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostNoCarryoverNotEqualTo(BigDecimal value) {
            addCriterion("fee_cost_no_carryover <>", value, "feeCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostNoCarryoverGreaterThan(BigDecimal value) {
            addCriterion("fee_cost_no_carryover >", value, "feeCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostNoCarryoverGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("fee_cost_no_carryover >=", value, "feeCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostNoCarryoverLessThan(BigDecimal value) {
            addCriterion("fee_cost_no_carryover <", value, "feeCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostNoCarryoverLessThanOrEqualTo(BigDecimal value) {
            addCriterion("fee_cost_no_carryover <=", value, "feeCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostNoCarryoverIn(List<BigDecimal> values) {
            addCriterion("fee_cost_no_carryover in", values, "feeCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostNoCarryoverNotIn(List<BigDecimal> values) {
            addCriterion("fee_cost_no_carryover not in", values, "feeCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostNoCarryoverBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee_cost_no_carryover between", value1, value2, "feeCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andFeeCostNoCarryoverNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee_cost_no_carryover not between", value1, value2, "feeCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andTotalCostNoCarryoverIsNull() {
            addCriterion("total_cost_no_carryover is null");
            return (Criteria) this;
        }

        public Criteria andTotalCostNoCarryoverIsNotNull() {
            addCriterion("total_cost_no_carryover is not null");
            return (Criteria) this;
        }

        public Criteria andTotalCostNoCarryoverEqualTo(BigDecimal value) {
            addCriterion("total_cost_no_carryover =", value, "totalCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andTotalCostNoCarryoverNotEqualTo(BigDecimal value) {
            addCriterion("total_cost_no_carryover <>", value, "totalCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andTotalCostNoCarryoverGreaterThan(BigDecimal value) {
            addCriterion("total_cost_no_carryover >", value, "totalCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andTotalCostNoCarryoverGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_cost_no_carryover >=", value, "totalCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andTotalCostNoCarryoverLessThan(BigDecimal value) {
            addCriterion("total_cost_no_carryover <", value, "totalCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andTotalCostNoCarryoverLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_cost_no_carryover <=", value, "totalCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andTotalCostNoCarryoverIn(List<BigDecimal> values) {
            addCriterion("total_cost_no_carryover in", values, "totalCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andTotalCostNoCarryoverNotIn(List<BigDecimal> values) {
            addCriterion("total_cost_no_carryover not in", values, "totalCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andTotalCostNoCarryoverBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_cost_no_carryover between", value1, value2, "totalCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andTotalCostNoCarryoverNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_cost_no_carryover not between", value1, value2, "totalCostNoCarryover");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}