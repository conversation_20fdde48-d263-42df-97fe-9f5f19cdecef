package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProjectCostFeeDetailRecord extends LongIdEntity implements Serializable {
    private Long id;

    private Long executeId;

    private Long summaryId;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private String orderCode;

    private String orderName;

    private String applyName;

    private Date applyDate;

    private String feeTypeName;

    private String feeItemName;

    private BigDecimal feeAmount;

    private String currencyName;

    private String vendorName;

    private String orderDesc;

    private Date glDate;

    private String importErpStatus;

    private Integer type;

    private Boolean deletedFlag;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private String apportionType;

    private String isCrossYear;

    private Long feeItemId;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getSummaryId() {
        return summaryId;
    }

    public void setSummaryId(Long summaryId) {
        this.summaryId = summaryId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode == null ? null : orderCode.trim();
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName == null ? null : orderName.trim();
    }

    public String getApplyName() {
        return applyName;
    }

    public void setApplyName(String applyName) {
        this.applyName = applyName == null ? null : applyName.trim();
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public String getFeeTypeName() {
        return feeTypeName;
    }

    public void setFeeTypeName(String feeTypeName) {
        this.feeTypeName = feeTypeName == null ? null : feeTypeName.trim();
    }

    public String getFeeItemName() {
        return feeItemName;
    }

    public void setFeeItemName(String feeItemName) {
        this.feeItemName = feeItemName == null ? null : feeItemName.trim();
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public String getCurrencyName() {
        return currencyName;
    }

    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName == null ? null : currencyName.trim();
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName == null ? null : vendorName.trim();
    }

    public String getOrderDesc() {
        return orderDesc;
    }

    public void setOrderDesc(String orderDesc) {
        this.orderDesc = orderDesc == null ? null : orderDesc.trim();
    }

    public Date getGlDate() {
        return glDate;
    }

    public void setGlDate(Date glDate) {
        this.glDate = glDate;
    }

    public String getImportErpStatus() {
        return importErpStatus;
    }

    public void setImportErpStatus(String importErpStatus) {
        this.importErpStatus = importErpStatus == null ? null : importErpStatus.trim();
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    public String getApportionType() {
        return apportionType;
    }

    public void setApportionType(String apportionType) {
        this.apportionType = apportionType == null ? null : apportionType.trim();
    }

    public String getIsCrossYear() {
        return isCrossYear;
    }

    public void setIsCrossYear(String isCrossYear) {
        this.isCrossYear = isCrossYear == null ? null : isCrossYear.trim();
    }

    public Long getFeeItemId() {
        return feeItemId;
    }

    public void setFeeItemId(Long feeItemId) {
        this.feeItemId = feeItemId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", executeId=").append(executeId);
        sb.append(", summaryId=").append(summaryId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", orderCode=").append(orderCode);
        sb.append(", orderName=").append(orderName);
        sb.append(", applyName=").append(applyName);
        sb.append(", applyDate=").append(applyDate);
        sb.append(", feeTypeName=").append(feeTypeName);
        sb.append(", feeItemName=").append(feeItemName);
        sb.append(", feeAmount=").append(feeAmount);
        sb.append(", currencyName=").append(currencyName);
        sb.append(", vendorName=").append(vendorName);
        sb.append(", orderDesc=").append(orderDesc);
        sb.append(", glDate=").append(glDate);
        sb.append(", importErpStatus=").append(importErpStatus);
        sb.append(", type=").append(type);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", apportionType=").append(apportionType);
        sb.append(", isCrossYear=").append(isCrossYear);
        sb.append(", feeItemId=").append(feeItemId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}