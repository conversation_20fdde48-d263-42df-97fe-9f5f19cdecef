package com.midea.pam.common.statistics.excelVo;

import com.midea.pam.common.util.DateUtils;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 **/
public class ReportProjectProfitQuoteCostExcelVO {
    // 报价成本明细
    @Excel(name = "物料预算",width = 20, replace = {"-_null"})
    private BigDecimal quoteCostHardwareWithoutTax;

    @Excel(name = "人力预算",width = 20, replace = {"-_null"})
    private BigDecimal quoteCostLabourWithoutTax;

    @Excel(name = "差旅费预算",width = 20, replace = {"-_null"})
    private BigDecimal quoteCostTravelWithoutTax;

    @Excel(name = "非差旅费预算",width = 20, replace = {"-_null"})
    private BigDecimal quoteCostOtherWithoutTax;

    public BigDecimal getQuoteCostHardwareWithoutTax() {
        return quoteCostHardwareWithoutTax;
    }

    public void setQuoteCostHardwareWithoutTax(BigDecimal quoteCostHardwareWithoutTax) {
        this.quoteCostHardwareWithoutTax = quoteCostHardwareWithoutTax;
    }

    public BigDecimal getQuoteCostLabourWithoutTax() {
        return quoteCostLabourWithoutTax;
    }

    public void setQuoteCostLabourWithoutTax(BigDecimal quoteCostLabourWithoutTax) {
        this.quoteCostLabourWithoutTax = quoteCostLabourWithoutTax;
    }

    public BigDecimal getQuoteCostTravelWithoutTax() {
        return quoteCostTravelWithoutTax;
    }

    public void setQuoteCostTravelWithoutTax(BigDecimal quoteCostTravelWithoutTax) {
        this.quoteCostTravelWithoutTax = quoteCostTravelWithoutTax;
    }

    public BigDecimal getQuoteCostOtherWithoutTax() {
        return quoteCostOtherWithoutTax;
    }

    public void setQuoteCostOtherWithoutTax(BigDecimal quoteCostOtherWithoutTax) {
        this.quoteCostOtherWithoutTax = quoteCostOtherWithoutTax;
    }
}
