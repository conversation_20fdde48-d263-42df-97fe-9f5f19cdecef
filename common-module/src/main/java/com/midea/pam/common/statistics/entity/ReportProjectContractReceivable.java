package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(value = "合同资产账龄报表")
public class ReportProjectContractReceivable extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目类型")
    private String projectTypeName;

    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    @ApiModelProperty(value = "关联合同id")
    private String contractIds;

    @ApiModelProperty(value = "CRM客户编码")
    private String customerCode;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "业务分部")
    private String businessSegment;

    @ApiModelProperty(value = "OU名称")
    private String ouName;

    @ApiModelProperty(value = "行业")
    private String industry;

    @ApiModelProperty(value = "签约中心")
    private String signingCenter;

    @ApiModelProperty(value = "项目累计收入-合同币种")
    private BigDecimal cumulativeRevenue;

    @ApiModelProperty(value = "项目累计开票-合同币种(含税)")
    private BigDecimal cumulativeTaxInvoicing;

    @ApiModelProperty(value = "项目累计开票-合同币种(不含税)")
    private BigDecimal cumulativeInvoicing;

    @ApiModelProperty(value = "项目累计回款-合同币种(不含税)")
    private BigDecimal cumulativeRepayment;

    @ApiModelProperty(value = "累计退款金额-合同币种(不含税)")
    private BigDecimal cumulativeRefund;

    @ApiModelProperty(value = "合同负债-合同币种")
    private BigDecimal contractLiabilities;

    @ApiModelProperty(value = "项目应收账款-合同币种(含税)")
    private BigDecimal projectAccountsTaxReceivable;

    @ApiModelProperty(value = "项目应收账款-合同币种(不含税)")
    private BigDecimal projectAccountsReceivable;

    @ApiModelProperty(value = "合同资产-合同币种")
    private BigDecimal balance;

    @ApiModelProperty(value = "规则值拼接")
    private String ruleValue;

    @ApiModelProperty(value = "规则范围拼接")
    private String ruleScope;

    @ApiModelProperty(value = "规则名称拼接")
    private String ruleName;

    @ApiModelProperty(value = "删除标志;0否/1是")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "项目合同币种")
    private String currency;

    @ApiModelProperty(value = "项目累计收入-本位币")
    private BigDecimal standardCumulativeRevenue;

    @ApiModelProperty(value = "项目累计开票-本位币(含税)")
    private BigDecimal standardCumulativeTaxInvoicing;

    @ApiModelProperty(value = "项目累计开票-本位币(不含税)")
    private BigDecimal standardCumulativeInvoicing;

    @ApiModelProperty(value = "项目累计回款-本位币(不含税)")
    private BigDecimal standardCumulativeRepayment;

    @ApiModelProperty(value = "累计退款金额-本位币(不含税)")
    private BigDecimal standardCumulativeRefund;

    @ApiModelProperty(value = "合同资产(本位币)")
    private BigDecimal standardBalance;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectTypeName() {
        return projectTypeName;
    }

    public void setProjectTypeName(String projectTypeName) {
        this.projectTypeName = projectTypeName == null ? null : projectTypeName.trim();
    }

    public String getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(String projectStatus) {
        this.projectStatus = projectStatus == null ? null : projectStatus.trim();
    }

    public String getContractIds() {
        return contractIds;
    }

    public void setContractIds(String contractIds) {
        this.contractIds = contractIds == null ? null : contractIds.trim();
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode == null ? null : customerCode.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public String getBusinessSegment() {
        return businessSegment;
    }

    public void setBusinessSegment(String businessSegment) {
        this.businessSegment = businessSegment == null ? null : businessSegment.trim();
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry == null ? null : industry.trim();
    }

    public String getSigningCenter() {
        return signingCenter;
    }

    public void setSigningCenter(String signingCenter) {
        this.signingCenter = signingCenter == null ? null : signingCenter.trim();
    }

    public BigDecimal getCumulativeRevenue() {
        return cumulativeRevenue;
    }

    public void setCumulativeRevenue(BigDecimal cumulativeRevenue) {
        this.cumulativeRevenue = cumulativeRevenue;
    }

    public BigDecimal getCumulativeTaxInvoicing() {
        return cumulativeTaxInvoicing;
    }

    public void setCumulativeTaxInvoicing(BigDecimal cumulativeTaxInvoicing) {
        this.cumulativeTaxInvoicing = cumulativeTaxInvoicing;
    }

    public BigDecimal getCumulativeInvoicing() {
        return cumulativeInvoicing;
    }

    public void setCumulativeInvoicing(BigDecimal cumulativeInvoicing) {
        this.cumulativeInvoicing = cumulativeInvoicing;
    }

    public BigDecimal getCumulativeRepayment() {
        return cumulativeRepayment;
    }

    public void setCumulativeRepayment(BigDecimal cumulativeRepayment) {
        this.cumulativeRepayment = cumulativeRepayment;
    }

    public BigDecimal getCumulativeRefund() {
        return cumulativeRefund;
    }

    public void setCumulativeRefund(BigDecimal cumulativeRefund) {
        this.cumulativeRefund = cumulativeRefund;
    }

    public BigDecimal getContractLiabilities() {
        return contractLiabilities;
    }

    public void setContractLiabilities(BigDecimal contractLiabilities) {
        this.contractLiabilities = contractLiabilities;
    }

    public BigDecimal getProjectAccountsTaxReceivable() {
        return projectAccountsTaxReceivable;
    }

    public void setProjectAccountsTaxReceivable(BigDecimal projectAccountsTaxReceivable) {
        this.projectAccountsTaxReceivable = projectAccountsTaxReceivable;
    }

    public BigDecimal getProjectAccountsReceivable() {
        return projectAccountsReceivable;
    }

    public void setProjectAccountsReceivable(BigDecimal projectAccountsReceivable) {
        this.projectAccountsReceivable = projectAccountsReceivable;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public String getRuleValue() {
        return ruleValue;
    }

    public void setRuleValue(String ruleValue) {
        this.ruleValue = ruleValue == null ? null : ruleValue.trim();
    }

    public String getRuleScope() {
        return ruleScope;
    }

    public void setRuleScope(String ruleScope) {
        this.ruleScope = ruleScope == null ? null : ruleScope.trim();
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName == null ? null : ruleName.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public BigDecimal getStandardCumulativeRevenue() {
        return standardCumulativeRevenue;
    }

    public void setStandardCumulativeRevenue(BigDecimal standardCumulativeRevenue) {
        this.standardCumulativeRevenue = standardCumulativeRevenue;
    }

    public BigDecimal getStandardCumulativeTaxInvoicing() {
        return standardCumulativeTaxInvoicing;
    }

    public void setStandardCumulativeTaxInvoicing(BigDecimal standardCumulativeTaxInvoicing) {
        this.standardCumulativeTaxInvoicing = standardCumulativeTaxInvoicing;
    }

    public BigDecimal getStandardCumulativeInvoicing() {
        return standardCumulativeInvoicing;
    }

    public void setStandardCumulativeInvoicing(BigDecimal standardCumulativeInvoicing) {
        this.standardCumulativeInvoicing = standardCumulativeInvoicing;
    }

    public BigDecimal getStandardCumulativeRepayment() {
        return standardCumulativeRepayment;
    }

    public void setStandardCumulativeRepayment(BigDecimal standardCumulativeRepayment) {
        this.standardCumulativeRepayment = standardCumulativeRepayment;
    }

    public BigDecimal getStandardCumulativeRefund() {
        return standardCumulativeRefund;
    }

    public void setStandardCumulativeRefund(BigDecimal standardCumulativeRefund) {
        this.standardCumulativeRefund = standardCumulativeRefund;
    }

    public BigDecimal getStandardBalance() {
        return standardBalance;
    }

    public void setStandardBalance(BigDecimal standardBalance) {
        this.standardBalance = standardBalance;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectTypeName=").append(projectTypeName);
        sb.append(", projectStatus=").append(projectStatus);
        sb.append(", contractIds=").append(contractIds);
        sb.append(", customerCode=").append(customerCode);
        sb.append(", customerName=").append(customerName);
        sb.append(", businessSegment=").append(businessSegment);
        sb.append(", ouName=").append(ouName);
        sb.append(", industry=").append(industry);
        sb.append(", signingCenter=").append(signingCenter);
        sb.append(", cumulativeRevenue=").append(cumulativeRevenue);
        sb.append(", cumulativeTaxInvoicing=").append(cumulativeTaxInvoicing);
        sb.append(", cumulativeInvoicing=").append(cumulativeInvoicing);
        sb.append(", cumulativeRepayment=").append(cumulativeRepayment);
        sb.append(", cumulativeRefund=").append(cumulativeRefund);
        sb.append(", contractLiabilities=").append(contractLiabilities);
        sb.append(", projectAccountsTaxReceivable=").append(projectAccountsTaxReceivable);
        sb.append(", projectAccountsReceivable=").append(projectAccountsReceivable);
        sb.append(", balance=").append(balance);
        sb.append(", ruleValue=").append(ruleValue);
        sb.append(", ruleScope=").append(ruleScope);
        sb.append(", ruleName=").append(ruleName);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", currency=").append(currency);
        sb.append(", standardCumulativeRevenue=").append(standardCumulativeRevenue);
        sb.append(", standardCumulativeTaxInvoicing=").append(standardCumulativeTaxInvoicing);
        sb.append(", standardCumulativeInvoicing=").append(standardCumulativeInvoicing);
        sb.append(", standardCumulativeRepayment=").append(standardCumulativeRepayment);
        sb.append(", standardCumulativeRefund=").append(standardCumulativeRefund);
        sb.append(", standardBalance=").append(standardBalance);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}