package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProjectCostSummaryItemRecord extends LongIdEntity implements Serializable {
    private Long id;

    private Long executeId;

    private Long projectId;

    private String itemName;

    private Byte itemType;

    private BigDecimal winBudget;

    private BigDecimal currentBudget;

    private BigDecimal incurredCost;

    private BigDecimal pendingCost;

    private BigDecimal totalCost;

    private BigDecimal remainderBudget;

    private BigDecimal targetCostDeviationRate;

    private BigDecimal targetCostDeviation;

    private BigDecimal remainTargetCost;

    private BigDecimal outerAdjustTargetBudget;

    private BigDecimal innerAdjustTargetBudget;

    private BigDecimal currentTargetCost;

    private BigDecimal outerAdjustTargetCost;

    private BigDecimal innerAdjustTargetCost;

    private BigDecimal initTargetCost;

    private BigDecimal incurredRatio;

    private Boolean deletedFlag;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }

    public Byte getItemType() {
        return itemType;
    }

    public void setItemType(Byte itemType) {
        this.itemType = itemType;
    }

    public BigDecimal getWinBudget() {
        return winBudget;
    }

    public void setWinBudget(BigDecimal winBudget) {
        this.winBudget = winBudget;
    }

    public BigDecimal getCurrentBudget() {
        return currentBudget;
    }

    public void setCurrentBudget(BigDecimal currentBudget) {
        this.currentBudget = currentBudget;
    }

    public BigDecimal getIncurredCost() {
        return incurredCost;
    }

    public void setIncurredCost(BigDecimal incurredCost) {
        this.incurredCost = incurredCost;
    }

    public BigDecimal getPendingCost() {
        return pendingCost;
    }

    public void setPendingCost(BigDecimal pendingCost) {
        this.pendingCost = pendingCost;
    }

    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    public BigDecimal getRemainderBudget() {
        return remainderBudget;
    }

    public void setRemainderBudget(BigDecimal remainderBudget) {
        this.remainderBudget = remainderBudget;
    }

    public BigDecimal getTargetCostDeviationRate() {
        return targetCostDeviationRate;
    }

    public void setTargetCostDeviationRate(BigDecimal targetCostDeviationRate) {
        this.targetCostDeviationRate = targetCostDeviationRate;
    }

    public BigDecimal getTargetCostDeviation() {
        return targetCostDeviation;
    }

    public void setTargetCostDeviation(BigDecimal targetCostDeviation) {
        this.targetCostDeviation = targetCostDeviation;
    }

    public BigDecimal getRemainTargetCost() {
        return remainTargetCost;
    }

    public void setRemainTargetCost(BigDecimal remainTargetCost) {
        this.remainTargetCost = remainTargetCost;
    }

    public BigDecimal getOuterAdjustTargetBudget() {
        return outerAdjustTargetBudget;
    }

    public void setOuterAdjustTargetBudget(BigDecimal outerAdjustTargetBudget) {
        this.outerAdjustTargetBudget = outerAdjustTargetBudget;
    }

    public BigDecimal getInnerAdjustTargetBudget() {
        return innerAdjustTargetBudget;
    }

    public void setInnerAdjustTargetBudget(BigDecimal innerAdjustTargetBudget) {
        this.innerAdjustTargetBudget = innerAdjustTargetBudget;
    }

    public BigDecimal getCurrentTargetCost() {
        return currentTargetCost;
    }

    public void setCurrentTargetCost(BigDecimal currentTargetCost) {
        this.currentTargetCost = currentTargetCost;
    }

    public BigDecimal getOuterAdjustTargetCost() {
        return outerAdjustTargetCost;
    }

    public void setOuterAdjustTargetCost(BigDecimal outerAdjustTargetCost) {
        this.outerAdjustTargetCost = outerAdjustTargetCost;
    }

    public BigDecimal getInnerAdjustTargetCost() {
        return innerAdjustTargetCost;
    }

    public void setInnerAdjustTargetCost(BigDecimal innerAdjustTargetCost) {
        this.innerAdjustTargetCost = innerAdjustTargetCost;
    }

    public BigDecimal getInitTargetCost() {
        return initTargetCost;
    }

    public void setInitTargetCost(BigDecimal initTargetCost) {
        this.initTargetCost = initTargetCost;
    }

    public BigDecimal getIncurredRatio() {
        return incurredRatio;
    }

    public void setIncurredRatio(BigDecimal incurredRatio) {
        this.incurredRatio = incurredRatio;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", itemName=").append(itemName);
        sb.append(", itemType=").append(itemType);
        sb.append(", winBudget=").append(winBudget);
        sb.append(", currentBudget=").append(currentBudget);
        sb.append(", incurredCost=").append(incurredCost);
        sb.append(", pendingCost=").append(pendingCost);
        sb.append(", totalCost=").append(totalCost);
        sb.append(", remainderBudget=").append(remainderBudget);
        sb.append(", targetCostDeviationRate=").append(targetCostDeviationRate);
        sb.append(", targetCostDeviation=").append(targetCostDeviation);
        sb.append(", remainTargetCost=").append(remainTargetCost);
        sb.append(", outerAdjustTargetBudget=").append(outerAdjustTargetBudget);
        sb.append(", innerAdjustTargetBudget=").append(innerAdjustTargetBudget);
        sb.append(", currentTargetCost=").append(currentTargetCost);
        sb.append(", outerAdjustTargetCost=").append(outerAdjustTargetCost);
        sb.append(", innerAdjustTargetCost=").append(innerAdjustTargetCost);
        sb.append(", initTargetCost=").append(initTargetCost);
        sb.append(", incurredRatio=").append(incurredRatio);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}