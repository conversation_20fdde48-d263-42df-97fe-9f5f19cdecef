package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class MaterialBudgetDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public MaterialBudgetDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andNumIsNull() {
            addCriterion("num is null");
            return (Criteria) this;
        }

        public Criteria andNumIsNotNull() {
            addCriterion("num is not null");
            return (Criteria) this;
        }

        public Criteria andNumEqualTo(String value) {
            addCriterion("num =", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotEqualTo(String value) {
            addCriterion("num <>", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThan(String value) {
            addCriterion("num >", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThanOrEqualTo(String value) {
            addCriterion("num >=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThan(String value) {
            addCriterion("num <", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThanOrEqualTo(String value) {
            addCriterion("num <=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLike(String value) {
            addCriterion("num like", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotLike(String value) {
            addCriterion("num not like", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumIn(List<String> values) {
            addCriterion("num in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotIn(List<String> values) {
            addCriterion("num not in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumBetween(String value1, String value2) {
            addCriterion("num between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotBetween(String value1, String value2) {
            addCriterion("num not between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andBoomLevelIsNull() {
            addCriterion("boom_level is null");
            return (Criteria) this;
        }

        public Criteria andBoomLevelIsNotNull() {
            addCriterion("boom_level is not null");
            return (Criteria) this;
        }

        public Criteria andBoomLevelEqualTo(Integer value) {
            addCriterion("boom_level =", value, "boomLevel");
            return (Criteria) this;
        }

        public Criteria andBoomLevelNotEqualTo(Integer value) {
            addCriterion("boom_level <>", value, "boomLevel");
            return (Criteria) this;
        }

        public Criteria andBoomLevelGreaterThan(Integer value) {
            addCriterion("boom_level >", value, "boomLevel");
            return (Criteria) this;
        }

        public Criteria andBoomLevelGreaterThanOrEqualTo(Integer value) {
            addCriterion("boom_level >=", value, "boomLevel");
            return (Criteria) this;
        }

        public Criteria andBoomLevelLessThan(Integer value) {
            addCriterion("boom_level <", value, "boomLevel");
            return (Criteria) this;
        }

        public Criteria andBoomLevelLessThanOrEqualTo(Integer value) {
            addCriterion("boom_level <=", value, "boomLevel");
            return (Criteria) this;
        }

        public Criteria andBoomLevelIn(List<Integer> values) {
            addCriterion("boom_level in", values, "boomLevel");
            return (Criteria) this;
        }

        public Criteria andBoomLevelNotIn(List<Integer> values) {
            addCriterion("boom_level not in", values, "boomLevel");
            return (Criteria) this;
        }

        public Criteria andBoomLevelBetween(Integer value1, Integer value2) {
            addCriterion("boom_level between", value1, value2, "boomLevel");
            return (Criteria) this;
        }

        public Criteria andBoomLevelNotBetween(Integer value1, Integer value2) {
            addCriterion("boom_level not between", value1, value2, "boomLevel");
            return (Criteria) this;
        }

        public Criteria andExtIsNull() {
            addCriterion("ext is null");
            return (Criteria) this;
        }

        public Criteria andExtIsNotNull() {
            addCriterion("ext is not null");
            return (Criteria) this;
        }

        public Criteria andExtEqualTo(String value) {
            addCriterion("ext =", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtNotEqualTo(String value) {
            addCriterion("ext <>", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtGreaterThan(String value) {
            addCriterion("ext >", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtGreaterThanOrEqualTo(String value) {
            addCriterion("ext >=", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtLessThan(String value) {
            addCriterion("ext <", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtLessThanOrEqualTo(String value) {
            addCriterion("ext <=", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtLike(String value) {
            addCriterion("ext like", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtNotLike(String value) {
            addCriterion("ext not like", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtIn(List<String> values) {
            addCriterion("ext in", values, "ext");
            return (Criteria) this;
        }

        public Criteria andExtNotIn(List<String> values) {
            addCriterion("ext not in", values, "ext");
            return (Criteria) this;
        }

        public Criteria andExtBetween(String value1, String value2) {
            addCriterion("ext between", value1, value2, "ext");
            return (Criteria) this;
        }

        public Criteria andExtNotBetween(String value1, String value2) {
            addCriterion("ext not between", value1, value2, "ext");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeIsNull() {
            addCriterion("materiel_type is null");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeIsNotNull() {
            addCriterion("materiel_type is not null");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeEqualTo(String value) {
            addCriterion("materiel_type =", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeNotEqualTo(String value) {
            addCriterion("materiel_type <>", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeGreaterThan(String value) {
            addCriterion("materiel_type >", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeGreaterThanOrEqualTo(String value) {
            addCriterion("materiel_type >=", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeLessThan(String value) {
            addCriterion("materiel_type <", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeLessThanOrEqualTo(String value) {
            addCriterion("materiel_type <=", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeLike(String value) {
            addCriterion("materiel_type like", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeNotLike(String value) {
            addCriterion("materiel_type not like", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeIn(List<String> values) {
            addCriterion("materiel_type in", values, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeNotIn(List<String> values) {
            addCriterion("materiel_type not in", values, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeBetween(String value1, String value2) {
            addCriterion("materiel_type between", value1, value2, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeNotBetween(String value1, String value2) {
            addCriterion("materiel_type not between", value1, value2, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNull() {
            addCriterion("materiel_descr is null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNotNull() {
            addCriterion("materiel_descr is not null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrEqualTo(String value) {
            addCriterion("materiel_descr =", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotEqualTo(String value) {
            addCriterion("materiel_descr <>", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThan(String value) {
            addCriterion("materiel_descr >", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThanOrEqualTo(String value) {
            addCriterion("materiel_descr >=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThan(String value) {
            addCriterion("materiel_descr <", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThanOrEqualTo(String value) {
            addCriterion("materiel_descr <=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLike(String value) {
            addCriterion("materiel_descr like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotLike(String value) {
            addCriterion("materiel_descr not like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIn(List<String> values) {
            addCriterion("materiel_descr in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotIn(List<String> values) {
            addCriterion("materiel_descr not in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrBetween(String value1, String value2) {
            addCriterion("materiel_descr between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotBetween(String value1, String value2) {
            addCriterion("materiel_descr not between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNull() {
            addCriterion("pam_code is null");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNotNull() {
            addCriterion("pam_code is not null");
            return (Criteria) this;
        }

        public Criteria andPamCodeEqualTo(String value) {
            addCriterion("pam_code =", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotEqualTo(String value) {
            addCriterion("pam_code <>", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThan(String value) {
            addCriterion("pam_code >", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("pam_code >=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThan(String value) {
            addCriterion("pam_code <", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThanOrEqualTo(String value) {
            addCriterion("pam_code <=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLike(String value) {
            addCriterion("pam_code like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotLike(String value) {
            addCriterion("pam_code not like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeIn(List<String> values) {
            addCriterion("pam_code in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotIn(List<String> values) {
            addCriterion("pam_code not in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeBetween(String value1, String value2) {
            addCriterion("pam_code between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotBetween(String value1, String value2) {
            addCriterion("pam_code not between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNull() {
            addCriterion("erp_code is null");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNotNull() {
            addCriterion("erp_code is not null");
            return (Criteria) this;
        }

        public Criteria andErpCodeEqualTo(String value) {
            addCriterion("erp_code =", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotEqualTo(String value) {
            addCriterion("erp_code <>", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThan(String value) {
            addCriterion("erp_code >", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("erp_code >=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThan(String value) {
            addCriterion("erp_code <", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThanOrEqualTo(String value) {
            addCriterion("erp_code <=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLike(String value) {
            addCriterion("erp_code like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotLike(String value) {
            addCriterion("erp_code not like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIn(List<String> values) {
            addCriterion("erp_code in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotIn(List<String> values) {
            addCriterion("erp_code not in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeBetween(String value1, String value2) {
            addCriterion("erp_code between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotBetween(String value1, String value2) {
            addCriterion("erp_code not between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andNumberIsNull() {
            addCriterion("number is null");
            return (Criteria) this;
        }

        public Criteria andNumberIsNotNull() {
            addCriterion("number is not null");
            return (Criteria) this;
        }

        public Criteria andNumberEqualTo(BigDecimal value) {
            addCriterion("number =", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotEqualTo(BigDecimal value) {
            addCriterion("number <>", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberGreaterThan(BigDecimal value) {
            addCriterion("number >", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("number >=", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberLessThan(BigDecimal value) {
            addCriterion("number <", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberLessThanOrEqualTo(BigDecimal value) {
            addCriterion("number <=", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberIn(List<BigDecimal> values) {
            addCriterion("number in", values, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotIn(List<BigDecimal> values) {
            addCriterion("number not in", values, "number");
            return (Criteria) this;
        }

        public Criteria andNumberBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("number between", value1, value2, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("number not between", value1, value2, "number");
            return (Criteria) this;
        }

        public Criteria andTotalNumIsNull() {
            addCriterion("total_num is null");
            return (Criteria) this;
        }

        public Criteria andTotalNumIsNotNull() {
            addCriterion("total_num is not null");
            return (Criteria) this;
        }

        public Criteria andTotalNumEqualTo(BigDecimal value) {
            addCriterion("total_num =", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumNotEqualTo(BigDecimal value) {
            addCriterion("total_num <>", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumGreaterThan(BigDecimal value) {
            addCriterion("total_num >", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_num >=", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumLessThan(BigDecimal value) {
            addCriterion("total_num <", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_num <=", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumIn(List<BigDecimal> values) {
            addCriterion("total_num in", values, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumNotIn(List<BigDecimal> values) {
            addCriterion("total_num not in", values, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_num between", value1, value2, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_num not between", value1, value2, "totalNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumIsNull() {
            addCriterion("requirement_num is null");
            return (Criteria) this;
        }

        public Criteria andRequirementNumIsNotNull() {
            addCriterion("requirement_num is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementNumEqualTo(BigDecimal value) {
            addCriterion("requirement_num =", value, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumNotEqualTo(BigDecimal value) {
            addCriterion("requirement_num <>", value, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumGreaterThan(BigDecimal value) {
            addCriterion("requirement_num >", value, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("requirement_num >=", value, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumLessThan(BigDecimal value) {
            addCriterion("requirement_num <", value, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("requirement_num <=", value, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumIn(List<BigDecimal> values) {
            addCriterion("requirement_num in", values, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumNotIn(List<BigDecimal> values) {
            addCriterion("requirement_num not in", values, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("requirement_num between", value1, value2, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("requirement_num not between", value1, value2, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderPlacedIsNull() {
            addCriterion("purchase_order_placed is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderPlacedIsNotNull() {
            addCriterion("purchase_order_placed is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderPlacedEqualTo(BigDecimal value) {
            addCriterion("purchase_order_placed =", value, "purchaseOrderPlaced");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderPlacedNotEqualTo(BigDecimal value) {
            addCriterion("purchase_order_placed <>", value, "purchaseOrderPlaced");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderPlacedGreaterThan(BigDecimal value) {
            addCriterion("purchase_order_placed >", value, "purchaseOrderPlaced");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderPlacedGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("purchase_order_placed >=", value, "purchaseOrderPlaced");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderPlacedLessThan(BigDecimal value) {
            addCriterion("purchase_order_placed <", value, "purchaseOrderPlaced");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderPlacedLessThanOrEqualTo(BigDecimal value) {
            addCriterion("purchase_order_placed <=", value, "purchaseOrderPlaced");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderPlacedIn(List<BigDecimal> values) {
            addCriterion("purchase_order_placed in", values, "purchaseOrderPlaced");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderPlacedNotIn(List<BigDecimal> values) {
            addCriterion("purchase_order_placed not in", values, "purchaseOrderPlaced");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderPlacedBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("purchase_order_placed between", value1, value2, "purchaseOrderPlaced");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderPlacedNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("purchase_order_placed not between", value1, value2, "purchaseOrderPlaced");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityIsNull() {
            addCriterion("inventory_quantity is null");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityIsNotNull() {
            addCriterion("inventory_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityEqualTo(BigDecimal value) {
            addCriterion("inventory_quantity =", value, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityNotEqualTo(BigDecimal value) {
            addCriterion("inventory_quantity <>", value, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityGreaterThan(BigDecimal value) {
            addCriterion("inventory_quantity >", value, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("inventory_quantity >=", value, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityLessThan(BigDecimal value) {
            addCriterion("inventory_quantity <", value, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("inventory_quantity <=", value, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityIn(List<BigDecimal> values) {
            addCriterion("inventory_quantity in", values, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityNotIn(List<BigDecimal> values) {
            addCriterion("inventory_quantity not in", values, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inventory_quantity between", value1, value2, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inventory_quantity not between", value1, value2, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andMaterialReceivedIsNull() {
            addCriterion("material_received is null");
            return (Criteria) this;
        }

        public Criteria andMaterialReceivedIsNotNull() {
            addCriterion("material_received is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialReceivedEqualTo(BigDecimal value) {
            addCriterion("material_received =", value, "materialReceived");
            return (Criteria) this;
        }

        public Criteria andMaterialReceivedNotEqualTo(BigDecimal value) {
            addCriterion("material_received <>", value, "materialReceived");
            return (Criteria) this;
        }

        public Criteria andMaterialReceivedGreaterThan(BigDecimal value) {
            addCriterion("material_received >", value, "materialReceived");
            return (Criteria) this;
        }

        public Criteria andMaterialReceivedGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_received >=", value, "materialReceived");
            return (Criteria) this;
        }

        public Criteria andMaterialReceivedLessThan(BigDecimal value) {
            addCriterion("material_received <", value, "materialReceived");
            return (Criteria) this;
        }

        public Criteria andMaterialReceivedLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_received <=", value, "materialReceived");
            return (Criteria) this;
        }

        public Criteria andMaterialReceivedIn(List<BigDecimal> values) {
            addCriterion("material_received in", values, "materialReceived");
            return (Criteria) this;
        }

        public Criteria andMaterialReceivedNotIn(List<BigDecimal> values) {
            addCriterion("material_received not in", values, "materialReceived");
            return (Criteria) this;
        }

        public Criteria andMaterialReceivedBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_received between", value1, value2, "materialReceived");
            return (Criteria) this;
        }

        public Criteria andMaterialReceivedNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_received not between", value1, value2, "materialReceived");
            return (Criteria) this;
        }

        public Criteria andNotOrderedIsNull() {
            addCriterion("not_ordered is null");
            return (Criteria) this;
        }

        public Criteria andNotOrderedIsNotNull() {
            addCriterion("not_ordered is not null");
            return (Criteria) this;
        }

        public Criteria andNotOrderedEqualTo(BigDecimal value) {
            addCriterion("not_ordered =", value, "notOrdered");
            return (Criteria) this;
        }

        public Criteria andNotOrderedNotEqualTo(BigDecimal value) {
            addCriterion("not_ordered <>", value, "notOrdered");
            return (Criteria) this;
        }

        public Criteria andNotOrderedGreaterThan(BigDecimal value) {
            addCriterion("not_ordered >", value, "notOrdered");
            return (Criteria) this;
        }

        public Criteria andNotOrderedGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("not_ordered >=", value, "notOrdered");
            return (Criteria) this;
        }

        public Criteria andNotOrderedLessThan(BigDecimal value) {
            addCriterion("not_ordered <", value, "notOrdered");
            return (Criteria) this;
        }

        public Criteria andNotOrderedLessThanOrEqualTo(BigDecimal value) {
            addCriterion("not_ordered <=", value, "notOrdered");
            return (Criteria) this;
        }

        public Criteria andNotOrderedIn(List<BigDecimal> values) {
            addCriterion("not_ordered in", values, "notOrdered");
            return (Criteria) this;
        }

        public Criteria andNotOrderedNotIn(List<BigDecimal> values) {
            addCriterion("not_ordered not in", values, "notOrdered");
            return (Criteria) this;
        }

        public Criteria andNotOrderedBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("not_ordered between", value1, value2, "notOrdered");
            return (Criteria) this;
        }

        public Criteria andNotOrderedNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("not_ordered not between", value1, value2, "notOrdered");
            return (Criteria) this;
        }

        public Criteria andPackagePriceEstimateUnitPriceIsNull() {
            addCriterion("package_price_estimate_unit_price is null");
            return (Criteria) this;
        }

        public Criteria andPackagePriceEstimateUnitPriceIsNotNull() {
            addCriterion("package_price_estimate_unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andPackagePriceEstimateUnitPriceEqualTo(BigDecimal value) {
            addCriterion("package_price_estimate_unit_price =", value, "packagePriceEstimateUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPackagePriceEstimateUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("package_price_estimate_unit_price <>", value, "packagePriceEstimateUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPackagePriceEstimateUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("package_price_estimate_unit_price >", value, "packagePriceEstimateUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPackagePriceEstimateUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("package_price_estimate_unit_price >=", value, "packagePriceEstimateUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPackagePriceEstimateUnitPriceLessThan(BigDecimal value) {
            addCriterion("package_price_estimate_unit_price <", value, "packagePriceEstimateUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPackagePriceEstimateUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("package_price_estimate_unit_price <=", value, "packagePriceEstimateUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPackagePriceEstimateUnitPriceIn(List<BigDecimal> values) {
            addCriterion("package_price_estimate_unit_price in", values, "packagePriceEstimateUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPackagePriceEstimateUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("package_price_estimate_unit_price not in", values, "packagePriceEstimateUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPackagePriceEstimateUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("package_price_estimate_unit_price between", value1, value2, "packagePriceEstimateUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPackagePriceEstimateUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("package_price_estimate_unit_price not between", value1, value2, "packagePriceEstimateUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPurchaseUnitPriceIsNull() {
            addCriterion("purchase_unit_price is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseUnitPriceIsNotNull() {
            addCriterion("purchase_unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseUnitPriceEqualTo(BigDecimal value) {
            addCriterion("purchase_unit_price =", value, "purchaseUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPurchaseUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("purchase_unit_price <>", value, "purchaseUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPurchaseUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("purchase_unit_price >", value, "purchaseUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPurchaseUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("purchase_unit_price >=", value, "purchaseUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPurchaseUnitPriceLessThan(BigDecimal value) {
            addCriterion("purchase_unit_price <", value, "purchaseUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPurchaseUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("purchase_unit_price <=", value, "purchaseUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPurchaseUnitPriceIn(List<BigDecimal> values) {
            addCriterion("purchase_unit_price in", values, "purchaseUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPurchaseUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("purchase_unit_price not in", values, "purchaseUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPurchaseUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("purchase_unit_price between", value1, value2, "purchaseUnitPrice");
            return (Criteria) this;
        }

        public Criteria andPurchaseUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("purchase_unit_price not between", value1, value2, "purchaseUnitPrice");
            return (Criteria) this;
        }

        public Criteria andAverageUnitCostMaterialIsNull() {
            addCriterion("average_unit_cost_material is null");
            return (Criteria) this;
        }

        public Criteria andAverageUnitCostMaterialIsNotNull() {
            addCriterion("average_unit_cost_material is not null");
            return (Criteria) this;
        }

        public Criteria andAverageUnitCostMaterialEqualTo(BigDecimal value) {
            addCriterion("average_unit_cost_material =", value, "averageUnitCostMaterial");
            return (Criteria) this;
        }

        public Criteria andAverageUnitCostMaterialNotEqualTo(BigDecimal value) {
            addCriterion("average_unit_cost_material <>", value, "averageUnitCostMaterial");
            return (Criteria) this;
        }

        public Criteria andAverageUnitCostMaterialGreaterThan(BigDecimal value) {
            addCriterion("average_unit_cost_material >", value, "averageUnitCostMaterial");
            return (Criteria) this;
        }

        public Criteria andAverageUnitCostMaterialGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("average_unit_cost_material >=", value, "averageUnitCostMaterial");
            return (Criteria) this;
        }

        public Criteria andAverageUnitCostMaterialLessThan(BigDecimal value) {
            addCriterion("average_unit_cost_material <", value, "averageUnitCostMaterial");
            return (Criteria) this;
        }

        public Criteria andAverageUnitCostMaterialLessThanOrEqualTo(BigDecimal value) {
            addCriterion("average_unit_cost_material <=", value, "averageUnitCostMaterial");
            return (Criteria) this;
        }

        public Criteria andAverageUnitCostMaterialIn(List<BigDecimal> values) {
            addCriterion("average_unit_cost_material in", values, "averageUnitCostMaterial");
            return (Criteria) this;
        }

        public Criteria andAverageUnitCostMaterialNotIn(List<BigDecimal> values) {
            addCriterion("average_unit_cost_material not in", values, "averageUnitCostMaterial");
            return (Criteria) this;
        }

        public Criteria andAverageUnitCostMaterialBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("average_unit_cost_material between", value1, value2, "averageUnitCostMaterial");
            return (Criteria) this;
        }

        public Criteria andAverageUnitCostMaterialNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("average_unit_cost_material not between", value1, value2, "averageUnitCostMaterial");
            return (Criteria) this;
        }

        public Criteria andExpectedPendingCostIsNull() {
            addCriterion("expected_pending_cost is null");
            return (Criteria) this;
        }

        public Criteria andExpectedPendingCostIsNotNull() {
            addCriterion("expected_pending_cost is not null");
            return (Criteria) this;
        }

        public Criteria andExpectedPendingCostEqualTo(BigDecimal value) {
            addCriterion("expected_pending_cost =", value, "expectedPendingCost");
            return (Criteria) this;
        }

        public Criteria andExpectedPendingCostNotEqualTo(BigDecimal value) {
            addCriterion("expected_pending_cost <>", value, "expectedPendingCost");
            return (Criteria) this;
        }

        public Criteria andExpectedPendingCostGreaterThan(BigDecimal value) {
            addCriterion("expected_pending_cost >", value, "expectedPendingCost");
            return (Criteria) this;
        }

        public Criteria andExpectedPendingCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("expected_pending_cost >=", value, "expectedPendingCost");
            return (Criteria) this;
        }

        public Criteria andExpectedPendingCostLessThan(BigDecimal value) {
            addCriterion("expected_pending_cost <", value, "expectedPendingCost");
            return (Criteria) this;
        }

        public Criteria andExpectedPendingCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("expected_pending_cost <=", value, "expectedPendingCost");
            return (Criteria) this;
        }

        public Criteria andExpectedPendingCostIn(List<BigDecimal> values) {
            addCriterion("expected_pending_cost in", values, "expectedPendingCost");
            return (Criteria) this;
        }

        public Criteria andExpectedPendingCostNotIn(List<BigDecimal> values) {
            addCriterion("expected_pending_cost not in", values, "expectedPendingCost");
            return (Criteria) this;
        }

        public Criteria andExpectedPendingCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("expected_pending_cost between", value1, value2, "expectedPendingCost");
            return (Criteria) this;
        }

        public Criteria andExpectedPendingCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("expected_pending_cost not between", value1, value2, "expectedPendingCost");
            return (Criteria) this;
        }

        public Criteria andTotalPurchaseCostIsNull() {
            addCriterion("total_purchase_cost is null");
            return (Criteria) this;
        }

        public Criteria andTotalPurchaseCostIsNotNull() {
            addCriterion("total_purchase_cost is not null");
            return (Criteria) this;
        }

        public Criteria andTotalPurchaseCostEqualTo(BigDecimal value) {
            addCriterion("total_purchase_cost =", value, "totalPurchaseCost");
            return (Criteria) this;
        }

        public Criteria andTotalPurchaseCostNotEqualTo(BigDecimal value) {
            addCriterion("total_purchase_cost <>", value, "totalPurchaseCost");
            return (Criteria) this;
        }

        public Criteria andTotalPurchaseCostGreaterThan(BigDecimal value) {
            addCriterion("total_purchase_cost >", value, "totalPurchaseCost");
            return (Criteria) this;
        }

        public Criteria andTotalPurchaseCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_purchase_cost >=", value, "totalPurchaseCost");
            return (Criteria) this;
        }

        public Criteria andTotalPurchaseCostLessThan(BigDecimal value) {
            addCriterion("total_purchase_cost <", value, "totalPurchaseCost");
            return (Criteria) this;
        }

        public Criteria andTotalPurchaseCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_purchase_cost <=", value, "totalPurchaseCost");
            return (Criteria) this;
        }

        public Criteria andTotalPurchaseCostIn(List<BigDecimal> values) {
            addCriterion("total_purchase_cost in", values, "totalPurchaseCost");
            return (Criteria) this;
        }

        public Criteria andTotalPurchaseCostNotIn(List<BigDecimal> values) {
            addCriterion("total_purchase_cost not in", values, "totalPurchaseCost");
            return (Criteria) this;
        }

        public Criteria andTotalPurchaseCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_purchase_cost between", value1, value2, "totalPurchaseCost");
            return (Criteria) this;
        }

        public Criteria andTotalPurchaseCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_purchase_cost not between", value1, value2, "totalPurchaseCost");
            return (Criteria) this;
        }

        public Criteria andTotalMaterialCostIsNull() {
            addCriterion("total_material_cost is null");
            return (Criteria) this;
        }

        public Criteria andTotalMaterialCostIsNotNull() {
            addCriterion("total_material_cost is not null");
            return (Criteria) this;
        }

        public Criteria andTotalMaterialCostEqualTo(BigDecimal value) {
            addCriterion("total_material_cost =", value, "totalMaterialCost");
            return (Criteria) this;
        }

        public Criteria andTotalMaterialCostNotEqualTo(BigDecimal value) {
            addCriterion("total_material_cost <>", value, "totalMaterialCost");
            return (Criteria) this;
        }

        public Criteria andTotalMaterialCostGreaterThan(BigDecimal value) {
            addCriterion("total_material_cost >", value, "totalMaterialCost");
            return (Criteria) this;
        }

        public Criteria andTotalMaterialCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_material_cost >=", value, "totalMaterialCost");
            return (Criteria) this;
        }

        public Criteria andTotalMaterialCostLessThan(BigDecimal value) {
            addCriterion("total_material_cost <", value, "totalMaterialCost");
            return (Criteria) this;
        }

        public Criteria andTotalMaterialCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_material_cost <=", value, "totalMaterialCost");
            return (Criteria) this;
        }

        public Criteria andTotalMaterialCostIn(List<BigDecimal> values) {
            addCriterion("total_material_cost in", values, "totalMaterialCost");
            return (Criteria) this;
        }

        public Criteria andTotalMaterialCostNotIn(List<BigDecimal> values) {
            addCriterion("total_material_cost not in", values, "totalMaterialCost");
            return (Criteria) this;
        }

        public Criteria andTotalMaterialCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_material_cost between", value1, value2, "totalMaterialCost");
            return (Criteria) this;
        }

        public Criteria andTotalMaterialCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_material_cost not between", value1, value2, "totalMaterialCost");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNull() {
            addCriterion("delivery_time is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNotNull() {
            addCriterion("delivery_time is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeEqualTo(Date value) {
            addCriterionForJDBCDate("delivery_time =", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotEqualTo(Date value) {
            addCriterionForJDBCDate("delivery_time <>", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThan(Date value) {
            addCriterionForJDBCDate("delivery_time >", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("delivery_time >=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThan(Date value) {
            addCriterionForJDBCDate("delivery_time <", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("delivery_time <=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIn(List<Date> values) {
            addCriterionForJDBCDate("delivery_time in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotIn(List<Date> values) {
            addCriterionForJDBCDate("delivery_time not in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("delivery_time between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("delivery_time not between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}