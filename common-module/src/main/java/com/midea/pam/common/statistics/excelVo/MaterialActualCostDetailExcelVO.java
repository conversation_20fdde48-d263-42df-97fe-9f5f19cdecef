package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: common-module
 * @description: 实际成本归集-物料成本明细导出实体
 * @author:zhongpeng
 * @create:2020-03-24 16:22
 **/
@Setter
@Getter
public class MaterialActualCostDetailExcelVO {
    @Excel(name = "序号", width = 10)
    private Integer num;

    @Excel(name = "归集日期", width = 20, format = "yyyy-MM-dd")
    private Date collectionDate;

    @Excel(name = "成本发生日期", width = 20, format = "yyyy-MM-dd")
    private Date costDate;

    @Excel(name = "结转状态", width = 20, replace = {"未结转_0", "已结转_1", "未结转_null"})
    private Integer carryStatus;

    @Excel(name = "结转期间", width = 20)
    private String glPeriod;

    @Excel(name = "项目编号", width = 30)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "单据编号", width = 20)
    private String code;

    @Excel(name = "单据入账日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date actualDate;

    @Excel(name = "物料编码", width = 20)
    private String materialCode;

    @Excel(name = "物料描述", width = 30)
    private String itemInfo;

    @Excel(name = "数量", width = 20)
    private BigDecimal actualAmount;

    @Excel(name = "单位", width = 20)
    private String unit;

    @Excel(name = "单位成本", width = 20)
    private BigDecimal actualCost;

    @Excel(name = "总成本", width = 20)
    private BigDecimal totalAmount;

    @Excel(name = "币种", width = 20)
    private String currency;

    @Excel(name = "业务实体", width = 30)
    private String ouName;

    @Excel(name = "WBS号", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项编码", width = 20)
    private String activityCode;

}
