package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 **/
public class ReportProjectDimensionExcelVO {
    @Excel(name = "序号",width = 10)
    private Integer num;

    @Excel(name = "项目编号",width = 20)
    private String projectCode;

    @Excel(name = "项目名称",width = 30)
    private String projectName;

    @Excel(name = "事业部",width = 15)
    private String divisionName;

    @Excel(name = "业务分类",width = 15)
    private String unitName;

    @Excel(name = "项目类型",width = 15)
    private String typeName;

    @Excel(name = "项目状态",width = 20,replace = {"审批驳回_-2", "财务驳回_-1", "草稿_0", "审批中_3", "项目进行中_4", "项目变更中_9", "结项_10",
            "预立项转正驳回_-3", "预立项审批驳回_-4", "审批撤回_11", "预立项审批撤回_13", "作废_12", "预立项转正审批中_7", "预立项转正审批撤回_14", "终止_16"})
    private String status;

    @Excel(name = "项目属性",width = 15, replace = {"内部_1", "外部_2", "研发_3"})
    private String priceType;

    @Excel(name = "项目金额（不含税）",width = 20)
    private BigDecimal amount;

    @Excel(name = "项目预算",width = 20)
    private BigDecimal budgetCost;

    @Excel(name = "币种",width = 15)
    private String currency;

    @Excel(name = "计划收入金额",width = 20)
    private BigDecimal incomeAmount;


    @Excel(name = "累计确认收入比例",width = 20,suffix = "%")
    private BigDecimal cumulativeIncomePercent;

    @Excel(name = "累计确认收入金额",width = 20)
    private BigDecimal currentIncomeTotalAmount;

    @Excel(name = "收入达成率",width = 20,suffix = "%")
    private BigDecimal attainmentIncomePercent;

    @Excel(name = "计划成本金额",width = 20)
    private BigDecimal costAmount;

    @Excel(name = "累计结转成本金额",width = 20)
    private BigDecimal currentCostActual;

    @Excel(name = "已结转人工成本",width = 20)
    private BigDecimal innerLaborCost;

    @Excel(name = "已结转费用",width = 20)
    private BigDecimal feeCost;

    @Excel(name = "预算占比",width = 20,suffix = "%")
    private BigDecimal budgetOfPercent;

    @Excel(name = "计划利润金额",width = 20)
    private BigDecimal planProfit;

    @Excel(name = "实际利润金额",width = 20)
    private BigDecimal actualProfit;

    @Excel(name = "利润执行对比",width = 20)
    private BigDecimal profitExecutionComparison;

    @Excel(name = "项目经理",width = 30)
    private String managerName;

    @Excel(name = "PAM子合同编号",width = 30)
    private String contractCode;

    @Excel(name = "项目开始时间", width = 15, format = "yyyy-MM-dd")
    private Date startDate;

    @Excel(name = "项目结束时间", width = 15, format = "yyyy-MM-dd")
    private Date endDate;

    @Excel(name = "业务实体",width = 30)
    private String ouName;

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getDivisionName() {
        return divisionName;
    }

    public void setDivisionName(String divisionName) {
        this.divisionName = divisionName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPriceType() {
        return priceType;
    }

    public void setPriceType(String priceType) {
        this.priceType = priceType;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getBudgetCost() {
        return budgetCost;
    }

    public void setBudgetCost(BigDecimal budgetCost) {
        this.budgetCost = budgetCost;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getIncomeAmount() {
        return incomeAmount;
    }

    public void setIncomeAmount(BigDecimal incomeAmount) {
        this.incomeAmount = incomeAmount;
    }


    public BigDecimal getCumulativeIncomePercent() {
        return cumulativeIncomePercent;
    }

    public void setCumulativeIncomePercent(BigDecimal cumulativeIncomePercent) {
        this.cumulativeIncomePercent = cumulativeIncomePercent;
    }

    public BigDecimal getCurrentIncomeTotalAmount() {
        return currentIncomeTotalAmount;
    }

    public void setCurrentIncomeTotalAmount(BigDecimal currentIncomeTotalAmount) {
        this.currentIncomeTotalAmount = currentIncomeTotalAmount;
    }

    public BigDecimal getAttainmentIncomePercent() {
        return attainmentIncomePercent;
    }

    public void setAttainmentIncomePercent(BigDecimal attainmentIncomePercent) {
        this.attainmentIncomePercent = attainmentIncomePercent;
    }


    public BigDecimal getCostAmount() {
        return costAmount;
    }

    public void setCostAmount(BigDecimal costAmount) {
        this.costAmount = costAmount;
    }


    public BigDecimal getCurrentCostActual() {
        return currentCostActual;
    }

    public void setCurrentCostActual(BigDecimal currentCostActual) {
        this.currentCostActual = currentCostActual;
    }


    public BigDecimal getInnerLaborCost() {
        return innerLaborCost;
    }

    public void setInnerLaborCost(BigDecimal innerLaborCost) {
        this.innerLaborCost = innerLaborCost;
    }


    public BigDecimal getFeeCost() {
        return feeCost;
    }

    public void setFeeCost(BigDecimal feeCost) {
        this.feeCost = feeCost;
    }


    public BigDecimal getBudgetOfPercent() {
        return budgetOfPercent;
    }

    public void setBudgetOfPercent(BigDecimal budgetOfPercent) {
        this.budgetOfPercent = budgetOfPercent;
    }

    public BigDecimal getPlanProfit() {
        return planProfit;
    }

    public void setPlanProfit(BigDecimal planProfit) {
        this.planProfit = planProfit;
    }


    public BigDecimal getActualProfit() {
        return actualProfit;
    }

    public void setActualProfit(BigDecimal actualProfit) {
        this.actualProfit = actualProfit;
    }


    public BigDecimal getProfitExecutionComparison() {
        return profitExecutionComparison;
    }

    public void setProfitExecutionComparison(BigDecimal profitExecutionComparison) {
        this.profitExecutionComparison = profitExecutionComparison;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName;
    }
}
