package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目变更里程碑汇总表")
public class ReportProjectChangeMilepost extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    @ApiModelProperty(value = "操作类型")
    private String operationType;

    @ApiModelProperty(value = "变更发起人")
    private String changeSubmitBy;

    @ApiModelProperty(value = "变更时间")
    private Date changeTime;

    @ApiModelProperty(value = "变更原因")
    private String changeReason;

    @ApiModelProperty(value = "变更原因说明")
    private String changeReasonDescribe;

    @ApiModelProperty(value = "变更后名称")
    private String afterName;

    @ApiModelProperty(value = "变更后计划开始时间")
    private Date afterPlanStartTime;

    @ApiModelProperty(value = "变更后计划结束时间")
    private Date afterPlanEndTime;

    @ApiModelProperty(value = "变更后责任人")
    private String afterResponsible;

    @ApiModelProperty(value = "变更里程碑名称")
    private String beforeName;

    @ApiModelProperty(value = "变更前计划开始时间")
    private Date beforePlanStartTime;

    @ApiModelProperty(value = "变更前计划结束时间")
    private Date beforePlanEndTime;

    @ApiModelProperty(value = "变更前责任人")
    private String beforeResponsible;

    @ApiModelProperty(value = "是否删除")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType == null ? null : operationType.trim();
    }

    public String getChangeSubmitBy() {
        return changeSubmitBy;
    }

    public void setChangeSubmitBy(String changeSubmitBy) {
        this.changeSubmitBy = changeSubmitBy == null ? null : changeSubmitBy.trim();
    }

    public Date getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(Date changeTime) {
        this.changeTime = changeTime;
    }

    public String getChangeReason() {
        return changeReason;
    }

    public void setChangeReason(String changeReason) {
        this.changeReason = changeReason == null ? null : changeReason.trim();
    }

    public String getChangeReasonDescribe() {
        return changeReasonDescribe;
    }

    public void setChangeReasonDescribe(String changeReasonDescribe) {
        this.changeReasonDescribe = changeReasonDescribe == null ? null : changeReasonDescribe.trim();
    }

    public String getAfterName() {
        return afterName;
    }

    public void setAfterName(String afterName) {
        this.afterName = afterName == null ? null : afterName.trim();
    }

    public Date getAfterPlanStartTime() {
        return afterPlanStartTime;
    }

    public void setAfterPlanStartTime(Date afterPlanStartTime) {
        this.afterPlanStartTime = afterPlanStartTime;
    }

    public Date getAfterPlanEndTime() {
        return afterPlanEndTime;
    }

    public void setAfterPlanEndTime(Date afterPlanEndTime) {
        this.afterPlanEndTime = afterPlanEndTime;
    }

    public String getAfterResponsible() {
        return afterResponsible;
    }

    public void setAfterResponsible(String afterResponsible) {
        this.afterResponsible = afterResponsible == null ? null : afterResponsible.trim();
    }

    public String getBeforeName() {
        return beforeName;
    }

    public void setBeforeName(String beforeName) {
        this.beforeName = beforeName == null ? null : beforeName.trim();
    }

    public Date getBeforePlanStartTime() {
        return beforePlanStartTime;
    }

    public void setBeforePlanStartTime(Date beforePlanStartTime) {
        this.beforePlanStartTime = beforePlanStartTime;
    }

    public Date getBeforePlanEndTime() {
        return beforePlanEndTime;
    }

    public void setBeforePlanEndTime(Date beforePlanEndTime) {
        this.beforePlanEndTime = beforePlanEndTime;
    }

    public String getBeforeResponsible() {
        return beforeResponsible;
    }

    public void setBeforeResponsible(String beforeResponsible) {
        this.beforeResponsible = beforeResponsible == null ? null : beforeResponsible.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", operationType=").append(operationType);
        sb.append(", changeSubmitBy=").append(changeSubmitBy);
        sb.append(", changeTime=").append(changeTime);
        sb.append(", changeReason=").append(changeReason);
        sb.append(", changeReasonDescribe=").append(changeReasonDescribe);
        sb.append(", afterName=").append(afterName);
        sb.append(", afterPlanStartTime=").append(afterPlanStartTime);
        sb.append(", afterPlanEndTime=").append(afterPlanEndTime);
        sb.append(", afterResponsible=").append(afterResponsible);
        sb.append(", beforeName=").append(beforeName);
        sb.append(", beforePlanStartTime=").append(beforePlanStartTime);
        sb.append(", beforePlanEndTime=").append(beforePlanEndTime);
        sb.append(", beforeResponsible=").append(beforeResponsible);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}