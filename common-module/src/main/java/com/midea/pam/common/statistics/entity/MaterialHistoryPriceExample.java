package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MaterialHistoryPriceExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public MaterialHistoryPriceExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNull() {
            addCriterion("pam_code is null");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNotNull() {
            addCriterion("pam_code is not null");
            return (Criteria) this;
        }

        public Criteria andPamCodeEqualTo(String value) {
            addCriterion("pam_code =", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotEqualTo(String value) {
            addCriterion("pam_code <>", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThan(String value) {
            addCriterion("pam_code >", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("pam_code >=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThan(String value) {
            addCriterion("pam_code <", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThanOrEqualTo(String value) {
            addCriterion("pam_code <=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLike(String value) {
            addCriterion("pam_code like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotLike(String value) {
            addCriterion("pam_code not like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeIn(List<String> values) {
            addCriterion("pam_code in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotIn(List<String> values) {
            addCriterion("pam_code not in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeBetween(String value1, String value2) {
            addCriterion("pam_code between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotBetween(String value1, String value2) {
            addCriterion("pam_code not between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeIsNull() {
            addCriterion("item_code is null");
            return (Criteria) this;
        }

        public Criteria andItemCodeIsNotNull() {
            addCriterion("item_code is not null");
            return (Criteria) this;
        }

        public Criteria andItemCodeEqualTo(String value) {
            addCriterion("item_code =", value, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeNotEqualTo(String value) {
            addCriterion("item_code <>", value, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeGreaterThan(String value) {
            addCriterion("item_code >", value, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeGreaterThanOrEqualTo(String value) {
            addCriterion("item_code >=", value, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeLessThan(String value) {
            addCriterion("item_code <", value, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeLessThanOrEqualTo(String value) {
            addCriterion("item_code <=", value, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeLike(String value) {
            addCriterion("item_code like", value, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeNotLike(String value) {
            addCriterion("item_code not like", value, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeIn(List<String> values) {
            addCriterion("item_code in", values, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeNotIn(List<String> values) {
            addCriterion("item_code not in", values, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeBetween(String value1, String value2) {
            addCriterion("item_code between", value1, value2, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeNotBetween(String value1, String value2) {
            addCriterion("item_code not between", value1, value2, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemInfoIsNull() {
            addCriterion("item_info is null");
            return (Criteria) this;
        }

        public Criteria andItemInfoIsNotNull() {
            addCriterion("item_info is not null");
            return (Criteria) this;
        }

        public Criteria andItemInfoEqualTo(String value) {
            addCriterion("item_info =", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoNotEqualTo(String value) {
            addCriterion("item_info <>", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoGreaterThan(String value) {
            addCriterion("item_info >", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoGreaterThanOrEqualTo(String value) {
            addCriterion("item_info >=", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoLessThan(String value) {
            addCriterion("item_info <", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoLessThanOrEqualTo(String value) {
            addCriterion("item_info <=", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoLike(String value) {
            addCriterion("item_info like", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoNotLike(String value) {
            addCriterion("item_info not like", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoIn(List<String> values) {
            addCriterion("item_info in", values, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoNotIn(List<String> values) {
            addCriterion("item_info not in", values, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoBetween(String value1, String value2) {
            addCriterion("item_info between", value1, value2, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoNotBetween(String value1, String value2) {
            addCriterion("item_info not between", value1, value2, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andPriceIsNull() {
            addCriterion("price is null");
            return (Criteria) this;
        }

        public Criteria andPriceIsNotNull() {
            addCriterion("price is not null");
            return (Criteria) this;
        }

        public Criteria andPriceEqualTo(BigDecimal value) {
            addCriterion("price =", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualTo(BigDecimal value) {
            addCriterion("price <>", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThan(BigDecimal value) {
            addCriterion("price >", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("price >=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThan(BigDecimal value) {
            addCriterion("price <", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("price <=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceIn(List<BigDecimal> values) {
            addCriterion("price in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotIn(List<BigDecimal> values) {
            addCriterion("price not in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price not between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andOrderDateIsNull() {
            addCriterion("order_date is null");
            return (Criteria) this;
        }

        public Criteria andOrderDateIsNotNull() {
            addCriterion("order_date is not null");
            return (Criteria) this;
        }

        public Criteria andOrderDateEqualTo(Date value) {
            addCriterion("order_date =", value, "orderDate");
            return (Criteria) this;
        }

        public Criteria andOrderDateNotEqualTo(Date value) {
            addCriterion("order_date <>", value, "orderDate");
            return (Criteria) this;
        }

        public Criteria andOrderDateGreaterThan(Date value) {
            addCriterion("order_date >", value, "orderDate");
            return (Criteria) this;
        }

        public Criteria andOrderDateGreaterThanOrEqualTo(Date value) {
            addCriterion("order_date >=", value, "orderDate");
            return (Criteria) this;
        }

        public Criteria andOrderDateLessThan(Date value) {
            addCriterion("order_date <", value, "orderDate");
            return (Criteria) this;
        }

        public Criteria andOrderDateLessThanOrEqualTo(Date value) {
            addCriterion("order_date <=", value, "orderDate");
            return (Criteria) this;
        }

        public Criteria andOrderDateIn(List<Date> values) {
            addCriterion("order_date in", values, "orderDate");
            return (Criteria) this;
        }

        public Criteria andOrderDateNotIn(List<Date> values) {
            addCriterion("order_date not in", values, "orderDate");
            return (Criteria) this;
        }

        public Criteria andOrderDateBetween(Date value1, Date value2) {
            addCriterion("order_date between", value1, value2, "orderDate");
            return (Criteria) this;
        }

        public Criteria andOrderDateNotBetween(Date value1, Date value2) {
            addCriterion("order_date not between", value1, value2, "orderDate");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationIsNull() {
            addCriterion("material_classification is null");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationIsNotNull() {
            addCriterion("material_classification is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationEqualTo(String value) {
            addCriterion("material_classification =", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotEqualTo(String value) {
            addCriterion("material_classification <>", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationGreaterThan(String value) {
            addCriterion("material_classification >", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationGreaterThanOrEqualTo(String value) {
            addCriterion("material_classification >=", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationLessThan(String value) {
            addCriterion("material_classification <", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationLessThanOrEqualTo(String value) {
            addCriterion("material_classification <=", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationLike(String value) {
            addCriterion("material_classification like", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotLike(String value) {
            addCriterion("material_classification not like", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationIn(List<String> values) {
            addCriterion("material_classification in", values, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotIn(List<String> values) {
            addCriterion("material_classification not in", values, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationBetween(String value1, String value2) {
            addCriterion("material_classification between", value1, value2, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotBetween(String value1, String value2) {
            addCriterion("material_classification not between", value1, value2, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassIsNull() {
            addCriterion("coding_middleclass is null");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassIsNotNull() {
            addCriterion("coding_middleclass is not null");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassEqualTo(String value) {
            addCriterion("coding_middleclass =", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassNotEqualTo(String value) {
            addCriterion("coding_middleclass <>", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassGreaterThan(String value) {
            addCriterion("coding_middleclass >", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassGreaterThanOrEqualTo(String value) {
            addCriterion("coding_middleclass >=", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassLessThan(String value) {
            addCriterion("coding_middleclass <", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassLessThanOrEqualTo(String value) {
            addCriterion("coding_middleclass <=", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassLike(String value) {
            addCriterion("coding_middleclass like", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassNotLike(String value) {
            addCriterion("coding_middleclass not like", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassIn(List<String> values) {
            addCriterion("coding_middleclass in", values, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassNotIn(List<String> values) {
            addCriterion("coding_middleclass not in", values, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassBetween(String value1, String value2) {
            addCriterion("coding_middleclass between", value1, value2, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassNotBetween(String value1, String value2) {
            addCriterion("coding_middleclass not between", value1, value2, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIsNull() {
            addCriterion("material_type is null");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIsNotNull() {
            addCriterion("material_type is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeEqualTo(String value) {
            addCriterion("material_type =", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotEqualTo(String value) {
            addCriterion("material_type <>", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeGreaterThan(String value) {
            addCriterion("material_type >", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeGreaterThanOrEqualTo(String value) {
            addCriterion("material_type >=", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLessThan(String value) {
            addCriterion("material_type <", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLessThanOrEqualTo(String value) {
            addCriterion("material_type <=", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLike(String value) {
            addCriterion("material_type like", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotLike(String value) {
            addCriterion("material_type not like", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIn(List<String> values) {
            addCriterion("material_type in", values, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotIn(List<String> values) {
            addCriterion("material_type not in", values, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeBetween(String value1, String value2) {
            addCriterion("material_type between", value1, value2, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotBetween(String value1, String value2) {
            addCriterion("material_type not between", value1, value2, "materialType");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIsNull() {
            addCriterion("figure_number is null");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIsNotNull() {
            addCriterion("figure_number is not null");
            return (Criteria) this;
        }

        public Criteria andFigureNumberEqualTo(String value) {
            addCriterion("figure_number =", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotEqualTo(String value) {
            addCriterion("figure_number <>", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberGreaterThan(String value) {
            addCriterion("figure_number >", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberGreaterThanOrEqualTo(String value) {
            addCriterion("figure_number >=", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLessThan(String value) {
            addCriterion("figure_number <", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLessThanOrEqualTo(String value) {
            addCriterion("figure_number <=", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLike(String value) {
            addCriterion("figure_number like", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotLike(String value) {
            addCriterion("figure_number not like", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIn(List<String> values) {
            addCriterion("figure_number in", values, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotIn(List<String> values) {
            addCriterion("figure_number not in", values, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberBetween(String value1, String value2) {
            addCriterion("figure_number between", value1, value2, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotBetween(String value1, String value2) {
            addCriterion("figure_number not between", value1, value2, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andChartVersionIsNull() {
            addCriterion("chart_version is null");
            return (Criteria) this;
        }

        public Criteria andChartVersionIsNotNull() {
            addCriterion("chart_version is not null");
            return (Criteria) this;
        }

        public Criteria andChartVersionEqualTo(String value) {
            addCriterion("chart_version =", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotEqualTo(String value) {
            addCriterion("chart_version <>", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionGreaterThan(String value) {
            addCriterion("chart_version >", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionGreaterThanOrEqualTo(String value) {
            addCriterion("chart_version >=", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionLessThan(String value) {
            addCriterion("chart_version <", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionLessThanOrEqualTo(String value) {
            addCriterion("chart_version <=", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionLike(String value) {
            addCriterion("chart_version like", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotLike(String value) {
            addCriterion("chart_version not like", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionIn(List<String> values) {
            addCriterion("chart_version in", values, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotIn(List<String> values) {
            addCriterion("chart_version not in", values, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionBetween(String value1, String value2) {
            addCriterion("chart_version between", value1, value2, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotBetween(String value1, String value2) {
            addCriterion("chart_version not between", value1, value2, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeIsNull() {
            addCriterion("machining_part_type is null");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeIsNotNull() {
            addCriterion("machining_part_type is not null");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeEqualTo(String value) {
            addCriterion("machining_part_type =", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNotEqualTo(String value) {
            addCriterion("machining_part_type <>", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeGreaterThan(String value) {
            addCriterion("machining_part_type >", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeGreaterThanOrEqualTo(String value) {
            addCriterion("machining_part_type >=", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeLessThan(String value) {
            addCriterion("machining_part_type <", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeLessThanOrEqualTo(String value) {
            addCriterion("machining_part_type <=", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeLike(String value) {
            addCriterion("machining_part_type like", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNotLike(String value) {
            addCriterion("machining_part_type not like", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeIn(List<String> values) {
            addCriterion("machining_part_type in", values, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNotIn(List<String> values) {
            addCriterion("machining_part_type not in", values, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeBetween(String value1, String value2) {
            addCriterion("machining_part_type between", value1, value2, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNotBetween(String value1, String value2) {
            addCriterion("machining_part_type not between", value1, value2, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMaterialIsNull() {
            addCriterion("material is null");
            return (Criteria) this;
        }

        public Criteria andMaterialIsNotNull() {
            addCriterion("material is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialEqualTo(String value) {
            addCriterion("material =", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotEqualTo(String value) {
            addCriterion("material <>", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialGreaterThan(String value) {
            addCriterion("material >", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialGreaterThanOrEqualTo(String value) {
            addCriterion("material >=", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLessThan(String value) {
            addCriterion("material <", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLessThanOrEqualTo(String value) {
            addCriterion("material <=", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLike(String value) {
            addCriterion("material like", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotLike(String value) {
            addCriterion("material not like", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialIn(List<String> values) {
            addCriterion("material in", values, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotIn(List<String> values) {
            addCriterion("material not in", values, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialBetween(String value1, String value2) {
            addCriterion("material between", value1, value2, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotBetween(String value1, String value2) {
            addCriterion("material not between", value1, value2, "material");
            return (Criteria) this;
        }

        public Criteria andUnitWeightIsNull() {
            addCriterion("unit_weight is null");
            return (Criteria) this;
        }

        public Criteria andUnitWeightIsNotNull() {
            addCriterion("unit_weight is not null");
            return (Criteria) this;
        }

        public Criteria andUnitWeightEqualTo(BigDecimal value) {
            addCriterion("unit_weight =", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNotEqualTo(BigDecimal value) {
            addCriterion("unit_weight <>", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightGreaterThan(BigDecimal value) {
            addCriterion("unit_weight >", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("unit_weight >=", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightLessThan(BigDecimal value) {
            addCriterion("unit_weight <", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightLessThanOrEqualTo(BigDecimal value) {
            addCriterion("unit_weight <=", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightIn(List<BigDecimal> values) {
            addCriterion("unit_weight in", values, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNotIn(List<BigDecimal> values) {
            addCriterion("unit_weight not in", values, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unit_weight between", value1, value2, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unit_weight not between", value1, value2, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingIsNull() {
            addCriterion("material_processing is null");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingIsNotNull() {
            addCriterion("material_processing is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingEqualTo(String value) {
            addCriterion("material_processing =", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingNotEqualTo(String value) {
            addCriterion("material_processing <>", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingGreaterThan(String value) {
            addCriterion("material_processing >", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingGreaterThanOrEqualTo(String value) {
            addCriterion("material_processing >=", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingLessThan(String value) {
            addCriterion("material_processing <", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingLessThanOrEqualTo(String value) {
            addCriterion("material_processing <=", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingLike(String value) {
            addCriterion("material_processing like", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingNotLike(String value) {
            addCriterion("material_processing not like", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingIn(List<String> values) {
            addCriterion("material_processing in", values, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingNotIn(List<String> values) {
            addCriterion("material_processing not in", values, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingBetween(String value1, String value2) {
            addCriterion("material_processing between", value1, value2, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingNotBetween(String value1, String value2) {
            addCriterion("material_processing not between", value1, value2, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeIsNull() {
            addCriterion("brand_material_code is null");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeIsNotNull() {
            addCriterion("brand_material_code is not null");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeEqualTo(String value) {
            addCriterion("brand_material_code =", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeNotEqualTo(String value) {
            addCriterion("brand_material_code <>", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeGreaterThan(String value) {
            addCriterion("brand_material_code >", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeGreaterThanOrEqualTo(String value) {
            addCriterion("brand_material_code >=", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeLessThan(String value) {
            addCriterion("brand_material_code <", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeLessThanOrEqualTo(String value) {
            addCriterion("brand_material_code <=", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeLike(String value) {
            addCriterion("brand_material_code like", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeNotLike(String value) {
            addCriterion("brand_material_code not like", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeIn(List<String> values) {
            addCriterion("brand_material_code in", values, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeNotIn(List<String> values) {
            addCriterion("brand_material_code not in", values, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeBetween(String value1, String value2) {
            addCriterion("brand_material_code between", value1, value2, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeNotBetween(String value1, String value2) {
            addCriterion("brand_material_code not between", value1, value2, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("source is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("source is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(Integer value) {
            addCriterion("source =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(Integer value) {
            addCriterion("source <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(Integer value) {
            addCriterion("source >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("source >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(Integer value) {
            addCriterion("source <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(Integer value) {
            addCriterion("source <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<Integer> values) {
            addCriterion("source in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<Integer> values) {
            addCriterion("source not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(Integer value1, Integer value2) {
            addCriterion("source between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("source not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}