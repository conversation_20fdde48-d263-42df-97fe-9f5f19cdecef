package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.basedata.dto.TeamUserDto;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.crm.dto.*;
import com.midea.pam.common.statistics.entity.ReportProjectDimension;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 创建时间 2017-11-14 8:32
 *
 * <AUTHOR>
 */
@ApiModel(value = "ReportProjectDimensionDto", description = "项目维度")
public class ReportProjectDimensionDto extends ReportProjectDimension {

    private String reportDimension;

    private Integer incomeNode;

    private String typeStr;

    private String pricetTypeStr;

    private String statusStr;

    private String unitIdStr;

    private String ouIdStr;

    private String divisionIdStr;

    private String periodNameStart;

    private String periodNameEnd;

    private String managerName;

    private List<Long> types;

    private List<Integer> priceTypes;

    private List<Integer> statuses;

    private List<Long> ouIds;

    private List<Long> unitIds;

    private List<Long> divisionIds;

    private List<Long> data;

    private Long personal;

    private Long companyId;

    public String getReportDimension() {
        return reportDimension;
    }

    public void setReportDimension(String reportDimension) {
        this.reportDimension = reportDimension;
    }

    public Integer getIncomeNode() {
        return incomeNode;
    }

    public void setIncomeNode(Integer incomeNode) {
        this.incomeNode = incomeNode;
    }

    public String getTypeStr() {
        return typeStr;
    }

    public void setTypeStr(String typeStr) {
        this.typeStr = typeStr;
    }

    public String getPricetTypeStr() {
        return pricetTypeStr;
    }

    public void setPricetTypeStr(String pricetTypeStr) {
        this.pricetTypeStr = pricetTypeStr;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public String getUnitIdStr() {
        return unitIdStr;
    }

    public void setUnitIdStr(String unitIdStr) {
        this.unitIdStr = unitIdStr;
    }

    public String getOuIdStr() {
        return ouIdStr;
    }

    public void setOuIdStr(String ouIdStr) {
        this.ouIdStr = ouIdStr;
    }

    public String getDivisionIdStr() {
        return divisionIdStr;
    }

    public void setDivisionIdStr(String divisionIdStr) {
        this.divisionIdStr = divisionIdStr;
    }

    public String getPeriodNameStart() {
        return periodNameStart;
    }

    public void setPeriodNameStart(String periodNameStart) {
        this.periodNameStart = periodNameStart;
    }

    public String getPeriodNameEnd() {
        return periodNameEnd;
    }

    public void setPeriodNameEnd(String periodNameEnd) {
        this.periodNameEnd = periodNameEnd;
    }

    @Override
    public String getManagerName() {
        return managerName;
    }

    @Override
    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public List<Long> getTypes() {
        return types;
    }

    public void setTypes(List<Long> types) {
        this.types = types;
    }

    public List<Integer> getPriceTypes() {
        return priceTypes;
    }

    public void setPriceTypes(List<Integer> priceTypes) {
        this.priceTypes = priceTypes;
    }

    public List<Integer> getStatuses() {
        return statuses;
    }

    public void setStatuses(List<Integer> statuses) {
        this.statuses = statuses;
    }

    public List<Long> getOuIds() {
        return ouIds;
    }

    public void setOuIds(List<Long> ouIds) {
        this.ouIds = ouIds;
    }

    public List<Long> getUnitIds() {
        return unitIds;
    }

    public void setUnitIds(List<Long> unitIds) {
        this.unitIds = unitIds;
    }

    public List<Long> getDivisionIds() {
        return divisionIds;
    }

    public void setDivisionIds(List<Long> divisionIds) {
        this.divisionIds = divisionIds;
    }

    public List<Long> getData() {
        return data;
    }

    public void setData(List<Long> data) {
        this.data = data;
    }

    public Long getPersonal() {
        return personal;
    }

    public void setPersonal(Long personal) {
        this.personal = personal;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
