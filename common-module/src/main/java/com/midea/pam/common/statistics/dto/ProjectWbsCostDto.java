package com.midea.pam.common.statistics.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class ProjectWbsCostDto {

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "WBS")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "使用单位")
    private Long unitId;

}