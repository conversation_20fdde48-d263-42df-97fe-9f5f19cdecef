package com.midea.pam.common.statistics.dto;


import java.util.Date;
import java.util.List;

public class WorkingHourSubmitErrorTemporaryDto {
    private Long id;

    //项目编码
    private String projectCode;
    //项目名称
    private String projectName;
    //填报人姓名
    private String userName;
    //填报人MIP
    private String userMip;
    //出勤日期--开始日期
    private Date applyDateStart;
    //出勤日期--结束日期
    private Date applyDateEnd;
    //填报日期--开始日期
    private Date createAtEnd;
    //填报日期--结束日期
    private Date createAtStart;
    //工时状态
    private String statusStr;
    private List<Integer> statusList;
    //工时id
    private Long workingHourId;

    //是否开票
    private String invoiceApplyFlag;
    private List<Integer> invoiceApplyFlagList;

    //是否结转
    private String costCollectionFlag;
    private List<Integer> costCollectionFlagList;

    private Long bizUnitId;

    //业务实体
    private String ouIdStr;
    private List<Long> ouIdList;


    public Long getWorkingHourId() {
        return workingHourId;
    }

    public void setWorkingHourId(Long workingHourId) {
        this.workingHourId = workingHourId;
    }

    public Long getBizUnitId() {
        return bizUnitId;
    }

    public void setBizUnitId(Long bizUnitId) {
        this.bizUnitId = bizUnitId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserMip() {
        return userMip;
    }

    public void setUserMip(String userMip) {
        this.userMip = userMip;
    }

    public Date getApplyDateStart() {
        return applyDateStart;
    }

    public void setApplyDateStart(Date applyDateStart) {
        this.applyDateStart = applyDateStart;
    }

    public Date getApplyDateEnd() {
        return applyDateEnd;
    }

    public void setApplyDateEnd(Date applyDateEnd) {
        this.applyDateEnd = applyDateEnd;
    }

    public Date getCreateAtEnd() {
        return createAtEnd;
    }

    public void setCreateAtEnd(Date createAtEnd) {
        this.createAtEnd = createAtEnd;
    }

    public Date getCreateAtStart() {
        return createAtStart;
    }

    public void setCreateAtStart(Date createAtStart) {
        this.createAtStart = createAtStart;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getInvoiceApplyFlag() {
        return invoiceApplyFlag;
    }

    public void setInvoiceApplyFlag(String invoiceApplyFlag) {
        this.invoiceApplyFlag = invoiceApplyFlag;
    }

    public List<Integer> getInvoiceApplyFlagList() {
        return invoiceApplyFlagList;
    }

    public void setInvoiceApplyFlagList(List<Integer> invoiceApplyFlagList) {
        this.invoiceApplyFlagList = invoiceApplyFlagList;
    }

    public String getCostCollectionFlag() {
        return costCollectionFlag;
    }

    public void setCostCollectionFlag(String costCollectionFlag) {
        this.costCollectionFlag = costCollectionFlag;
    }

    public List<Integer> getCostCollectionFlagList() {
        return costCollectionFlagList;
    }

    public void setCostCollectionFlagList(List<Integer> costCollectionFlagList) {
        this.costCollectionFlagList = costCollectionFlagList;
    }

    public String getOuIdStr() {
        return ouIdStr;
    }

    public void setOuIdStr(String ouIdStr) {
        this.ouIdStr = ouIdStr;
    }

    public List<Long> getOuIdList() {
        return ouIdList;
    }

    public void setOuIdList(List<Long> ouIdList) {
        this.ouIdList = ouIdList;
    }
}
