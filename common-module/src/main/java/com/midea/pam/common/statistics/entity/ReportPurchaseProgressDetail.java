package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "采购进展报表明细表")
public class ReportPurchaseProgressDetail extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "项目业务分类")
    private String projectSort;

    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    @ApiModelProperty(value = "采购需求id")
    private Long materialPurchaseRequirementId;

    @ApiModelProperty(value = "单据类型:1：采购订单，2：采购合同")
    private Integer requirementType;

    @ApiModelProperty(value = "订单行/合同号ID")
    private Long codeId;

    @ApiModelProperty(value = "订单号/合同号")
    private String code;

    @ApiModelProperty(value = "行号")
    private Integer lineNumber;

    @ApiModelProperty(value = "物料编码-PAM编码")
    private String pamCode;

    @ApiModelProperty(value = "物料编码-ERP编码")
    private String erpCode;

    @ApiModelProperty(value = "物料描述")
    private String materielDescr;

    @ApiModelProperty(value = "物料计量单位")
    private String unit;

    @ApiModelProperty(value = "PO采购员")
    private String poBuyer;

    @ApiModelProperty(value = "需求日期")
    private Date demandDate;

    @ApiModelProperty(value = "PO供方承诺交期")
    private Date poDelivery;

    @ApiModelProperty(value = "PO行跟踪日期")
    private Date poTrack;

    @ApiModelProperty(value = "最新PO接收时间")
    private String newestPoReceive;

    @ApiModelProperty(value = "最新PO入库时间")
    private String newestPoWarehousing;

    @ApiModelProperty(value = "PO行数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "PO行取消数量")
    private BigDecimal cancelQuantity;

    @ApiModelProperty(value = "接收数量")
    private BigDecimal receiveQuantity;

    @ApiModelProperty(value = "检验后待入库数量")
    private BigDecimal checkoutWarehousingQuantity;

    @ApiModelProperty(value = "入库数量")
    private BigDecimal warehousingQuantity;

    @ApiModelProperty(value = "未送货数量")
    private BigDecimal notDeliveryQuantity;

    @ApiModelProperty(value = "待检验数量")
    private BigDecimal checkoutQuantity;

    @ApiModelProperty(value = " PO物料行到货状态（已收/未收/部分收）")
    private Integer poArrival;

    @ApiModelProperty(value = " PO行状态")
    private Integer poStatus;

    @ApiModelProperty(value = "PO行已开票数量")
    private BigDecimal invoicingQuantity;

    @ApiModelProperty(value = "PO行未开票未数量")
    private BigDecimal notInvoicingQuantity;

    @ApiModelProperty(value = " 供应商编码")
    private String vendorNum;

    @ApiModelProperty(value = " 供应商名称")
    private String vendorName;

    @ApiModelProperty(value = " 供应商地点")
    private String vendorSiteCode;

    @ApiModelProperty(value = "wbs编码")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "需求发布单号")
    private String requirementCode;

    @ApiModelProperty(value = "删除标志")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType == null ? null : projectType.trim();
    }

    public String getProjectSort() {
        return projectSort;
    }

    public void setProjectSort(String projectSort) {
        this.projectSort = projectSort == null ? null : projectSort.trim();
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public Long getMaterialPurchaseRequirementId() {
        return materialPurchaseRequirementId;
    }

    public void setMaterialPurchaseRequirementId(Long materialPurchaseRequirementId) {
        this.materialPurchaseRequirementId = materialPurchaseRequirementId;
    }

    public Integer getRequirementType() {
        return requirementType;
    }

    public void setRequirementType(Integer requirementType) {
        this.requirementType = requirementType;
    }

    public Long getCodeId() {
        return codeId;
    }

    public void setCodeId(Long codeId) {
        this.codeId = codeId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public Integer getLineNumber() {
        return lineNumber;
    }

    public void setLineNumber(Integer lineNumber) {
        this.lineNumber = lineNumber;
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode == null ? null : pamCode.trim();
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode == null ? null : erpCode.trim();
    }

    public String getMaterielDescr() {
        return materielDescr;
    }

    public void setMaterielDescr(String materielDescr) {
        this.materielDescr = materielDescr == null ? null : materielDescr.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public String getPoBuyer() {
        return poBuyer;
    }

    public void setPoBuyer(String poBuyer) {
        this.poBuyer = poBuyer == null ? null : poBuyer.trim();
    }

    public Date getDemandDate() {
        return demandDate;
    }

    public void setDemandDate(Date demandDate) {
        this.demandDate = demandDate;
    }

    public Date getPoDelivery() {
        return poDelivery;
    }

    public void setPoDelivery(Date poDelivery) {
        this.poDelivery = poDelivery;
    }

    public Date getPoTrack() {
        return poTrack;
    }

    public void setPoTrack(Date poTrack) {
        this.poTrack = poTrack;
    }

    public String getNewestPoReceive() {
        return newestPoReceive;
    }

    public void setNewestPoReceive(String newestPoReceive) {
        this.newestPoReceive = newestPoReceive == null ? null : newestPoReceive.trim();
    }

    public String getNewestPoWarehousing() {
        return newestPoWarehousing;
    }

    public void setNewestPoWarehousing(String newestPoWarehousing) {
        this.newestPoWarehousing = newestPoWarehousing == null ? null : newestPoWarehousing.trim();
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getCancelQuantity() {
        return cancelQuantity;
    }

    public void setCancelQuantity(BigDecimal cancelQuantity) {
        this.cancelQuantity = cancelQuantity;
    }

    public BigDecimal getReceiveQuantity() {
        return receiveQuantity;
    }

    public void setReceiveQuantity(BigDecimal receiveQuantity) {
        this.receiveQuantity = receiveQuantity;
    }

    public BigDecimal getCheckoutWarehousingQuantity() {
        return checkoutWarehousingQuantity;
    }

    public void setCheckoutWarehousingQuantity(BigDecimal checkoutWarehousingQuantity) {
        this.checkoutWarehousingQuantity = checkoutWarehousingQuantity;
    }

    public BigDecimal getWarehousingQuantity() {
        return warehousingQuantity;
    }

    public void setWarehousingQuantity(BigDecimal warehousingQuantity) {
        this.warehousingQuantity = warehousingQuantity;
    }

    public BigDecimal getNotDeliveryQuantity() {
        return notDeliveryQuantity;
    }

    public void setNotDeliveryQuantity(BigDecimal notDeliveryQuantity) {
        this.notDeliveryQuantity = notDeliveryQuantity;
    }

    public BigDecimal getCheckoutQuantity() {
        return checkoutQuantity;
    }

    public void setCheckoutQuantity(BigDecimal checkoutQuantity) {
        this.checkoutQuantity = checkoutQuantity;
    }

    public Integer getPoArrival() {
        return poArrival;
    }

    public void setPoArrival(Integer poArrival) {
        this.poArrival = poArrival;
    }

    public Integer getPoStatus() {
        return poStatus;
    }

    public void setPoStatus(Integer poStatus) {
        this.poStatus = poStatus;
    }

    public BigDecimal getInvoicingQuantity() {
        return invoicingQuantity;
    }

    public void setInvoicingQuantity(BigDecimal invoicingQuantity) {
        this.invoicingQuantity = invoicingQuantity;
    }

    public BigDecimal getNotInvoicingQuantity() {
        return notInvoicingQuantity;
    }

    public void setNotInvoicingQuantity(BigDecimal notInvoicingQuantity) {
        this.notInvoicingQuantity = notInvoicingQuantity;
    }

    public String getVendorNum() {
        return vendorNum;
    }

    public void setVendorNum(String vendorNum) {
        this.vendorNum = vendorNum == null ? null : vendorNum.trim();
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName == null ? null : vendorName.trim();
    }

    public String getVendorSiteCode() {
        return vendorSiteCode;
    }

    public void setVendorSiteCode(String vendorSiteCode) {
        this.vendorSiteCode = vendorSiteCode == null ? null : vendorSiteCode.trim();
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public String getRequirementCode() {
        return requirementCode;
    }

    public void setRequirementCode(String requirementCode) {
        this.requirementCode = requirementCode == null ? null : requirementCode.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectType=").append(projectType);
        sb.append(", projectSort=").append(projectSort);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", materialPurchaseRequirementId=").append(materialPurchaseRequirementId);
        sb.append(", requirementType=").append(requirementType);
        sb.append(", codeId=").append(codeId);
        sb.append(", code=").append(code);
        sb.append(", lineNumber=").append(lineNumber);
        sb.append(", pamCode=").append(pamCode);
        sb.append(", erpCode=").append(erpCode);
        sb.append(", materielDescr=").append(materielDescr);
        sb.append(", unit=").append(unit);
        sb.append(", poBuyer=").append(poBuyer);
        sb.append(", demandDate=").append(demandDate);
        sb.append(", poDelivery=").append(poDelivery);
        sb.append(", poTrack=").append(poTrack);
        sb.append(", newestPoReceive=").append(newestPoReceive);
        sb.append(", newestPoWarehousing=").append(newestPoWarehousing);
        sb.append(", quantity=").append(quantity);
        sb.append(", cancelQuantity=").append(cancelQuantity);
        sb.append(", receiveQuantity=").append(receiveQuantity);
        sb.append(", checkoutWarehousingQuantity=").append(checkoutWarehousingQuantity);
        sb.append(", warehousingQuantity=").append(warehousingQuantity);
        sb.append(", notDeliveryQuantity=").append(notDeliveryQuantity);
        sb.append(", checkoutQuantity=").append(checkoutQuantity);
        sb.append(", poArrival=").append(poArrival);
        sb.append(", poStatus=").append(poStatus);
        sb.append(", invoicingQuantity=").append(invoicingQuantity);
        sb.append(", notInvoicingQuantity=").append(notInvoicingQuantity);
        sb.append(", vendorNum=").append(vendorNum);
        sb.append(", vendorName=").append(vendorName);
        sb.append(", vendorSiteCode=").append(vendorSiteCode);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", requirementCode=").append(requirementCode);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}