package com.midea.pam.common.statistics.excelVo;

import com.midea.pam.common.util.BigDecimalUtils;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

@Getter
@Setter
public class ReportProjectWbsTransitCostExcelVo {
    @Excel(name = "序号", width = 5)
    private Integer number;

    @Excel(name = "项目号", width = 20)
    private String projectCode;

    @Excel(name = "类型", width = 20)
    private String type;

    @Excel(name = "WBS", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项", width = 15)
    private String activityCode;

    @Excel(name = "采购订单号/采购合同编号/EC单号/罚扣编号", width = 25)
    private String num;

    @Excel(name = "合同名称", width = 25)
    private String purchaseContractName;

    @Excel(name = "供应商名称/人员", width = 25)
    private String vendorName;

    @Excel(name = "物料PAM编码/时间", width = 25)
    private String pamCode;

    @Excel(name = "物料ERP编码", width = 25)
    private String erpCode;

    @Excel(name = "物料描述/角色", width = 25)
    private String materielDescr;

    @Excel(name = "下达数量/已填报待审批的工时（h）", width = 15)
    private BigDecimal orderNum;

    @Excel(name = "入库数量/标准费率（d）", width = 15)
    private BigDecimal storageCount;

    @Excel(name = "取消数量", width = 15)
    private BigDecimal cancelNum;

    @Excel(name = "折后价（不含税）", width = 15)
    private BigDecimal discountPrice;

    @Excel(name = "总价（不含税）", width = 15)
    private BigDecimal totalPrice;

    @Excel(name = "累计进度执行金额（不含税）", width = 15)
    private BigDecimal budgetExecuteAmountTotal;

    @Excel(name = "已对账工时金额（不含税）", width = 15)
    private BigDecimal billMhAmount;

    @Excel(name = "已对账费用（不含税）", width = 15)
    private BigDecimal billCostAmount;

    @Excel(name = "在途成本", width = 15)
    private BigDecimal onTheWayCost;

    @Excel(name = "采购订单创建时间/采购合同创建时间/工时创建时间/费用入账日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date dataTime;

    @Excel(name = "项目成本更新时间", width = 30)
    private String executeTime;

    public BigDecimal getOrderNum() {
        return BigDecimalUtils.scale(orderNum);
    }

    public BigDecimal getStorageCount() {
        return BigDecimalUtils.scale(storageCount);
    }

    public BigDecimal getCancelNum() {
        return BigDecimalUtils.scale(cancelNum);
    }

    public BigDecimal getDiscountPrice() {
        return BigDecimalUtils.scale(discountPrice);
    }

    public BigDecimal getTotalPrice() {
        return BigDecimalUtils.scale(totalPrice);
    }

    public BigDecimal getBudgetExecuteAmountTotal() {
        return BigDecimalUtils.scale(budgetExecuteAmountTotal);
    }

    public BigDecimal getBillMhAmount() {
        return BigDecimalUtils.scale(billMhAmount);
    }

    public BigDecimal getBillCostAmount() {
        return BigDecimalUtils.scale(billCostAmount);
    }

    public BigDecimal getOnTheWayCost() {
        return BigDecimalUtils.scale(onTheWayCost);
    }
}
