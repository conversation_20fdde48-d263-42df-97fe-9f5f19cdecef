package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(value = "wbs预算快照行表")
public class ProjectWbsBudgetVersionDetail extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "wbs预算快照头表id")
    private Long versionId;

    @ApiModelProperty(value = "wbs预算编制id")
    private Long projectWbsBudgetId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "活动事项序号")
    private String activityOrderNo;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "活动类别名称")
    private String activityName;

    @ApiModelProperty(value = "活动类别属性")
    private String activityType;

    @ApiModelProperty(value = "wbs短码")
    private String wbsFullCode;

    @ApiModelProperty(value = "wbs全码")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "最底层wbs编码")
    private String wbsLastCode;

    @ApiModelProperty(value = "动态列wbs模板规则id数组")
    private String dynamicWbsTemplateRuleIds;

    @ApiModelProperty(value = "动态列字段名数组")
    private String dynamicFields;

    @ApiModelProperty(value = "动态列编码数组")
    private String dynamicValues;

    @ApiModelProperty(value = "预算金额")
    private BigDecimal price;

    @ApiModelProperty(value = "父级id（wbs）")
    private Long parentWbsId;

    @ApiModelProperty(value = "父级id（activity）")
    private Long parentActivityId;

    @ApiModelProperty(value = "版本号")
    private Long version;

    @ApiModelProperty(value = "经济事项id")
    private Long feeTypeId;

    @ApiModelProperty(value = "经济事项名称")
    private String feeTypeName;

    @ApiModelProperty(value = "是否同步ems（0不同步/1同步）")
    private Boolean feeSyncEms;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getVersionId() {
        return versionId;
    }

    public void setVersionId(Long versionId) {
        this.versionId = versionId;
    }

    public Long getProjectWbsBudgetId() {
        return projectWbsBudgetId;
    }

    public void setProjectWbsBudgetId(Long projectWbsBudgetId) {
        this.projectWbsBudgetId = projectWbsBudgetId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getActivityOrderNo() {
        return activityOrderNo;
    }

    public void setActivityOrderNo(String activityOrderNo) {
        this.activityOrderNo = activityOrderNo == null ? null : activityOrderNo.trim();
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName == null ? null : activityName.trim();
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType == null ? null : activityType.trim();
    }

    public String getWbsFullCode() {
        return wbsFullCode;
    }

    public void setWbsFullCode(String wbsFullCode) {
        this.wbsFullCode = wbsFullCode == null ? null : wbsFullCode.trim();
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public String getWbsLastCode() {
        return wbsLastCode;
    }

    public void setWbsLastCode(String wbsLastCode) {
        this.wbsLastCode = wbsLastCode == null ? null : wbsLastCode.trim();
    }

    public String getDynamicWbsTemplateRuleIds() {
        return dynamicWbsTemplateRuleIds;
    }

    public void setDynamicWbsTemplateRuleIds(String dynamicWbsTemplateRuleIds) {
        this.dynamicWbsTemplateRuleIds = dynamicWbsTemplateRuleIds == null ? null : dynamicWbsTemplateRuleIds.trim();
    }

    public String getDynamicFields() {
        return dynamicFields;
    }

    public void setDynamicFields(String dynamicFields) {
        this.dynamicFields = dynamicFields == null ? null : dynamicFields.trim();
    }

    public String getDynamicValues() {
        return dynamicValues;
    }

    public void setDynamicValues(String dynamicValues) {
        this.dynamicValues = dynamicValues == null ? null : dynamicValues.trim();
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getParentWbsId() {
        return parentWbsId;
    }

    public void setParentWbsId(Long parentWbsId) {
        this.parentWbsId = parentWbsId;
    }

    public Long getParentActivityId() {
        return parentActivityId;
    }

    public void setParentActivityId(Long parentActivityId) {
        this.parentActivityId = parentActivityId;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Long getFeeTypeId() {
        return feeTypeId;
    }

    public void setFeeTypeId(Long feeTypeId) {
        this.feeTypeId = feeTypeId;
    }

    public String getFeeTypeName() {
        return feeTypeName;
    }

    public void setFeeTypeName(String feeTypeName) {
        this.feeTypeName = feeTypeName == null ? null : feeTypeName.trim();
    }

    public Boolean getFeeSyncEms() {
        return feeSyncEms;
    }

    public void setFeeSyncEms(Boolean feeSyncEms) {
        this.feeSyncEms = feeSyncEms;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", versionId=").append(versionId);
        sb.append(", projectWbsBudgetId=").append(projectWbsBudgetId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", description=").append(description);
        sb.append(", activityOrderNo=").append(activityOrderNo);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", activityName=").append(activityName);
        sb.append(", activityType=").append(activityType);
        sb.append(", wbsFullCode=").append(wbsFullCode);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", wbsLastCode=").append(wbsLastCode);
        sb.append(", dynamicWbsTemplateRuleIds=").append(dynamicWbsTemplateRuleIds);
        sb.append(", dynamicFields=").append(dynamicFields);
        sb.append(", dynamicValues=").append(dynamicValues);
        sb.append(", price=").append(price);
        sb.append(", parentWbsId=").append(parentWbsId);
        sb.append(", parentActivityId=").append(parentActivityId);
        sb.append(", version=").append(version);
        sb.append(", feeTypeId=").append(feeTypeId);
        sb.append(", feeTypeName=").append(feeTypeName);
        sb.append(", feeSyncEms=").append(feeSyncEms);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}