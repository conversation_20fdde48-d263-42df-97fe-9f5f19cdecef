package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProjectCostRevenueOrderRecord extends LongIdEntity implements Serializable {
    private Long id;

    private Long executeId;

    private Long summaryId;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private String orderCode;

    private Date inputDate;

    private Long milepostId;

    private String milepostName;

    private BigDecimal materialCost;

    private BigDecimal materialOutsourceCost;

    private BigDecimal innerLaborCost;

    private BigDecimal outerLaborCost;

    private BigDecimal feeCost;

    private BigDecimal otherCost;

    private BigDecimal subTotal;

    private Boolean deletedFlag;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getSummaryId() {
        return summaryId;
    }

    public void setSummaryId(Long summaryId) {
        this.summaryId = summaryId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode == null ? null : orderCode.trim();
    }

    public Date getInputDate() {
        return inputDate;
    }

    public void setInputDate(Date inputDate) {
        this.inputDate = inputDate;
    }

    public Long getMilepostId() {
        return milepostId;
    }

    public void setMilepostId(Long milepostId) {
        this.milepostId = milepostId;
    }

    public String getMilepostName() {
        return milepostName;
    }

    public void setMilepostName(String milepostName) {
        this.milepostName = milepostName == null ? null : milepostName.trim();
    }

    public BigDecimal getMaterialCost() {
        return materialCost;
    }

    public void setMaterialCost(BigDecimal materialCost) {
        this.materialCost = materialCost;
    }

    public BigDecimal getMaterialOutsourceCost() {
        return materialOutsourceCost;
    }

    public void setMaterialOutsourceCost(BigDecimal materialOutsourceCost) {
        this.materialOutsourceCost = materialOutsourceCost;
    }

    public BigDecimal getInnerLaborCost() {
        return innerLaborCost;
    }

    public void setInnerLaborCost(BigDecimal innerLaborCost) {
        this.innerLaborCost = innerLaborCost;
    }

    public BigDecimal getOuterLaborCost() {
        return outerLaborCost;
    }

    public void setOuterLaborCost(BigDecimal outerLaborCost) {
        this.outerLaborCost = outerLaborCost;
    }

    public BigDecimal getFeeCost() {
        return feeCost;
    }

    public void setFeeCost(BigDecimal feeCost) {
        this.feeCost = feeCost;
    }

    public BigDecimal getOtherCost() {
        return otherCost;
    }

    public void setOtherCost(BigDecimal otherCost) {
        this.otherCost = otherCost;
    }

    public BigDecimal getSubTotal() {
        return subTotal;
    }

    public void setSubTotal(BigDecimal subTotal) {
        this.subTotal = subTotal;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", executeId=").append(executeId);
        sb.append(", summaryId=").append(summaryId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", orderCode=").append(orderCode);
        sb.append(", inputDate=").append(inputDate);
        sb.append(", milepostId=").append(milepostId);
        sb.append(", milepostName=").append(milepostName);
        sb.append(", materialCost=").append(materialCost);
        sb.append(", materialOutsourceCost=").append(materialOutsourceCost);
        sb.append(", innerLaborCost=").append(innerLaborCost);
        sb.append(", outerLaborCost=").append(outerLaborCost);
        sb.append(", feeCost=").append(feeCost);
        sb.append(", otherCost=").append(otherCost);
        sb.append(", subTotal=").append(subTotal);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}