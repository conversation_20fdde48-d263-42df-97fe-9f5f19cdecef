package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(value = "")
public class ProjectWbsCostSummary extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "线体")
    private String lineBoby;

    @ApiModelProperty(value = "工位")
    private String station;

    @ApiModelProperty(value = "工具")
    private String tool;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "活动类别名称")
    private String activityName;

    @ApiModelProperty(value = "活动类别属性")
    private String activityType;

    @ApiModelProperty(value = "wbs编码")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "wbs短码")
    private String wbsFullCode;

    @ApiModelProperty(value = "预算金额")
    private BigDecimal price;

    @ApiModelProperty(value = "预算基线")
    private BigDecimal baselineCost;

    @ApiModelProperty(value = "需求预算")
    private BigDecimal demandCost;

    @ApiModelProperty(value = "在途成本")
    private BigDecimal onTheWayCost;

    @ApiModelProperty(value = "已发生成本")
    private BigDecimal incurredCost;

    @ApiModelProperty(value = "剩余可用预算")
    private BigDecimal remainingCost;

    @ApiModelProperty(value = "累计变更金额")
    private BigDecimal changeAccumulateCost;

    @ApiModelProperty(value = "使用单位id")
    private Long unitId;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getLineBoby() {
        return lineBoby;
    }

    public void setLineBoby(String lineBoby) {
        this.lineBoby = lineBoby == null ? null : lineBoby.trim();
    }

    public String getStation() {
        return station;
    }

    public void setStation(String station) {
        this.station = station == null ? null : station.trim();
    }

    public String getTool() {
        return tool;
    }

    public void setTool(String tool) {
        this.tool = tool == null ? null : tool.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName == null ? null : activityName.trim();
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType == null ? null : activityType.trim();
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public String getWbsFullCode() {
        return wbsFullCode;
    }

    public void setWbsFullCode(String wbsFullCode) {
        this.wbsFullCode = wbsFullCode == null ? null : wbsFullCode.trim();
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getBaselineCost() {
        return baselineCost;
    }

    public void setBaselineCost(BigDecimal baselineCost) {
        this.baselineCost = baselineCost;
    }

    public BigDecimal getDemandCost() {
        return demandCost;
    }

    public void setDemandCost(BigDecimal demandCost) {
        this.demandCost = demandCost;
    }

    public BigDecimal getOnTheWayCost() {
        return onTheWayCost;
    }

    public void setOnTheWayCost(BigDecimal onTheWayCost) {
        this.onTheWayCost = onTheWayCost;
    }

    public BigDecimal getIncurredCost() {
        return incurredCost;
    }

    public void setIncurredCost(BigDecimal incurredCost) {
        this.incurredCost = incurredCost;
    }

    public BigDecimal getRemainingCost() {
        return remainingCost;
    }

    public void setRemainingCost(BigDecimal remainingCost) {
        this.remainingCost = remainingCost;
    }

    public BigDecimal getChangeAccumulateCost() {
        return changeAccumulateCost;
    }

    public void setChangeAccumulateCost(BigDecimal changeAccumulateCost) {
        this.changeAccumulateCost = changeAccumulateCost;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", amount=").append(amount);
        sb.append(", lineBoby=").append(lineBoby);
        sb.append(", station=").append(station);
        sb.append(", tool=").append(tool);
        sb.append(", description=").append(description);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", activityName=").append(activityName);
        sb.append(", activityType=").append(activityType);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", wbsFullCode=").append(wbsFullCode);
        sb.append(", price=").append(price);
        sb.append(", baselineCost=").append(baselineCost);
        sb.append(", demandCost=").append(demandCost);
        sb.append(", onTheWayCost=").append(onTheWayCost);
        sb.append(", incurredCost=").append(incurredCost);
        sb.append(", remainingCost=").append(remainingCost);
        sb.append(", changeAccumulateCost=").append(changeAccumulateCost);
        sb.append(", unitId=").append(unitId);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}