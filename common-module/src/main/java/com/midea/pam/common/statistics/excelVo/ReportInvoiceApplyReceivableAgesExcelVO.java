package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/7/02
 * 财务口径应收报表ExcelVO
 */
public class ReportInvoiceApplyReceivableAgesExcelVO {

    @Excel(name = "子合同编号", width = 15)
    private String code;

    @Excel(name = "子合同名称", width = 30)
    private String name;

    @Excel(name = "主合同编号", width = 15)
    private String parentCode;

    @Excel(name = "主合同名称", width = 30)
    private String parentName;

    @Excel(name = "业务实体", width = 30)
    private String ouName;

    @Excel(name = "销售部门", width = 20)
    private String unitName;

    @Excel(name = "业务模式", width = 20)
    private String projectType;

    @Excel(name = "客户CRM编码", width = 30)
    private String customerCode;

    @Excel(name = "客户名称", width = 30)
    private String customerName;

    @Excel(name = "客户属性", width = 10, replace = {"外部客户_0", "内部客户_1", "_null"})
    private Integer customerType;

    @Excel(name = "项目编号", width = 15)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "项目经理", width = 15)
    private String projectManager;

    @Excel(name = "销售经理", width = 15)
    private String salesManager;

    @Excel(name = "币种", width = 10)
    private String currency;

    private BigDecimal amount;

    private BigDecimal excludingTaxAmount;

    private BigDecimal conversionRate;

    private BigDecimal standardAmount;

    private BigDecimal standardExcludingTaxAmount;

    private BigDecimal invoiceAmount;

    private BigDecimal receiptAmount;

    private BigDecimal receivableAmount;

    private BigDecimal ageAmount0;

    private BigDecimal ageAmount1;

    private BigDecimal ageAmount2;

    private BigDecimal ageAmount3;

    private BigDecimal ageAmount4;

    private BigDecimal ageAmount5;

    private BigDecimal ageAmount6;

    private BigDecimal ageAmount7;

    @Excel(name = "子合同含税金额（原币）", width = 20)
    private String amount_dt;

    @Excel(name = "子合同不含税金额（原币）", width = 20)
    private String excludingTaxAmount_dt;

    @Excel(name = "子合同汇率", width = 10)
    private String conversionRate_dt;

    @Excel(name = "子合同含税金额（本位币）", width = 20)
    private String standardAmount_dt;

    @Excel(name = "子合同不含税金额（本位币）", width = 20)
    private String standardExcludingTaxAmount_dt;

    @Excel(name = "子合同已开票金额（原币）", width = 20)
    private String invoiceAmount_dt;

    @Excel(name = "子合同已收款金额（原币）", width = 20)
    private String receiptAmount_dt;

    @Excel(name = "应收余额（原币）", width = 20)
    private String receivableAmount_dt;

    @Excel(name = "未逾期额", width = 15)
    private String ageAmount0_dt;

    @Excel(name = "0-1个月", width = 15)
    private String ageAmount1_dt;

    private String ageAmount2_dt;

    private String ageAmount3_dt;

    @Excel(name = "1-3个月", width = 15)
    private String ageAmount7_dt;

    @Excel(name = "4-6个月", width = 15)
    private String ageAmount4_dt;

    @Excel(name = "7-12个月", width = 15)
    private String ageAmount5_dt;

    @Excel(name = ">1年", width = 15)
    private String ageAmount6_dt;


    private String ruleValue;

    private String ruleName;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode == null ? null : parentCode.trim();
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName == null ? null : parentName.trim();
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType == null ? null : projectType.trim();
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode == null ? null : customerCode.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public String getSalesManager() {
        return salesManager;
    }

    public void setSalesManager(String salesManager) {
        this.salesManager = salesManager == null ? null : salesManager.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getExcludingTaxAmount() {
        return excludingTaxAmount;
    }

    public void setExcludingTaxAmount(BigDecimal excludingTaxAmount) {
        this.excludingTaxAmount = excludingTaxAmount;
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public BigDecimal getStandardAmount() {
        return standardAmount;
    }

    public void setStandardAmount(BigDecimal standardAmount) {
        this.standardAmount = standardAmount;
    }

    public BigDecimal getStandardExcludingTaxAmount() {
        return standardExcludingTaxAmount;
    }

    public void setStandardExcludingTaxAmount(BigDecimal standardExcludingTaxAmount) {
        this.standardExcludingTaxAmount = standardExcludingTaxAmount;
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public BigDecimal getReceiptAmount() {
        return receiptAmount;
    }

    public void setReceiptAmount(BigDecimal receiptAmount) {
        this.receiptAmount = receiptAmount;
    }

    public BigDecimal getReceivableAmount() {
        return receivableAmount;
    }

    public void setReceivableAmount(BigDecimal receivableAmount) {
        this.receivableAmount = receivableAmount;
    }

    public BigDecimal getAgeAmount0() {
        return ageAmount0;
    }

    public void setAgeAmount0(BigDecimal ageAmount0) {
        this.ageAmount0 = ageAmount0;
    }

    public BigDecimal getAgeAmount1() {
        return ageAmount1;
    }

    public void setAgeAmount1(BigDecimal ageAmount1) {
        this.ageAmount1 = ageAmount1;
    }

    public BigDecimal getAgeAmount2() {
        return ageAmount2;
    }

    public void setAgeAmount2(BigDecimal ageAmount2) {
        this.ageAmount2 = ageAmount2;
    }

    public BigDecimal getAgeAmount3() {
        return ageAmount3;
    }

    public void setAgeAmount3(BigDecimal ageAmount3) {
        this.ageAmount3 = ageAmount3;
    }

    public BigDecimal getAgeAmount4() {
        return ageAmount4;
    }

    public void setAgeAmount4(BigDecimal ageAmount4) {
        this.ageAmount4 = ageAmount4;
    }

    public BigDecimal getAgeAmount5() {
        return ageAmount5;
    }

    public void setAgeAmount5(BigDecimal ageAmount5) {
        this.ageAmount5 = ageAmount5;
    }

    public BigDecimal getAgeAmount6() {
        return ageAmount6;
    }

    public void setAgeAmount6(BigDecimal ageAmount6) {
        this.ageAmount6 = ageAmount6;
    }

    public String getAmount_dt() {
        if (Objects.nonNull(this.amount)) {
            return this.amount.stripTrailingZeros().toPlainString();
        } else {
            return this.amount_dt;
        }
    }

    public void setAmount_dt(String amount_dt) {
        this.amount_dt = amount_dt;
    }

    public String getExcludingTaxAmount_dt() {
        if (Objects.nonNull(this.excludingTaxAmount)) {
            return this.excludingTaxAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.excludingTaxAmount_dt;
        }
    }

    public void setExcludingTaxAmount_dt(String excludingTaxAmount_dt) {
        this.excludingTaxAmount_dt = excludingTaxAmount_dt;
    }

    public String getConversionRate_dt() {
        if (Objects.nonNull(this.conversionRate)) {
            return this.conversionRate.stripTrailingZeros().toPlainString();
        } else {
            return this.conversionRate_dt;
        }
    }

    public void setConversionRate_dt(String conversionRate_dt) {
        this.conversionRate_dt = conversionRate_dt;
    }

    public String getStandardAmount_dt() {
        if (Objects.nonNull(this.standardAmount)) {
            return this.standardAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.standardAmount_dt;
        }
    }

    public void setStandardAmount_dt(String standardAmount_dt) {
        this.standardAmount_dt = standardAmount_dt;
    }

    public String getStandardExcludingTaxAmount_dt() {
        if (Objects.nonNull(this.standardExcludingTaxAmount)) {
            return this.standardExcludingTaxAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.standardExcludingTaxAmount_dt;
        }
    }

    public void setStandardExcludingTaxAmount_dt(String standardExcludingTaxAmount_dt) {
        this.standardExcludingTaxAmount_dt = standardExcludingTaxAmount_dt;
    }

    public String getInvoiceAmount_dt() {
        if (Objects.nonNull(this.invoiceAmount)) {
            return this.invoiceAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.invoiceAmount_dt;
        }
    }

    public void setInvoiceAmount_dt(String invoiceAmount_dt) {
        this.invoiceAmount_dt = invoiceAmount_dt;
    }

    public String getReceiptAmount_dt() {
        if (Objects.nonNull(this.receiptAmount)) {
            return this.receiptAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.receiptAmount_dt;
        }
    }

    public void setReceiptAmount_dt(String receiptAmount_dt) {
        this.receiptAmount_dt = receiptAmount_dt;
    }

    public String getReceivableAmount_dt() {
        if (Objects.nonNull(this.receivableAmount)) {
            return this.receivableAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.receivableAmount_dt;
        }
    }

    public void setReceivableAmount_dt(String receivableAmount_dt) {
        this.receivableAmount_dt = receivableAmount_dt;
    }

    public String getAgeAmount0_dt() {
        if (Objects.nonNull(this.ageAmount0)) {
            return this.ageAmount0.stripTrailingZeros().toPlainString();
        } else {
            return this.ageAmount0_dt;
        }
    }

    public void setAgeAmount0_dt(String ageAmount0_dt) {
        this.ageAmount0_dt = ageAmount0_dt;
    }

    public String getAgeAmount1_dt() {
        if (Objects.nonNull(this.ageAmount1)) {
            return this.ageAmount1.stripTrailingZeros().toPlainString();
        } else {
            return this.ageAmount1_dt;
        }
    }

    public void setAgeAmount1_dt(String ageAmount1_dt) {
        this.ageAmount1_dt = ageAmount1_dt;
    }

    public String getAgeAmount2_dt() {
        if (Objects.nonNull(this.ageAmount2)) {
            return this.ageAmount2.stripTrailingZeros().toPlainString();
        } else {
            return this.ageAmount2_dt;
        }
    }

    public void setAgeAmount2_dt(String ageAmount2_dt) {
        this.ageAmount2_dt = ageAmount2_dt;
    }

    public String getAgeAmount3_dt() {
        if (Objects.nonNull(this.ageAmount3)) {
            return this.ageAmount3.stripTrailingZeros().toPlainString();
        } else {
            return this.ageAmount3_dt;
        }
    }

    public void setAgeAmount3_dt(String ageAmount3_dt) {
        this.ageAmount3_dt = ageAmount3_dt;
    }

    public String getAgeAmount4_dt() {
        if (Objects.nonNull(this.ageAmount4)) {
            return this.ageAmount4.stripTrailingZeros().toPlainString();
        } else {
            return this.ageAmount4_dt;
        }
    }

    public void setAgeAmount4_dt(String ageAmount4_dt) {
        this.ageAmount4_dt = ageAmount4_dt;
    }

    public String getAgeAmount5_dt() {
        if (Objects.nonNull(this.ageAmount5)) {
            return this.ageAmount5.stripTrailingZeros().toPlainString();
        } else {
            return this.ageAmount5_dt;
        }
    }

    public void setAgeAmount5_dt(String ageAmount5_dt) {
        this.ageAmount5_dt = ageAmount5_dt;
    }

    public String getAgeAmount6_dt() {
        if (Objects.nonNull(this.ageAmount6)) {
            return this.ageAmount6.stripTrailingZeros().toPlainString();
        } else {
            return this.ageAmount6_dt;
        }
    }

    public void setAgeAmount6_dt(String ageAmount6_dt) {
        this.ageAmount6_dt = ageAmount6_dt;
    }

    public BigDecimal getAgeAmount7() {
        return ageAmount7;
    }

    public void setAgeAmount7(BigDecimal ageAmount7) {
        this.ageAmount7 = ageAmount7;
    }

    public String getAgeAmount7_dt() {
        if (Objects.nonNull(this.ageAmount7)) {
            return this.ageAmount7.stripTrailingZeros().toPlainString();
        } else {
            return this.ageAmount7_dt;
        }
    }

    public void setAgeAmount7_dt(String ageAmount7_dt) {
        this.ageAmount7_dt = ageAmount7_dt;
    }

    public String getRuleValue() {
        return ruleValue;
    }

    public void setRuleValue(String ruleValue) {
        this.ruleValue = ruleValue;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }
}