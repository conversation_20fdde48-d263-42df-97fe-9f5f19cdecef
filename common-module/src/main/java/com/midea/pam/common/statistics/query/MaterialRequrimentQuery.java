package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/22
 */
@ApiModel(value = "MaterialRequrimentQuery", description = "缺料报表查询参数")
public class MaterialRequrimentQuery {

    private Long id;

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    @ApiModelProperty(value = "业务实体ID")
    private Long ouId;

    @ApiModelProperty(value = "库存组织ID")
    private Long organizationId;

    @ApiModelProperty(value = "项目类型ID列表")
    private List<Long> projectTypeList;

    @ApiModelProperty(value = "项目编码/名称")
    private String projectCodeOrName;

    @ApiModelProperty(value = "模组编码")
    private String moduleCode;

    @ApiModelProperty(value = "工单任务号")
    private String ticketTaskCode;

    @ApiModelProperty(value = "工单任务开始时间")
    private Date ticketPlanStartAt;

    @ApiModelProperty(value = "工单任务结束时间")
    private Date ticketPlanEndAt;

    @ApiModelProperty(value = "物料ERP编码")
    private String erpCode;

    @ApiModelProperty(value = "默认字库：MA0111，可以改")
    private String subinventoryCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public List<Long> getProjectTypeList() {
        return projectTypeList;
    }

    public void setProjectTypeList(List<Long> projectTypeList) {
        this.projectTypeList = projectTypeList;
    }

    public String getProjectCodeOrName() {
        return projectCodeOrName;
    }

    public void setProjectCodeOrName(String projectCodeOrName) {
        this.projectCodeOrName = projectCodeOrName;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getTicketTaskCode() {
        return ticketTaskCode;
    }

    public void setTicketTaskCode(String ticketTaskCode) {
        this.ticketTaskCode = ticketTaskCode;
    }

    public Date getTicketPlanStartAt() {
        return ticketPlanStartAt;
    }

    public void setTicketPlanStartAt(Date ticketPlanStartAt) {
        this.ticketPlanStartAt = ticketPlanStartAt;
    }

    public Date getTicketPlanEndAt() {
        return ticketPlanEndAt;
    }

    public void setTicketPlanEndAt(Date ticketPlanEndAt) {
        this.ticketPlanEndAt = ticketPlanEndAt;
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode;
    }

    public String getSubinventoryCode() {
        return subinventoryCode;
    }

    public void setSubinventoryCode(String subinventoryCode) {
        this.subinventoryCode = subinventoryCode;
    }

    @Override
    public String toString() {
        return "MaterialRequrimentQuery{" +
                "id=" + id +
                ", executeId=" + executeId +
                ", ouId=" + ouId +
                ", ouId=" + ouId +
                ", projectTypeList=" + projectTypeList +
                ", projectCodeOrName='" + projectCodeOrName + '\'' +
                ", moduleCode='" + moduleCode + '\'' +
                ", ticketTaskCode='" + ticketTaskCode + '\'' +
                ", ticketPlanStartAt=" + ticketPlanStartAt +
                ", ticketPlanEndAt=" + ticketPlanEndAt +
                ", erpCode='" + erpCode + '\'' +
                ", subinventoryCode='" + subinventoryCode + '\'' +
                '}';
    }
}
