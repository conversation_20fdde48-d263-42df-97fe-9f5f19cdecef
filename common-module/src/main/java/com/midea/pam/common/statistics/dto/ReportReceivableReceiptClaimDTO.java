package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.ReportReceivableReceiptClaim;

import java.util.Date;

public class ReportReceivableReceiptClaimDTO extends ReportReceivableReceiptClaim {

    private Long contractId;

    private String invoicePlanDetailIdStr;

    private Date invoicePlanDetailDate;

    private Date contractStartTime;

    private Date milePostStartTime;

    private Date milePostEndTime;

    private Date milePostActualStartTime;

    private Date milePostActualEndTime;

    private Date contractFilingDate;

    public Date getInvoicePlanDetailDate() {
        return invoicePlanDetailDate;
    }

    public void setInvoicePlanDetailDate(Date invoicePlanDetailDate) {
        this.invoicePlanDetailDate = invoicePlanDetailDate;
    }

    public Date getContractStartTime() {
        return contractStartTime;
    }

    public void setContractStartTime(Date contractStartTime) {
        this.contractStartTime = contractStartTime;
    }

    public Date getMilePostStartTime() {
        return milePostStartTime;
    }

    public void setMilePostStartTime(Date milePostStartTime) {
        this.milePostStartTime = milePostStartTime;
    }

    public Date getMilePostEndTime() {
        return milePostEndTime;
    }

    public void setMilePostEndTime(Date milePostEndTime) {
        this.milePostEndTime = milePostEndTime;
    }

    public Date getMilePostActualStartTime() {
        return milePostActualStartTime;
    }

    public void setMilePostActualStartTime(Date milePostActualStartTime) {
        this.milePostActualStartTime = milePostActualStartTime;
    }

    public Date getMilePostActualEndTime() {
        return milePostActualEndTime;
    }

    public void setMilePostActualEndTime(Date milePostActualEndTime) {
        this.milePostActualEndTime = milePostActualEndTime;
    }

    public Date getContractFilingDate() {
        return contractFilingDate;
    }

    public void setContractFilingDate(Date contractFilingDate) {
        this.contractFilingDate = contractFilingDate;
    }

    public String getInvoicePlanDetailIdStr() {
        return invoicePlanDetailIdStr;
    }

    public void setInvoicePlanDetailIdStr(String invoicePlanDetailIdStr) {
        this.invoicePlanDetailIdStr = invoicePlanDetailIdStr;
    }

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

}