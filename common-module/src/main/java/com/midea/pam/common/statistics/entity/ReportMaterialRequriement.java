package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/4/22
 */
public class ReportMaterialRequriement extends LongIdEntity implements Serializable {

    private Long id;

    private Long executeId;

    /**
     * 工单任务号
     */
    private String ticketTaskCode;

    /**
     * 项目号
     */
    private String projectCode;

    /**
     * 开始时间（工单计划开工日期）
     */
    private Date ticketPlanTime;

    /**
     * 装配件编码
     */
    private String theassemblyCode;

    /**
     * 装配件名称（装配件描述）
     */
    private String theassemblyDes;

    /**
     * 装配件单位
     */
    private String theassemblyUnit;

    /**
     * 模组编码
     */
    private String moduleCode;

    /**
     * 模组名称
     */
    private String moduleName;

    /**
     * 任务状态
     */
    private Integer taskStatus;

    /**
     * 计划数量（装配总共数量）
     */
    private BigDecimal planNum;

    /**
     * 组件编码
     */
    private String erpCode;

    /**
     * 组件名称
     */
    private String materielDescr;

    /**
     * 单位（物料单位）
     */
    private String materialUnit;

    /**
     * 供应子库
     */
    private String subinventoryCode;

    /**
     * 任务需求量（装配总共数量）
     */
    private BigDecimal taskRequirementNum;

    /**
     * 本期需求总数量（所有物料的需求数量）
     */
    private BigDecimal totalRequirementNum;

    /**
     * 已发料数量（工单任务（已发料数量-已退料数量））
     */
    private BigDecimal issuedMaterialNum;

    /**
     * 未发料数量（任务需求量-已发料数量）
     */
    private BigDecimal unissuedMaterialNum;

    /**
     * 现有量（(物料+供应子库+库存组织)库存现有量）
     */
    private BigDecimal storageCurrentNum;

    /**
     * 可用量
     */
    private BigDecimal availableNum;

    /**
     * 本任务缺料数量
     */
    private BigDecimal taskRequriementNum;

    /**
     * 本期总缺料数量（总需求-总发料-总现有量）
     */
    private BigDecimal totalRequriementNum;

    /**
     * 在途po总数
     */
    private BigDecimal deliveredPoTotalNum;

    /**
     * 发运日期（最晚日期交货时间）
     */
    private String needByDate;

    /**
     * 在途订单明细（供应商名称+ponumber+line+数量）
     */
    private String deliveredPoDetail;

    /**
     * 采购员（物料采购员）
     */
    private String buyer;

    /**
     * 物料类型
     */
    private String materialCategory;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getTicketTaskCode() {
        return ticketTaskCode;
    }

    public void setTicketTaskCode(String ticketTaskCode) {
        this.ticketTaskCode = ticketTaskCode == null ? null : ticketTaskCode.trim();
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public Date getTicketPlanTime() {
        return ticketPlanTime;
    }

    public void setTicketPlanTime(Date ticketPlanTime) {
        this.ticketPlanTime = ticketPlanTime;
    }

    public String getTheassemblyCode() {
        return theassemblyCode;
    }

    public void setTheassemblyCode(String theassemblyCode) {
        this.theassemblyCode = theassemblyCode == null ? null : theassemblyCode.trim();
    }

    public String getTheassemblyDes() {
        return theassemblyDes;
    }

    public void setTheassemblyDes(String theassemblyDes) {
        this.theassemblyDes = theassemblyDes == null ? null : theassemblyDes.trim();
    }

    public String getTheassemblyUnit() {
        return theassemblyUnit;
    }

    public void setTheassemblyUnit(String theassemblyUnit) {
        this.theassemblyUnit = theassemblyUnit == null ? null : theassemblyUnit.trim();
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode == null ? null : moduleCode.trim();
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName == null ? null : moduleName.trim();
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public BigDecimal getPlanNum() {
        return planNum;
    }

    public void setPlanNum(BigDecimal planNum) {
        this.planNum = planNum;
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode == null ? null : erpCode.trim();
    }

    public String getMaterielDescr() {
        return materielDescr;
    }

    public void setMaterielDescr(String materielDescr) {
        this.materielDescr = materielDescr == null ? null : materielDescr.trim();
    }

    public String getMaterialUnit() {
        return materialUnit;
    }

    public void setMaterialUnit(String materialUnit) {
        this.materialUnit = materialUnit == null ? null : materialUnit.trim();
    }

    public String getSubinventoryCode() {
        return subinventoryCode;
    }

    public void setSubinventoryCode(String subinventoryCode) {
        this.subinventoryCode = subinventoryCode == null ? null : subinventoryCode.trim();
    }

    public BigDecimal getTaskRequirementNum() {
        return taskRequirementNum;
    }

    public void setTaskRequirementNum(BigDecimal taskRequirementNum) {
        this.taskRequirementNum = taskRequirementNum;
    }

    public BigDecimal getTotalRequirementNum() {
        return totalRequirementNum;
    }

    public void setTotalRequirementNum(BigDecimal totalRequirementNum) {
        this.totalRequirementNum = totalRequirementNum;
    }

    public BigDecimal getIssuedMaterialNum() {
        return issuedMaterialNum;
    }

    public void setIssuedMaterialNum(BigDecimal issuedMaterialNum) {
        this.issuedMaterialNum = issuedMaterialNum;
    }

    public BigDecimal getUnissuedMaterialNum() {
        return unissuedMaterialNum;
    }

    public void setUnissuedMaterialNum(BigDecimal unissuedMaterialNum) {
        this.unissuedMaterialNum = unissuedMaterialNum;
    }

    public BigDecimal getStorageCurrentNum() {
        return storageCurrentNum;
    }

    public void setStorageCurrentNum(BigDecimal storageCurrentNum) {
        this.storageCurrentNum = storageCurrentNum;
    }

    public BigDecimal getAvailableNum() {
        return availableNum;
    }

    public void setAvailableNum(BigDecimal availableNum) {
        this.availableNum = availableNum;
    }

    public BigDecimal getTaskRequriementNum() {
        return taskRequriementNum;
    }

    public void setTaskRequriementNum(BigDecimal taskRequriementNum) {
        this.taskRequriementNum = taskRequriementNum;
    }

    public BigDecimal getTotalRequriementNum() {
        return totalRequriementNum;
    }

    public void setTotalRequriementNum(BigDecimal totalRequriementNum) {
        this.totalRequriementNum = totalRequriementNum;
    }

    public BigDecimal getDeliveredPoTotalNum() {
        return deliveredPoTotalNum;
    }

    public void setDeliveredPoTotalNum(BigDecimal deliveredPoTotalNum) {
        this.deliveredPoTotalNum = deliveredPoTotalNum;
    }

    public String getNeedByDate() {
        return needByDate;
    }

    public void setNeedByDate(String needByDate) {
        this.needByDate = needByDate == null ? null : needByDate.trim();
    }

    public String getDeliveredPoDetail() {
        return deliveredPoDetail;
    }

    public void setDeliveredPoDetail(String deliveredPoDetail) {
        this.deliveredPoDetail = deliveredPoDetail == null ? null : deliveredPoDetail.trim();
    }

    public String getBuyer() {
        return buyer;
    }

    public void setBuyer(String buyer) {
        this.buyer = buyer == null ? null : buyer.trim();
    }

    public String getMaterialCategory() {
        return materialCategory;
    }

    public void setMaterialCategory(String materialCategory) {
        this.materialCategory = materialCategory == null ? null : materialCategory.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", executeId=").append(executeId);
        sb.append(", ticketTaskCode=").append(ticketTaskCode);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", ticketPlanTime=").append(ticketPlanTime);
        sb.append(", theassemblyCode=").append(theassemblyCode);
        sb.append(", theassemblyDes=").append(theassemblyDes);
        sb.append(", theassemblyUnit=").append(theassemblyUnit);
        sb.append(", moduleCode=").append(moduleCode);
        sb.append(", moduleName=").append(moduleName);
        sb.append(", taskStatus=").append(taskStatus);
        sb.append(", planNum=").append(planNum);
        sb.append(", erpCode=").append(erpCode);
        sb.append(", materielDescr=").append(materielDescr);
        sb.append(", materialUnit=").append(materialUnit);
        sb.append(", subinventoryCode=").append(subinventoryCode);
        sb.append(", taskRequirementNum=").append(taskRequirementNum);
        sb.append(", totalRequirementNum=").append(totalRequirementNum);
        sb.append(", issuedMaterialNum=").append(issuedMaterialNum);
        sb.append(", unissuedMaterialNum=").append(unissuedMaterialNum);
        sb.append(", storageCurrentNum=").append(storageCurrentNum);
        sb.append(", availableNum=").append(availableNum);
        sb.append(", taskRequriementNum=").append(taskRequriementNum);
        sb.append(", totalRequriementNum=").append(totalRequriementNum);
        sb.append(", deliveredPoTotalNum=").append(deliveredPoTotalNum);
        sb.append(", needByDate=").append(needByDate);
        sb.append(", deliveredPoDetail=").append(deliveredPoDetail);
        sb.append(", buyer=").append(buyer);
        sb.append(", materialCategory=").append(materialCategory);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
