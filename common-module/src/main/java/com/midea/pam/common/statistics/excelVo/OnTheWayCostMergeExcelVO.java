package com.midea.pam.common.statistics.excelVo;


import com.midea.pam.common.util.BigDecimalUtils;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-12-9
 * @description 在途成本-合并
 */
@Getter
@Setter
public class OnTheWayCostMergeExcelVO {

    @Excel(name = "序号", width = 5)
    private Integer number;

    @Excel(name = "类型", width = 20)
    private String type;

    @Excel(name = "WBS", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项", width = 15)
    private String activityCode;

    @Excel(name = "采购订单号/采购合同编号/EC单号/罚扣编号", width = 25)
    private String num;

    //采购合同编号
    private String purchaseContractCode;

    //EC单号
    private String feeApplyCode;

    @Excel(name = "合同名称", width = 25)
    private String purchaseContractName;

    @Excel(name = "供应商名称/人员", width = 25)
    private String vendorName;

    //人员
    private String userMip;

    @Excel(name = "物料PAM编码/时间", width = 25)
    private String pamCode;

    //时间
    private Date applyDate;

    @Excel(name = "物料ERP编码", width = 25)
    private String erpCode;

    @Excel(name = "物料描述/角色", width = 25)
    private String materielDescr;

    //角色(工时)
    private String level;

    //角色(点工)
    private String roleName;

    @Excel(name = "下达数量/已填报待审批的工时（h）", width = 15)
    private BigDecimal orderNum;

    //已填报待审批的工时（h）
    private BigDecimal workingHours;

    @Excel(name = "入库数量/标准费率（d）", width = 15)
    private BigDecimal storageCount;

    //标准费率（d）
    private BigDecimal costMoney;

    @Excel(name = "取消数量", width = 15)
    private BigDecimal cancelNum;

    @Excel(name = "折后价（不含税）", width = 15)
    private BigDecimal discountPrice;

    @Excel(name = "总价（不含税）", width = 15)
    private BigDecimal totalPrice;

    @Excel(name = "累计进度执行金额（不含税）", width = 15)
    private BigDecimal budgetExecuteAmountTotal;

    @Excel(name = "已对账工时金额（不含税）", width = 15)
    private BigDecimal billMhAmount;

    @Excel(name = "已对账费用（不含税）", width = 15)
    private BigDecimal billCostAmount;

    @Excel(name = "在途成本", width = 15)
    private BigDecimal onTheWayCost;

    //po的未接收数量*po单价金额
    private BigDecimal poTotalAmount;

    //采购合同分配金额-进度执行金额
    private BigDecimal allocationExecuteAmount;

    //已填报待审批的工时*标准费率/8  |  总价（不含税）-本位币
    private BigDecimal totalAmount;

    //已申请未报销金额
    private BigDecimal amount;

    //点工采购合同分配金额-已对账金额
    private BigDecimal surplusAmount;

    @Excel(name = "采购订单创建时间/采购合同创建时间/工时创建时间/费用入账日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date dataTime;

    //合同创建时间
    private Date contractCreateTime;


    public BigDecimal getOrderNum() {
        return BigDecimalUtils.scale(orderNum);
    }

    public BigDecimal getStorageCount() {
        return BigDecimalUtils.scale(storageCount);
    }

    public BigDecimal getCancelNum() {
        return BigDecimalUtils.scale(cancelNum);
    }

    public BigDecimal getDiscountPrice() {
        return BigDecimalUtils.scale(discountPrice);
    }

    public BigDecimal getTotalPrice() {
        return BigDecimalUtils.scale(totalPrice);
    }

    public BigDecimal getBudgetExecuteAmountTotal() {
        return BigDecimalUtils.scale(budgetExecuteAmountTotal);
    }

    public BigDecimal getBillMhAmount() {
        return BigDecimalUtils.scale(billMhAmount);
    }

    public BigDecimal getBillCostAmount() {
        return BigDecimalUtils.scale(billCostAmount);
    }

    public BigDecimal getOnTheWayCost() {
        return BigDecimalUtils.scale(onTheWayCost);
    }

}
