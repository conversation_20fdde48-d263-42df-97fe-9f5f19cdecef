package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Description
 * Created by chenchong
 * Date 2022/09/05 16:57
 */
@Setter
@Getter
public class ReportPurchaseProgressDetailExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer number;

    @Excel(name = "单据类型", width = 15, replace = {"采购订单_1", "采购合同_2"})
    private Integer requirementType;

    @Excel(name = "订单号/合同号", width = 25)
    private String code;

    @Excel(name = "行号", width = 15)
    private Integer lineNumber;

    @Excel(name = "物料编码-PAM编码", width = 20)
    private String pamCode;

    @Excel(name = "物料编码-ERP编码", width = 20)
    private String erpCode;

    @Excel(name = "物料描述", width = 30)
    private String materielDescr;

    @Excel(name = "物料计量单位", width = 15)
    private String unit;

    @Excel(name = "PO采购员", width = 15)
    private String poBuyer;

    @Excel(name = "需求日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date demandDate;

    @Excel(name = "PO供方承诺交期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date poDelivery;

    @Excel(name = "PO行跟踪日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date poTrack;

    @Excel(name = "最新PO接收时间", width = 15)
    private String newestPoReceive;

    @Excel(name = "最新PO入库时间", width = 15)
    private String newestPoWarehousing;

    @Excel(name = "PO行数量", width = 15)
    private BigDecimal quantity;

    @Excel(name = "PO行取消数量", width = 15)
    private BigDecimal cancelQuantity;

    @Excel(name = "接收数量", width = 15)
    private BigDecimal receiveQuantity;

    @Excel(name = "检验后待入库数量", width = 15)
    private BigDecimal checkoutWarehousingQuantity;

    @Excel(name = "入库数量", width = 15)
    private BigDecimal warehousingQuantity;

    @Excel(name = "未送货数量", width = 15)
    private BigDecimal notDeliveryQuantity;

    @Excel(name = "待检验数量", width = 15)
    private BigDecimal checkoutQuantity;

    @Excel(name = " PO物料行到货状态", width = 15, replace = {"已收_0", "未收_1", "部分收_2", "_null"})
    private Integer poArrival;

    @Excel(name = " PO行状态", width = 15, replace = {"有效_0", "关闭_1"})
    private Integer poStatus;

    @Excel(name = "PO行已开票数量", width = 15)
    private BigDecimal invoicingQuantity;

    @Excel(name = "PO行未开票未数量", width = 15)
    private BigDecimal notInvoicingQuantity;

    @Excel(name = " 供应商编码", width = 15)
    private String vendorNum;

    @Excel(name = " 供应商名称", width = 30)
    private String vendorName;

    @Excel(name = " 供应商地点", width = 30)
    private String vendorSiteCode;

    @Excel(name = "wbs编码", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项编码", width = 15)
    private String activityCode;

    @Excel(name = "项目编号", width = 15)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "项目类型", width = 25)
    private String projectType;

    @Excel(name = "项目业务分类", width = 15)
    private String projectSort;

    @Excel(name = "项目经理", width = 15)
    private String projectManager;
}
