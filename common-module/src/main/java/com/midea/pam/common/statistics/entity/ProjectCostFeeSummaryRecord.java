package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProjectCostFeeSummaryRecord extends LongIdEntity implements Serializable {
    private Long id;

    private Long executeId;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private BigDecimal cost;

    private BigDecimal budget;

    private BigDecimal incurredCost;

    private BigDecimal remainderBudget;

    private BigDecimal otherCurrentTargetCost;

    private BigDecimal travelCurrentTargetCost;

    private BigDecimal incurredRatio;

    private Boolean deletedFlag;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    public BigDecimal getBudget() {
        return budget;
    }

    public void setBudget(BigDecimal budget) {
        this.budget = budget;
    }

    public BigDecimal getIncurredCost() {
        return incurredCost;
    }

    public void setIncurredCost(BigDecimal incurredCost) {
        this.incurredCost = incurredCost;
    }

    public BigDecimal getRemainderBudget() {
        return remainderBudget;
    }

    public void setRemainderBudget(BigDecimal remainderBudget) {
        this.remainderBudget = remainderBudget;
    }

    public BigDecimal getOtherCurrentTargetCost() {
        return otherCurrentTargetCost;
    }

    public void setOtherCurrentTargetCost(BigDecimal otherCurrentTargetCost) {
        this.otherCurrentTargetCost = otherCurrentTargetCost;
    }

    public BigDecimal getTravelCurrentTargetCost() {
        return travelCurrentTargetCost;
    }

    public void setTravelCurrentTargetCost(BigDecimal travelCurrentTargetCost) {
        this.travelCurrentTargetCost = travelCurrentTargetCost;
    }

    public BigDecimal getIncurredRatio() {
        return incurredRatio;
    }

    public void setIncurredRatio(BigDecimal incurredRatio) {
        this.incurredRatio = incurredRatio;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", cost=").append(cost);
        sb.append(", budget=").append(budget);
        sb.append(", incurredCost=").append(incurredCost);
        sb.append(", remainderBudget=").append(remainderBudget);
        sb.append(", otherCurrentTargetCost=").append(otherCurrentTargetCost);
        sb.append(", travelCurrentTargetCost=").append(travelCurrentTargetCost);
        sb.append(", incurredRatio=").append(incurredRatio);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}