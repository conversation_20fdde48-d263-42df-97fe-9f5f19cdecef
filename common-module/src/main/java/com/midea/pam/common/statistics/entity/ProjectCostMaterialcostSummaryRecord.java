package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProjectCostMaterialcostSummaryRecord extends LongIdEntity implements Serializable {
    private Long id;

    private Long executeId;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private BigDecimal budgetPriceTotal;

    private BigDecimal handledCost;

    private BigDecimal restBudgetPrice;

    private BigDecimal currentTargetCost;

    private BigDecimal handledCostRatio;

    private Boolean deletedFlag;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public BigDecimal getBudgetPriceTotal() {
        return budgetPriceTotal;
    }

    public void setBudgetPriceTotal(BigDecimal budgetPriceTotal) {
        this.budgetPriceTotal = budgetPriceTotal;
    }

    public BigDecimal getHandledCost() {
        return handledCost;
    }

    public void setHandledCost(BigDecimal handledCost) {
        this.handledCost = handledCost;
    }

    public BigDecimal getRestBudgetPrice() {
        return restBudgetPrice;
    }

    public void setRestBudgetPrice(BigDecimal restBudgetPrice) {
        this.restBudgetPrice = restBudgetPrice;
    }

    public BigDecimal getCurrentTargetCost() {
        return currentTargetCost;
    }

    public void setCurrentTargetCost(BigDecimal currentTargetCost) {
        this.currentTargetCost = currentTargetCost;
    }

    public BigDecimal getHandledCostRatio() {
        return handledCostRatio;
    }

    public void setHandledCostRatio(BigDecimal handledCostRatio) {
        this.handledCostRatio = handledCostRatio;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", budgetPriceTotal=").append(budgetPriceTotal);
        sb.append(", handledCost=").append(handledCost);
        sb.append(", restBudgetPrice=").append(restBudgetPrice);
        sb.append(", currentTargetCost=").append(currentTargetCost);
        sb.append(", handledCostRatio=").append(handledCostRatio);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}