package com.midea.pam.common.ctc.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ContractOriginExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ContractOriginExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andContractIdIsNull() {
            addCriterion("contract_id is null");
            return (Criteria) this;
        }

        public Criteria andContractIdIsNotNull() {
            addCriterion("contract_id is not null");
            return (Criteria) this;
        }

        public Criteria andContractIdEqualTo(Long value) {
            addCriterion("contract_id =", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdNotEqualTo(Long value) {
            addCriterion("contract_id <>", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdGreaterThan(Long value) {
            addCriterion("contract_id >", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdGreaterThanOrEqualTo(Long value) {
            addCriterion("contract_id >=", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdLessThan(Long value) {
            addCriterion("contract_id <", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdLessThanOrEqualTo(Long value) {
            addCriterion("contract_id <=", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdIn(List<Long> values) {
            addCriterion("contract_id in", values, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdNotIn(List<Long> values) {
            addCriterion("contract_id not in", values, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdBetween(Long value1, Long value2) {
            addCriterion("contract_id between", value1, value2, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdNotBetween(Long value1, Long value2) {
            addCriterion("contract_id not between", value1, value2, "contractId");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andFrameFlagIsNull() {
            addCriterion("frame_flag is null");
            return (Criteria) this;
        }

        public Criteria andFrameFlagIsNotNull() {
            addCriterion("frame_flag is not null");
            return (Criteria) this;
        }

        public Criteria andFrameFlagEqualTo(Boolean value) {
            addCriterion("frame_flag =", value, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagNotEqualTo(Boolean value) {
            addCriterion("frame_flag <>", value, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagGreaterThan(Boolean value) {
            addCriterion("frame_flag >", value, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("frame_flag >=", value, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagLessThan(Boolean value) {
            addCriterion("frame_flag <", value, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("frame_flag <=", value, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagIn(List<Boolean> values) {
            addCriterion("frame_flag in", values, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagNotIn(List<Boolean> values) {
            addCriterion("frame_flag not in", values, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("frame_flag between", value1, value2, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("frame_flag not between", value1, value2, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsIdIsNull() {
            addCriterion("legal_affairs_id is null");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsIdIsNotNull() {
            addCriterion("legal_affairs_id is not null");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsIdEqualTo(String value) {
            addCriterion("legal_affairs_id =", value, "legalAffairsId");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsIdNotEqualTo(String value) {
            addCriterion("legal_affairs_id <>", value, "legalAffairsId");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsIdGreaterThan(String value) {
            addCriterion("legal_affairs_id >", value, "legalAffairsId");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsIdGreaterThanOrEqualTo(String value) {
            addCriterion("legal_affairs_id >=", value, "legalAffairsId");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsIdLessThan(String value) {
            addCriterion("legal_affairs_id <", value, "legalAffairsId");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsIdLessThanOrEqualTo(String value) {
            addCriterion("legal_affairs_id <=", value, "legalAffairsId");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsIdLike(String value) {
            addCriterion("legal_affairs_id like", value, "legalAffairsId");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsIdNotLike(String value) {
            addCriterion("legal_affairs_id not like", value, "legalAffairsId");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsIdIn(List<String> values) {
            addCriterion("legal_affairs_id in", values, "legalAffairsId");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsIdNotIn(List<String> values) {
            addCriterion("legal_affairs_id not in", values, "legalAffairsId");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsIdBetween(String value1, String value2) {
            addCriterion("legal_affairs_id between", value1, value2, "legalAffairsId");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsIdNotBetween(String value1, String value2) {
            addCriterion("legal_affairs_id not between", value1, value2, "legalAffairsId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Long value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Long value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Long value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Long value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Long value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Long value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Long> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Long> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Long value1, Long value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Long value1, Long value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(Long value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(Long value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(Long value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(Long value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(Long value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<Long> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<Long> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(Long value1, Long value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(Long value1, Long value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Date value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Date value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Date value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Date> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Date> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andOriginalContractAnnexIsNull() {
            addCriterion("original_contract_annex is null");
            return (Criteria) this;
        }

        public Criteria andOriginalContractAnnexIsNotNull() {
            addCriterion("original_contract_annex is not null");
            return (Criteria) this;
        }

        public Criteria andOriginalContractAnnexEqualTo(String value) {
            addCriterion("original_contract_annex =", value, "originalContractAnnex");
            return (Criteria) this;
        }

        public Criteria andOriginalContractAnnexNotEqualTo(String value) {
            addCriterion("original_contract_annex <>", value, "originalContractAnnex");
            return (Criteria) this;
        }

        public Criteria andOriginalContractAnnexGreaterThan(String value) {
            addCriterion("original_contract_annex >", value, "originalContractAnnex");
            return (Criteria) this;
        }

        public Criteria andOriginalContractAnnexGreaterThanOrEqualTo(String value) {
            addCriterion("original_contract_annex >=", value, "originalContractAnnex");
            return (Criteria) this;
        }

        public Criteria andOriginalContractAnnexLessThan(String value) {
            addCriterion("original_contract_annex <", value, "originalContractAnnex");
            return (Criteria) this;
        }

        public Criteria andOriginalContractAnnexLessThanOrEqualTo(String value) {
            addCriterion("original_contract_annex <=", value, "originalContractAnnex");
            return (Criteria) this;
        }

        public Criteria andOriginalContractAnnexLike(String value) {
            addCriterion("original_contract_annex like", value, "originalContractAnnex");
            return (Criteria) this;
        }

        public Criteria andOriginalContractAnnexNotLike(String value) {
            addCriterion("original_contract_annex not like", value, "originalContractAnnex");
            return (Criteria) this;
        }

        public Criteria andOriginalContractAnnexIn(List<String> values) {
            addCriterion("original_contract_annex in", values, "originalContractAnnex");
            return (Criteria) this;
        }

        public Criteria andOriginalContractAnnexNotIn(List<String> values) {
            addCriterion("original_contract_annex not in", values, "originalContractAnnex");
            return (Criteria) this;
        }

        public Criteria andOriginalContractAnnexBetween(String value1, String value2) {
            addCriterion("original_contract_annex between", value1, value2, "originalContractAnnex");
            return (Criteria) this;
        }

        public Criteria andOriginalContractAnnexNotBetween(String value1, String value2) {
            addCriterion("original_contract_annex not between", value1, value2, "originalContractAnnex");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andAmountIsNull() {
            addCriterion("amount is null");
            return (Criteria) this;
        }

        public Criteria andAmountIsNotNull() {
            addCriterion("amount is not null");
            return (Criteria) this;
        }

        public Criteria andAmountEqualTo(BigDecimal value) {
            addCriterion("amount =", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotEqualTo(BigDecimal value) {
            addCriterion("amount <>", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThan(BigDecimal value) {
            addCriterion("amount >", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("amount >=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThan(BigDecimal value) {
            addCriterion("amount <", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("amount <=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountIn(List<BigDecimal> values) {
            addCriterion("amount in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotIn(List<BigDecimal> values) {
            addCriterion("amount not in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount not between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andCostIsNull() {
            addCriterion("cost is null");
            return (Criteria) this;
        }

        public Criteria andCostIsNotNull() {
            addCriterion("cost is not null");
            return (Criteria) this;
        }

        public Criteria andCostEqualTo(BigDecimal value) {
            addCriterion("cost =", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostNotEqualTo(BigDecimal value) {
            addCriterion("cost <>", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostGreaterThan(BigDecimal value) {
            addCriterion("cost >", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cost >=", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostLessThan(BigDecimal value) {
            addCriterion("cost <", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cost <=", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostIn(List<BigDecimal> values) {
            addCriterion("cost in", values, "cost");
            return (Criteria) this;
        }

        public Criteria andCostNotIn(List<BigDecimal> values) {
            addCriterion("cost not in", values, "cost");
            return (Criteria) this;
        }

        public Criteria andCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost between", value1, value2, "cost");
            return (Criteria) this;
        }

        public Criteria andCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost not between", value1, value2, "cost");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Long value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Long value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Long value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Long value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Long> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Long> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Long value1, Long value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andSalesManagerIsNull() {
            addCriterion("sales_manager is null");
            return (Criteria) this;
        }

        public Criteria andSalesManagerIsNotNull() {
            addCriterion("sales_manager is not null");
            return (Criteria) this;
        }

        public Criteria andSalesManagerEqualTo(Long value) {
            addCriterion("sales_manager =", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNotEqualTo(Long value) {
            addCriterion("sales_manager <>", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerGreaterThan(Long value) {
            addCriterion("sales_manager >", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerGreaterThanOrEqualTo(Long value) {
            addCriterion("sales_manager >=", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerLessThan(Long value) {
            addCriterion("sales_manager <", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerLessThanOrEqualTo(Long value) {
            addCriterion("sales_manager <=", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerIn(List<Long> values) {
            addCriterion("sales_manager in", values, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNotIn(List<Long> values) {
            addCriterion("sales_manager not in", values, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerBetween(Long value1, Long value2) {
            addCriterion("sales_manager between", value1, value2, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNotBetween(Long value1, Long value2) {
            addCriterion("sales_manager not between", value1, value2, "salesManager");
            return (Criteria) this;
        }

        public Criteria andAnnexIsNull() {
            addCriterion("annex is null");
            return (Criteria) this;
        }

        public Criteria andAnnexIsNotNull() {
            addCriterion("annex is not null");
            return (Criteria) this;
        }

        public Criteria andAnnexEqualTo(String value) {
            addCriterion("annex =", value, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexNotEqualTo(String value) {
            addCriterion("annex <>", value, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexGreaterThan(String value) {
            addCriterion("annex >", value, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexGreaterThanOrEqualTo(String value) {
            addCriterion("annex >=", value, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexLessThan(String value) {
            addCriterion("annex <", value, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexLessThanOrEqualTo(String value) {
            addCriterion("annex <=", value, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexLike(String value) {
            addCriterion("annex like", value, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexNotLike(String value) {
            addCriterion("annex not like", value, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexIn(List<String> values) {
            addCriterion("annex in", values, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexNotIn(List<String> values) {
            addCriterion("annex not in", values, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexBetween(String value1, String value2) {
            addCriterion("annex between", value1, value2, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexNotBetween(String value1, String value2) {
            addCriterion("annex not between", value1, value2, "annex");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andManagerIsNull() {
            addCriterion("manager is null");
            return (Criteria) this;
        }

        public Criteria andManagerIsNotNull() {
            addCriterion("manager is not null");
            return (Criteria) this;
        }

        public Criteria andManagerEqualTo(Long value) {
            addCriterion("manager =", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerNotEqualTo(Long value) {
            addCriterion("manager <>", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerGreaterThan(Long value) {
            addCriterion("manager >", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerGreaterThanOrEqualTo(Long value) {
            addCriterion("manager >=", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerLessThan(Long value) {
            addCriterion("manager <", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerLessThanOrEqualTo(Long value) {
            addCriterion("manager <=", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerIn(List<Long> values) {
            addCriterion("manager in", values, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerNotIn(List<Long> values) {
            addCriterion("manager not in", values, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerBetween(Long value1, Long value2) {
            addCriterion("manager between", value1, value2, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerNotBetween(Long value1, Long value2) {
            addCriterion("manager not between", value1, value2, "manager");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentIdIsNull() {
            addCriterion("profit_department_id is null");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentIdIsNotNull() {
            addCriterion("profit_department_id is not null");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentIdEqualTo(Long value) {
            addCriterion("profit_department_id =", value, "profitDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentIdNotEqualTo(Long value) {
            addCriterion("profit_department_id <>", value, "profitDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentIdGreaterThan(Long value) {
            addCriterion("profit_department_id >", value, "profitDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("profit_department_id >=", value, "profitDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentIdLessThan(Long value) {
            addCriterion("profit_department_id <", value, "profitDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentIdLessThanOrEqualTo(Long value) {
            addCriterion("profit_department_id <=", value, "profitDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentIdIn(List<Long> values) {
            addCriterion("profit_department_id in", values, "profitDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentIdNotIn(List<Long> values) {
            addCriterion("profit_department_id not in", values, "profitDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentIdBetween(Long value1, Long value2) {
            addCriterion("profit_department_id between", value1, value2, "profitDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentIdNotBetween(Long value1, Long value2) {
            addCriterion("profit_department_id not between", value1, value2, "profitDepartmentId");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentIsNull() {
            addCriterion("business_segment is null");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentIsNotNull() {
            addCriterion("business_segment is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentEqualTo(String value) {
            addCriterion("business_segment =", value, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentNotEqualTo(String value) {
            addCriterion("business_segment <>", value, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentGreaterThan(String value) {
            addCriterion("business_segment >", value, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentGreaterThanOrEqualTo(String value) {
            addCriterion("business_segment >=", value, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentLessThan(String value) {
            addCriterion("business_segment <", value, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentLessThanOrEqualTo(String value) {
            addCriterion("business_segment <=", value, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentLike(String value) {
            addCriterion("business_segment like", value, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentNotLike(String value) {
            addCriterion("business_segment not like", value, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentIn(List<String> values) {
            addCriterion("business_segment in", values, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentNotIn(List<String> values) {
            addCriterion("business_segment not in", values, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentBetween(String value1, String value2) {
            addCriterion("business_segment between", value1, value2, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentNotBetween(String value1, String value2) {
            addCriterion("business_segment not between", value1, value2, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIdIsNull() {
            addCriterion("business_type_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIdIsNotNull() {
            addCriterion("business_type_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIdEqualTo(Long value) {
            addCriterion("business_type_id =", value, "businessTypeId");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIdNotEqualTo(Long value) {
            addCriterion("business_type_id <>", value, "businessTypeId");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIdGreaterThan(Long value) {
            addCriterion("business_type_id >", value, "businessTypeId");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("business_type_id >=", value, "businessTypeId");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIdLessThan(Long value) {
            addCriterion("business_type_id <", value, "businessTypeId");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIdLessThanOrEqualTo(Long value) {
            addCriterion("business_type_id <=", value, "businessTypeId");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIdIn(List<Long> values) {
            addCriterion("business_type_id in", values, "businessTypeId");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIdNotIn(List<Long> values) {
            addCriterion("business_type_id not in", values, "businessTypeId");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIdBetween(Long value1, Long value2) {
            addCriterion("business_type_id between", value1, value2, "businessTypeId");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIdNotBetween(Long value1, Long value2) {
            addCriterion("business_type_id not between", value1, value2, "businessTypeId");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNull() {
            addCriterion("parent_id is null");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNotNull() {
            addCriterion("parent_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentIdEqualTo(Long value) {
            addCriterion("parent_id =", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotEqualTo(Long value) {
            addCriterion("parent_id <>", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThan(Long value) {
            addCriterion("parent_id >", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("parent_id >=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThan(Long value) {
            addCriterion("parent_id <", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThanOrEqualTo(Long value) {
            addCriterion("parent_id <=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdIn(List<Long> values) {
            addCriterion("parent_id in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotIn(List<Long> values) {
            addCriterion("parent_id not in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdBetween(Long value1, Long value2) {
            addCriterion("parent_id between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotBetween(Long value1, Long value2) {
            addCriterion("parent_id not between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andFrameContractIdIsNull() {
            addCriterion("frame_contract_id is null");
            return (Criteria) this;
        }

        public Criteria andFrameContractIdIsNotNull() {
            addCriterion("frame_contract_id is not null");
            return (Criteria) this;
        }

        public Criteria andFrameContractIdEqualTo(Long value) {
            addCriterion("frame_contract_id =", value, "frameContractId");
            return (Criteria) this;
        }

        public Criteria andFrameContractIdNotEqualTo(Long value) {
            addCriterion("frame_contract_id <>", value, "frameContractId");
            return (Criteria) this;
        }

        public Criteria andFrameContractIdGreaterThan(Long value) {
            addCriterion("frame_contract_id >", value, "frameContractId");
            return (Criteria) this;
        }

        public Criteria andFrameContractIdGreaterThanOrEqualTo(Long value) {
            addCriterion("frame_contract_id >=", value, "frameContractId");
            return (Criteria) this;
        }

        public Criteria andFrameContractIdLessThan(Long value) {
            addCriterion("frame_contract_id <", value, "frameContractId");
            return (Criteria) this;
        }

        public Criteria andFrameContractIdLessThanOrEqualTo(Long value) {
            addCriterion("frame_contract_id <=", value, "frameContractId");
            return (Criteria) this;
        }

        public Criteria andFrameContractIdIn(List<Long> values) {
            addCriterion("frame_contract_id in", values, "frameContractId");
            return (Criteria) this;
        }

        public Criteria andFrameContractIdNotIn(List<Long> values) {
            addCriterion("frame_contract_id not in", values, "frameContractId");
            return (Criteria) this;
        }

        public Criteria andFrameContractIdBetween(Long value1, Long value2) {
            addCriterion("frame_contract_id between", value1, value2, "frameContractId");
            return (Criteria) this;
        }

        public Criteria andFrameContractIdNotBetween(Long value1, Long value2) {
            addCriterion("frame_contract_id not between", value1, value2, "frameContractId");
            return (Criteria) this;
        }

        public Criteria andIndustryIsNull() {
            addCriterion("industry is null");
            return (Criteria) this;
        }

        public Criteria andIndustryIsNotNull() {
            addCriterion("industry is not null");
            return (Criteria) this;
        }

        public Criteria andIndustryEqualTo(Long value) {
            addCriterion("industry =", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotEqualTo(Long value) {
            addCriterion("industry <>", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryGreaterThan(Long value) {
            addCriterion("industry >", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryGreaterThanOrEqualTo(Long value) {
            addCriterion("industry >=", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLessThan(Long value) {
            addCriterion("industry <", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLessThanOrEqualTo(Long value) {
            addCriterion("industry <=", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryIn(List<Long> values) {
            addCriterion("industry in", values, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotIn(List<Long> values) {
            addCriterion("industry not in", values, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryBetween(Long value1, Long value2) {
            addCriterion("industry between", value1, value2, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotBetween(Long value1, Long value2) {
            addCriterion("industry not between", value1, value2, "industry");
            return (Criteria) this;
        }

        public Criteria andSigningCenterIsNull() {
            addCriterion("signing_center is null");
            return (Criteria) this;
        }

        public Criteria andSigningCenterIsNotNull() {
            addCriterion("signing_center is not null");
            return (Criteria) this;
        }

        public Criteria andSigningCenterEqualTo(Long value) {
            addCriterion("signing_center =", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterNotEqualTo(Long value) {
            addCriterion("signing_center <>", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterGreaterThan(Long value) {
            addCriterion("signing_center >", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterGreaterThanOrEqualTo(Long value) {
            addCriterion("signing_center >=", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLessThan(Long value) {
            addCriterion("signing_center <", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLessThanOrEqualTo(Long value) {
            addCriterion("signing_center <=", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterIn(List<Long> values) {
            addCriterion("signing_center in", values, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterNotIn(List<Long> values) {
            addCriterion("signing_center not in", values, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterBetween(Long value1, Long value2) {
            addCriterion("signing_center between", value1, value2, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterNotBetween(Long value1, Long value2) {
            addCriterion("signing_center not between", value1, value2, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andWhetherCustomerConfirmIsNull() {
            addCriterion("whether_customer_confirm is null");
            return (Criteria) this;
        }

        public Criteria andWhetherCustomerConfirmIsNotNull() {
            addCriterion("whether_customer_confirm is not null");
            return (Criteria) this;
        }

        public Criteria andWhetherCustomerConfirmEqualTo(Boolean value) {
            addCriterion("whether_customer_confirm =", value, "whetherCustomerConfirm");
            return (Criteria) this;
        }

        public Criteria andWhetherCustomerConfirmNotEqualTo(Boolean value) {
            addCriterion("whether_customer_confirm <>", value, "whetherCustomerConfirm");
            return (Criteria) this;
        }

        public Criteria andWhetherCustomerConfirmGreaterThan(Boolean value) {
            addCriterion("whether_customer_confirm >", value, "whetherCustomerConfirm");
            return (Criteria) this;
        }

        public Criteria andWhetherCustomerConfirmGreaterThanOrEqualTo(Boolean value) {
            addCriterion("whether_customer_confirm >=", value, "whetherCustomerConfirm");
            return (Criteria) this;
        }

        public Criteria andWhetherCustomerConfirmLessThan(Boolean value) {
            addCriterion("whether_customer_confirm <", value, "whetherCustomerConfirm");
            return (Criteria) this;
        }

        public Criteria andWhetherCustomerConfirmLessThanOrEqualTo(Boolean value) {
            addCriterion("whether_customer_confirm <=", value, "whetherCustomerConfirm");
            return (Criteria) this;
        }

        public Criteria andWhetherCustomerConfirmIn(List<Boolean> values) {
            addCriterion("whether_customer_confirm in", values, "whetherCustomerConfirm");
            return (Criteria) this;
        }

        public Criteria andWhetherCustomerConfirmNotIn(List<Boolean> values) {
            addCriterion("whether_customer_confirm not in", values, "whetherCustomerConfirm");
            return (Criteria) this;
        }

        public Criteria andWhetherCustomerConfirmBetween(Boolean value1, Boolean value2) {
            addCriterion("whether_customer_confirm between", value1, value2, "whetherCustomerConfirm");
            return (Criteria) this;
        }

        public Criteria andWhetherCustomerConfirmNotBetween(Boolean value1, Boolean value2) {
            addCriterion("whether_customer_confirm not between", value1, value2, "whetherCustomerConfirm");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeIsNull() {
            addCriterion("notsync_type is null");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeIsNotNull() {
            addCriterion("notsync_type is not null");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeEqualTo(String value) {
            addCriterion("notsync_type =", value, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeNotEqualTo(String value) {
            addCriterion("notsync_type <>", value, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeGreaterThan(String value) {
            addCriterion("notsync_type >", value, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeGreaterThanOrEqualTo(String value) {
            addCriterion("notsync_type >=", value, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeLessThan(String value) {
            addCriterion("notsync_type <", value, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeLessThanOrEqualTo(String value) {
            addCriterion("notsync_type <=", value, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeLike(String value) {
            addCriterion("notsync_type like", value, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeNotLike(String value) {
            addCriterion("notsync_type not like", value, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeIn(List<String> values) {
            addCriterion("notsync_type in", values, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeNotIn(List<String> values) {
            addCriterion("notsync_type not in", values, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeBetween(String value1, String value2) {
            addCriterion("notsync_type between", value1, value2, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeNotBetween(String value1, String value2) {
            addCriterion("notsync_type not between", value1, value2, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonIsNull() {
            addCriterion("notsync_reason is null");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonIsNotNull() {
            addCriterion("notsync_reason is not null");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonEqualTo(String value) {
            addCriterion("notsync_reason =", value, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonNotEqualTo(String value) {
            addCriterion("notsync_reason <>", value, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonGreaterThan(String value) {
            addCriterion("notsync_reason >", value, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonGreaterThanOrEqualTo(String value) {
            addCriterion("notsync_reason >=", value, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonLessThan(String value) {
            addCriterion("notsync_reason <", value, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonLessThanOrEqualTo(String value) {
            addCriterion("notsync_reason <=", value, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonLike(String value) {
            addCriterion("notsync_reason like", value, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonNotLike(String value) {
            addCriterion("notsync_reason not like", value, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonIn(List<String> values) {
            addCriterion("notsync_reason in", values, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonNotIn(List<String> values) {
            addCriterion("notsync_reason not in", values, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonBetween(String value1, String value2) {
            addCriterion("notsync_reason between", value1, value2, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonNotBetween(String value1, String value2) {
            addCriterion("notsync_reason not between", value1, value2, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNull() {
            addCriterion("customer_code is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNotNull() {
            addCriterion("customer_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeEqualTo(String value) {
            addCriterion("customer_code =", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotEqualTo(String value) {
            addCriterion("customer_code <>", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThan(String value) {
            addCriterion("customer_code >", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("customer_code >=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThan(String value) {
            addCriterion("customer_code <", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThanOrEqualTo(String value) {
            addCriterion("customer_code <=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLike(String value) {
            addCriterion("customer_code like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotLike(String value) {
            addCriterion("customer_code not like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIn(List<String> values) {
            addCriterion("customer_code in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotIn(List<String> values) {
            addCriterion("customer_code not in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBetween(String value1, String value2) {
            addCriterion("customer_code between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotBetween(String value1, String value2) {
            addCriterion("customer_code not between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andFilingDateIsNull() {
            addCriterion("filing_date is null");
            return (Criteria) this;
        }

        public Criteria andFilingDateIsNotNull() {
            addCriterion("filing_date is not null");
            return (Criteria) this;
        }

        public Criteria andFilingDateEqualTo(Date value) {
            addCriterion("filing_date =", value, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateNotEqualTo(Date value) {
            addCriterion("filing_date <>", value, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateGreaterThan(Date value) {
            addCriterion("filing_date >", value, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateGreaterThanOrEqualTo(Date value) {
            addCriterion("filing_date >=", value, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateLessThan(Date value) {
            addCriterion("filing_date <", value, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateLessThanOrEqualTo(Date value) {
            addCriterion("filing_date <=", value, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateIn(List<Date> values) {
            addCriterion("filing_date in", values, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateNotIn(List<Date> values) {
            addCriterion("filing_date not in", values, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateBetween(Date value1, Date value2) {
            addCriterion("filing_date between", value1, value2, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateNotBetween(Date value1, Date value2) {
            addCriterion("filing_date not between", value1, value2, "filingDate");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountIsNull() {
            addCriterion("excluding_tax_amount is null");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountIsNotNull() {
            addCriterion("excluding_tax_amount is not null");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountEqualTo(BigDecimal value) {
            addCriterion("excluding_tax_amount =", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountNotEqualTo(BigDecimal value) {
            addCriterion("excluding_tax_amount <>", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountGreaterThan(BigDecimal value) {
            addCriterion("excluding_tax_amount >", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("excluding_tax_amount >=", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountLessThan(BigDecimal value) {
            addCriterion("excluding_tax_amount <", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("excluding_tax_amount <=", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountIn(List<BigDecimal> values) {
            addCriterion("excluding_tax_amount in", values, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountNotIn(List<BigDecimal> values) {
            addCriterion("excluding_tax_amount not in", values, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("excluding_tax_amount between", value1, value2, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("excluding_tax_amount not between", value1, value2, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIsNull() {
            addCriterion("change_reason is null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIsNotNull() {
            addCriterion("change_reason is not null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonEqualTo(String value) {
            addCriterion("change_reason =", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotEqualTo(String value) {
            addCriterion("change_reason <>", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonGreaterThan(String value) {
            addCriterion("change_reason >", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonGreaterThanOrEqualTo(String value) {
            addCriterion("change_reason >=", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLessThan(String value) {
            addCriterion("change_reason <", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLessThanOrEqualTo(String value) {
            addCriterion("change_reason <=", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLike(String value) {
            addCriterion("change_reason like", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotLike(String value) {
            addCriterion("change_reason not like", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIn(List<String> values) {
            addCriterion("change_reason in", values, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotIn(List<String> values) {
            addCriterion("change_reason not in", values, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonBetween(String value1, String value2) {
            addCriterion("change_reason between", value1, value2, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotBetween(String value1, String value2) {
            addCriterion("change_reason not between", value1, value2, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangewayIdIsNull() {
            addCriterion("changeway_id is null");
            return (Criteria) this;
        }

        public Criteria andChangewayIdIsNotNull() {
            addCriterion("changeway_id is not null");
            return (Criteria) this;
        }

        public Criteria andChangewayIdEqualTo(Long value) {
            addCriterion("changeway_id =", value, "changewayId");
            return (Criteria) this;
        }

        public Criteria andChangewayIdNotEqualTo(Long value) {
            addCriterion("changeway_id <>", value, "changewayId");
            return (Criteria) this;
        }

        public Criteria andChangewayIdGreaterThan(Long value) {
            addCriterion("changeway_id >", value, "changewayId");
            return (Criteria) this;
        }

        public Criteria andChangewayIdGreaterThanOrEqualTo(Long value) {
            addCriterion("changeway_id >=", value, "changewayId");
            return (Criteria) this;
        }

        public Criteria andChangewayIdLessThan(Long value) {
            addCriterion("changeway_id <", value, "changewayId");
            return (Criteria) this;
        }

        public Criteria andChangewayIdLessThanOrEqualTo(Long value) {
            addCriterion("changeway_id <=", value, "changewayId");
            return (Criteria) this;
        }

        public Criteria andChangewayIdIn(List<Long> values) {
            addCriterion("changeway_id in", values, "changewayId");
            return (Criteria) this;
        }

        public Criteria andChangewayIdNotIn(List<Long> values) {
            addCriterion("changeway_id not in", values, "changewayId");
            return (Criteria) this;
        }

        public Criteria andChangewayIdBetween(Long value1, Long value2) {
            addCriterion("changeway_id between", value1, value2, "changewayId");
            return (Criteria) this;
        }

        public Criteria andChangewayIdNotBetween(Long value1, Long value2) {
            addCriterion("changeway_id not between", value1, value2, "changewayId");
            return (Criteria) this;
        }

        public Criteria andEampurchaseIdIsNull() {
            addCriterion("eampurchase_id is null");
            return (Criteria) this;
        }

        public Criteria andEampurchaseIdIsNotNull() {
            addCriterion("eampurchase_id is not null");
            return (Criteria) this;
        }

        public Criteria andEampurchaseIdEqualTo(String value) {
            addCriterion("eampurchase_id =", value, "eampurchaseId");
            return (Criteria) this;
        }

        public Criteria andEampurchaseIdNotEqualTo(String value) {
            addCriterion("eampurchase_id <>", value, "eampurchaseId");
            return (Criteria) this;
        }

        public Criteria andEampurchaseIdGreaterThan(String value) {
            addCriterion("eampurchase_id >", value, "eampurchaseId");
            return (Criteria) this;
        }

        public Criteria andEampurchaseIdGreaterThanOrEqualTo(String value) {
            addCriterion("eampurchase_id >=", value, "eampurchaseId");
            return (Criteria) this;
        }

        public Criteria andEampurchaseIdLessThan(String value) {
            addCriterion("eampurchase_id <", value, "eampurchaseId");
            return (Criteria) this;
        }

        public Criteria andEampurchaseIdLessThanOrEqualTo(String value) {
            addCriterion("eampurchase_id <=", value, "eampurchaseId");
            return (Criteria) this;
        }

        public Criteria andEampurchaseIdLike(String value) {
            addCriterion("eampurchase_id like", value, "eampurchaseId");
            return (Criteria) this;
        }

        public Criteria andEampurchaseIdNotLike(String value) {
            addCriterion("eampurchase_id not like", value, "eampurchaseId");
            return (Criteria) this;
        }

        public Criteria andEampurchaseIdIn(List<String> values) {
            addCriterion("eampurchase_id in", values, "eampurchaseId");
            return (Criteria) this;
        }

        public Criteria andEampurchaseIdNotIn(List<String> values) {
            addCriterion("eampurchase_id not in", values, "eampurchaseId");
            return (Criteria) this;
        }

        public Criteria andEampurchaseIdBetween(String value1, String value2) {
            addCriterion("eampurchase_id between", value1, value2, "eampurchaseId");
            return (Criteria) this;
        }

        public Criteria andEampurchaseIdNotBetween(String value1, String value2) {
            addCriterion("eampurchase_id not between", value1, value2, "eampurchaseId");
            return (Criteria) this;
        }

        public Criteria andInvalidIdIsNull() {
            addCriterion("invalid_id is null");
            return (Criteria) this;
        }

        public Criteria andInvalidIdIsNotNull() {
            addCriterion("invalid_id is not null");
            return (Criteria) this;
        }

        public Criteria andInvalidIdEqualTo(Long value) {
            addCriterion("invalid_id =", value, "invalidId");
            return (Criteria) this;
        }

        public Criteria andInvalidIdNotEqualTo(Long value) {
            addCriterion("invalid_id <>", value, "invalidId");
            return (Criteria) this;
        }

        public Criteria andInvalidIdGreaterThan(Long value) {
            addCriterion("invalid_id >", value, "invalidId");
            return (Criteria) this;
        }

        public Criteria andInvalidIdGreaterThanOrEqualTo(Long value) {
            addCriterion("invalid_id >=", value, "invalidId");
            return (Criteria) this;
        }

        public Criteria andInvalidIdLessThan(Long value) {
            addCriterion("invalid_id <", value, "invalidId");
            return (Criteria) this;
        }

        public Criteria andInvalidIdLessThanOrEqualTo(Long value) {
            addCriterion("invalid_id <=", value, "invalidId");
            return (Criteria) this;
        }

        public Criteria andInvalidIdIn(List<Long> values) {
            addCriterion("invalid_id in", values, "invalidId");
            return (Criteria) this;
        }

        public Criteria andInvalidIdNotIn(List<Long> values) {
            addCriterion("invalid_id not in", values, "invalidId");
            return (Criteria) this;
        }

        public Criteria andInvalidIdBetween(Long value1, Long value2) {
            addCriterion("invalid_id between", value1, value2, "invalidId");
            return (Criteria) this;
        }

        public Criteria andInvalidIdNotBetween(Long value1, Long value2) {
            addCriterion("invalid_id not between", value1, value2, "invalidId");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeIsNull() {
            addCriterion("invalid_time is null");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeIsNotNull() {
            addCriterion("invalid_time is not null");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeEqualTo(Date value) {
            addCriterion("invalid_time =", value, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeNotEqualTo(Date value) {
            addCriterion("invalid_time <>", value, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeGreaterThan(Date value) {
            addCriterion("invalid_time >", value, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("invalid_time >=", value, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeLessThan(Date value) {
            addCriterion("invalid_time <", value, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeLessThanOrEqualTo(Date value) {
            addCriterion("invalid_time <=", value, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeIn(List<Date> values) {
            addCriterion("invalid_time in", values, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeNotIn(List<Date> values) {
            addCriterion("invalid_time not in", values, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeBetween(Date value1, Date value2) {
            addCriterion("invalid_time between", value1, value2, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeNotBetween(Date value1, Date value2) {
            addCriterion("invalid_time not between", value1, value2, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidReasonIsNull() {
            addCriterion("invalid_reason is null");
            return (Criteria) this;
        }

        public Criteria andInvalidReasonIsNotNull() {
            addCriterion("invalid_reason is not null");
            return (Criteria) this;
        }

        public Criteria andInvalidReasonEqualTo(String value) {
            addCriterion("invalid_reason =", value, "invalidReason");
            return (Criteria) this;
        }

        public Criteria andInvalidReasonNotEqualTo(String value) {
            addCriterion("invalid_reason <>", value, "invalidReason");
            return (Criteria) this;
        }

        public Criteria andInvalidReasonGreaterThan(String value) {
            addCriterion("invalid_reason >", value, "invalidReason");
            return (Criteria) this;
        }

        public Criteria andInvalidReasonGreaterThanOrEqualTo(String value) {
            addCriterion("invalid_reason >=", value, "invalidReason");
            return (Criteria) this;
        }

        public Criteria andInvalidReasonLessThan(String value) {
            addCriterion("invalid_reason <", value, "invalidReason");
            return (Criteria) this;
        }

        public Criteria andInvalidReasonLessThanOrEqualTo(String value) {
            addCriterion("invalid_reason <=", value, "invalidReason");
            return (Criteria) this;
        }

        public Criteria andInvalidReasonLike(String value) {
            addCriterion("invalid_reason like", value, "invalidReason");
            return (Criteria) this;
        }

        public Criteria andInvalidReasonNotLike(String value) {
            addCriterion("invalid_reason not like", value, "invalidReason");
            return (Criteria) this;
        }

        public Criteria andInvalidReasonIn(List<String> values) {
            addCriterion("invalid_reason in", values, "invalidReason");
            return (Criteria) this;
        }

        public Criteria andInvalidReasonNotIn(List<String> values) {
            addCriterion("invalid_reason not in", values, "invalidReason");
            return (Criteria) this;
        }

        public Criteria andInvalidReasonBetween(String value1, String value2) {
            addCriterion("invalid_reason between", value1, value2, "invalidReason");
            return (Criteria) this;
        }

        public Criteria andInvalidReasonNotBetween(String value1, String value2) {
            addCriterion("invalid_reason not between", value1, value2, "invalidReason");
            return (Criteria) this;
        }

        public Criteria andConversionTypeIsNull() {
            addCriterion("conversion_type is null");
            return (Criteria) this;
        }

        public Criteria andConversionTypeIsNotNull() {
            addCriterion("conversion_type is not null");
            return (Criteria) this;
        }

        public Criteria andConversionTypeEqualTo(String value) {
            addCriterion("conversion_type =", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeNotEqualTo(String value) {
            addCriterion("conversion_type <>", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeGreaterThan(String value) {
            addCriterion("conversion_type >", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeGreaterThanOrEqualTo(String value) {
            addCriterion("conversion_type >=", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeLessThan(String value) {
            addCriterion("conversion_type <", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeLessThanOrEqualTo(String value) {
            addCriterion("conversion_type <=", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeLike(String value) {
            addCriterion("conversion_type like", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeNotLike(String value) {
            addCriterion("conversion_type not like", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeIn(List<String> values) {
            addCriterion("conversion_type in", values, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeNotIn(List<String> values) {
            addCriterion("conversion_type not in", values, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeBetween(String value1, String value2) {
            addCriterion("conversion_type between", value1, value2, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeNotBetween(String value1, String value2) {
            addCriterion("conversion_type not between", value1, value2, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionDateIsNull() {
            addCriterion("conversion_date is null");
            return (Criteria) this;
        }

        public Criteria andConversionDateIsNotNull() {
            addCriterion("conversion_date is not null");
            return (Criteria) this;
        }

        public Criteria andConversionDateEqualTo(Date value) {
            addCriterion("conversion_date =", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateNotEqualTo(Date value) {
            addCriterion("conversion_date <>", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateGreaterThan(Date value) {
            addCriterion("conversion_date >", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateGreaterThanOrEqualTo(Date value) {
            addCriterion("conversion_date >=", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateLessThan(Date value) {
            addCriterion("conversion_date <", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateLessThanOrEqualTo(Date value) {
            addCriterion("conversion_date <=", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateIn(List<Date> values) {
            addCriterion("conversion_date in", values, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateNotIn(List<Date> values) {
            addCriterion("conversion_date not in", values, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateBetween(Date value1, Date value2) {
            addCriterion("conversion_date between", value1, value2, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateNotBetween(Date value1, Date value2) {
            addCriterion("conversion_date not between", value1, value2, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionRateIsNull() {
            addCriterion("conversion_rate is null");
            return (Criteria) this;
        }

        public Criteria andConversionRateIsNotNull() {
            addCriterion("conversion_rate is not null");
            return (Criteria) this;
        }

        public Criteria andConversionRateEqualTo(BigDecimal value) {
            addCriterion("conversion_rate =", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotEqualTo(BigDecimal value) {
            addCriterion("conversion_rate <>", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateGreaterThan(BigDecimal value) {
            addCriterion("conversion_rate >", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("conversion_rate >=", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateLessThan(BigDecimal value) {
            addCriterion("conversion_rate <", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("conversion_rate <=", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateIn(List<BigDecimal> values) {
            addCriterion("conversion_rate in", values, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotIn(List<BigDecimal> values) {
            addCriterion("conversion_rate not in", values, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("conversion_rate between", value1, value2, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("conversion_rate not between", value1, value2, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andClassesIsNull() {
            addCriterion("classes is null");
            return (Criteria) this;
        }

        public Criteria andClassesIsNotNull() {
            addCriterion("classes is not null");
            return (Criteria) this;
        }

        public Criteria andClassesEqualTo(Integer value) {
            addCriterion("classes =", value, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesNotEqualTo(Integer value) {
            addCriterion("classes <>", value, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesGreaterThan(Integer value) {
            addCriterion("classes >", value, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesGreaterThanOrEqualTo(Integer value) {
            addCriterion("classes >=", value, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesLessThan(Integer value) {
            addCriterion("classes <", value, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesLessThanOrEqualTo(Integer value) {
            addCriterion("classes <=", value, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesIn(List<Integer> values) {
            addCriterion("classes in", values, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesNotIn(List<Integer> values) {
            addCriterion("classes not in", values, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesBetween(Integer value1, Integer value2) {
            addCriterion("classes between", value1, value2, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesNotBetween(Integer value1, Integer value2) {
            addCriterion("classes not between", value1, value2, "classes");
            return (Criteria) this;
        }

        public Criteria andBelongAreaIsNull() {
            addCriterion("belong_area is null");
            return (Criteria) this;
        }

        public Criteria andBelongAreaIsNotNull() {
            addCriterion("belong_area is not null");
            return (Criteria) this;
        }

        public Criteria andBelongAreaEqualTo(Integer value) {
            addCriterion("belong_area =", value, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaNotEqualTo(Integer value) {
            addCriterion("belong_area <>", value, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaGreaterThan(Integer value) {
            addCriterion("belong_area >", value, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaGreaterThanOrEqualTo(Integer value) {
            addCriterion("belong_area >=", value, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaLessThan(Integer value) {
            addCriterion("belong_area <", value, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaLessThanOrEqualTo(Integer value) {
            addCriterion("belong_area <=", value, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaIn(List<Integer> values) {
            addCriterion("belong_area in", values, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaNotIn(List<Integer> values) {
            addCriterion("belong_area not in", values, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaBetween(Integer value1, Integer value2) {
            addCriterion("belong_area between", value1, value2, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaNotBetween(Integer value1, Integer value2) {
            addCriterion("belong_area not between", value1, value2, "belongArea");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIsNull() {
            addCriterion("payment_method is null");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIsNotNull() {
            addCriterion("payment_method is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodEqualTo(String value) {
            addCriterion("payment_method =", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotEqualTo(String value) {
            addCriterion("payment_method <>", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodGreaterThan(String value) {
            addCriterion("payment_method >", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodGreaterThanOrEqualTo(String value) {
            addCriterion("payment_method >=", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodLessThan(String value) {
            addCriterion("payment_method <", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodLessThanOrEqualTo(String value) {
            addCriterion("payment_method <=", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodLike(String value) {
            addCriterion("payment_method like", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotLike(String value) {
            addCriterion("payment_method not like", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIn(List<String> values) {
            addCriterion("payment_method in", values, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotIn(List<String> values) {
            addCriterion("payment_method not in", values, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodBetween(String value1, String value2) {
            addCriterion("payment_method between", value1, value2, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotBetween(String value1, String value2) {
            addCriterion("payment_method not between", value1, value2, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIsNull() {
            addCriterion("invoice_type is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIsNotNull() {
            addCriterion("invoice_type is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeEqualTo(Integer value) {
            addCriterion("invoice_type =", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotEqualTo(Integer value) {
            addCriterion("invoice_type <>", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeGreaterThan(Integer value) {
            addCriterion("invoice_type >", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("invoice_type >=", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeLessThan(Integer value) {
            addCriterion("invoice_type <", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeLessThanOrEqualTo(Integer value) {
            addCriterion("invoice_type <=", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIn(List<Integer> values) {
            addCriterion("invoice_type in", values, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotIn(List<Integer> values) {
            addCriterion("invoice_type not in", values, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeBetween(Integer value1, Integer value2) {
            addCriterion("invoice_type between", value1, value2, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("invoice_type not between", value1, value2, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractIsNull() {
            addCriterion("is_electronic_contract is null");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractIsNotNull() {
            addCriterion("is_electronic_contract is not null");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractEqualTo(Boolean value) {
            addCriterion("is_electronic_contract =", value, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractNotEqualTo(Boolean value) {
            addCriterion("is_electronic_contract <>", value, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractGreaterThan(Boolean value) {
            addCriterion("is_electronic_contract >", value, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_electronic_contract >=", value, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractLessThan(Boolean value) {
            addCriterion("is_electronic_contract <", value, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractLessThanOrEqualTo(Boolean value) {
            addCriterion("is_electronic_contract <=", value, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractIn(List<Boolean> values) {
            addCriterion("is_electronic_contract in", values, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractNotIn(List<Boolean> values) {
            addCriterion("is_electronic_contract not in", values, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractBetween(Boolean value1, Boolean value2) {
            addCriterion("is_electronic_contract between", value1, value2, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_electronic_contract not between", value1, value2, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andOtherNameIsNull() {
            addCriterion("other_name is null");
            return (Criteria) this;
        }

        public Criteria andOtherNameIsNotNull() {
            addCriterion("other_name is not null");
            return (Criteria) this;
        }

        public Criteria andOtherNameEqualTo(String value) {
            addCriterion("other_name =", value, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameNotEqualTo(String value) {
            addCriterion("other_name <>", value, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameGreaterThan(String value) {
            addCriterion("other_name >", value, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameGreaterThanOrEqualTo(String value) {
            addCriterion("other_name >=", value, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameLessThan(String value) {
            addCriterion("other_name <", value, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameLessThanOrEqualTo(String value) {
            addCriterion("other_name <=", value, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameLike(String value) {
            addCriterion("other_name like", value, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameNotLike(String value) {
            addCriterion("other_name not like", value, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameIn(List<String> values) {
            addCriterion("other_name in", values, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameNotIn(List<String> values) {
            addCriterion("other_name not in", values, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameBetween(String value1, String value2) {
            addCriterion("other_name between", value1, value2, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameNotBetween(String value1, String value2) {
            addCriterion("other_name not between", value1, value2, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherIdIsNull() {
            addCriterion("other_id is null");
            return (Criteria) this;
        }

        public Criteria andOtherIdIsNotNull() {
            addCriterion("other_id is not null");
            return (Criteria) this;
        }

        public Criteria andOtherIdEqualTo(String value) {
            addCriterion("other_id =", value, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdNotEqualTo(String value) {
            addCriterion("other_id <>", value, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdGreaterThan(String value) {
            addCriterion("other_id >", value, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdGreaterThanOrEqualTo(String value) {
            addCriterion("other_id >=", value, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdLessThan(String value) {
            addCriterion("other_id <", value, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdLessThanOrEqualTo(String value) {
            addCriterion("other_id <=", value, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdLike(String value) {
            addCriterion("other_id like", value, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdNotLike(String value) {
            addCriterion("other_id not like", value, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdIn(List<String> values) {
            addCriterion("other_id in", values, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdNotIn(List<String> values) {
            addCriterion("other_id not in", values, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdBetween(String value1, String value2) {
            addCriterion("other_id between", value1, value2, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdNotBetween(String value1, String value2) {
            addCriterion("other_id not between", value1, value2, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneIsNull() {
            addCriterion("other_phone is null");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneIsNotNull() {
            addCriterion("other_phone is not null");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneEqualTo(String value) {
            addCriterion("other_phone =", value, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneNotEqualTo(String value) {
            addCriterion("other_phone <>", value, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneGreaterThan(String value) {
            addCriterion("other_phone >", value, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("other_phone >=", value, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneLessThan(String value) {
            addCriterion("other_phone <", value, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneLessThanOrEqualTo(String value) {
            addCriterion("other_phone <=", value, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneLike(String value) {
            addCriterion("other_phone like", value, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneNotLike(String value) {
            addCriterion("other_phone not like", value, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneIn(List<String> values) {
            addCriterion("other_phone in", values, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneNotIn(List<String> values) {
            addCriterion("other_phone not in", values, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneBetween(String value1, String value2) {
            addCriterion("other_phone between", value1, value2, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneNotBetween(String value1, String value2) {
            addCriterion("other_phone not between", value1, value2, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherEmailIsNull() {
            addCriterion("other_email is null");
            return (Criteria) this;
        }

        public Criteria andOtherEmailIsNotNull() {
            addCriterion("other_email is not null");
            return (Criteria) this;
        }

        public Criteria andOtherEmailEqualTo(String value) {
            addCriterion("other_email =", value, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailNotEqualTo(String value) {
            addCriterion("other_email <>", value, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailGreaterThan(String value) {
            addCriterion("other_email >", value, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailGreaterThanOrEqualTo(String value) {
            addCriterion("other_email >=", value, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailLessThan(String value) {
            addCriterion("other_email <", value, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailLessThanOrEqualTo(String value) {
            addCriterion("other_email <=", value, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailLike(String value) {
            addCriterion("other_email like", value, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailNotLike(String value) {
            addCriterion("other_email not like", value, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailIn(List<String> values) {
            addCriterion("other_email in", values, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailNotIn(List<String> values) {
            addCriterion("other_email not in", values, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailBetween(String value1, String value2) {
            addCriterion("other_email between", value1, value2, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailNotBetween(String value1, String value2) {
            addCriterion("other_email not between", value1, value2, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateIsNull() {
            addCriterion("public_or_private is null");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateIsNotNull() {
            addCriterion("public_or_private is not null");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateEqualTo(Integer value) {
            addCriterion("public_or_private =", value, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateNotEqualTo(Integer value) {
            addCriterion("public_or_private <>", value, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateGreaterThan(Integer value) {
            addCriterion("public_or_private >", value, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateGreaterThanOrEqualTo(Integer value) {
            addCriterion("public_or_private >=", value, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateLessThan(Integer value) {
            addCriterion("public_or_private <", value, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateLessThanOrEqualTo(Integer value) {
            addCriterion("public_or_private <=", value, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateIn(List<Integer> values) {
            addCriterion("public_or_private in", values, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateNotIn(List<Integer> values) {
            addCriterion("public_or_private not in", values, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateBetween(Integer value1, Integer value2) {
            addCriterion("public_or_private between", value1, value2, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateNotBetween(Integer value1, Integer value2) {
            addCriterion("public_or_private not between", value1, value2, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andSealCategoryIsNull() {
            addCriterion("seal_category is null");
            return (Criteria) this;
        }

        public Criteria andSealCategoryIsNotNull() {
            addCriterion("seal_category is not null");
            return (Criteria) this;
        }

        public Criteria andSealCategoryEqualTo(Integer value) {
            addCriterion("seal_category =", value, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryNotEqualTo(Integer value) {
            addCriterion("seal_category <>", value, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryGreaterThan(Integer value) {
            addCriterion("seal_category >", value, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryGreaterThanOrEqualTo(Integer value) {
            addCriterion("seal_category >=", value, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryLessThan(Integer value) {
            addCriterion("seal_category <", value, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryLessThanOrEqualTo(Integer value) {
            addCriterion("seal_category <=", value, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryIn(List<Integer> values) {
            addCriterion("seal_category in", values, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryNotIn(List<Integer> values) {
            addCriterion("seal_category not in", values, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryBetween(Integer value1, Integer value2) {
            addCriterion("seal_category between", value1, value2, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryNotBetween(Integer value1, Integer value2) {
            addCriterion("seal_category not between", value1, value2, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsIsNull() {
            addCriterion("seal_admin_account_ids is null");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsIsNotNull() {
            addCriterion("seal_admin_account_ids is not null");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsEqualTo(String value) {
            addCriterion("seal_admin_account_ids =", value, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsNotEqualTo(String value) {
            addCriterion("seal_admin_account_ids <>", value, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsGreaterThan(String value) {
            addCriterion("seal_admin_account_ids >", value, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsGreaterThanOrEqualTo(String value) {
            addCriterion("seal_admin_account_ids >=", value, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsLessThan(String value) {
            addCriterion("seal_admin_account_ids <", value, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsLessThanOrEqualTo(String value) {
            addCriterion("seal_admin_account_ids <=", value, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsLike(String value) {
            addCriterion("seal_admin_account_ids like", value, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsNotLike(String value) {
            addCriterion("seal_admin_account_ids not like", value, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsIn(List<String> values) {
            addCriterion("seal_admin_account_ids in", values, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsNotIn(List<String> values) {
            addCriterion("seal_admin_account_ids not in", values, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsBetween(String value1, String value2) {
            addCriterion("seal_admin_account_ids between", value1, value2, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsNotBetween(String value1, String value2) {
            addCriterion("seal_admin_account_ids not between", value1, value2, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andIfUploadChangeFileIsNull() {
            addCriterion("if_upload_change_file is null");
            return (Criteria) this;
        }

        public Criteria andIfUploadChangeFileIsNotNull() {
            addCriterion("if_upload_change_file is not null");
            return (Criteria) this;
        }

        public Criteria andIfUploadChangeFileEqualTo(String value) {
            addCriterion("if_upload_change_file =", value, "ifUploadChangeFile");
            return (Criteria) this;
        }

        public Criteria andIfUploadChangeFileNotEqualTo(String value) {
            addCriterion("if_upload_change_file <>", value, "ifUploadChangeFile");
            return (Criteria) this;
        }

        public Criteria andIfUploadChangeFileGreaterThan(String value) {
            addCriterion("if_upload_change_file >", value, "ifUploadChangeFile");
            return (Criteria) this;
        }

        public Criteria andIfUploadChangeFileGreaterThanOrEqualTo(String value) {
            addCriterion("if_upload_change_file >=", value, "ifUploadChangeFile");
            return (Criteria) this;
        }

        public Criteria andIfUploadChangeFileLessThan(String value) {
            addCriterion("if_upload_change_file <", value, "ifUploadChangeFile");
            return (Criteria) this;
        }

        public Criteria andIfUploadChangeFileLessThanOrEqualTo(String value) {
            addCriterion("if_upload_change_file <=", value, "ifUploadChangeFile");
            return (Criteria) this;
        }

        public Criteria andIfUploadChangeFileLike(String value) {
            addCriterion("if_upload_change_file like", value, "ifUploadChangeFile");
            return (Criteria) this;
        }

        public Criteria andIfUploadChangeFileNotLike(String value) {
            addCriterion("if_upload_change_file not like", value, "ifUploadChangeFile");
            return (Criteria) this;
        }

        public Criteria andIfUploadChangeFileIn(List<String> values) {
            addCriterion("if_upload_change_file in", values, "ifUploadChangeFile");
            return (Criteria) this;
        }

        public Criteria andIfUploadChangeFileNotIn(List<String> values) {
            addCriterion("if_upload_change_file not in", values, "ifUploadChangeFile");
            return (Criteria) this;
        }

        public Criteria andIfUploadChangeFileBetween(String value1, String value2) {
            addCriterion("if_upload_change_file between", value1, value2, "ifUploadChangeFile");
            return (Criteria) this;
        }

        public Criteria andIfUploadChangeFileNotBetween(String value1, String value2) {
            addCriterion("if_upload_change_file not between", value1, value2, "ifUploadChangeFile");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdIsNull() {
            addCriterion("legal_business_id is null");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdIsNotNull() {
            addCriterion("legal_business_id is not null");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdEqualTo(String value) {
            addCriterion("legal_business_id =", value, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdNotEqualTo(String value) {
            addCriterion("legal_business_id <>", value, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdGreaterThan(String value) {
            addCriterion("legal_business_id >", value, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdGreaterThanOrEqualTo(String value) {
            addCriterion("legal_business_id >=", value, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdLessThan(String value) {
            addCriterion("legal_business_id <", value, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdLessThanOrEqualTo(String value) {
            addCriterion("legal_business_id <=", value, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdLike(String value) {
            addCriterion("legal_business_id like", value, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdNotLike(String value) {
            addCriterion("legal_business_id not like", value, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdIn(List<String> values) {
            addCriterion("legal_business_id in", values, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdNotIn(List<String> values) {
            addCriterion("legal_business_id not in", values, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdBetween(String value1, String value2) {
            addCriterion("legal_business_id between", value1, value2, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdNotBetween(String value1, String value2) {
            addCriterion("legal_business_id not between", value1, value2, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumIsNull() {
            addCriterion("legal_contract_num is null");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumIsNotNull() {
            addCriterion("legal_contract_num is not null");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumEqualTo(String value) {
            addCriterion("legal_contract_num =", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumNotEqualTo(String value) {
            addCriterion("legal_contract_num <>", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumGreaterThan(String value) {
            addCriterion("legal_contract_num >", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumGreaterThanOrEqualTo(String value) {
            addCriterion("legal_contract_num >=", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumLessThan(String value) {
            addCriterion("legal_contract_num <", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumLessThanOrEqualTo(String value) {
            addCriterion("legal_contract_num <=", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumLike(String value) {
            addCriterion("legal_contract_num like", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumNotLike(String value) {
            addCriterion("legal_contract_num not like", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumIn(List<String> values) {
            addCriterion("legal_contract_num in", values, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumNotIn(List<String> values) {
            addCriterion("legal_contract_num not in", values, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumBetween(String value1, String value2) {
            addCriterion("legal_contract_num between", value1, value2, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumNotBetween(String value1, String value2) {
            addCriterion("legal_contract_num not between", value1, value2, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andImportFlagIsNull() {
            addCriterion("import_flag is null");
            return (Criteria) this;
        }

        public Criteria andImportFlagIsNotNull() {
            addCriterion("import_flag is not null");
            return (Criteria) this;
        }

        public Criteria andImportFlagEqualTo(Boolean value) {
            addCriterion("import_flag =", value, "importFlag");
            return (Criteria) this;
        }

        public Criteria andImportFlagNotEqualTo(Boolean value) {
            addCriterion("import_flag <>", value, "importFlag");
            return (Criteria) this;
        }

        public Criteria andImportFlagGreaterThan(Boolean value) {
            addCriterion("import_flag >", value, "importFlag");
            return (Criteria) this;
        }

        public Criteria andImportFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("import_flag >=", value, "importFlag");
            return (Criteria) this;
        }

        public Criteria andImportFlagLessThan(Boolean value) {
            addCriterion("import_flag <", value, "importFlag");
            return (Criteria) this;
        }

        public Criteria andImportFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("import_flag <=", value, "importFlag");
            return (Criteria) this;
        }

        public Criteria andImportFlagIn(List<Boolean> values) {
            addCriterion("import_flag in", values, "importFlag");
            return (Criteria) this;
        }

        public Criteria andImportFlagNotIn(List<Boolean> values) {
            addCriterion("import_flag not in", values, "importFlag");
            return (Criteria) this;
        }

        public Criteria andImportFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("import_flag between", value1, value2, "importFlag");
            return (Criteria) this;
        }

        public Criteria andImportFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("import_flag not between", value1, value2, "importFlag");
            return (Criteria) this;
        }

        public Criteria andApprovalDateIsNull() {
            addCriterion("approval_date is null");
            return (Criteria) this;
        }

        public Criteria andApprovalDateIsNotNull() {
            addCriterion("approval_date is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalDateEqualTo(Date value) {
            addCriterion("approval_date =", value, "approvalDate");
            return (Criteria) this;
        }

        public Criteria andApprovalDateNotEqualTo(Date value) {
            addCriterion("approval_date <>", value, "approvalDate");
            return (Criteria) this;
        }

        public Criteria andApprovalDateGreaterThan(Date value) {
            addCriterion("approval_date >", value, "approvalDate");
            return (Criteria) this;
        }

        public Criteria andApprovalDateGreaterThanOrEqualTo(Date value) {
            addCriterion("approval_date >=", value, "approvalDate");
            return (Criteria) this;
        }

        public Criteria andApprovalDateLessThan(Date value) {
            addCriterion("approval_date <", value, "approvalDate");
            return (Criteria) this;
        }

        public Criteria andApprovalDateLessThanOrEqualTo(Date value) {
            addCriterion("approval_date <=", value, "approvalDate");
            return (Criteria) this;
        }

        public Criteria andApprovalDateIn(List<Date> values) {
            addCriterion("approval_date in", values, "approvalDate");
            return (Criteria) this;
        }

        public Criteria andApprovalDateNotIn(List<Date> values) {
            addCriterion("approval_date not in", values, "approvalDate");
            return (Criteria) this;
        }

        public Criteria andApprovalDateBetween(Date value1, Date value2) {
            addCriterion("approval_date between", value1, value2, "approvalDate");
            return (Criteria) this;
        }

        public Criteria andApprovalDateNotBetween(Date value1, Date value2) {
            addCriterion("approval_date not between", value1, value2, "approvalDate");
            return (Criteria) this;
        }

        public Criteria andCollectionConfigurationIdIsNull() {
            addCriterion("collection_configuration_id is null");
            return (Criteria) this;
        }

        public Criteria andCollectionConfigurationIdIsNotNull() {
            addCriterion("collection_configuration_id is not null");
            return (Criteria) this;
        }

        public Criteria andCollectionConfigurationIdEqualTo(String value) {
            addCriterion("collection_configuration_id =", value, "collectionConfigurationId");
            return (Criteria) this;
        }

        public Criteria andCollectionConfigurationIdNotEqualTo(String value) {
            addCriterion("collection_configuration_id <>", value, "collectionConfigurationId");
            return (Criteria) this;
        }

        public Criteria andCollectionConfigurationIdGreaterThan(String value) {
            addCriterion("collection_configuration_id >", value, "collectionConfigurationId");
            return (Criteria) this;
        }

        public Criteria andCollectionConfigurationIdGreaterThanOrEqualTo(String value) {
            addCriterion("collection_configuration_id >=", value, "collectionConfigurationId");
            return (Criteria) this;
        }

        public Criteria andCollectionConfigurationIdLessThan(String value) {
            addCriterion("collection_configuration_id <", value, "collectionConfigurationId");
            return (Criteria) this;
        }

        public Criteria andCollectionConfigurationIdLessThanOrEqualTo(String value) {
            addCriterion("collection_configuration_id <=", value, "collectionConfigurationId");
            return (Criteria) this;
        }

        public Criteria andCollectionConfigurationIdLike(String value) {
            addCriterion("collection_configuration_id like", value, "collectionConfigurationId");
            return (Criteria) this;
        }

        public Criteria andCollectionConfigurationIdNotLike(String value) {
            addCriterion("collection_configuration_id not like", value, "collectionConfigurationId");
            return (Criteria) this;
        }

        public Criteria andCollectionConfigurationIdIn(List<String> values) {
            addCriterion("collection_configuration_id in", values, "collectionConfigurationId");
            return (Criteria) this;
        }

        public Criteria andCollectionConfigurationIdNotIn(List<String> values) {
            addCriterion("collection_configuration_id not in", values, "collectionConfigurationId");
            return (Criteria) this;
        }

        public Criteria andCollectionConfigurationIdBetween(String value1, String value2) {
            addCriterion("collection_configuration_id between", value1, value2, "collectionConfigurationId");
            return (Criteria) this;
        }

        public Criteria andCollectionConfigurationIdNotBetween(String value1, String value2) {
            addCriterion("collection_configuration_id not between", value1, value2, "collectionConfigurationId");
            return (Criteria) this;
        }

        public Criteria andIsDoubleChapterContractIsNull() {
            addCriterion("is_double_chapter_contract is null");
            return (Criteria) this;
        }

        public Criteria andIsDoubleChapterContractIsNotNull() {
            addCriterion("is_double_chapter_contract is not null");
            return (Criteria) this;
        }

        public Criteria andIsDoubleChapterContractEqualTo(Boolean value) {
            addCriterion("is_double_chapter_contract =", value, "isDoubleChapterContract");
            return (Criteria) this;
        }

        public Criteria andIsDoubleChapterContractNotEqualTo(Boolean value) {
            addCriterion("is_double_chapter_contract <>", value, "isDoubleChapterContract");
            return (Criteria) this;
        }

        public Criteria andIsDoubleChapterContractGreaterThan(Boolean value) {
            addCriterion("is_double_chapter_contract >", value, "isDoubleChapterContract");
            return (Criteria) this;
        }

        public Criteria andIsDoubleChapterContractGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_double_chapter_contract >=", value, "isDoubleChapterContract");
            return (Criteria) this;
        }

        public Criteria andIsDoubleChapterContractLessThan(Boolean value) {
            addCriterion("is_double_chapter_contract <", value, "isDoubleChapterContract");
            return (Criteria) this;
        }

        public Criteria andIsDoubleChapterContractLessThanOrEqualTo(Boolean value) {
            addCriterion("is_double_chapter_contract <=", value, "isDoubleChapterContract");
            return (Criteria) this;
        }

        public Criteria andIsDoubleChapterContractIn(List<Boolean> values) {
            addCriterion("is_double_chapter_contract in", values, "isDoubleChapterContract");
            return (Criteria) this;
        }

        public Criteria andIsDoubleChapterContractNotIn(List<Boolean> values) {
            addCriterion("is_double_chapter_contract not in", values, "isDoubleChapterContract");
            return (Criteria) this;
        }

        public Criteria andIsDoubleChapterContractBetween(Boolean value1, Boolean value2) {
            addCriterion("is_double_chapter_contract between", value1, value2, "isDoubleChapterContract");
            return (Criteria) this;
        }

        public Criteria andIsDoubleChapterContractNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_double_chapter_contract not between", value1, value2, "isDoubleChapterContract");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagIsNull() {
            addCriterion("is_synchronize_legal_system_flag is null");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagIsNotNull() {
            addCriterion("is_synchronize_legal_system_flag is not null");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagEqualTo(Boolean value) {
            addCriterion("is_synchronize_legal_system_flag =", value, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagNotEqualTo(Boolean value) {
            addCriterion("is_synchronize_legal_system_flag <>", value, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagGreaterThan(Boolean value) {
            addCriterion("is_synchronize_legal_system_flag >", value, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_synchronize_legal_system_flag >=", value, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagLessThan(Boolean value) {
            addCriterion("is_synchronize_legal_system_flag <", value, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("is_synchronize_legal_system_flag <=", value, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagIn(List<Boolean> values) {
            addCriterion("is_synchronize_legal_system_flag in", values, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagNotIn(List<Boolean> values) {
            addCriterion("is_synchronize_legal_system_flag not in", values, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("is_synchronize_legal_system_flag between", value1, value2, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_synchronize_legal_system_flag not between", value1, value2, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIfWatermarkingIsNull() {
            addCriterion("if_watermarking is null");
            return (Criteria) this;
        }

        public Criteria andIfWatermarkingIsNotNull() {
            addCriterion("if_watermarking is not null");
            return (Criteria) this;
        }

        public Criteria andIfWatermarkingEqualTo(Boolean value) {
            addCriterion("if_watermarking =", value, "ifWatermarking");
            return (Criteria) this;
        }

        public Criteria andIfWatermarkingNotEqualTo(Boolean value) {
            addCriterion("if_watermarking <>", value, "ifWatermarking");
            return (Criteria) this;
        }

        public Criteria andIfWatermarkingGreaterThan(Boolean value) {
            addCriterion("if_watermarking >", value, "ifWatermarking");
            return (Criteria) this;
        }

        public Criteria andIfWatermarkingGreaterThanOrEqualTo(Boolean value) {
            addCriterion("if_watermarking >=", value, "ifWatermarking");
            return (Criteria) this;
        }

        public Criteria andIfWatermarkingLessThan(Boolean value) {
            addCriterion("if_watermarking <", value, "ifWatermarking");
            return (Criteria) this;
        }

        public Criteria andIfWatermarkingLessThanOrEqualTo(Boolean value) {
            addCriterion("if_watermarking <=", value, "ifWatermarking");
            return (Criteria) this;
        }

        public Criteria andIfWatermarkingIn(List<Boolean> values) {
            addCriterion("if_watermarking in", values, "ifWatermarking");
            return (Criteria) this;
        }

        public Criteria andIfWatermarkingNotIn(List<Boolean> values) {
            addCriterion("if_watermarking not in", values, "ifWatermarking");
            return (Criteria) this;
        }

        public Criteria andIfWatermarkingBetween(Boolean value1, Boolean value2) {
            addCriterion("if_watermarking between", value1, value2, "ifWatermarking");
            return (Criteria) this;
        }

        public Criteria andIfWatermarkingNotBetween(Boolean value1, Boolean value2) {
            addCriterion("if_watermarking not between", value1, value2, "ifWatermarking");
            return (Criteria) this;
        }

        public Criteria andNoWatermarkingReasonIsNull() {
            addCriterion("no_watermarking_reason is null");
            return (Criteria) this;
        }

        public Criteria andNoWatermarkingReasonIsNotNull() {
            addCriterion("no_watermarking_reason is not null");
            return (Criteria) this;
        }

        public Criteria andNoWatermarkingReasonEqualTo(String value) {
            addCriterion("no_watermarking_reason =", value, "noWatermarkingReason");
            return (Criteria) this;
        }

        public Criteria andNoWatermarkingReasonNotEqualTo(String value) {
            addCriterion("no_watermarking_reason <>", value, "noWatermarkingReason");
            return (Criteria) this;
        }

        public Criteria andNoWatermarkingReasonGreaterThan(String value) {
            addCriterion("no_watermarking_reason >", value, "noWatermarkingReason");
            return (Criteria) this;
        }

        public Criteria andNoWatermarkingReasonGreaterThanOrEqualTo(String value) {
            addCriterion("no_watermarking_reason >=", value, "noWatermarkingReason");
            return (Criteria) this;
        }

        public Criteria andNoWatermarkingReasonLessThan(String value) {
            addCriterion("no_watermarking_reason <", value, "noWatermarkingReason");
            return (Criteria) this;
        }

        public Criteria andNoWatermarkingReasonLessThanOrEqualTo(String value) {
            addCriterion("no_watermarking_reason <=", value, "noWatermarkingReason");
            return (Criteria) this;
        }

        public Criteria andNoWatermarkingReasonLike(String value) {
            addCriterion("no_watermarking_reason like", value, "noWatermarkingReason");
            return (Criteria) this;
        }

        public Criteria andNoWatermarkingReasonNotLike(String value) {
            addCriterion("no_watermarking_reason not like", value, "noWatermarkingReason");
            return (Criteria) this;
        }

        public Criteria andNoWatermarkingReasonIn(List<String> values) {
            addCriterion("no_watermarking_reason in", values, "noWatermarkingReason");
            return (Criteria) this;
        }

        public Criteria andNoWatermarkingReasonNotIn(List<String> values) {
            addCriterion("no_watermarking_reason not in", values, "noWatermarkingReason");
            return (Criteria) this;
        }

        public Criteria andNoWatermarkingReasonBetween(String value1, String value2) {
            addCriterion("no_watermarking_reason between", value1, value2, "noWatermarkingReason");
            return (Criteria) this;
        }

        public Criteria andNoWatermarkingReasonNotBetween(String value1, String value2) {
            addCriterion("no_watermarking_reason not between", value1, value2, "noWatermarkingReason");
            return (Criteria) this;
        }

        public Criteria andProcessGrossProfitRatioIsNull() {
            addCriterion("process_gross_profit_ratio is null");
            return (Criteria) this;
        }

        public Criteria andProcessGrossProfitRatioIsNotNull() {
            addCriterion("process_gross_profit_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andProcessGrossProfitRatioEqualTo(BigDecimal value) {
            addCriterion("process_gross_profit_ratio =", value, "processGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andProcessGrossProfitRatioNotEqualTo(BigDecimal value) {
            addCriterion("process_gross_profit_ratio <>", value, "processGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andProcessGrossProfitRatioGreaterThan(BigDecimal value) {
            addCriterion("process_gross_profit_ratio >", value, "processGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andProcessGrossProfitRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("process_gross_profit_ratio >=", value, "processGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andProcessGrossProfitRatioLessThan(BigDecimal value) {
            addCriterion("process_gross_profit_ratio <", value, "processGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andProcessGrossProfitRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("process_gross_profit_ratio <=", value, "processGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andProcessGrossProfitRatioIn(List<BigDecimal> values) {
            addCriterion("process_gross_profit_ratio in", values, "processGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andProcessGrossProfitRatioNotIn(List<BigDecimal> values) {
            addCriterion("process_gross_profit_ratio not in", values, "processGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andProcessGrossProfitRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("process_gross_profit_ratio between", value1, value2, "processGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andProcessGrossProfitRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("process_gross_profit_ratio not between", value1, value2, "processGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andClientContractNumberIsNull() {
            addCriterion("client_contract_number is null");
            return (Criteria) this;
        }

        public Criteria andClientContractNumberIsNotNull() {
            addCriterion("client_contract_number is not null");
            return (Criteria) this;
        }

        public Criteria andClientContractNumberEqualTo(String value) {
            addCriterion("client_contract_number =", value, "clientContractNumber");
            return (Criteria) this;
        }

        public Criteria andClientContractNumberNotEqualTo(String value) {
            addCriterion("client_contract_number <>", value, "clientContractNumber");
            return (Criteria) this;
        }

        public Criteria andClientContractNumberGreaterThan(String value) {
            addCriterion("client_contract_number >", value, "clientContractNumber");
            return (Criteria) this;
        }

        public Criteria andClientContractNumberGreaterThanOrEqualTo(String value) {
            addCriterion("client_contract_number >=", value, "clientContractNumber");
            return (Criteria) this;
        }

        public Criteria andClientContractNumberLessThan(String value) {
            addCriterion("client_contract_number <", value, "clientContractNumber");
            return (Criteria) this;
        }

        public Criteria andClientContractNumberLessThanOrEqualTo(String value) {
            addCriterion("client_contract_number <=", value, "clientContractNumber");
            return (Criteria) this;
        }

        public Criteria andClientContractNumberLike(String value) {
            addCriterion("client_contract_number like", value, "clientContractNumber");
            return (Criteria) this;
        }

        public Criteria andClientContractNumberNotLike(String value) {
            addCriterion("client_contract_number not like", value, "clientContractNumber");
            return (Criteria) this;
        }

        public Criteria andClientContractNumberIn(List<String> values) {
            addCriterion("client_contract_number in", values, "clientContractNumber");
            return (Criteria) this;
        }

        public Criteria andClientContractNumberNotIn(List<String> values) {
            addCriterion("client_contract_number not in", values, "clientContractNumber");
            return (Criteria) this;
        }

        public Criteria andClientContractNumberBetween(String value1, String value2) {
            addCriterion("client_contract_number between", value1, value2, "clientContractNumber");
            return (Criteria) this;
        }

        public Criteria andClientContractNumberNotBetween(String value1, String value2) {
            addCriterion("client_contract_number not between", value1, value2, "clientContractNumber");
            return (Criteria) this;
        }

        public Criteria andFileAttachmentIdIsNull() {
            addCriterion("file_attachment_id is null");
            return (Criteria) this;
        }

        public Criteria andFileAttachmentIdIsNotNull() {
            addCriterion("file_attachment_id is not null");
            return (Criteria) this;
        }

        public Criteria andFileAttachmentIdEqualTo(String value) {
            addCriterion("file_attachment_id =", value, "fileAttachmentId");
            return (Criteria) this;
        }

        public Criteria andFileAttachmentIdNotEqualTo(String value) {
            addCriterion("file_attachment_id <>", value, "fileAttachmentId");
            return (Criteria) this;
        }

        public Criteria andFileAttachmentIdGreaterThan(String value) {
            addCriterion("file_attachment_id >", value, "fileAttachmentId");
            return (Criteria) this;
        }

        public Criteria andFileAttachmentIdGreaterThanOrEqualTo(String value) {
            addCriterion("file_attachment_id >=", value, "fileAttachmentId");
            return (Criteria) this;
        }

        public Criteria andFileAttachmentIdLessThan(String value) {
            addCriterion("file_attachment_id <", value, "fileAttachmentId");
            return (Criteria) this;
        }

        public Criteria andFileAttachmentIdLessThanOrEqualTo(String value) {
            addCriterion("file_attachment_id <=", value, "fileAttachmentId");
            return (Criteria) this;
        }

        public Criteria andFileAttachmentIdLike(String value) {
            addCriterion("file_attachment_id like", value, "fileAttachmentId");
            return (Criteria) this;
        }

        public Criteria andFileAttachmentIdNotLike(String value) {
            addCriterion("file_attachment_id not like", value, "fileAttachmentId");
            return (Criteria) this;
        }

        public Criteria andFileAttachmentIdIn(List<String> values) {
            addCriterion("file_attachment_id in", values, "fileAttachmentId");
            return (Criteria) this;
        }

        public Criteria andFileAttachmentIdNotIn(List<String> values) {
            addCriterion("file_attachment_id not in", values, "fileAttachmentId");
            return (Criteria) this;
        }

        public Criteria andFileAttachmentIdBetween(String value1, String value2) {
            addCriterion("file_attachment_id between", value1, value2, "fileAttachmentId");
            return (Criteria) this;
        }

        public Criteria andFileAttachmentIdNotBetween(String value1, String value2) {
            addCriterion("file_attachment_id not between", value1, value2, "fileAttachmentId");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNull() {
            addCriterion("ou_name is null");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNotNull() {
            addCriterion("ou_name is not null");
            return (Criteria) this;
        }

        public Criteria andOuNameEqualTo(String value) {
            addCriterion("ou_name =", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotEqualTo(String value) {
            addCriterion("ou_name <>", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThan(String value) {
            addCriterion("ou_name >", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThanOrEqualTo(String value) {
            addCriterion("ou_name >=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThan(String value) {
            addCriterion("ou_name <", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThanOrEqualTo(String value) {
            addCriterion("ou_name <=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLike(String value) {
            addCriterion("ou_name like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotLike(String value) {
            addCriterion("ou_name not like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameIn(List<String> values) {
            addCriterion("ou_name in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotIn(List<String> values) {
            addCriterion("ou_name not in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameBetween(String value1, String value2) {
            addCriterion("ou_name between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotBetween(String value1, String value2) {
            addCriterion("ou_name not between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNull() {
            addCriterion("unit_name is null");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNotNull() {
            addCriterion("unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andUnitNameEqualTo(String value) {
            addCriterion("unit_name =", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotEqualTo(String value) {
            addCriterion("unit_name <>", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThan(String value) {
            addCriterion("unit_name >", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("unit_name >=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThan(String value) {
            addCriterion("unit_name <", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThanOrEqualTo(String value) {
            addCriterion("unit_name <=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLike(String value) {
            addCriterion("unit_name like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotLike(String value) {
            addCriterion("unit_name not like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIn(List<String> values) {
            addCriterion("unit_name in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotIn(List<String> values) {
            addCriterion("unit_name not in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameBetween(String value1, String value2) {
            addCriterion("unit_name between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotBetween(String value1, String value2) {
            addCriterion("unit_name not between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNameIsNull() {
            addCriterion("sales_manager_name is null");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNameIsNotNull() {
            addCriterion("sales_manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNameEqualTo(String value) {
            addCriterion("sales_manager_name =", value, "salesManagerName");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNameNotEqualTo(String value) {
            addCriterion("sales_manager_name <>", value, "salesManagerName");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNameGreaterThan(String value) {
            addCriterion("sales_manager_name >", value, "salesManagerName");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("sales_manager_name >=", value, "salesManagerName");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNameLessThan(String value) {
            addCriterion("sales_manager_name <", value, "salesManagerName");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNameLessThanOrEqualTo(String value) {
            addCriterion("sales_manager_name <=", value, "salesManagerName");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNameLike(String value) {
            addCriterion("sales_manager_name like", value, "salesManagerName");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNameNotLike(String value) {
            addCriterion("sales_manager_name not like", value, "salesManagerName");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNameIn(List<String> values) {
            addCriterion("sales_manager_name in", values, "salesManagerName");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNameNotIn(List<String> values) {
            addCriterion("sales_manager_name not in", values, "salesManagerName");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNameBetween(String value1, String value2) {
            addCriterion("sales_manager_name between", value1, value2, "salesManagerName");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNameNotBetween(String value1, String value2) {
            addCriterion("sales_manager_name not between", value1, value2, "salesManagerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameIsNull() {
            addCriterion("manager_name is null");
            return (Criteria) this;
        }

        public Criteria andManagerNameIsNotNull() {
            addCriterion("manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andManagerNameEqualTo(String value) {
            addCriterion("manager_name =", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotEqualTo(String value) {
            addCriterion("manager_name <>", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameGreaterThan(String value) {
            addCriterion("manager_name >", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("manager_name >=", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameLessThan(String value) {
            addCriterion("manager_name <", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameLessThanOrEqualTo(String value) {
            addCriterion("manager_name <=", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameLike(String value) {
            addCriterion("manager_name like", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotLike(String value) {
            addCriterion("manager_name not like", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameIn(List<String> values) {
            addCriterion("manager_name in", values, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotIn(List<String> values) {
            addCriterion("manager_name not in", values, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameBetween(String value1, String value2) {
            addCriterion("manager_name between", value1, value2, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotBetween(String value1, String value2) {
            addCriterion("manager_name not between", value1, value2, "managerName");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentNameIsNull() {
            addCriterion("profit_department_name is null");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentNameIsNotNull() {
            addCriterion("profit_department_name is not null");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentNameEqualTo(String value) {
            addCriterion("profit_department_name =", value, "profitDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentNameNotEqualTo(String value) {
            addCriterion("profit_department_name <>", value, "profitDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentNameGreaterThan(String value) {
            addCriterion("profit_department_name >", value, "profitDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentNameGreaterThanOrEqualTo(String value) {
            addCriterion("profit_department_name >=", value, "profitDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentNameLessThan(String value) {
            addCriterion("profit_department_name <", value, "profitDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentNameLessThanOrEqualTo(String value) {
            addCriterion("profit_department_name <=", value, "profitDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentNameLike(String value) {
            addCriterion("profit_department_name like", value, "profitDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentNameNotLike(String value) {
            addCriterion("profit_department_name not like", value, "profitDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentNameIn(List<String> values) {
            addCriterion("profit_department_name in", values, "profitDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentNameNotIn(List<String> values) {
            addCriterion("profit_department_name not in", values, "profitDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentNameBetween(String value1, String value2) {
            addCriterion("profit_department_name between", value1, value2, "profitDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProfitDepartmentNameNotBetween(String value1, String value2) {
            addCriterion("profit_department_name not between", value1, value2, "profitDepartmentName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameIsNull() {
            addCriterion("business_type_name is null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameIsNotNull() {
            addCriterion("business_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameEqualTo(String value) {
            addCriterion("business_type_name =", value, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameNotEqualTo(String value) {
            addCriterion("business_type_name <>", value, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameGreaterThan(String value) {
            addCriterion("business_type_name >", value, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("business_type_name >=", value, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameLessThan(String value) {
            addCriterion("business_type_name <", value, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameLessThanOrEqualTo(String value) {
            addCriterion("business_type_name <=", value, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameLike(String value) {
            addCriterion("business_type_name like", value, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameNotLike(String value) {
            addCriterion("business_type_name not like", value, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameIn(List<String> values) {
            addCriterion("business_type_name in", values, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameNotIn(List<String> values) {
            addCriterion("business_type_name not in", values, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameBetween(String value1, String value2) {
            addCriterion("business_type_name between", value1, value2, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameNotBetween(String value1, String value2) {
            addCriterion("business_type_name not between", value1, value2, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andParentCodeIsNull() {
            addCriterion("parent_code is null");
            return (Criteria) this;
        }

        public Criteria andParentCodeIsNotNull() {
            addCriterion("parent_code is not null");
            return (Criteria) this;
        }

        public Criteria andParentCodeEqualTo(String value) {
            addCriterion("parent_code =", value, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeNotEqualTo(String value) {
            addCriterion("parent_code <>", value, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeGreaterThan(String value) {
            addCriterion("parent_code >", value, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeGreaterThanOrEqualTo(String value) {
            addCriterion("parent_code >=", value, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeLessThan(String value) {
            addCriterion("parent_code <", value, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeLessThanOrEqualTo(String value) {
            addCriterion("parent_code <=", value, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeLike(String value) {
            addCriterion("parent_code like", value, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeNotLike(String value) {
            addCriterion("parent_code not like", value, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeIn(List<String> values) {
            addCriterion("parent_code in", values, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeNotIn(List<String> values) {
            addCriterion("parent_code not in", values, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeBetween(String value1, String value2) {
            addCriterion("parent_code between", value1, value2, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeNotBetween(String value1, String value2) {
            addCriterion("parent_code not between", value1, value2, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentNameIsNull() {
            addCriterion("parent_name is null");
            return (Criteria) this;
        }

        public Criteria andParentNameIsNotNull() {
            addCriterion("parent_name is not null");
            return (Criteria) this;
        }

        public Criteria andParentNameEqualTo(String value) {
            addCriterion("parent_name =", value, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameNotEqualTo(String value) {
            addCriterion("parent_name <>", value, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameGreaterThan(String value) {
            addCriterion("parent_name >", value, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameGreaterThanOrEqualTo(String value) {
            addCriterion("parent_name >=", value, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameLessThan(String value) {
            addCriterion("parent_name <", value, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameLessThanOrEqualTo(String value) {
            addCriterion("parent_name <=", value, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameLike(String value) {
            addCriterion("parent_name like", value, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameNotLike(String value) {
            addCriterion("parent_name not like", value, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameIn(List<String> values) {
            addCriterion("parent_name in", values, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameNotIn(List<String> values) {
            addCriterion("parent_name not in", values, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameBetween(String value1, String value2) {
            addCriterion("parent_name between", value1, value2, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameNotBetween(String value1, String value2) {
            addCriterion("parent_name not between", value1, value2, "parentName");
            return (Criteria) this;
        }

        public Criteria andIndustryLevelIsNull() {
            addCriterion("industry_level is null");
            return (Criteria) this;
        }

        public Criteria andIndustryLevelIsNotNull() {
            addCriterion("industry_level is not null");
            return (Criteria) this;
        }

        public Criteria andIndustryLevelEqualTo(String value) {
            addCriterion("industry_level =", value, "industryLevel");
            return (Criteria) this;
        }

        public Criteria andIndustryLevelNotEqualTo(String value) {
            addCriterion("industry_level <>", value, "industryLevel");
            return (Criteria) this;
        }

        public Criteria andIndustryLevelGreaterThan(String value) {
            addCriterion("industry_level >", value, "industryLevel");
            return (Criteria) this;
        }

        public Criteria andIndustryLevelGreaterThanOrEqualTo(String value) {
            addCriterion("industry_level >=", value, "industryLevel");
            return (Criteria) this;
        }

        public Criteria andIndustryLevelLessThan(String value) {
            addCriterion("industry_level <", value, "industryLevel");
            return (Criteria) this;
        }

        public Criteria andIndustryLevelLessThanOrEqualTo(String value) {
            addCriterion("industry_level <=", value, "industryLevel");
            return (Criteria) this;
        }

        public Criteria andIndustryLevelLike(String value) {
            addCriterion("industry_level like", value, "industryLevel");
            return (Criteria) this;
        }

        public Criteria andIndustryLevelNotLike(String value) {
            addCriterion("industry_level not like", value, "industryLevel");
            return (Criteria) this;
        }

        public Criteria andIndustryLevelIn(List<String> values) {
            addCriterion("industry_level in", values, "industryLevel");
            return (Criteria) this;
        }

        public Criteria andIndustryLevelNotIn(List<String> values) {
            addCriterion("industry_level not in", values, "industryLevel");
            return (Criteria) this;
        }

        public Criteria andIndustryLevelBetween(String value1, String value2) {
            addCriterion("industry_level between", value1, value2, "industryLevel");
            return (Criteria) this;
        }

        public Criteria andIndustryLevelNotBetween(String value1, String value2) {
            addCriterion("industry_level not between", value1, value2, "industryLevel");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLevelIsNull() {
            addCriterion("signing_center_level is null");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLevelIsNotNull() {
            addCriterion("signing_center_level is not null");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLevelEqualTo(String value) {
            addCriterion("signing_center_level =", value, "signingCenterLevel");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLevelNotEqualTo(String value) {
            addCriterion("signing_center_level <>", value, "signingCenterLevel");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLevelGreaterThan(String value) {
            addCriterion("signing_center_level >", value, "signingCenterLevel");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLevelGreaterThanOrEqualTo(String value) {
            addCriterion("signing_center_level >=", value, "signingCenterLevel");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLevelLessThan(String value) {
            addCriterion("signing_center_level <", value, "signingCenterLevel");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLevelLessThanOrEqualTo(String value) {
            addCriterion("signing_center_level <=", value, "signingCenterLevel");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLevelLike(String value) {
            addCriterion("signing_center_level like", value, "signingCenterLevel");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLevelNotLike(String value) {
            addCriterion("signing_center_level not like", value, "signingCenterLevel");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLevelIn(List<String> values) {
            addCriterion("signing_center_level in", values, "signingCenterLevel");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLevelNotIn(List<String> values) {
            addCriterion("signing_center_level not in", values, "signingCenterLevel");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLevelBetween(String value1, String value2) {
            addCriterion("signing_center_level between", value1, value2, "signingCenterLevel");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLevelNotBetween(String value1, String value2) {
            addCriterion("signing_center_level not between", value1, value2, "signingCenterLevel");
            return (Criteria) this;
        }

        public Criteria andCreateByNameIsNull() {
            addCriterion("create_by_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateByNameIsNotNull() {
            addCriterion("create_by_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByNameEqualTo(String value) {
            addCriterion("create_by_name =", value, "createByName");
            return (Criteria) this;
        }

        public Criteria andCreateByNameNotEqualTo(String value) {
            addCriterion("create_by_name <>", value, "createByName");
            return (Criteria) this;
        }

        public Criteria andCreateByNameGreaterThan(String value) {
            addCriterion("create_by_name >", value, "createByName");
            return (Criteria) this;
        }

        public Criteria andCreateByNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_by_name >=", value, "createByName");
            return (Criteria) this;
        }

        public Criteria andCreateByNameLessThan(String value) {
            addCriterion("create_by_name <", value, "createByName");
            return (Criteria) this;
        }

        public Criteria andCreateByNameLessThanOrEqualTo(String value) {
            addCriterion("create_by_name <=", value, "createByName");
            return (Criteria) this;
        }

        public Criteria andCreateByNameLike(String value) {
            addCriterion("create_by_name like", value, "createByName");
            return (Criteria) this;
        }

        public Criteria andCreateByNameNotLike(String value) {
            addCriterion("create_by_name not like", value, "createByName");
            return (Criteria) this;
        }

        public Criteria andCreateByNameIn(List<String> values) {
            addCriterion("create_by_name in", values, "createByName");
            return (Criteria) this;
        }

        public Criteria andCreateByNameNotIn(List<String> values) {
            addCriterion("create_by_name not in", values, "createByName");
            return (Criteria) this;
        }

        public Criteria andCreateByNameBetween(String value1, String value2) {
            addCriterion("create_by_name between", value1, value2, "createByName");
            return (Criteria) this;
        }

        public Criteria andCreateByNameNotBetween(String value1, String value2) {
            addCriterion("create_by_name not between", value1, value2, "createByName");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}