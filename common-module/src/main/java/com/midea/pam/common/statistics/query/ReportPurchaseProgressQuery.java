package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Description
 * Created by chenchong
 * Date 2022/11/5 16:07
 */
@Getter
@Setter
@ApiModel(value = "ReportPurchaseProgressQuery", description = "采购进展报表")
public class ReportPurchaseProgressQuery {

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    private Long projectId;

    private Long personal;

    private Long companyId;

    @ApiModelProperty(value = "项目类型")
    private List<Long> projectTypeIds;

    @ApiModelProperty(value = "业务分类")
    private List<Long> projectSortIds;

    @ApiModelProperty(value = "项目经理")
    private Long projectManagerId;

    @Override
    public String toString() {
        return "ReportPurchaseProgressQuery{" +
                "executeId=" + executeId +
                ", personal=" + personal +
                ", companyId=" + companyId +
                ", projectTypeIds=" + projectTypeIds +
                ", projectSortIds=" + projectSortIds +
                ", projectManagerId=" + projectManagerId +
                '}';
    }

}

