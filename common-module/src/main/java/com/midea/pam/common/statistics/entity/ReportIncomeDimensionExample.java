package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class ReportIncomeDimensionExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportIncomeDimensionExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Long value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Long value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Long value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Long value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Long> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Long> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Long value1, Long value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNull() {
            addCriterion("unit_name is null");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNotNull() {
            addCriterion("unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andUnitNameEqualTo(String value) {
            addCriterion("unit_name =", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotEqualTo(String value) {
            addCriterion("unit_name <>", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThan(String value) {
            addCriterion("unit_name >", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("unit_name >=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThan(String value) {
            addCriterion("unit_name <", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThanOrEqualTo(String value) {
            addCriterion("unit_name <=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLike(String value) {
            addCriterion("unit_name like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotLike(String value) {
            addCriterion("unit_name not like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIn(List<String> values) {
            addCriterion("unit_name in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotIn(List<String> values) {
            addCriterion("unit_name not in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameBetween(String value1, String value2) {
            addCriterion("unit_name between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotBetween(String value1, String value2) {
            addCriterion("unit_name not between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeIsNull() {
            addCriterion("division_code is null");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeIsNotNull() {
            addCriterion("division_code is not null");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeEqualTo(String value) {
            addCriterion("division_code =", value, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeNotEqualTo(String value) {
            addCriterion("division_code <>", value, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeGreaterThan(String value) {
            addCriterion("division_code >", value, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeGreaterThanOrEqualTo(String value) {
            addCriterion("division_code >=", value, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeLessThan(String value) {
            addCriterion("division_code <", value, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeLessThanOrEqualTo(String value) {
            addCriterion("division_code <=", value, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeLike(String value) {
            addCriterion("division_code like", value, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeNotLike(String value) {
            addCriterion("division_code not like", value, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeIn(List<String> values) {
            addCriterion("division_code in", values, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeNotIn(List<String> values) {
            addCriterion("division_code not in", values, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeBetween(String value1, String value2) {
            addCriterion("division_code between", value1, value2, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeNotBetween(String value1, String value2) {
            addCriterion("division_code not between", value1, value2, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionNameIsNull() {
            addCriterion("division_name is null");
            return (Criteria) this;
        }

        public Criteria andDivisionNameIsNotNull() {
            addCriterion("division_name is not null");
            return (Criteria) this;
        }

        public Criteria andDivisionNameEqualTo(String value) {
            addCriterion("division_name =", value, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameNotEqualTo(String value) {
            addCriterion("division_name <>", value, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameGreaterThan(String value) {
            addCriterion("division_name >", value, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameGreaterThanOrEqualTo(String value) {
            addCriterion("division_name >=", value, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameLessThan(String value) {
            addCriterion("division_name <", value, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameLessThanOrEqualTo(String value) {
            addCriterion("division_name <=", value, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameLike(String value) {
            addCriterion("division_name like", value, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameNotLike(String value) {
            addCriterion("division_name not like", value, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameIn(List<String> values) {
            addCriterion("division_name in", values, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameNotIn(List<String> values) {
            addCriterion("division_name not in", values, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameBetween(String value1, String value2) {
            addCriterion("division_name between", value1, value2, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameNotBetween(String value1, String value2) {
            addCriterion("division_name not between", value1, value2, "divisionName");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Long value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Long value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Long value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Long value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Long value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Long value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Long> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Long> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Long value1, Long value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Long value1, Long value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNameIsNull() {
            addCriterion("type_name is null");
            return (Criteria) this;
        }

        public Criteria andTypeNameIsNotNull() {
            addCriterion("type_name is not null");
            return (Criteria) this;
        }

        public Criteria andTypeNameEqualTo(String value) {
            addCriterion("type_name =", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameNotEqualTo(String value) {
            addCriterion("type_name <>", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameGreaterThan(String value) {
            addCriterion("type_name >", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("type_name >=", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameLessThan(String value) {
            addCriterion("type_name <", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameLessThanOrEqualTo(String value) {
            addCriterion("type_name <=", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameLike(String value) {
            addCriterion("type_name like", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameNotLike(String value) {
            addCriterion("type_name not like", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameIn(List<String> values) {
            addCriterion("type_name in", values, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameNotIn(List<String> values) {
            addCriterion("type_name not in", values, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameBetween(String value1, String value2) {
            addCriterion("type_name between", value1, value2, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameNotBetween(String value1, String value2) {
            addCriterion("type_name not between", value1, value2, "typeName");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andPriceTypeIsNull() {
            addCriterion("price_type is null");
            return (Criteria) this;
        }

        public Criteria andPriceTypeIsNotNull() {
            addCriterion("price_type is not null");
            return (Criteria) this;
        }

        public Criteria andPriceTypeEqualTo(String value) {
            addCriterion("price_type =", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeNotEqualTo(String value) {
            addCriterion("price_type <>", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeGreaterThan(String value) {
            addCriterion("price_type >", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeGreaterThanOrEqualTo(String value) {
            addCriterion("price_type >=", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeLessThan(String value) {
            addCriterion("price_type <", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeLessThanOrEqualTo(String value) {
            addCriterion("price_type <=", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeLike(String value) {
            addCriterion("price_type like", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeNotLike(String value) {
            addCriterion("price_type not like", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeIn(List<String> values) {
            addCriterion("price_type in", values, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeNotIn(List<String> values) {
            addCriterion("price_type not in", values, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeBetween(String value1, String value2) {
            addCriterion("price_type between", value1, value2, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeNotBetween(String value1, String value2) {
            addCriterion("price_type not between", value1, value2, "priceType");
            return (Criteria) this;
        }

        public Criteria andManagerNameIsNull() {
            addCriterion("manager_name is null");
            return (Criteria) this;
        }

        public Criteria andManagerNameIsNotNull() {
            addCriterion("manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andManagerNameEqualTo(String value) {
            addCriterion("manager_name =", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotEqualTo(String value) {
            addCriterion("manager_name <>", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameGreaterThan(String value) {
            addCriterion("manager_name >", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("manager_name >=", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameLessThan(String value) {
            addCriterion("manager_name <", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameLessThanOrEqualTo(String value) {
            addCriterion("manager_name <=", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameLike(String value) {
            addCriterion("manager_name like", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotLike(String value) {
            addCriterion("manager_name not like", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameIn(List<String> values) {
            addCriterion("manager_name in", values, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotIn(List<String> values) {
            addCriterion("manager_name not in", values, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameBetween(String value1, String value2) {
            addCriterion("manager_name between", value1, value2, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotBetween(String value1, String value2) {
            addCriterion("manager_name not between", value1, value2, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerIdIsNull() {
            addCriterion("manager_id is null");
            return (Criteria) this;
        }

        public Criteria andManagerIdIsNotNull() {
            addCriterion("manager_id is not null");
            return (Criteria) this;
        }

        public Criteria andManagerIdEqualTo(Long value) {
            addCriterion("manager_id =", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdNotEqualTo(Long value) {
            addCriterion("manager_id <>", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdGreaterThan(Long value) {
            addCriterion("manager_id >", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("manager_id >=", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdLessThan(Long value) {
            addCriterion("manager_id <", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdLessThanOrEqualTo(Long value) {
            addCriterion("manager_id <=", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdIn(List<Long> values) {
            addCriterion("manager_id in", values, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdNotIn(List<Long> values) {
            addCriterion("manager_id not in", values, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdBetween(Long value1, Long value2) {
            addCriterion("manager_id between", value1, value2, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdNotBetween(Long value1, Long value2) {
            addCriterion("manager_id not between", value1, value2, "managerId");
            return (Criteria) this;
        }

        public Criteria andAmountIsNull() {
            addCriterion("amount is null");
            return (Criteria) this;
        }

        public Criteria andAmountIsNotNull() {
            addCriterion("amount is not null");
            return (Criteria) this;
        }

        public Criteria andAmountEqualTo(BigDecimal value) {
            addCriterion("amount =", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotEqualTo(BigDecimal value) {
            addCriterion("amount <>", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThan(BigDecimal value) {
            addCriterion("amount >", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("amount >=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThan(BigDecimal value) {
            addCriterion("amount <", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("amount <=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountIn(List<BigDecimal> values) {
            addCriterion("amount in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotIn(List<BigDecimal> values) {
            addCriterion("amount not in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount not between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andBudgetCostIsNull() {
            addCriterion("budget_cost is null");
            return (Criteria) this;
        }

        public Criteria andBudgetCostIsNotNull() {
            addCriterion("budget_cost is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetCostEqualTo(BigDecimal value) {
            addCriterion("budget_cost =", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostNotEqualTo(BigDecimal value) {
            addCriterion("budget_cost <>", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostGreaterThan(BigDecimal value) {
            addCriterion("budget_cost >", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_cost >=", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostLessThan(BigDecimal value) {
            addCriterion("budget_cost <", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_cost <=", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostIn(List<BigDecimal> values) {
            addCriterion("budget_cost in", values, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostNotIn(List<BigDecimal> values) {
            addCriterion("budget_cost not in", values, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_cost between", value1, value2, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_cost not between", value1, value2, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andPeriodNameIsNull() {
            addCriterion("period_name is null");
            return (Criteria) this;
        }

        public Criteria andPeriodNameIsNotNull() {
            addCriterion("period_name is not null");
            return (Criteria) this;
        }

        public Criteria andPeriodNameEqualTo(String value) {
            addCriterion("period_name =", value, "periodName");
            return (Criteria) this;
        }

        public Criteria andPeriodNameNotEqualTo(String value) {
            addCriterion("period_name <>", value, "periodName");
            return (Criteria) this;
        }

        public Criteria andPeriodNameGreaterThan(String value) {
            addCriterion("period_name >", value, "periodName");
            return (Criteria) this;
        }

        public Criteria andPeriodNameGreaterThanOrEqualTo(String value) {
            addCriterion("period_name >=", value, "periodName");
            return (Criteria) this;
        }

        public Criteria andPeriodNameLessThan(String value) {
            addCriterion("period_name <", value, "periodName");
            return (Criteria) this;
        }

        public Criteria andPeriodNameLessThanOrEqualTo(String value) {
            addCriterion("period_name <=", value, "periodName");
            return (Criteria) this;
        }

        public Criteria andPeriodNameLike(String value) {
            addCriterion("period_name like", value, "periodName");
            return (Criteria) this;
        }

        public Criteria andPeriodNameNotLike(String value) {
            addCriterion("period_name not like", value, "periodName");
            return (Criteria) this;
        }

        public Criteria andPeriodNameIn(List<String> values) {
            addCriterion("period_name in", values, "periodName");
            return (Criteria) this;
        }

        public Criteria andPeriodNameNotIn(List<String> values) {
            addCriterion("period_name not in", values, "periodName");
            return (Criteria) this;
        }

        public Criteria andPeriodNameBetween(String value1, String value2) {
            addCriterion("period_name between", value1, value2, "periodName");
            return (Criteria) this;
        }

        public Criteria andPeriodNameNotBetween(String value1, String value2) {
            addCriterion("period_name not between", value1, value2, "periodName");
            return (Criteria) this;
        }

        public Criteria andHelpFlagIsNull() {
            addCriterion("help_flag is null");
            return (Criteria) this;
        }

        public Criteria andHelpFlagIsNotNull() {
            addCriterion("help_flag is not null");
            return (Criteria) this;
        }

        public Criteria andHelpFlagEqualTo(Integer value) {
            addCriterion("help_flag =", value, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagNotEqualTo(Integer value) {
            addCriterion("help_flag <>", value, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagGreaterThan(Integer value) {
            addCriterion("help_flag >", value, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("help_flag >=", value, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagLessThan(Integer value) {
            addCriterion("help_flag <", value, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagLessThanOrEqualTo(Integer value) {
            addCriterion("help_flag <=", value, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagIn(List<Integer> values) {
            addCriterion("help_flag in", values, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagNotIn(List<Integer> values) {
            addCriterion("help_flag not in", values, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagBetween(Integer value1, Integer value2) {
            addCriterion("help_flag between", value1, value2, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("help_flag not between", value1, value2, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andIncomeNameIsNull() {
            addCriterion("income_name is null");
            return (Criteria) this;
        }

        public Criteria andIncomeNameIsNotNull() {
            addCriterion("income_name is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeNameEqualTo(String value) {
            addCriterion("income_name =", value, "incomeName");
            return (Criteria) this;
        }

        public Criteria andIncomeNameNotEqualTo(String value) {
            addCriterion("income_name <>", value, "incomeName");
            return (Criteria) this;
        }

        public Criteria andIncomeNameGreaterThan(String value) {
            addCriterion("income_name >", value, "incomeName");
            return (Criteria) this;
        }

        public Criteria andIncomeNameGreaterThanOrEqualTo(String value) {
            addCriterion("income_name >=", value, "incomeName");
            return (Criteria) this;
        }

        public Criteria andIncomeNameLessThan(String value) {
            addCriterion("income_name <", value, "incomeName");
            return (Criteria) this;
        }

        public Criteria andIncomeNameLessThanOrEqualTo(String value) {
            addCriterion("income_name <=", value, "incomeName");
            return (Criteria) this;
        }

        public Criteria andIncomeNameLike(String value) {
            addCriterion("income_name like", value, "incomeName");
            return (Criteria) this;
        }

        public Criteria andIncomeNameNotLike(String value) {
            addCriterion("income_name not like", value, "incomeName");
            return (Criteria) this;
        }

        public Criteria andIncomeNameIn(List<String> values) {
            addCriterion("income_name in", values, "incomeName");
            return (Criteria) this;
        }

        public Criteria andIncomeNameNotIn(List<String> values) {
            addCriterion("income_name not in", values, "incomeName");
            return (Criteria) this;
        }

        public Criteria andIncomeNameBetween(String value1, String value2) {
            addCriterion("income_name between", value1, value2, "incomeName");
            return (Criteria) this;
        }

        public Criteria andIncomeNameNotBetween(String value1, String value2) {
            addCriterion("income_name not between", value1, value2, "incomeName");
            return (Criteria) this;
        }

        public Criteria andOrderNumIsNull() {
            addCriterion("order_num is null");
            return (Criteria) this;
        }

        public Criteria andOrderNumIsNotNull() {
            addCriterion("order_num is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNumEqualTo(Integer value) {
            addCriterion("order_num =", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotEqualTo(Integer value) {
            addCriterion("order_num <>", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumGreaterThan(Integer value) {
            addCriterion("order_num >", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_num >=", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLessThan(Integer value) {
            addCriterion("order_num <", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLessThanOrEqualTo(Integer value) {
            addCriterion("order_num <=", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumIn(List<Integer> values) {
            addCriterion("order_num in", values, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotIn(List<Integer> values) {
            addCriterion("order_num not in", values, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumBetween(Integer value1, Integer value2) {
            addCriterion("order_num between", value1, value2, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotBetween(Integer value1, Integer value2) {
            addCriterion("order_num not between", value1, value2, "orderNum");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusIsNull() {
            addCriterion("milepost_status is null");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusIsNotNull() {
            addCriterion("milepost_status is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusEqualTo(Integer value) {
            addCriterion("milepost_status =", value, "milepostStatus");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusNotEqualTo(Integer value) {
            addCriterion("milepost_status <>", value, "milepostStatus");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusGreaterThan(Integer value) {
            addCriterion("milepost_status >", value, "milepostStatus");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("milepost_status >=", value, "milepostStatus");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusLessThan(Integer value) {
            addCriterion("milepost_status <", value, "milepostStatus");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusLessThanOrEqualTo(Integer value) {
            addCriterion("milepost_status <=", value, "milepostStatus");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusIn(List<Integer> values) {
            addCriterion("milepost_status in", values, "milepostStatus");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusNotIn(List<Integer> values) {
            addCriterion("milepost_status not in", values, "milepostStatus");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusBetween(Integer value1, Integer value2) {
            addCriterion("milepost_status between", value1, value2, "milepostStatus");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("milepost_status not between", value1, value2, "milepostStatus");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Date value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Date value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Date value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Date> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Date> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andActualStartTimeIsNull() {
            addCriterion("actual_start_time is null");
            return (Criteria) this;
        }

        public Criteria andActualStartTimeIsNotNull() {
            addCriterion("actual_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andActualStartTimeEqualTo(Date value) {
            addCriterion("actual_start_time =", value, "actualStartTime");
            return (Criteria) this;
        }

        public Criteria andActualStartTimeNotEqualTo(Date value) {
            addCriterion("actual_start_time <>", value, "actualStartTime");
            return (Criteria) this;
        }

        public Criteria andActualStartTimeGreaterThan(Date value) {
            addCriterion("actual_start_time >", value, "actualStartTime");
            return (Criteria) this;
        }

        public Criteria andActualStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("actual_start_time >=", value, "actualStartTime");
            return (Criteria) this;
        }

        public Criteria andActualStartTimeLessThan(Date value) {
            addCriterion("actual_start_time <", value, "actualStartTime");
            return (Criteria) this;
        }

        public Criteria andActualStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("actual_start_time <=", value, "actualStartTime");
            return (Criteria) this;
        }

        public Criteria andActualStartTimeIn(List<Date> values) {
            addCriterion("actual_start_time in", values, "actualStartTime");
            return (Criteria) this;
        }

        public Criteria andActualStartTimeNotIn(List<Date> values) {
            addCriterion("actual_start_time not in", values, "actualStartTime");
            return (Criteria) this;
        }

        public Criteria andActualStartTimeBetween(Date value1, Date value2) {
            addCriterion("actual_start_time between", value1, value2, "actualStartTime");
            return (Criteria) this;
        }

        public Criteria andActualStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("actual_start_time not between", value1, value2, "actualStartTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeIsNull() {
            addCriterion("actual_end_time is null");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeIsNotNull() {
            addCriterion("actual_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeEqualTo(Date value) {
            addCriterion("actual_end_time =", value, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeNotEqualTo(Date value) {
            addCriterion("actual_end_time <>", value, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeGreaterThan(Date value) {
            addCriterion("actual_end_time >", value, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("actual_end_time >=", value, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeLessThan(Date value) {
            addCriterion("actual_end_time <", value, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("actual_end_time <=", value, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeIn(List<Date> values) {
            addCriterion("actual_end_time in", values, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeNotIn(List<Date> values) {
            addCriterion("actual_end_time not in", values, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeBetween(Date value1, Date value2) {
            addCriterion("actual_end_time between", value1, value2, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("actual_end_time not between", value1, value2, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andFixedIsNull() {
            addCriterion("fixed is null");
            return (Criteria) this;
        }

        public Criteria andFixedIsNotNull() {
            addCriterion("fixed is not null");
            return (Criteria) this;
        }

        public Criteria andFixedEqualTo(Boolean value) {
            addCriterion("fixed =", value, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedNotEqualTo(Boolean value) {
            addCriterion("fixed <>", value, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedGreaterThan(Boolean value) {
            addCriterion("fixed >", value, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("fixed >=", value, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedLessThan(Boolean value) {
            addCriterion("fixed <", value, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedLessThanOrEqualTo(Boolean value) {
            addCriterion("fixed <=", value, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedIn(List<Boolean> values) {
            addCriterion("fixed in", values, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedNotIn(List<Boolean> values) {
            addCriterion("fixed not in", values, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedBetween(Boolean value1, Boolean value2) {
            addCriterion("fixed between", value1, value2, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("fixed not between", value1, value2, "fixed");
            return (Criteria) this;
        }

        public Criteria andIncomeFlagIsNull() {
            addCriterion("income_flag is null");
            return (Criteria) this;
        }

        public Criteria andIncomeFlagIsNotNull() {
            addCriterion("income_flag is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeFlagEqualTo(Boolean value) {
            addCriterion("income_flag =", value, "incomeFlag");
            return (Criteria) this;
        }

        public Criteria andIncomeFlagNotEqualTo(Boolean value) {
            addCriterion("income_flag <>", value, "incomeFlag");
            return (Criteria) this;
        }

        public Criteria andIncomeFlagGreaterThan(Boolean value) {
            addCriterion("income_flag >", value, "incomeFlag");
            return (Criteria) this;
        }

        public Criteria andIncomeFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("income_flag >=", value, "incomeFlag");
            return (Criteria) this;
        }

        public Criteria andIncomeFlagLessThan(Boolean value) {
            addCriterion("income_flag <", value, "incomeFlag");
            return (Criteria) this;
        }

        public Criteria andIncomeFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("income_flag <=", value, "incomeFlag");
            return (Criteria) this;
        }

        public Criteria andIncomeFlagIn(List<Boolean> values) {
            addCriterion("income_flag in", values, "incomeFlag");
            return (Criteria) this;
        }

        public Criteria andIncomeFlagNotIn(List<Boolean> values) {
            addCriterion("income_flag not in", values, "incomeFlag");
            return (Criteria) this;
        }

        public Criteria andIncomeFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("income_flag between", value1, value2, "incomeFlag");
            return (Criteria) this;
        }

        public Criteria andIncomeFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("income_flag not between", value1, value2, "incomeFlag");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioIsNull() {
            addCriterion("income_ratio is null");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioIsNotNull() {
            addCriterion("income_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioEqualTo(BigDecimal value) {
            addCriterion("income_ratio =", value, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioNotEqualTo(BigDecimal value) {
            addCriterion("income_ratio <>", value, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioGreaterThan(BigDecimal value) {
            addCriterion("income_ratio >", value, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income_ratio >=", value, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioLessThan(BigDecimal value) {
            addCriterion("income_ratio <", value, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income_ratio <=", value, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioIn(List<BigDecimal> values) {
            addCriterion("income_ratio in", values, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioNotIn(List<BigDecimal> values) {
            addCriterion("income_ratio not in", values, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_ratio between", value1, value2, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_ratio not between", value1, value2, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeAmountIsNull() {
            addCriterion("income_amount is null");
            return (Criteria) this;
        }

        public Criteria andIncomeAmountIsNotNull() {
            addCriterion("income_amount is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeAmountEqualTo(BigDecimal value) {
            addCriterion("income_amount =", value, "incomeAmount");
            return (Criteria) this;
        }

        public Criteria andIncomeAmountNotEqualTo(BigDecimal value) {
            addCriterion("income_amount <>", value, "incomeAmount");
            return (Criteria) this;
        }

        public Criteria andIncomeAmountGreaterThan(BigDecimal value) {
            addCriterion("income_amount >", value, "incomeAmount");
            return (Criteria) this;
        }

        public Criteria andIncomeAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income_amount >=", value, "incomeAmount");
            return (Criteria) this;
        }

        public Criteria andIncomeAmountLessThan(BigDecimal value) {
            addCriterion("income_amount <", value, "incomeAmount");
            return (Criteria) this;
        }

        public Criteria andIncomeAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income_amount <=", value, "incomeAmount");
            return (Criteria) this;
        }

        public Criteria andIncomeAmountIn(List<BigDecimal> values) {
            addCriterion("income_amount in", values, "incomeAmount");
            return (Criteria) this;
        }

        public Criteria andIncomeAmountNotIn(List<BigDecimal> values) {
            addCriterion("income_amount not in", values, "incomeAmount");
            return (Criteria) this;
        }

        public Criteria andIncomeAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_amount between", value1, value2, "incomeAmount");
            return (Criteria) this;
        }

        public Criteria andIncomeAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_amount not between", value1, value2, "incomeAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomePercentIsNull() {
            addCriterion("current_income_percent is null");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomePercentIsNotNull() {
            addCriterion("current_income_percent is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomePercentEqualTo(BigDecimal value) {
            addCriterion("current_income_percent =", value, "currentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomePercentNotEqualTo(BigDecimal value) {
            addCriterion("current_income_percent <>", value, "currentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomePercentGreaterThan(BigDecimal value) {
            addCriterion("current_income_percent >", value, "currentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomePercentGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("current_income_percent >=", value, "currentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomePercentLessThan(BigDecimal value) {
            addCriterion("current_income_percent <", value, "currentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomePercentLessThanOrEqualTo(BigDecimal value) {
            addCriterion("current_income_percent <=", value, "currentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomePercentIn(List<BigDecimal> values) {
            addCriterion("current_income_percent in", values, "currentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomePercentNotIn(List<BigDecimal> values) {
            addCriterion("current_income_percent not in", values, "currentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomePercentBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_income_percent between", value1, value2, "currentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomePercentNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_income_percent not between", value1, value2, "currentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomeTotalAmountIsNull() {
            addCriterion("current_income_total_amount is null");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomeTotalAmountIsNotNull() {
            addCriterion("current_income_total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomeTotalAmountEqualTo(BigDecimal value) {
            addCriterion("current_income_total_amount =", value, "currentIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomeTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("current_income_total_amount <>", value, "currentIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomeTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("current_income_total_amount >", value, "currentIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomeTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("current_income_total_amount >=", value, "currentIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomeTotalAmountLessThan(BigDecimal value) {
            addCriterion("current_income_total_amount <", value, "currentIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomeTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("current_income_total_amount <=", value, "currentIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomeTotalAmountIn(List<BigDecimal> values) {
            addCriterion("current_income_total_amount in", values, "currentIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomeTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("current_income_total_amount not in", values, "currentIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomeTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_income_total_amount between", value1, value2, "currentIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentIncomeTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_income_total_amount not between", value1, value2, "currentIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andAttainmentIncomePercentIsNull() {
            addCriterion("attainment_income_percent is null");
            return (Criteria) this;
        }

        public Criteria andAttainmentIncomePercentIsNotNull() {
            addCriterion("attainment_income_percent is not null");
            return (Criteria) this;
        }

        public Criteria andAttainmentIncomePercentEqualTo(BigDecimal value) {
            addCriterion("attainment_income_percent =", value, "attainmentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andAttainmentIncomePercentNotEqualTo(BigDecimal value) {
            addCriterion("attainment_income_percent <>", value, "attainmentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andAttainmentIncomePercentGreaterThan(BigDecimal value) {
            addCriterion("attainment_income_percent >", value, "attainmentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andAttainmentIncomePercentGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("attainment_income_percent >=", value, "attainmentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andAttainmentIncomePercentLessThan(BigDecimal value) {
            addCriterion("attainment_income_percent <", value, "attainmentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andAttainmentIncomePercentLessThanOrEqualTo(BigDecimal value) {
            addCriterion("attainment_income_percent <=", value, "attainmentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andAttainmentIncomePercentIn(List<BigDecimal> values) {
            addCriterion("attainment_income_percent in", values, "attainmentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andAttainmentIncomePercentNotIn(List<BigDecimal> values) {
            addCriterion("attainment_income_percent not in", values, "attainmentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andAttainmentIncomePercentBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("attainment_income_percent between", value1, value2, "attainmentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andAttainmentIncomePercentNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("attainment_income_percent not between", value1, value2, "attainmentIncomePercent");
            return (Criteria) this;
        }

        public Criteria andCostAmountIsNull() {
            addCriterion("cost_amount is null");
            return (Criteria) this;
        }

        public Criteria andCostAmountIsNotNull() {
            addCriterion("cost_amount is not null");
            return (Criteria) this;
        }

        public Criteria andCostAmountEqualTo(BigDecimal value) {
            addCriterion("cost_amount =", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountNotEqualTo(BigDecimal value) {
            addCriterion("cost_amount <>", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountGreaterThan(BigDecimal value) {
            addCriterion("cost_amount >", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cost_amount >=", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountLessThan(BigDecimal value) {
            addCriterion("cost_amount <", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cost_amount <=", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountIn(List<BigDecimal> values) {
            addCriterion("cost_amount in", values, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountNotIn(List<BigDecimal> values) {
            addCriterion("cost_amount not in", values, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost_amount between", value1, value2, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost_amount not between", value1, value2, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCurrentCostActualIsNull() {
            addCriterion("current_cost_actual is null");
            return (Criteria) this;
        }

        public Criteria andCurrentCostActualIsNotNull() {
            addCriterion("current_cost_actual is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentCostActualEqualTo(BigDecimal value) {
            addCriterion("current_cost_actual =", value, "currentCostActual");
            return (Criteria) this;
        }

        public Criteria andCurrentCostActualNotEqualTo(BigDecimal value) {
            addCriterion("current_cost_actual <>", value, "currentCostActual");
            return (Criteria) this;
        }

        public Criteria andCurrentCostActualGreaterThan(BigDecimal value) {
            addCriterion("current_cost_actual >", value, "currentCostActual");
            return (Criteria) this;
        }

        public Criteria andCurrentCostActualGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("current_cost_actual >=", value, "currentCostActual");
            return (Criteria) this;
        }

        public Criteria andCurrentCostActualLessThan(BigDecimal value) {
            addCriterion("current_cost_actual <", value, "currentCostActual");
            return (Criteria) this;
        }

        public Criteria andCurrentCostActualLessThanOrEqualTo(BigDecimal value) {
            addCriterion("current_cost_actual <=", value, "currentCostActual");
            return (Criteria) this;
        }

        public Criteria andCurrentCostActualIn(List<BigDecimal> values) {
            addCriterion("current_cost_actual in", values, "currentCostActual");
            return (Criteria) this;
        }

        public Criteria andCurrentCostActualNotIn(List<BigDecimal> values) {
            addCriterion("current_cost_actual not in", values, "currentCostActual");
            return (Criteria) this;
        }

        public Criteria andCurrentCostActualBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_cost_actual between", value1, value2, "currentCostActual");
            return (Criteria) this;
        }

        public Criteria andCurrentCostActualNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_cost_actual not between", value1, value2, "currentCostActual");
            return (Criteria) this;
        }

        public Criteria andBudgetOfPercentIsNull() {
            addCriterion("budget_of_percent is null");
            return (Criteria) this;
        }

        public Criteria andBudgetOfPercentIsNotNull() {
            addCriterion("budget_of_percent is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetOfPercentEqualTo(BigDecimal value) {
            addCriterion("budget_of_percent =", value, "budgetOfPercent");
            return (Criteria) this;
        }

        public Criteria andBudgetOfPercentNotEqualTo(BigDecimal value) {
            addCriterion("budget_of_percent <>", value, "budgetOfPercent");
            return (Criteria) this;
        }

        public Criteria andBudgetOfPercentGreaterThan(BigDecimal value) {
            addCriterion("budget_of_percent >", value, "budgetOfPercent");
            return (Criteria) this;
        }

        public Criteria andBudgetOfPercentGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_of_percent >=", value, "budgetOfPercent");
            return (Criteria) this;
        }

        public Criteria andBudgetOfPercentLessThan(BigDecimal value) {
            addCriterion("budget_of_percent <", value, "budgetOfPercent");
            return (Criteria) this;
        }

        public Criteria andBudgetOfPercentLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_of_percent <=", value, "budgetOfPercent");
            return (Criteria) this;
        }

        public Criteria andBudgetOfPercentIn(List<BigDecimal> values) {
            addCriterion("budget_of_percent in", values, "budgetOfPercent");
            return (Criteria) this;
        }

        public Criteria andBudgetOfPercentNotIn(List<BigDecimal> values) {
            addCriterion("budget_of_percent not in", values, "budgetOfPercent");
            return (Criteria) this;
        }

        public Criteria andBudgetOfPercentBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_of_percent between", value1, value2, "budgetOfPercent");
            return (Criteria) this;
        }

        public Criteria andBudgetOfPercentNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_of_percent not between", value1, value2, "budgetOfPercent");
            return (Criteria) this;
        }

        public Criteria andPlanProfitIsNull() {
            addCriterion("plan_profit is null");
            return (Criteria) this;
        }

        public Criteria andPlanProfitIsNotNull() {
            addCriterion("plan_profit is not null");
            return (Criteria) this;
        }

        public Criteria andPlanProfitEqualTo(BigDecimal value) {
            addCriterion("plan_profit =", value, "planProfit");
            return (Criteria) this;
        }

        public Criteria andPlanProfitNotEqualTo(BigDecimal value) {
            addCriterion("plan_profit <>", value, "planProfit");
            return (Criteria) this;
        }

        public Criteria andPlanProfitGreaterThan(BigDecimal value) {
            addCriterion("plan_profit >", value, "planProfit");
            return (Criteria) this;
        }

        public Criteria andPlanProfitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("plan_profit >=", value, "planProfit");
            return (Criteria) this;
        }

        public Criteria andPlanProfitLessThan(BigDecimal value) {
            addCriterion("plan_profit <", value, "planProfit");
            return (Criteria) this;
        }

        public Criteria andPlanProfitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("plan_profit <=", value, "planProfit");
            return (Criteria) this;
        }

        public Criteria andPlanProfitIn(List<BigDecimal> values) {
            addCriterion("plan_profit in", values, "planProfit");
            return (Criteria) this;
        }

        public Criteria andPlanProfitNotIn(List<BigDecimal> values) {
            addCriterion("plan_profit not in", values, "planProfit");
            return (Criteria) this;
        }

        public Criteria andPlanProfitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("plan_profit between", value1, value2, "planProfit");
            return (Criteria) this;
        }

        public Criteria andPlanProfitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("plan_profit not between", value1, value2, "planProfit");
            return (Criteria) this;
        }

        public Criteria andActualProfitIsNull() {
            addCriterion("actual_profit is null");
            return (Criteria) this;
        }

        public Criteria andActualProfitIsNotNull() {
            addCriterion("actual_profit is not null");
            return (Criteria) this;
        }

        public Criteria andActualProfitEqualTo(BigDecimal value) {
            addCriterion("actual_profit =", value, "actualProfit");
            return (Criteria) this;
        }

        public Criteria andActualProfitNotEqualTo(BigDecimal value) {
            addCriterion("actual_profit <>", value, "actualProfit");
            return (Criteria) this;
        }

        public Criteria andActualProfitGreaterThan(BigDecimal value) {
            addCriterion("actual_profit >", value, "actualProfit");
            return (Criteria) this;
        }

        public Criteria andActualProfitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_profit >=", value, "actualProfit");
            return (Criteria) this;
        }

        public Criteria andActualProfitLessThan(BigDecimal value) {
            addCriterion("actual_profit <", value, "actualProfit");
            return (Criteria) this;
        }

        public Criteria andActualProfitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_profit <=", value, "actualProfit");
            return (Criteria) this;
        }

        public Criteria andActualProfitIn(List<BigDecimal> values) {
            addCriterion("actual_profit in", values, "actualProfit");
            return (Criteria) this;
        }

        public Criteria andActualProfitNotIn(List<BigDecimal> values) {
            addCriterion("actual_profit not in", values, "actualProfit");
            return (Criteria) this;
        }

        public Criteria andActualProfitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_profit between", value1, value2, "actualProfit");
            return (Criteria) this;
        }

        public Criteria andActualProfitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_profit not between", value1, value2, "actualProfit");
            return (Criteria) this;
        }

        public Criteria andProfitExecutionComparisonIsNull() {
            addCriterion("profit_execution_comparison is null");
            return (Criteria) this;
        }

        public Criteria andProfitExecutionComparisonIsNotNull() {
            addCriterion("profit_execution_comparison is not null");
            return (Criteria) this;
        }

        public Criteria andProfitExecutionComparisonEqualTo(BigDecimal value) {
            addCriterion("profit_execution_comparison =", value, "profitExecutionComparison");
            return (Criteria) this;
        }

        public Criteria andProfitExecutionComparisonNotEqualTo(BigDecimal value) {
            addCriterion("profit_execution_comparison <>", value, "profitExecutionComparison");
            return (Criteria) this;
        }

        public Criteria andProfitExecutionComparisonGreaterThan(BigDecimal value) {
            addCriterion("profit_execution_comparison >", value, "profitExecutionComparison");
            return (Criteria) this;
        }

        public Criteria andProfitExecutionComparisonGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("profit_execution_comparison >=", value, "profitExecutionComparison");
            return (Criteria) this;
        }

        public Criteria andProfitExecutionComparisonLessThan(BigDecimal value) {
            addCriterion("profit_execution_comparison <", value, "profitExecutionComparison");
            return (Criteria) this;
        }

        public Criteria andProfitExecutionComparisonLessThanOrEqualTo(BigDecimal value) {
            addCriterion("profit_execution_comparison <=", value, "profitExecutionComparison");
            return (Criteria) this;
        }

        public Criteria andProfitExecutionComparisonIn(List<BigDecimal> values) {
            addCriterion("profit_execution_comparison in", values, "profitExecutionComparison");
            return (Criteria) this;
        }

        public Criteria andProfitExecutionComparisonNotIn(List<BigDecimal> values) {
            addCriterion("profit_execution_comparison not in", values, "profitExecutionComparison");
            return (Criteria) this;
        }

        public Criteria andProfitExecutionComparisonBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("profit_execution_comparison between", value1, value2, "profitExecutionComparison");
            return (Criteria) this;
        }

        public Criteria andProfitExecutionComparisonNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("profit_execution_comparison not between", value1, value2, "profitExecutionComparison");
            return (Criteria) this;
        }

        public Criteria andGrossProfitRateIsNull() {
            addCriterion("gross_profit_rate is null");
            return (Criteria) this;
        }

        public Criteria andGrossProfitRateIsNotNull() {
            addCriterion("gross_profit_rate is not null");
            return (Criteria) this;
        }

        public Criteria andGrossProfitRateEqualTo(BigDecimal value) {
            addCriterion("gross_profit_rate =", value, "grossProfitRate");
            return (Criteria) this;
        }

        public Criteria andGrossProfitRateNotEqualTo(BigDecimal value) {
            addCriterion("gross_profit_rate <>", value, "grossProfitRate");
            return (Criteria) this;
        }

        public Criteria andGrossProfitRateGreaterThan(BigDecimal value) {
            addCriterion("gross_profit_rate >", value, "grossProfitRate");
            return (Criteria) this;
        }

        public Criteria andGrossProfitRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("gross_profit_rate >=", value, "grossProfitRate");
            return (Criteria) this;
        }

        public Criteria andGrossProfitRateLessThan(BigDecimal value) {
            addCriterion("gross_profit_rate <", value, "grossProfitRate");
            return (Criteria) this;
        }

        public Criteria andGrossProfitRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("gross_profit_rate <=", value, "grossProfitRate");
            return (Criteria) this;
        }

        public Criteria andGrossProfitRateIn(List<BigDecimal> values) {
            addCriterion("gross_profit_rate in", values, "grossProfitRate");
            return (Criteria) this;
        }

        public Criteria andGrossProfitRateNotIn(List<BigDecimal> values) {
            addCriterion("gross_profit_rate not in", values, "grossProfitRate");
            return (Criteria) this;
        }

        public Criteria andGrossProfitRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("gross_profit_rate between", value1, value2, "grossProfitRate");
            return (Criteria) this;
        }

        public Criteria andGrossProfitRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("gross_profit_rate not between", value1, value2, "grossProfitRate");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNull() {
            addCriterion("contract_code is null");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNotNull() {
            addCriterion("contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andContractCodeEqualTo(String value) {
            addCriterion("contract_code =", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotEqualTo(String value) {
            addCriterion("contract_code <>", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThan(String value) {
            addCriterion("contract_code >", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_code >=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThan(String value) {
            addCriterion("contract_code <", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThanOrEqualTo(String value) {
            addCriterion("contract_code <=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLike(String value) {
            addCriterion("contract_code like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotLike(String value) {
            addCriterion("contract_code not like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeIn(List<String> values) {
            addCriterion("contract_code in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotIn(List<String> values) {
            addCriterion("contract_code not in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeBetween(String value1, String value2) {
            addCriterion("contract_code between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotBetween(String value1, String value2) {
            addCriterion("contract_code not between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNull() {
            addCriterion("start_date is null");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNotNull() {
            addCriterion("start_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateEqualTo(Date value) {
            addCriterionForJDBCDate("start_date =", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("start_date <>", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThan(Date value) {
            addCriterionForJDBCDate("start_date >", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_date >=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThan(Date value) {
            addCriterionForJDBCDate("start_date <", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_date <=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateIn(List<Date> values) {
            addCriterionForJDBCDate("start_date in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("start_date not in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_date between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_date not between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNull() {
            addCriterion("end_date is null");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNotNull() {
            addCriterion("end_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndDateEqualTo(Date value) {
            addCriterionForJDBCDate("end_date =", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("end_date <>", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThan(Date value) {
            addCriterionForJDBCDate("end_date >", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_date >=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThan(Date value) {
            addCriterionForJDBCDate("end_date <", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_date <=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIn(List<Date> values) {
            addCriterionForJDBCDate("end_date in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("end_date not in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_date between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_date not between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNull() {
            addCriterion("ou_name is null");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNotNull() {
            addCriterion("ou_name is not null");
            return (Criteria) this;
        }

        public Criteria andOuNameEqualTo(String value) {
            addCriterion("ou_name =", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotEqualTo(String value) {
            addCriterion("ou_name <>", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThan(String value) {
            addCriterion("ou_name >", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThanOrEqualTo(String value) {
            addCriterion("ou_name >=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThan(String value) {
            addCriterion("ou_name <", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThanOrEqualTo(String value) {
            addCriterion("ou_name <=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLike(String value) {
            addCriterion("ou_name like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotLike(String value) {
            addCriterion("ou_name not like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameIn(List<String> values) {
            addCriterion("ou_name in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotIn(List<String> values) {
            addCriterion("ou_name not in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameBetween(String value1, String value2) {
            addCriterion("ou_name between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotBetween(String value1, String value2) {
            addCriterion("ou_name not between", value1, value2, "ouName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}