package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目总成本记录")
public class ProjectCostSummaryRecord extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "执行批次ID")
    private Long batchNum;

    @ApiModelProperty(value = "执行ID")
    private Long executeId;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目当前状态")
    private Integer projectStatus;

    @ApiModelProperty(value = "已确认收入总额（原币）")
    private BigDecimal confirmedIncomeTotalAmount;

    @ApiModelProperty(value = "已确认收入总额（本位币）")
    private BigDecimal standardConfirmedIncomeTotalAmount;

    @ApiModelProperty(value = "已确认成本总额")
    private BigDecimal confirmedCostTotalAmount;

    @ApiModelProperty(value = "已确认毛利率")
    private BigDecimal confirmedGrossProfitRatio;

    @ApiModelProperty(value = "已确认汇兑损益")
    private BigDecimal confirmedExchangeAmount;

    @ApiModelProperty(value = "项目总预算")
    private BigDecimal projectBudget;

    @ApiModelProperty(value = "项目金额")
    private BigDecimal projectAmount;

    @ApiModelProperty(value = "合同不含税金额（本位币）")
    private BigDecimal projectContractStandardAmount;

    @ApiModelProperty(value = "已发生成本")
    private BigDecimal incurredCost;

    @ApiModelProperty(value = "剩余预算")
    private BigDecimal remainderBudget;

    @ApiModelProperty(value = "当前目标成本")
    private BigDecimal currentTargetCost;

    @ApiModelProperty(value = "执行状态(1失败/0成功/-1运行中)")
    private Integer status;

    @ApiModelProperty(value = "赢单成本小计")
    private BigDecimal winTotalCost;

    @ApiModelProperty(value = "赢单毛利额")
    private BigDecimal winGrassProfitAmount;

    @ApiModelProperty(value = "赢单毛利率")
    private BigDecimal winGrassProfitRatio;

    @ApiModelProperty(value = "当前成本小计")
    private BigDecimal currentTotalCost;

    @ApiModelProperty(value = "当前毛利额")
    private BigDecimal currentGrassProfitAmount;

    @ApiModelProperty(value = "当前毛利率")
    private BigDecimal currentGrassProfitRatio;

    @ApiModelProperty(value = "预算毛利额")
    private BigDecimal targetBudgetGrassProfitRatio;

    @ApiModelProperty(value = "预算毛利率")
    private BigDecimal targetBudgetGrassProfitAmount;

    @ApiModelProperty(value = "目标毛利率")
    private BigDecimal targetCostGrassProfitRatio;

    @ApiModelProperty(value = "目标毛利额")
    private BigDecimal targetCostGrassProfitAmount;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getBatchNum() {
        return batchNum;
    }

    public void setBatchNum(Long batchNum) {
        this.batchNum = batchNum;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public Integer getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(Integer projectStatus) {
        this.projectStatus = projectStatus;
    }

    public BigDecimal getConfirmedIncomeTotalAmount() {
        return confirmedIncomeTotalAmount;
    }

    public void setConfirmedIncomeTotalAmount(BigDecimal confirmedIncomeTotalAmount) {
        this.confirmedIncomeTotalAmount = confirmedIncomeTotalAmount;
    }

    public BigDecimal getStandardConfirmedIncomeTotalAmount() {
        return standardConfirmedIncomeTotalAmount;
    }

    public void setStandardConfirmedIncomeTotalAmount(BigDecimal standardConfirmedIncomeTotalAmount) {
        this.standardConfirmedIncomeTotalAmount = standardConfirmedIncomeTotalAmount;
    }

    public BigDecimal getConfirmedCostTotalAmount() {
        return confirmedCostTotalAmount;
    }

    public void setConfirmedCostTotalAmount(BigDecimal confirmedCostTotalAmount) {
        this.confirmedCostTotalAmount = confirmedCostTotalAmount;
    }

    public BigDecimal getConfirmedGrossProfitRatio() {
        return confirmedGrossProfitRatio;
    }

    public void setConfirmedGrossProfitRatio(BigDecimal confirmedGrossProfitRatio) {
        this.confirmedGrossProfitRatio = confirmedGrossProfitRatio;
    }

    public BigDecimal getConfirmedExchangeAmount() {
        return confirmedExchangeAmount;
    }

    public void setConfirmedExchangeAmount(BigDecimal confirmedExchangeAmount) {
        this.confirmedExchangeAmount = confirmedExchangeAmount;
    }

    public BigDecimal getProjectBudget() {
        return projectBudget;
    }

    public void setProjectBudget(BigDecimal projectBudget) {
        this.projectBudget = projectBudget;
    }

    public BigDecimal getProjectAmount() {
        return projectAmount;
    }

    public void setProjectAmount(BigDecimal projectAmount) {
        this.projectAmount = projectAmount;
    }

    public BigDecimal getProjectContractStandardAmount() {
        return projectContractStandardAmount;
    }

    public void setProjectContractStandardAmount(BigDecimal projectContractStandardAmount) {
        this.projectContractStandardAmount = projectContractStandardAmount;
    }

    public BigDecimal getIncurredCost() {
        return incurredCost;
    }

    public void setIncurredCost(BigDecimal incurredCost) {
        this.incurredCost = incurredCost;
    }

    public BigDecimal getRemainderBudget() {
        return remainderBudget;
    }

    public void setRemainderBudget(BigDecimal remainderBudget) {
        this.remainderBudget = remainderBudget;
    }

    public BigDecimal getCurrentTargetCost() {
        return currentTargetCost;
    }

    public void setCurrentTargetCost(BigDecimal currentTargetCost) {
        this.currentTargetCost = currentTargetCost;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getWinTotalCost() {
        return winTotalCost;
    }

    public void setWinTotalCost(BigDecimal winTotalCost) {
        this.winTotalCost = winTotalCost;
    }

    public BigDecimal getWinGrassProfitAmount() {
        return winGrassProfitAmount;
    }

    public void setWinGrassProfitAmount(BigDecimal winGrassProfitAmount) {
        this.winGrassProfitAmount = winGrassProfitAmount;
    }

    public BigDecimal getWinGrassProfitRatio() {
        return winGrassProfitRatio;
    }

    public void setWinGrassProfitRatio(BigDecimal winGrassProfitRatio) {
        this.winGrassProfitRatio = winGrassProfitRatio;
    }

    public BigDecimal getCurrentTotalCost() {
        return currentTotalCost;
    }

    public void setCurrentTotalCost(BigDecimal currentTotalCost) {
        this.currentTotalCost = currentTotalCost;
    }

    public BigDecimal getCurrentGrassProfitAmount() {
        return currentGrassProfitAmount;
    }

    public void setCurrentGrassProfitAmount(BigDecimal currentGrassProfitAmount) {
        this.currentGrassProfitAmount = currentGrassProfitAmount;
    }

    public BigDecimal getCurrentGrassProfitRatio() {
        return currentGrassProfitRatio;
    }

    public void setCurrentGrassProfitRatio(BigDecimal currentGrassProfitRatio) {
        this.currentGrassProfitRatio = currentGrassProfitRatio;
    }

    public BigDecimal getTargetBudgetGrassProfitRatio() {
        return targetBudgetGrassProfitRatio;
    }

    public void setTargetBudgetGrassProfitRatio(BigDecimal targetBudgetGrassProfitRatio) {
        this.targetBudgetGrassProfitRatio = targetBudgetGrassProfitRatio;
    }

    public BigDecimal getTargetBudgetGrassProfitAmount() {
        return targetBudgetGrassProfitAmount;
    }

    public void setTargetBudgetGrassProfitAmount(BigDecimal targetBudgetGrassProfitAmount) {
        this.targetBudgetGrassProfitAmount = targetBudgetGrassProfitAmount;
    }

    public BigDecimal getTargetCostGrassProfitRatio() {
        return targetCostGrassProfitRatio;
    }

    public void setTargetCostGrassProfitRatio(BigDecimal targetCostGrassProfitRatio) {
        this.targetCostGrassProfitRatio = targetCostGrassProfitRatio;
    }

    public BigDecimal getTargetCostGrassProfitAmount() {
        return targetCostGrassProfitAmount;
    }

    public void setTargetCostGrassProfitAmount(BigDecimal targetCostGrassProfitAmount) {
        this.targetCostGrassProfitAmount = targetCostGrassProfitAmount;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", batchNum=").append(batchNum);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectStatus=").append(projectStatus);
        sb.append(", confirmedIncomeTotalAmount=").append(confirmedIncomeTotalAmount);
        sb.append(", standardConfirmedIncomeTotalAmount=").append(standardConfirmedIncomeTotalAmount);
        sb.append(", confirmedCostTotalAmount=").append(confirmedCostTotalAmount);
        sb.append(", confirmedGrossProfitRatio=").append(confirmedGrossProfitRatio);
        sb.append(", confirmedExchangeAmount=").append(confirmedExchangeAmount);
        sb.append(", projectBudget=").append(projectBudget);
        sb.append(", projectAmount=").append(projectAmount);
        sb.append(", projectContractStandardAmount=").append(projectContractStandardAmount);
        sb.append(", incurredCost=").append(incurredCost);
        sb.append(", remainderBudget=").append(remainderBudget);
        sb.append(", currentTargetCost=").append(currentTargetCost);
        sb.append(", status=").append(status);
        sb.append(", winTotalCost=").append(winTotalCost);
        sb.append(", winGrassProfitAmount=").append(winGrassProfitAmount);
        sb.append(", winGrassProfitRatio=").append(winGrassProfitRatio);
        sb.append(", currentTotalCost=").append(currentTotalCost);
        sb.append(", currentGrassProfitAmount=").append(currentGrassProfitAmount);
        sb.append(", currentGrassProfitRatio=").append(currentGrassProfitRatio);
        sb.append(", targetBudgetGrassProfitRatio=").append(targetBudgetGrassProfitRatio);
        sb.append(", targetBudgetGrassProfitAmount=").append(targetBudgetGrassProfitAmount);
        sb.append(", targetCostGrassProfitRatio=").append(targetCostGrassProfitRatio);
        sb.append(", targetCostGrassProfitAmount=").append(targetCostGrassProfitAmount);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}