package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: common-module
 * @description: 实际成本归集-资产折旧成本明细导出实体
 * @author:ygx
 * @create:2025-01-22 16:52
 **/
@Setter
@Getter
public class ProjectAssetDeprnCostDetailExcelVO {
    @Excel(name = "序号", width = 10)
    private Integer num;

    @Excel(name = "归集日期", width = 20, format = "yyyy-MM-dd")
    private Date collectionDate;

    @Excel(name = "成本发生日期", width = 20, format = "yyyy-MM-dd")
    private Date costDate;

    @Excel(name = "结转状态", width = 20, replace = {"未结转_0", "已结转_1", "未结转_null"})
    private Integer carryStatus;

    @Excel(name = "结转期间", width = 20)
    private String glPeriod;

    @Excel(name = "项目编号", width = 30)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "资产编号", width = 30)
    private String assetNumber;

    @Excel(name = "资产说明", width = 50)
    private String description;

    @Excel(name = "折旧期间", width = 20)
    private String periodName;

    @Excel(name = "折旧金额", width = 30)
    private BigDecimal deprnAmount;

    @Excel(name = "业务实体", width = 30)
    private String ouName;
}
