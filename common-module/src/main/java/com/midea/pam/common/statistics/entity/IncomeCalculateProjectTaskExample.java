package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class IncomeCalculateProjectTaskExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public IncomeCalculateProjectTaskExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCalculateIdIsNull() {
            addCriterion("calculate_id is null");
            return (Criteria) this;
        }

        public Criteria andCalculateIdIsNotNull() {
            addCriterion("calculate_id is not null");
            return (Criteria) this;
        }

        public Criteria andCalculateIdEqualTo(Long value) {
            addCriterion("calculate_id =", value, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdNotEqualTo(Long value) {
            addCriterion("calculate_id <>", value, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdGreaterThan(Long value) {
            addCriterion("calculate_id >", value, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("calculate_id >=", value, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdLessThan(Long value) {
            addCriterion("calculate_id <", value, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdLessThanOrEqualTo(Long value) {
            addCriterion("calculate_id <=", value, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdIn(List<Long> values) {
            addCriterion("calculate_id in", values, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdNotIn(List<Long> values) {
            addCriterion("calculate_id not in", values, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdBetween(Long value1, Long value2) {
            addCriterion("calculate_id between", value1, value2, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdNotBetween(Long value1, Long value2) {
            addCriterion("calculate_id not between", value1, value2, "calculateId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNull() {
            addCriterion("project_manager is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNotNull() {
            addCriterion("project_manager is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerEqualTo(Long value) {
            addCriterion("project_manager =", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotEqualTo(Long value) {
            addCriterion("project_manager <>", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThan(Long value) {
            addCriterion("project_manager >", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThanOrEqualTo(Long value) {
            addCriterion("project_manager >=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThan(Long value) {
            addCriterion("project_manager <", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThanOrEqualTo(Long value) {
            addCriterion("project_manager <=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIn(List<Long> values) {
            addCriterion("project_manager in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotIn(List<Long> values) {
            addCriterion("project_manager not in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerBetween(Long value1, Long value2) {
            addCriterion("project_manager between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotBetween(Long value1, Long value2) {
            addCriterion("project_manager not between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameIsNull() {
            addCriterion("project_manager_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameIsNotNull() {
            addCriterion("project_manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameEqualTo(String value) {
            addCriterion("project_manager_name =", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameNotEqualTo(String value) {
            addCriterion("project_manager_name <>", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameGreaterThan(String value) {
            addCriterion("project_manager_name >", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_manager_name >=", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameLessThan(String value) {
            addCriterion("project_manager_name <", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameLessThanOrEqualTo(String value) {
            addCriterion("project_manager_name <=", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameLike(String value) {
            addCriterion("project_manager_name like", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameNotLike(String value) {
            addCriterion("project_manager_name not like", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameIn(List<String> values) {
            addCriterion("project_manager_name in", values, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameNotIn(List<String> values) {
            addCriterion("project_manager_name not in", values, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameBetween(String value1, String value2) {
            addCriterion("project_manager_name between", value1, value2, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameNotBetween(String value1, String value2) {
            addCriterion("project_manager_name not between", value1, value2, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNull() {
            addCriterion("project_type is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNotNull() {
            addCriterion("project_type is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeEqualTo(Long value) {
            addCriterion("project_type =", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotEqualTo(Long value) {
            addCriterion("project_type <>", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThan(Long value) {
            addCriterion("project_type >", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThanOrEqualTo(Long value) {
            addCriterion("project_type >=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThan(Long value) {
            addCriterion("project_type <", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThanOrEqualTo(Long value) {
            addCriterion("project_type <=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIn(List<Long> values) {
            addCriterion("project_type in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotIn(List<Long> values) {
            addCriterion("project_type not in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeBetween(Long value1, Long value2) {
            addCriterion("project_type between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotBetween(Long value1, Long value2) {
            addCriterion("project_type not between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameIsNull() {
            addCriterion("project_type_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameIsNotNull() {
            addCriterion("project_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameEqualTo(String value) {
            addCriterion("project_type_name =", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotEqualTo(String value) {
            addCriterion("project_type_name <>", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameGreaterThan(String value) {
            addCriterion("project_type_name >", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_type_name >=", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameLessThan(String value) {
            addCriterion("project_type_name <", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameLessThanOrEqualTo(String value) {
            addCriterion("project_type_name <=", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameLike(String value) {
            addCriterion("project_type_name like", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotLike(String value) {
            addCriterion("project_type_name not like", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameIn(List<String> values) {
            addCriterion("project_type_name in", values, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotIn(List<String> values) {
            addCriterion("project_type_name not in", values, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameBetween(String value1, String value2) {
            addCriterion("project_type_name between", value1, value2, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotBetween(String value1, String value2) {
            addCriterion("project_type_name not between", value1, value2, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeIsNull() {
            addCriterion("project_price_type is null");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeIsNotNull() {
            addCriterion("project_price_type is not null");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeEqualTo(String value) {
            addCriterion("project_price_type =", value, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeNotEqualTo(String value) {
            addCriterion("project_price_type <>", value, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeGreaterThan(String value) {
            addCriterion("project_price_type >", value, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeGreaterThanOrEqualTo(String value) {
            addCriterion("project_price_type >=", value, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeLessThan(String value) {
            addCriterion("project_price_type <", value, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeLessThanOrEqualTo(String value) {
            addCriterion("project_price_type <=", value, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeLike(String value) {
            addCriterion("project_price_type like", value, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeNotLike(String value) {
            addCriterion("project_price_type not like", value, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeIn(List<String> values) {
            addCriterion("project_price_type in", values, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeNotIn(List<String> values) {
            addCriterion("project_price_type not in", values, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeBetween(String value1, String value2) {
            addCriterion("project_price_type between", value1, value2, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeNotBetween(String value1, String value2) {
            addCriterion("project_price_type not between", value1, value2, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Long value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Long value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Long value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Long value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Long> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Long> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Long value1, Long value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNull() {
            addCriterion("unit_name is null");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNotNull() {
            addCriterion("unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andUnitNameEqualTo(String value) {
            addCriterion("unit_name =", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotEqualTo(String value) {
            addCriterion("unit_name <>", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThan(String value) {
            addCriterion("unit_name >", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("unit_name >=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThan(String value) {
            addCriterion("unit_name <", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThanOrEqualTo(String value) {
            addCriterion("unit_name <=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLike(String value) {
            addCriterion("unit_name like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotLike(String value) {
            addCriterion("unit_name not like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIn(List<String> values) {
            addCriterion("unit_name in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotIn(List<String> values) {
            addCriterion("unit_name not in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameBetween(String value1, String value2) {
            addCriterion("unit_name between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotBetween(String value1, String value2) {
            addCriterion("unit_name not between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdIsNull() {
            addCriterion("product_org_id is null");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdIsNotNull() {
            addCriterion("product_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdEqualTo(Long value) {
            addCriterion("product_org_id =", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdNotEqualTo(Long value) {
            addCriterion("product_org_id <>", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdGreaterThan(Long value) {
            addCriterion("product_org_id >", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("product_org_id >=", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdLessThan(Long value) {
            addCriterion("product_org_id <", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("product_org_id <=", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdIn(List<Long> values) {
            addCriterion("product_org_id in", values, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdNotIn(List<Long> values) {
            addCriterion("product_org_id not in", values, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdBetween(Long value1, Long value2) {
            addCriterion("product_org_id between", value1, value2, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("product_org_id not between", value1, value2, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameIsNull() {
            addCriterion("product_org_name is null");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameIsNotNull() {
            addCriterion("product_org_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameEqualTo(String value) {
            addCriterion("product_org_name =", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameNotEqualTo(String value) {
            addCriterion("product_org_name <>", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameGreaterThan(String value) {
            addCriterion("product_org_name >", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_org_name >=", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameLessThan(String value) {
            addCriterion("product_org_name <", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameLessThanOrEqualTo(String value) {
            addCriterion("product_org_name <=", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameLike(String value) {
            addCriterion("product_org_name like", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameNotLike(String value) {
            addCriterion("product_org_name not like", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameIn(List<String> values) {
            addCriterion("product_org_name in", values, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameNotIn(List<String> values) {
            addCriterion("product_org_name not in", values, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameBetween(String value1, String value2) {
            addCriterion("product_org_name between", value1, value2, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameNotBetween(String value1, String value2) {
            addCriterion("product_org_name not between", value1, value2, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNull() {
            addCriterion("department_id is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNotNull() {
            addCriterion("department_id is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdEqualTo(Long value) {
            addCriterion("department_id =", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotEqualTo(Long value) {
            addCriterion("department_id <>", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThan(Long value) {
            addCriterion("department_id >", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("department_id >=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThan(Long value) {
            addCriterion("department_id <", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThanOrEqualTo(Long value) {
            addCriterion("department_id <=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIn(List<Long> values) {
            addCriterion("department_id in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotIn(List<Long> values) {
            addCriterion("department_id not in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdBetween(Long value1, Long value2) {
            addCriterion("department_id between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotBetween(Long value1, Long value2) {
            addCriterion("department_id not between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIsNull() {
            addCriterion("department_name is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIsNotNull() {
            addCriterion("department_name is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameEqualTo(String value) {
            addCriterion("department_name =", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotEqualTo(String value) {
            addCriterion("department_name <>", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameGreaterThan(String value) {
            addCriterion("department_name >", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameGreaterThanOrEqualTo(String value) {
            addCriterion("department_name >=", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLessThan(String value) {
            addCriterion("department_name <", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLessThanOrEqualTo(String value) {
            addCriterion("department_name <=", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLike(String value) {
            addCriterion("department_name like", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotLike(String value) {
            addCriterion("department_name not like", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIn(List<String> values) {
            addCriterion("department_name in", values, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotIn(List<String> values) {
            addCriterion("department_name not in", values, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameBetween(String value1, String value2) {
            addCriterion("department_name between", value1, value2, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotBetween(String value1, String value2) {
            addCriterion("department_name not between", value1, value2, "departmentName");
            return (Criteria) this;
        }

        public Criteria andIncomePointIdIsNull() {
            addCriterion("income_point_id is null");
            return (Criteria) this;
        }

        public Criteria andIncomePointIdIsNotNull() {
            addCriterion("income_point_id is not null");
            return (Criteria) this;
        }

        public Criteria andIncomePointIdEqualTo(Long value) {
            addCriterion("income_point_id =", value, "incomePointId");
            return (Criteria) this;
        }

        public Criteria andIncomePointIdNotEqualTo(Long value) {
            addCriterion("income_point_id <>", value, "incomePointId");
            return (Criteria) this;
        }

        public Criteria andIncomePointIdGreaterThan(Long value) {
            addCriterion("income_point_id >", value, "incomePointId");
            return (Criteria) this;
        }

        public Criteria andIncomePointIdGreaterThanOrEqualTo(Long value) {
            addCriterion("income_point_id >=", value, "incomePointId");
            return (Criteria) this;
        }

        public Criteria andIncomePointIdLessThan(Long value) {
            addCriterion("income_point_id <", value, "incomePointId");
            return (Criteria) this;
        }

        public Criteria andIncomePointIdLessThanOrEqualTo(Long value) {
            addCriterion("income_point_id <=", value, "incomePointId");
            return (Criteria) this;
        }

        public Criteria andIncomePointIdIn(List<Long> values) {
            addCriterion("income_point_id in", values, "incomePointId");
            return (Criteria) this;
        }

        public Criteria andIncomePointIdNotIn(List<Long> values) {
            addCriterion("income_point_id not in", values, "incomePointId");
            return (Criteria) this;
        }

        public Criteria andIncomePointIdBetween(Long value1, Long value2) {
            addCriterion("income_point_id between", value1, value2, "incomePointId");
            return (Criteria) this;
        }

        public Criteria andIncomePointIdNotBetween(Long value1, Long value2) {
            addCriterion("income_point_id not between", value1, value2, "incomePointId");
            return (Criteria) this;
        }

        public Criteria andIncomePointNameIsNull() {
            addCriterion("income_point_name is null");
            return (Criteria) this;
        }

        public Criteria andIncomePointNameIsNotNull() {
            addCriterion("income_point_name is not null");
            return (Criteria) this;
        }

        public Criteria andIncomePointNameEqualTo(String value) {
            addCriterion("income_point_name =", value, "incomePointName");
            return (Criteria) this;
        }

        public Criteria andIncomePointNameNotEqualTo(String value) {
            addCriterion("income_point_name <>", value, "incomePointName");
            return (Criteria) this;
        }

        public Criteria andIncomePointNameGreaterThan(String value) {
            addCriterion("income_point_name >", value, "incomePointName");
            return (Criteria) this;
        }

        public Criteria andIncomePointNameGreaterThanOrEqualTo(String value) {
            addCriterion("income_point_name >=", value, "incomePointName");
            return (Criteria) this;
        }

        public Criteria andIncomePointNameLessThan(String value) {
            addCriterion("income_point_name <", value, "incomePointName");
            return (Criteria) this;
        }

        public Criteria andIncomePointNameLessThanOrEqualTo(String value) {
            addCriterion("income_point_name <=", value, "incomePointName");
            return (Criteria) this;
        }

        public Criteria andIncomePointNameLike(String value) {
            addCriterion("income_point_name like", value, "incomePointName");
            return (Criteria) this;
        }

        public Criteria andIncomePointNameNotLike(String value) {
            addCriterion("income_point_name not like", value, "incomePointName");
            return (Criteria) this;
        }

        public Criteria andIncomePointNameIn(List<String> values) {
            addCriterion("income_point_name in", values, "incomePointName");
            return (Criteria) this;
        }

        public Criteria andIncomePointNameNotIn(List<String> values) {
            addCriterion("income_point_name not in", values, "incomePointName");
            return (Criteria) this;
        }

        public Criteria andIncomePointNameBetween(String value1, String value2) {
            addCriterion("income_point_name between", value1, value2, "incomePointName");
            return (Criteria) this;
        }

        public Criteria andIncomePointNameNotBetween(String value1, String value2) {
            addCriterion("income_point_name not between", value1, value2, "incomePointName");
            return (Criteria) this;
        }

        public Criteria andIncomePointTypeIsNull() {
            addCriterion("income_point_type is null");
            return (Criteria) this;
        }

        public Criteria andIncomePointTypeIsNotNull() {
            addCriterion("income_point_type is not null");
            return (Criteria) this;
        }

        public Criteria andIncomePointTypeEqualTo(Integer value) {
            addCriterion("income_point_type =", value, "incomePointType");
            return (Criteria) this;
        }

        public Criteria andIncomePointTypeNotEqualTo(Integer value) {
            addCriterion("income_point_type <>", value, "incomePointType");
            return (Criteria) this;
        }

        public Criteria andIncomePointTypeGreaterThan(Integer value) {
            addCriterion("income_point_type >", value, "incomePointType");
            return (Criteria) this;
        }

        public Criteria andIncomePointTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("income_point_type >=", value, "incomePointType");
            return (Criteria) this;
        }

        public Criteria andIncomePointTypeLessThan(Integer value) {
            addCriterion("income_point_type <", value, "incomePointType");
            return (Criteria) this;
        }

        public Criteria andIncomePointTypeLessThanOrEqualTo(Integer value) {
            addCriterion("income_point_type <=", value, "incomePointType");
            return (Criteria) this;
        }

        public Criteria andIncomePointTypeIn(List<Integer> values) {
            addCriterion("income_point_type in", values, "incomePointType");
            return (Criteria) this;
        }

        public Criteria andIncomePointTypeNotIn(List<Integer> values) {
            addCriterion("income_point_type not in", values, "incomePointType");
            return (Criteria) this;
        }

        public Criteria andIncomePointTypeBetween(Integer value1, Integer value2) {
            addCriterion("income_point_type between", value1, value2, "incomePointType");
            return (Criteria) this;
        }

        public Criteria andIncomePointTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("income_point_type not between", value1, value2, "incomePointType");
            return (Criteria) this;
        }

        public Criteria andHelpFlagIsNull() {
            addCriterion("help_flag is null");
            return (Criteria) this;
        }

        public Criteria andHelpFlagIsNotNull() {
            addCriterion("help_flag is not null");
            return (Criteria) this;
        }

        public Criteria andHelpFlagEqualTo(Boolean value) {
            addCriterion("help_flag =", value, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagNotEqualTo(Boolean value) {
            addCriterion("help_flag <>", value, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagGreaterThan(Boolean value) {
            addCriterion("help_flag >", value, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("help_flag >=", value, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagLessThan(Boolean value) {
            addCriterion("help_flag <", value, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("help_flag <=", value, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagIn(List<Boolean> values) {
            addCriterion("help_flag in", values, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagNotIn(List<Boolean> values) {
            addCriterion("help_flag not in", values, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("help_flag between", value1, value2, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andHelpFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("help_flag not between", value1, value2, "helpFlag");
            return (Criteria) this;
        }

        public Criteria andMainIncomeIsNull() {
            addCriterion("main_income is null");
            return (Criteria) this;
        }

        public Criteria andMainIncomeIsNotNull() {
            addCriterion("main_income is not null");
            return (Criteria) this;
        }

        public Criteria andMainIncomeEqualTo(BigDecimal value) {
            addCriterion("main_income =", value, "mainIncome");
            return (Criteria) this;
        }

        public Criteria andMainIncomeNotEqualTo(BigDecimal value) {
            addCriterion("main_income <>", value, "mainIncome");
            return (Criteria) this;
        }

        public Criteria andMainIncomeGreaterThan(BigDecimal value) {
            addCriterion("main_income >", value, "mainIncome");
            return (Criteria) this;
        }

        public Criteria andMainIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("main_income >=", value, "mainIncome");
            return (Criteria) this;
        }

        public Criteria andMainIncomeLessThan(BigDecimal value) {
            addCriterion("main_income <", value, "mainIncome");
            return (Criteria) this;
        }

        public Criteria andMainIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("main_income <=", value, "mainIncome");
            return (Criteria) this;
        }

        public Criteria andMainIncomeIn(List<BigDecimal> values) {
            addCriterion("main_income in", values, "mainIncome");
            return (Criteria) this;
        }

        public Criteria andMainIncomeNotIn(List<BigDecimal> values) {
            addCriterion("main_income not in", values, "mainIncome");
            return (Criteria) this;
        }

        public Criteria andMainIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("main_income between", value1, value2, "mainIncome");
            return (Criteria) this;
        }

        public Criteria andMainIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("main_income not between", value1, value2, "mainIncome");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeIsNull() {
            addCriterion("help_income is null");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeIsNotNull() {
            addCriterion("help_income is not null");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeEqualTo(BigDecimal value) {
            addCriterion("help_income =", value, "helpIncome");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeNotEqualTo(BigDecimal value) {
            addCriterion("help_income <>", value, "helpIncome");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeGreaterThan(BigDecimal value) {
            addCriterion("help_income >", value, "helpIncome");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("help_income >=", value, "helpIncome");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeLessThan(BigDecimal value) {
            addCriterion("help_income <", value, "helpIncome");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("help_income <=", value, "helpIncome");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeIn(List<BigDecimal> values) {
            addCriterion("help_income in", values, "helpIncome");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeNotIn(List<BigDecimal> values) {
            addCriterion("help_income not in", values, "helpIncome");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("help_income between", value1, value2, "helpIncome");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("help_income not between", value1, value2, "helpIncome");
            return (Criteria) this;
        }

        public Criteria andCarryStatusIsNull() {
            addCriterion("carry_status is null");
            return (Criteria) this;
        }

        public Criteria andCarryStatusIsNotNull() {
            addCriterion("carry_status is not null");
            return (Criteria) this;
        }

        public Criteria andCarryStatusEqualTo(Boolean value) {
            addCriterion("carry_status =", value, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusNotEqualTo(Boolean value) {
            addCriterion("carry_status <>", value, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusGreaterThan(Boolean value) {
            addCriterion("carry_status >", value, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusGreaterThanOrEqualTo(Boolean value) {
            addCriterion("carry_status >=", value, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusLessThan(Boolean value) {
            addCriterion("carry_status <", value, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusLessThanOrEqualTo(Boolean value) {
            addCriterion("carry_status <=", value, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusIn(List<Boolean> values) {
            addCriterion("carry_status in", values, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusNotIn(List<Boolean> values) {
            addCriterion("carry_status not in", values, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusBetween(Boolean value1, Boolean value2) {
            addCriterion("carry_status between", value1, value2, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusNotBetween(Boolean value1, Boolean value2) {
            addCriterion("carry_status not between", value1, value2, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCostIsNull() {
            addCriterion("cost is null");
            return (Criteria) this;
        }

        public Criteria andCostIsNotNull() {
            addCriterion("cost is not null");
            return (Criteria) this;
        }

        public Criteria andCostEqualTo(BigDecimal value) {
            addCriterion("cost =", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostNotEqualTo(BigDecimal value) {
            addCriterion("cost <>", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostGreaterThan(BigDecimal value) {
            addCriterion("cost >", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cost >=", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostLessThan(BigDecimal value) {
            addCriterion("cost <", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cost <=", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostIn(List<BigDecimal> values) {
            addCriterion("cost in", values, "cost");
            return (Criteria) this;
        }

        public Criteria andCostNotIn(List<BigDecimal> values) {
            addCriterion("cost not in", values, "cost");
            return (Criteria) this;
        }

        public Criteria andCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost between", value1, value2, "cost");
            return (Criteria) this;
        }

        public Criteria andCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost not between", value1, value2, "cost");
            return (Criteria) this;
        }

        public Criteria andIncomeIsNull() {
            addCriterion("income is null");
            return (Criteria) this;
        }

        public Criteria andIncomeIsNotNull() {
            addCriterion("income is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeEqualTo(BigDecimal value) {
            addCriterion("income =", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotEqualTo(BigDecimal value) {
            addCriterion("income <>", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeGreaterThan(BigDecimal value) {
            addCriterion("income >", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income >=", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeLessThan(BigDecimal value) {
            addCriterion("income <", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income <=", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeIn(List<BigDecimal> values) {
            addCriterion("income in", values, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotIn(List<BigDecimal> values) {
            addCriterion("income not in", values, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income between", value1, value2, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income not between", value1, value2, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioIsNull() {
            addCriterion("income_ratio is null");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioIsNotNull() {
            addCriterion("income_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioEqualTo(BigDecimal value) {
            addCriterion("income_ratio =", value, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioNotEqualTo(BigDecimal value) {
            addCriterion("income_ratio <>", value, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioGreaterThan(BigDecimal value) {
            addCriterion("income_ratio >", value, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income_ratio >=", value, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioLessThan(BigDecimal value) {
            addCriterion("income_ratio <", value, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income_ratio <=", value, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioIn(List<BigDecimal> values) {
            addCriterion("income_ratio in", values, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioNotIn(List<BigDecimal> values) {
            addCriterion("income_ratio not in", values, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_ratio between", value1, value2, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andIncomeRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_ratio not between", value1, value2, "incomeRatio");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeRatioIsNull() {
            addCriterion("plan_income_ratio is null");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeRatioIsNotNull() {
            addCriterion("plan_income_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeRatioEqualTo(BigDecimal value) {
            addCriterion("plan_income_ratio =", value, "planIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeRatioNotEqualTo(BigDecimal value) {
            addCriterion("plan_income_ratio <>", value, "planIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeRatioGreaterThan(BigDecimal value) {
            addCriterion("plan_income_ratio >", value, "planIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("plan_income_ratio >=", value, "planIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeRatioLessThan(BigDecimal value) {
            addCriterion("plan_income_ratio <", value, "planIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("plan_income_ratio <=", value, "planIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeRatioIn(List<BigDecimal> values) {
            addCriterion("plan_income_ratio in", values, "planIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeRatioNotIn(List<BigDecimal> values) {
            addCriterion("plan_income_ratio not in", values, "planIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("plan_income_ratio between", value1, value2, "planIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("plan_income_ratio not between", value1, value2, "planIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeIsNull() {
            addCriterion("plan_income is null");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeIsNotNull() {
            addCriterion("plan_income is not null");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeEqualTo(BigDecimal value) {
            addCriterion("plan_income =", value, "planIncome");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeNotEqualTo(BigDecimal value) {
            addCriterion("plan_income <>", value, "planIncome");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeGreaterThan(BigDecimal value) {
            addCriterion("plan_income >", value, "planIncome");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("plan_income >=", value, "planIncome");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeLessThan(BigDecimal value) {
            addCriterion("plan_income <", value, "planIncome");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("plan_income <=", value, "planIncome");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeIn(List<BigDecimal> values) {
            addCriterion("plan_income in", values, "planIncome");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeNotIn(List<BigDecimal> values) {
            addCriterion("plan_income not in", values, "planIncome");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("plan_income between", value1, value2, "planIncome");
            return (Criteria) this;
        }

        public Criteria andPlanIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("plan_income not between", value1, value2, "planIncome");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Date value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Date value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Date value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Date> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Date> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeIsNull() {
            addCriterion("actual_end_time is null");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeIsNotNull() {
            addCriterion("actual_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeEqualTo(Date value) {
            addCriterion("actual_end_time =", value, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeNotEqualTo(Date value) {
            addCriterion("actual_end_time <>", value, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeGreaterThan(Date value) {
            addCriterion("actual_end_time >", value, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("actual_end_time >=", value, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeLessThan(Date value) {
            addCriterion("actual_end_time <", value, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("actual_end_time <=", value, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeIn(List<Date> values) {
            addCriterion("actual_end_time in", values, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeNotIn(List<Date> values) {
            addCriterion("actual_end_time not in", values, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeBetween(Date value1, Date value2) {
            addCriterion("actual_end_time between", value1, value2, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andActualEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("actual_end_time not between", value1, value2, "actualEndTime");
            return (Criteria) this;
        }

        public Criteria andMonthIsNull() {
            addCriterion("month is null");
            return (Criteria) this;
        }

        public Criteria andMonthIsNotNull() {
            addCriterion("month is not null");
            return (Criteria) this;
        }

        public Criteria andMonthEqualTo(String value) {
            addCriterion("month =", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotEqualTo(String value) {
            addCriterion("month <>", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthGreaterThan(String value) {
            addCriterion("month >", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthGreaterThanOrEqualTo(String value) {
            addCriterion("month >=", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthLessThan(String value) {
            addCriterion("month <", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthLessThanOrEqualTo(String value) {
            addCriterion("month <=", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthLike(String value) {
            addCriterion("month like", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotLike(String value) {
            addCriterion("month not like", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthIn(List<String> values) {
            addCriterion("month in", values, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotIn(List<String> values) {
            addCriterion("month not in", values, "month");
            return (Criteria) this;
        }

        public Criteria andMonthBetween(String value1, String value2) {
            addCriterion("month between", value1, value2, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotBetween(String value1, String value2) {
            addCriterion("month not between", value1, value2, "month");
            return (Criteria) this;
        }

        public Criteria andContractNameIsNull() {
            addCriterion("contract_name is null");
            return (Criteria) this;
        }

        public Criteria andContractNameIsNotNull() {
            addCriterion("contract_name is not null");
            return (Criteria) this;
        }

        public Criteria andContractNameEqualTo(String value) {
            addCriterion("contract_name =", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotEqualTo(String value) {
            addCriterion("contract_name <>", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThan(String value) {
            addCriterion("contract_name >", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThanOrEqualTo(String value) {
            addCriterion("contract_name >=", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameLessThan(String value) {
            addCriterion("contract_name <", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameLessThanOrEqualTo(String value) {
            addCriterion("contract_name <=", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameLike(String value) {
            addCriterion("contract_name like", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotLike(String value) {
            addCriterion("contract_name not like", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameIn(List<String> values) {
            addCriterion("contract_name in", values, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotIn(List<String> values) {
            addCriterion("contract_name not in", values, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameBetween(String value1, String value2) {
            addCriterion("contract_name between", value1, value2, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotBetween(String value1, String value2) {
            addCriterion("contract_name not between", value1, value2, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNull() {
            addCriterion("contract_code is null");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNotNull() {
            addCriterion("contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andContractCodeEqualTo(String value) {
            addCriterion("contract_code =", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotEqualTo(String value) {
            addCriterion("contract_code <>", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThan(String value) {
            addCriterion("contract_code >", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_code >=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThan(String value) {
            addCriterion("contract_code <", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThanOrEqualTo(String value) {
            addCriterion("contract_code <=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLike(String value) {
            addCriterion("contract_code like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotLike(String value) {
            addCriterion("contract_code not like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeIn(List<String> values) {
            addCriterion("contract_code in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotIn(List<String> values) {
            addCriterion("contract_code not in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeBetween(String value1, String value2) {
            addCriterion("contract_code between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotBetween(String value1, String value2) {
            addCriterion("contract_code not between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}