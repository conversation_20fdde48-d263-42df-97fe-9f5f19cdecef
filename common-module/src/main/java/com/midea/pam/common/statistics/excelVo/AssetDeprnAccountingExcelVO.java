package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class AssetDeprnAccountingExcelVO {

    @Excel(name = "序号")
    private Integer num;

    @Excel(name = "入账单号", width = 25)
    private String code;

    @Excel(name = "状态", width = 15, replace = {"生效_0", "作废_1", "_null"})
    private Integer status;

    @Excel(name = "折旧期间", width = 10)
    private String periodName;

    @Excel(name = "折旧金额", width = 30)
    private BigDecimal totalDeprnAmount;

    @Excel(name = "入账日期", width = 20, exportFormat = "yyyy-MM-dd")
    private Date glDate;

    @Excel(name = "会计期间", width = 20)
    private String glPeriod;

    @Excel(name = "同步状态", width = 15, replace = {"已同步_1", "未同步_2", "同步中_3", "同步失败_4", "_null"})
    private Integer erpStatus;

    @Excel(name = "币种")
    private String currency;

    @Excel(name = "冲销状态", width = 15, replace = {"未冲销_1", "已冲销_2", "不可冲销_3", "_null"})
    private Integer writeOffStatus;

    @Excel(name = "业务实体", width = 30)
    private String ouName;

    @Excel(name = "创建人", width = 20)
    private String createByName;

    @Excel(name = "创建日期", width = 20, exportFormat = "yyyy-MM-dd")
    private Date createAt;
}
