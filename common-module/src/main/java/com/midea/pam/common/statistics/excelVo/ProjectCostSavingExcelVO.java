package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Description
 * Created by chenchong
 * Date 2022/09/05 16:57
 */
@Setter
@Getter
public class ProjectCostSavingExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer number;

    @Excel(name = "项目号", width = 20)
    private String projectCode;

    @Excel(name = "需求单号", width = 25)
    private String requirementCode;

    @Excel(name = "PAM物料编码", width = 25)
    private String pamCode;

    @Excel(name = "ERP物料编码", width = 25)
    private String erpCode;

    @Excel(name = "物料描述", width = 25)
    private String materielDescr;

    @Excel(name = "wbs编码", width = 25)
    private String wbsSummaryCode;

    @Excel(name = "活动事项编码", width = 15)
    private String activityCode;

    @Excel(name = "详设单据状态", width = 20, replace = {"草稿_0", "待处理_1", "审核中_2", "驳回_3", "生效_4", "作废_5", "变更中_6", "'已提交'_7", " _null"})
    private Integer requirementStatus;

    @Excel(name = "需求分类", width = 15, replace = {"物料采购_0", "外包_1"})
    private Integer demandType;

    @Excel(name = "需求单据-采购需求状态", replace = { "已完成_true", "未完成_false", "_null" })
    private Boolean receiptsStatus;

    @Excel(name = "采购需求状态", width = 15, replace = {"已完成_0", "未完成_1"})
    private Integer purchaseStatus;

    @Excel(name = "需求审批通过日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date demandApprovalTime;

    @Excel(name = "需求预算总额", width = 20)
    private BigDecimal budgetOccupiedAmount;

    @Excel(name = "采购订单下达汇总数量", width = 20)
    private BigDecimal releasedQuantity;

    @Excel(name = "采购订单下达汇总金额", width = 20)
    private BigDecimal totalAmount;

    @Excel(name = "节约金额Saving", width = 20)
    private BigDecimal saving;

    @Excel(name = "累计收货数量", width = 20)
    private BigDecimal receiptQuantity;

    @Excel(name = "累计收货金额", width = 20)
    private BigDecimal receiptAmount;

    @Excel(name = "对应订单或合同", width = 30)
    private String purchaseCodes;

    @Excel(name = "供应商编号", width = 20)
    private String vendorCode;

    @Excel(name = "供应商名称", width = 20)
    private String vendorName;

    @Excel(name = "供应商地点", width = 20)
    private String vendorSiteCode;

    @Excel(name = "采购员", width = 20, replace = {" _null"})
    private String buyerNames;

    @Excel(name = "品牌", width = 25)
    private String brand;

    @Excel(name = "超预算说明", width = 30)
    private String overBudgetDes;

    @Excel(name = "备注", width = 30)
    private String remark;

}
