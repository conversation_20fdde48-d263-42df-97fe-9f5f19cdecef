package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.RolePortalTargetRel;

public class RolePortalTargetRelDto extends RolePortalTargetRel {

    private String moduleCode;

    private String moduleName;

    private String targetName;

    private Integer level;

    private String remark;

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}