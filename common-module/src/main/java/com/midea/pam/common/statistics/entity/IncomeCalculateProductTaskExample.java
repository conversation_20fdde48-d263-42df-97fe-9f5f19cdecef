package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class IncomeCalculateProductTaskExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public IncomeCalculateProductTaskExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCalculateIdIsNull() {
            addCriterion("calculate_id is null");
            return (Criteria) this;
        }

        public Criteria andCalculateIdIsNotNull() {
            addCriterion("calculate_id is not null");
            return (Criteria) this;
        }

        public Criteria andCalculateIdEqualTo(Long value) {
            addCriterion("calculate_id =", value, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdNotEqualTo(Long value) {
            addCriterion("calculate_id <>", value, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdGreaterThan(Long value) {
            addCriterion("calculate_id >", value, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("calculate_id >=", value, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdLessThan(Long value) {
            addCriterion("calculate_id <", value, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdLessThanOrEqualTo(Long value) {
            addCriterion("calculate_id <=", value, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdIn(List<Long> values) {
            addCriterion("calculate_id in", values, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdNotIn(List<Long> values) {
            addCriterion("calculate_id not in", values, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdBetween(Long value1, Long value2) {
            addCriterion("calculate_id between", value1, value2, "calculateId");
            return (Criteria) this;
        }

        public Criteria andCalculateIdNotBetween(Long value1, Long value2) {
            addCriterion("calculate_id not between", value1, value2, "calculateId");
            return (Criteria) this;
        }

        public Criteria andMonthIsNull() {
            addCriterion("month is null");
            return (Criteria) this;
        }

        public Criteria andMonthIsNotNull() {
            addCriterion("month is not null");
            return (Criteria) this;
        }

        public Criteria andMonthEqualTo(String value) {
            addCriterion("month =", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotEqualTo(String value) {
            addCriterion("month <>", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthGreaterThan(String value) {
            addCriterion("month >", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthGreaterThanOrEqualTo(String value) {
            addCriterion("month >=", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthLessThan(String value) {
            addCriterion("month <", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthLessThanOrEqualTo(String value) {
            addCriterion("month <=", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthLike(String value) {
            addCriterion("month like", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotLike(String value) {
            addCriterion("month not like", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthIn(List<String> values) {
            addCriterion("month in", values, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotIn(List<String> values) {
            addCriterion("month not in", values, "month");
            return (Criteria) this;
        }

        public Criteria andMonthBetween(String value1, String value2) {
            addCriterion("month between", value1, value2, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotBetween(String value1, String value2) {
            addCriterion("month not between", value1, value2, "month");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdIsNull() {
            addCriterion("product_org_id is null");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdIsNotNull() {
            addCriterion("product_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdEqualTo(Long value) {
            addCriterion("product_org_id =", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdNotEqualTo(Long value) {
            addCriterion("product_org_id <>", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdGreaterThan(Long value) {
            addCriterion("product_org_id >", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("product_org_id >=", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdLessThan(Long value) {
            addCriterion("product_org_id <", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("product_org_id <=", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdIn(List<Long> values) {
            addCriterion("product_org_id in", values, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdNotIn(List<Long> values) {
            addCriterion("product_org_id not in", values, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdBetween(Long value1, Long value2) {
            addCriterion("product_org_id between", value1, value2, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("product_org_id not between", value1, value2, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameIsNull() {
            addCriterion("product_org_name is null");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameIsNotNull() {
            addCriterion("product_org_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameEqualTo(String value) {
            addCriterion("product_org_name =", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameNotEqualTo(String value) {
            addCriterion("product_org_name <>", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameGreaterThan(String value) {
            addCriterion("product_org_name >", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_org_name >=", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameLessThan(String value) {
            addCriterion("product_org_name <", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameLessThanOrEqualTo(String value) {
            addCriterion("product_org_name <=", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameLike(String value) {
            addCriterion("product_org_name like", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameNotLike(String value) {
            addCriterion("product_org_name not like", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameIn(List<String> values) {
            addCriterion("product_org_name in", values, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameNotIn(List<String> values) {
            addCriterion("product_org_name not in", values, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameBetween(String value1, String value2) {
            addCriterion("product_org_name between", value1, value2, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameNotBetween(String value1, String value2) {
            addCriterion("product_org_name not between", value1, value2, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Long value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Long value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Long value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Long value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Long> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Long> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Long value1, Long value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNull() {
            addCriterion("unit_name is null");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNotNull() {
            addCriterion("unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andUnitNameEqualTo(String value) {
            addCriterion("unit_name =", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotEqualTo(String value) {
            addCriterion("unit_name <>", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThan(String value) {
            addCriterion("unit_name >", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("unit_name >=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThan(String value) {
            addCriterion("unit_name <", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThanOrEqualTo(String value) {
            addCriterion("unit_name <=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLike(String value) {
            addCriterion("unit_name like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotLike(String value) {
            addCriterion("unit_name not like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIn(List<String> values) {
            addCriterion("unit_name in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotIn(List<String> values) {
            addCriterion("unit_name not in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameBetween(String value1, String value2) {
            addCriterion("unit_name between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotBetween(String value1, String value2) {
            addCriterion("unit_name not between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNull() {
            addCriterion("department_id is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNotNull() {
            addCriterion("department_id is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdEqualTo(Long value) {
            addCriterion("department_id =", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotEqualTo(Long value) {
            addCriterion("department_id <>", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThan(Long value) {
            addCriterion("department_id >", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("department_id >=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThan(Long value) {
            addCriterion("department_id <", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThanOrEqualTo(Long value) {
            addCriterion("department_id <=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIn(List<Long> values) {
            addCriterion("department_id in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotIn(List<Long> values) {
            addCriterion("department_id not in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdBetween(Long value1, Long value2) {
            addCriterion("department_id between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotBetween(Long value1, Long value2) {
            addCriterion("department_id not between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIsNull() {
            addCriterion("department_name is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIsNotNull() {
            addCriterion("department_name is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameEqualTo(String value) {
            addCriterion("department_name =", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotEqualTo(String value) {
            addCriterion("department_name <>", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameGreaterThan(String value) {
            addCriterion("department_name >", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameGreaterThanOrEqualTo(String value) {
            addCriterion("department_name >=", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLessThan(String value) {
            addCriterion("department_name <", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLessThanOrEqualTo(String value) {
            addCriterion("department_name <=", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLike(String value) {
            addCriterion("department_name like", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotLike(String value) {
            addCriterion("department_name not like", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIn(List<String> values) {
            addCriterion("department_name in", values, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotIn(List<String> values) {
            addCriterion("department_name not in", values, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameBetween(String value1, String value2) {
            addCriterion("department_name between", value1, value2, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotBetween(String value1, String value2) {
            addCriterion("department_name not between", value1, value2, "departmentName");
            return (Criteria) this;
        }

        public Criteria andProductManagerIsNull() {
            addCriterion("product_manager is null");
            return (Criteria) this;
        }

        public Criteria andProductManagerIsNotNull() {
            addCriterion("product_manager is not null");
            return (Criteria) this;
        }

        public Criteria andProductManagerEqualTo(Long value) {
            addCriterion("product_manager =", value, "productManager");
            return (Criteria) this;
        }

        public Criteria andProductManagerNotEqualTo(Long value) {
            addCriterion("product_manager <>", value, "productManager");
            return (Criteria) this;
        }

        public Criteria andProductManagerGreaterThan(Long value) {
            addCriterion("product_manager >", value, "productManager");
            return (Criteria) this;
        }

        public Criteria andProductManagerGreaterThanOrEqualTo(Long value) {
            addCriterion("product_manager >=", value, "productManager");
            return (Criteria) this;
        }

        public Criteria andProductManagerLessThan(Long value) {
            addCriterion("product_manager <", value, "productManager");
            return (Criteria) this;
        }

        public Criteria andProductManagerLessThanOrEqualTo(Long value) {
            addCriterion("product_manager <=", value, "productManager");
            return (Criteria) this;
        }

        public Criteria andProductManagerIn(List<Long> values) {
            addCriterion("product_manager in", values, "productManager");
            return (Criteria) this;
        }

        public Criteria andProductManagerNotIn(List<Long> values) {
            addCriterion("product_manager not in", values, "productManager");
            return (Criteria) this;
        }

        public Criteria andProductManagerBetween(Long value1, Long value2) {
            addCriterion("product_manager between", value1, value2, "productManager");
            return (Criteria) this;
        }

        public Criteria andProductManagerNotBetween(Long value1, Long value2) {
            addCriterion("product_manager not between", value1, value2, "productManager");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameIsNull() {
            addCriterion("product_manager_name is null");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameIsNotNull() {
            addCriterion("product_manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameEqualTo(String value) {
            addCriterion("product_manager_name =", value, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameNotEqualTo(String value) {
            addCriterion("product_manager_name <>", value, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameGreaterThan(String value) {
            addCriterion("product_manager_name >", value, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_manager_name >=", value, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameLessThan(String value) {
            addCriterion("product_manager_name <", value, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameLessThanOrEqualTo(String value) {
            addCriterion("product_manager_name <=", value, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameLike(String value) {
            addCriterion("product_manager_name like", value, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameNotLike(String value) {
            addCriterion("product_manager_name not like", value, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameIn(List<String> values) {
            addCriterion("product_manager_name in", values, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameNotIn(List<String> values) {
            addCriterion("product_manager_name not in", values, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameBetween(String value1, String value2) {
            addCriterion("product_manager_name between", value1, value2, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameNotBetween(String value1, String value2) {
            addCriterion("product_manager_name not between", value1, value2, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andOutsourcePriceIsNull() {
            addCriterion("outsource_price is null");
            return (Criteria) this;
        }

        public Criteria andOutsourcePriceIsNotNull() {
            addCriterion("outsource_price is not null");
            return (Criteria) this;
        }

        public Criteria andOutsourcePriceEqualTo(BigDecimal value) {
            addCriterion("outsource_price =", value, "outsourcePrice");
            return (Criteria) this;
        }

        public Criteria andOutsourcePriceNotEqualTo(BigDecimal value) {
            addCriterion("outsource_price <>", value, "outsourcePrice");
            return (Criteria) this;
        }

        public Criteria andOutsourcePriceGreaterThan(BigDecimal value) {
            addCriterion("outsource_price >", value, "outsourcePrice");
            return (Criteria) this;
        }

        public Criteria andOutsourcePriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("outsource_price >=", value, "outsourcePrice");
            return (Criteria) this;
        }

        public Criteria andOutsourcePriceLessThan(BigDecimal value) {
            addCriterion("outsource_price <", value, "outsourcePrice");
            return (Criteria) this;
        }

        public Criteria andOutsourcePriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("outsource_price <=", value, "outsourcePrice");
            return (Criteria) this;
        }

        public Criteria andOutsourcePriceIn(List<BigDecimal> values) {
            addCriterion("outsource_price in", values, "outsourcePrice");
            return (Criteria) this;
        }

        public Criteria andOutsourcePriceNotIn(List<BigDecimal> values) {
            addCriterion("outsource_price not in", values, "outsourcePrice");
            return (Criteria) this;
        }

        public Criteria andOutsourcePriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outsource_price between", value1, value2, "outsourcePrice");
            return (Criteria) this;
        }

        public Criteria andOutsourcePriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outsource_price not between", value1, value2, "outsourcePrice");
            return (Criteria) this;
        }

        public Criteria andOutsourceDaysIsNull() {
            addCriterion("outsource_days is null");
            return (Criteria) this;
        }

        public Criteria andOutsourceDaysIsNotNull() {
            addCriterion("outsource_days is not null");
            return (Criteria) this;
        }

        public Criteria andOutsourceDaysEqualTo(BigDecimal value) {
            addCriterion("outsource_days =", value, "outsourceDays");
            return (Criteria) this;
        }

        public Criteria andOutsourceDaysNotEqualTo(BigDecimal value) {
            addCriterion("outsource_days <>", value, "outsourceDays");
            return (Criteria) this;
        }

        public Criteria andOutsourceDaysGreaterThan(BigDecimal value) {
            addCriterion("outsource_days >", value, "outsourceDays");
            return (Criteria) this;
        }

        public Criteria andOutsourceDaysGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("outsource_days >=", value, "outsourceDays");
            return (Criteria) this;
        }

        public Criteria andOutsourceDaysLessThan(BigDecimal value) {
            addCriterion("outsource_days <", value, "outsourceDays");
            return (Criteria) this;
        }

        public Criteria andOutsourceDaysLessThanOrEqualTo(BigDecimal value) {
            addCriterion("outsource_days <=", value, "outsourceDays");
            return (Criteria) this;
        }

        public Criteria andOutsourceDaysIn(List<BigDecimal> values) {
            addCriterion("outsource_days in", values, "outsourceDays");
            return (Criteria) this;
        }

        public Criteria andOutsourceDaysNotIn(List<BigDecimal> values) {
            addCriterion("outsource_days not in", values, "outsourceDays");
            return (Criteria) this;
        }

        public Criteria andOutsourceDaysBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outsource_days between", value1, value2, "outsourceDays");
            return (Criteria) this;
        }

        public Criteria andOutsourceDaysNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outsource_days not between", value1, value2, "outsourceDays");
            return (Criteria) this;
        }

        public Criteria andOutsourceIncomeIsNull() {
            addCriterion("outsource_income is null");
            return (Criteria) this;
        }

        public Criteria andOutsourceIncomeIsNotNull() {
            addCriterion("outsource_income is not null");
            return (Criteria) this;
        }

        public Criteria andOutsourceIncomeEqualTo(BigDecimal value) {
            addCriterion("outsource_income =", value, "outsourceIncome");
            return (Criteria) this;
        }

        public Criteria andOutsourceIncomeNotEqualTo(BigDecimal value) {
            addCriterion("outsource_income <>", value, "outsourceIncome");
            return (Criteria) this;
        }

        public Criteria andOutsourceIncomeGreaterThan(BigDecimal value) {
            addCriterion("outsource_income >", value, "outsourceIncome");
            return (Criteria) this;
        }

        public Criteria andOutsourceIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("outsource_income >=", value, "outsourceIncome");
            return (Criteria) this;
        }

        public Criteria andOutsourceIncomeLessThan(BigDecimal value) {
            addCriterion("outsource_income <", value, "outsourceIncome");
            return (Criteria) this;
        }

        public Criteria andOutsourceIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("outsource_income <=", value, "outsourceIncome");
            return (Criteria) this;
        }

        public Criteria andOutsourceIncomeIn(List<BigDecimal> values) {
            addCriterion("outsource_income in", values, "outsourceIncome");
            return (Criteria) this;
        }

        public Criteria andOutsourceIncomeNotIn(List<BigDecimal> values) {
            addCriterion("outsource_income not in", values, "outsourceIncome");
            return (Criteria) this;
        }

        public Criteria andOutsourceIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outsource_income between", value1, value2, "outsourceIncome");
            return (Criteria) this;
        }

        public Criteria andOutsourceIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outsource_income not between", value1, value2, "outsourceIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeIsNull() {
            addCriterion("inner_income is null");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeIsNotNull() {
            addCriterion("inner_income is not null");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeEqualTo(BigDecimal value) {
            addCriterion("inner_income =", value, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeNotEqualTo(BigDecimal value) {
            addCriterion("inner_income <>", value, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeGreaterThan(BigDecimal value) {
            addCriterion("inner_income >", value, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_income >=", value, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeLessThan(BigDecimal value) {
            addCriterion("inner_income <", value, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_income <=", value, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeIn(List<BigDecimal> values) {
            addCriterion("inner_income in", values, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeNotIn(List<BigDecimal> values) {
            addCriterion("inner_income not in", values, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_income between", value1, value2, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_income not between", value1, value2, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeIsNull() {
            addCriterion("outter_income is null");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeIsNotNull() {
            addCriterion("outter_income is not null");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeEqualTo(BigDecimal value) {
            addCriterion("outter_income =", value, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeNotEqualTo(BigDecimal value) {
            addCriterion("outter_income <>", value, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeGreaterThan(BigDecimal value) {
            addCriterion("outter_income >", value, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("outter_income >=", value, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeLessThan(BigDecimal value) {
            addCriterion("outter_income <", value, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("outter_income <=", value, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeIn(List<BigDecimal> values) {
            addCriterion("outter_income in", values, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeNotIn(List<BigDecimal> values) {
            addCriterion("outter_income not in", values, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outter_income between", value1, value2, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outter_income not between", value1, value2, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}