package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/5/29
 * @description
 */
public class ProjectCostEaDetailRecordExcelVO {

    @Excel(name = "序号", width = 10)
    private int num;

    @Excel(name = "项目编号", width = 20)
    private String projectCode;

    @Excel(name = "项目名称", width = 20)
    private String projectName;

    @Excel(name = "EA单号", width = 20)
    private String feeApplyCode;

    @Excel(name = "申请日期", width = 15, format = "yyyy-MM-dd")
    private Date submitedTime;

    @Excel(name = "申请人", width = 10)
    private String applyName;

    @Excel(name = "费用类型", width = 15)
    private String feeItemName;

    @Excel(name = "经济事项", width = 15)
    private String feeTypeName;

    @Excel(name = "批准金额(原币)", width = 15)
    private BigDecimal approveAmount;

    @Excel(name = "剩余EA可用金额", width = 15)
    private BigDecimal overplusEaAmount;

    @Excel(name = "币种", width = 10)
    private String currencyName;

    @Excel(name = "业务描述", width = 30)
    private String sensitiveInfo;

    @Excel(name = "单据状态", width = 10)
    private String orderStatus;

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getFeeApplyCode() {
        return feeApplyCode;
    }

    public void setFeeApplyCode(String feeApplyCode) {
        this.feeApplyCode = feeApplyCode;
    }

    public Date getSubmitedTime() {
        return submitedTime;
    }

    public void setSubmitedTime(Date submitedTime) {
        this.submitedTime = submitedTime;
    }

    public String getApplyName() {
        return applyName;
    }

    public void setApplyName(String applyName) {
        this.applyName = applyName;
    }

    public String getFeeItemName() {
        return feeItemName;
    }

    public void setFeeItemName(String feeItemName) {
        this.feeItemName = feeItemName;
    }

    public String getFeeTypeName() {
        return feeTypeName;
    }

    public void setFeeTypeName(String feeTypeName) {
        this.feeTypeName = feeTypeName;
    }

    public BigDecimal getApproveAmount() {
        return approveAmount;
    }

    public void setApproveAmount(BigDecimal approveAmount) {
        this.approveAmount = approveAmount;
    }

    public BigDecimal getOverplusEaAmount() {
        return overplusEaAmount;
    }

    public void setOverplusEaAmount(BigDecimal overplusEaAmount) {
        this.overplusEaAmount = overplusEaAmount;
    }

    public String getCurrencyName() {
        return currencyName;
    }

    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName;
    }

    public String getSensitiveInfo() {
        return sensitiveInfo;
    }

    public void setSensitiveInfo(String sensitiveInfo) {
        this.sensitiveInfo = sensitiveInfo;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }
}
