package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class ProjectWbsBudgetVersionQuery {

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    private String businessTypeStr;

    @ApiModelProperty("版本时间起始")
    private String versionTimeStart;

    @ApiModelProperty("版本时间截止")
    private String versionTimeEnd;

    @ApiModelProperty(value = "版本号")
    private String versionCode;

    @ApiModelProperty("创建人名称")
    private String createByName;

}