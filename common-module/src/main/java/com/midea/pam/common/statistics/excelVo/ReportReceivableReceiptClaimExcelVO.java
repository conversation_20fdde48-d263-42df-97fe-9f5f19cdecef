package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/7/28
 * 管理口径应收报表ExcelVO
 */
public class ReportReceivableReceiptClaimExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer number;

    @Excel(name = "项目编号", width = 15)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "项目经理", width = 15)
    private String projectManager;

    @Excel(name = "销售经理", width = 15)
    private String salesManager;

    @Excel(name = "子合同编号", width = 15)
    private String code;

    @Excel(name = "子合同名称", width = 30)
    private String name;

    @Excel(name = "主合同编号", width = 15)
    private String parentCode;

    @Excel(name = "主合同名称", width = 30)
    private String parentName;

    @Excel(name = "销售部门", width = 20)
    private String unitName;

    @Excel(name = "业务模式", width = 20)
    private String projectType;

    @Excel(name = "客户CRM编码", width = 30)
    private String customerCode;

    @Excel(name = "客户名称", width = 30)
    private String customerName;

    @Excel(name = "客户属性", width = 10, replace = {"外部客户_0","内部客户_1","_null"})
    private Integer customerType;

    @Excel(name = "子合同开票总额（含税）", width = 15)
    private String invoiceAmount_dt;

    @Excel(name = "回款计划编号", width = 15)
    private String receiptPlanDetailCode;

    @Excel(name = "笔数", width = 10)
    private Integer receiptPlanDetailNum;

    @Excel(name = "回款计划日期", width = 15, format = "yyyy-MM-dd")
    private Date receiptPlanDetailDate;

    @Excel(name = "回款计划金额(含税)", width = 20)
    private String receiptPlanDetailAmount_dt;

    private BigDecimal receiptPlanDetailAmount;

    @Excel(name = "回款条件", width = 25)
    private String requirement;

    @Excel(name = "关联开票计划", width = 20)
    private String invoiceApplyDetailsCode;

    @Excel(name = "关联里程碑", width = 20)
    private String milestoneName;

    @Excel(name = "实际回款金额", width = 15)
    private String actualAmount_dt;

    @Excel(name = "到期未回款金额", width = 15)
    private String expireAmount_dt;

    @Excel(name = "回款条件设置关联节点", width = 20, replace = {"开票_1","里程碑_2","合同签订_3","_null"})
    private String receiptConfigurationType;

    @Excel(name = "回款条件设置规则编码", width = 20,
            replace = {"最新开票申请审批通过日期_0","最早开票申请审批通过日期_1","开票计划日期_2",
                    "最新税票日期_3","最早税票日期_4","里程碑计划开始日期_5",
                    "里程碑计划结束日期_6","里程碑实际开始日期_7","里程碑实际结束日期_8",
                    "合同归档日期_9","子合同生效日期_10","_null"})
    private String receiptConfigurationRule;

    @Excel(name = "起算日", width = 15, format = "yyyy-MM-dd")
    private Date receiptDateStart;

    @Excel(name = "账期", width = 10)
    private String days_dt;

    @Excel(name = "应收到期日", width = 15, format = "yyyy-MM-dd")
    private Date receiptDateEnd;

    private BigDecimal actualAmount;

    private BigDecimal expireAmount;

    private BigDecimal days;

    private BigDecimal invoiceAmount;

    private BigDecimal ageAmount0;

    private BigDecimal ageAmount1;

    private BigDecimal ageAmount2;

    private BigDecimal ageAmount3;

    private BigDecimal ageAmount4;

    private BigDecimal ageAmount5;

    private BigDecimal ageAmount6;


    @Excel(name = "未到期金额", width = 15)
    private String ageAmount0_dt;

    @Excel(name = "0-1个月", width = 15)
    private String ageAmount1_dt;

    @Excel(name = "1-2个月", width = 15)
    private String ageAmount2_dt;

    @Excel(name = "2-3个月", width = 15)
    private String ageAmount3_dt;

    @Excel(name = "4-6个月", width = 15)
    private String ageAmount4_dt;

    @Excel(name = "7-12个月", width = 15)
    private String ageAmount5_dt;

    @Excel(name = ">1年", width = 15)
    private String ageAmount6_dt;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode == null ? null : parentCode.trim();
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName == null ? null : parentName.trim();
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType == null ? null : projectType.trim();
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode == null ? null : customerCode.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public String getSalesManager() {
        return salesManager;
    }

    public void setSalesManager(String salesManager) {
        this.salesManager = salesManager == null ? null : salesManager.trim();
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public BigDecimal getAgeAmount0() {
        return ageAmount0;
    }

    public void setAgeAmount0(BigDecimal ageAmount0) {
        this.ageAmount0 = ageAmount0;
    }

    public BigDecimal getAgeAmount1() {
        return ageAmount1;
    }

    public void setAgeAmount1(BigDecimal ageAmount1) {
        this.ageAmount1 = ageAmount1;
    }

    public BigDecimal getAgeAmount2() {
        return ageAmount2;
    }

    public void setAgeAmount2(BigDecimal ageAmount2) {
        this.ageAmount2 = ageAmount2;
    }

    public BigDecimal getAgeAmount3() {
        return ageAmount3;
    }

    public void setAgeAmount3(BigDecimal ageAmount3) {
        this.ageAmount3 = ageAmount3;
    }

    public BigDecimal getAgeAmount4() {
        return ageAmount4;
    }

    public void setAgeAmount4(BigDecimal ageAmount4) {
        this.ageAmount4 = ageAmount4;
    }

    public BigDecimal getAgeAmount5() {
        return ageAmount5;
    }

    public void setAgeAmount5(BigDecimal ageAmount5) {
        this.ageAmount5 = ageAmount5;
    }

    public BigDecimal getAgeAmount6() {
        return ageAmount6;
    }

    public void setAgeAmount6(BigDecimal ageAmount6) {
        this.ageAmount6 = ageAmount6;
    }

    public String getInvoiceAmount_dt() {
        if (Objects.nonNull(this.invoiceAmount)) {
            return this.invoiceAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.invoiceAmount_dt;
        }
    }

    public void setInvoiceAmount_dt(String invoiceAmount_dt) {
        this.invoiceAmount_dt = invoiceAmount_dt;
    }

    public String getAgeAmount0_dt() {
        if (Objects.nonNull(this.ageAmount0)) {
            return this.ageAmount0.stripTrailingZeros().toPlainString();
        } else {
            return this.ageAmount0_dt;
        }
    }

    public void setAgeAmount0_dt(String ageAmount0_dt) {
        this.ageAmount0_dt = ageAmount0_dt;
    }

    public String getAgeAmount1_dt() {
        if (Objects.nonNull(this.ageAmount1)) {
            return this.ageAmount1.stripTrailingZeros().toPlainString();
        } else {
            return this.ageAmount1_dt;
        }
    }

    public void setAgeAmount1_dt(String ageAmount1_dt) {
        this.ageAmount1_dt = ageAmount1_dt;
    }

    public String getAgeAmount2_dt() {
        if (Objects.nonNull(this.ageAmount2)) {
            return this.ageAmount2.stripTrailingZeros().toPlainString();
        } else {
            return this.ageAmount2_dt;
        }
    }

    public void setAgeAmount2_dt(String ageAmount2_dt) {
        this.ageAmount2_dt = ageAmount2_dt;
    }

    public String getAgeAmount3_dt() {
        if (Objects.nonNull(this.ageAmount3)) {
            return this.ageAmount3.stripTrailingZeros().toPlainString();
        } else {
            return this.ageAmount3_dt;
        }
    }

    public void setAgeAmount3_dt(String ageAmount3_dt) {
        this.ageAmount3_dt = ageAmount3_dt;
    }

    public String getAgeAmount4_dt() {
        if (Objects.nonNull(this.ageAmount4)) {
            return this.ageAmount4.stripTrailingZeros().toPlainString();
        } else {
            return this.ageAmount4_dt;
        }
    }

    public void setAgeAmount4_dt(String ageAmount4_dt) {
        this.ageAmount4_dt = ageAmount4_dt;
    }

    public String getAgeAmount5_dt() {
        if (Objects.nonNull(this.ageAmount5)) {
            return this.ageAmount5.stripTrailingZeros().toPlainString();
        } else {
            return this.ageAmount5_dt;
        }
    }

    public void setAgeAmount5_dt(String ageAmount5_dt) {
        this.ageAmount5_dt = ageAmount5_dt;
    }

    public String getAgeAmount6_dt() {
        if (Objects.nonNull(this.ageAmount6)) {
            return this.ageAmount6.stripTrailingZeros().toPlainString();
        } else {
            return this.ageAmount6_dt;
        }
    }

    public void setAgeAmount6_dt(String ageAmount6_dt) {
        this.ageAmount6_dt = ageAmount6_dt;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public String getReceiptPlanDetailCode() {
        return receiptPlanDetailCode;
    }

    public void setReceiptPlanDetailCode(String receiptPlanDetailCode) {
        this.receiptPlanDetailCode = receiptPlanDetailCode;
    }

    public Integer getReceiptPlanDetailNum() {
        return receiptPlanDetailNum;
    }

    public void setReceiptPlanDetailNum(Integer receiptPlanDetailNum) {
        this.receiptPlanDetailNum = receiptPlanDetailNum;
    }

    public Date getReceiptPlanDetailDate() {
        return receiptPlanDetailDate;
    }

    public void setReceiptPlanDetailDate(Date receiptPlanDetailDate) {
        this.receiptPlanDetailDate = receiptPlanDetailDate;
    }

    public BigDecimal getReceiptPlanDetailAmount() {
        return receiptPlanDetailAmount;
    }

    public void setReceiptPlanDetailAmount(BigDecimal receiptPlanDetailAmount) {
        this.receiptPlanDetailAmount = receiptPlanDetailAmount;
    }

    public String getRequirement() {
        return requirement;
    }

    public void setRequirement(String requirement) {
        this.requirement = requirement;
    }

    public String getInvoiceApplyDetailsCode() {
        return invoiceApplyDetailsCode;
    }

    public void setInvoiceApplyDetailsCode(String invoiceApplyDetailsCode) {
        this.invoiceApplyDetailsCode = invoiceApplyDetailsCode;
    }

    public String getMilestoneName() {
        return milestoneName;
    }

    public void setMilestoneName(String milestoneName) {
        this.milestoneName = milestoneName;
    }

    public String getActualAmount_dt() {
        if (Objects.nonNull(this.getActualAmount())) {
            return this.actualAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.actualAmount_dt;
        }
    }

    public void setActualAmount_dt(String actualAmount_dt) {
        this.actualAmount_dt = actualAmount_dt;
    }

    public String getExpireAmount_dt() {
        if (Objects.nonNull(this.getExpireAmount())) {
            return this.expireAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.expireAmount_dt;
        }
    }

    public void setExpireAmount_dt(String expireAmount_dt) {
        this.expireAmount_dt = expireAmount_dt;
    }

    public String getReceiptConfigurationType() {
        return receiptConfigurationType;
    }

    public void setReceiptConfigurationType(String receiptConfigurationType) {
        this.receiptConfigurationType = receiptConfigurationType;
    }

    public String getReceiptConfigurationRule() {
        return receiptConfigurationRule;
    }

    public void setReceiptConfigurationRule(String receiptConfigurationRule) {
        this.receiptConfigurationRule = receiptConfigurationRule;
    }

    public Date getReceiptDateStart() {
        return receiptDateStart;
    }

    public void setReceiptDateStart(Date receiptDateStart) {
        this.receiptDateStart = receiptDateStart;
    }

    public String getDays_dt() {
        if (Objects.nonNull(this.getDays())) {
            return this.days.stripTrailingZeros().toPlainString();
        } else {
            return this.days_dt;
        }
    }

    public void setDays_dt(String days_dt) {
        this.days_dt = days_dt;
    }

    public Date getReceiptDateEnd() {
        return receiptDateEnd;
    }

    public void setReceiptDateEnd(Date receiptDateEnd) {
        this.receiptDateEnd = receiptDateEnd;
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public BigDecimal getExpireAmount() {
        return expireAmount;
    }

    public void setExpireAmount(BigDecimal expireAmount) {
        this.expireAmount = expireAmount;
    }

    public BigDecimal getDays() {
        return days;
    }

    public void setDays(BigDecimal days) {
        this.days = days;
    }

    public String getReceiptPlanDetailAmount_dt() {
        if (Objects.nonNull(this.getReceiptPlanDetailAmount())) {
            return this.receiptPlanDetailAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.receiptPlanDetailAmount_dt;
        }
    }

    public void setReceiptPlanDetailAmount_dt(String receiptPlanDetailAmount_dt) {
        this.receiptPlanDetailAmount_dt = receiptPlanDetailAmount_dt;
    }

}