package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/10/9
 * @description
 */
public class ReportMilepostDesignPlanChangeKunShanExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer num;

    @Excel(name = "变更时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    private Date changeTime;

    @Excel(name = "变更人", width = 15)
    private String changeUserName;

    @Excel(name = "项目经理", width = 15)
    private String managerName;

    @Excel(name = "变更原因", width = 15)
    private String changeReason;

    @Excel(name = "变动类型", width = 15, replace = {"增加_1", "修改_2", "删除_3","物料类型变更_4"})
    private Integer changeType;

    @Excel(name = "原因描述", width = 15)
    private String changeRemark;

    @Excel(name = "项目号", width = 15)
    private String projectCode;

    @Excel(name = "项目类型", width = 15)
    private String projectTypeName;

    @Excel(name = "项目名称", width = 15)
    private String projectName;

    @Excel(name = "业务分类", width = 15)
    private String unitName;

    @Excel(name = "模组名称", width = 30)
    private String modelName;

    @Excel(name = "物料类型", width = 15)
    private String materialCategory;

    @Excel(name = "是否需求确认", width = 15)
    private String showRequirementConfirm;

    @Excel(name = "模组PAM编码", width = 15)
    private String modelPamCode;

    @Excel(name = "PAM物料编码", width = 15)
    private String materialPamCode;

    @Excel(name = "ERP物料编码", width = 15)
    private String materialErpCode;

    @Excel(name = "物料描述", width = 30)
    private String materialDesc;

    @Excel(name = "变更单位", width = 15)
    private String materialUnit;

    @Excel(name = "变更单套数量", width = 15)
    private BigDecimal materialChangeNum;

    @Excel(name = "变更总数", width = 15)
    private BigDecimal materialChangeTotalNum;

    @Excel(name = "变更设计单价", width = 15)
    private BigDecimal materialChangePrice;

    @Excel(name = "变更设计金额", width = 15)
    private BigDecimal materialChangeTotalAmount;

    @Excel(name = "工单任务号", width = 15)
    private String ticketTaskCode;

    @Excel(name = "已领料数量", width = 15)
    private BigDecimal actualAmount;

    @Excel(name = "行创建日期", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    private Date changeCreateAt;

    @Excel(name = "行更新日期'", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    private Date changeUpdateAt;

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Date getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(Date changeTime) {
        this.changeTime = changeTime;
    }

    public String getChangeUserName() {
        return changeUserName;
    }

    public void setChangeUserName(String changeUserName) {
        this.changeUserName = changeUserName;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public String getChangeReason() {
        return changeReason;
    }

    public void setChangeReason(String changeReason) {
        this.changeReason = changeReason;
    }

    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    public String getChangeRemark() {
        return changeRemark;
    }

    public void setChangeRemark(String changeRemark) {
        this.changeRemark = changeRemark;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectTypeName() {
        return projectTypeName;
    }

    public void setProjectTypeName(String projectTypeName) {
        this.projectTypeName = projectTypeName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getModelPamCode() {
        return modelPamCode;
    }

    public void setModelPamCode(String modelPamCode) {
        this.modelPamCode = modelPamCode;
    }

    public String getMaterialPamCode() {
        return materialPamCode;
    }

    public void setMaterialPamCode(String materialPamCode) {
        this.materialPamCode = materialPamCode;
    }

    public String getMaterialErpCode() {
        return materialErpCode;
    }

    public void setMaterialErpCode(String materialErpCode) {
        this.materialErpCode = materialErpCode;
    }

    public String getMaterialDesc() {
        return materialDesc;
    }

    public void setMaterialDesc(String materialDesc) {
        this.materialDesc = materialDesc;
    }

    public String getMaterialUnit() {
        return materialUnit;
    }

    public void setMaterialUnit(String materialUnit) {
        this.materialUnit = materialUnit;
    }

    public BigDecimal getMaterialChangeNum() {
        return materialChangeNum;
    }

    public void setMaterialChangeNum(BigDecimal materialChangeNum) {
        this.materialChangeNum = materialChangeNum;
    }

    public BigDecimal getMaterialChangeTotalNum() {
        return materialChangeTotalNum;
    }

    public void setMaterialChangeTotalNum(BigDecimal materialChangeTotalNum) {
        this.materialChangeTotalNum = materialChangeTotalNum;
    }

    public BigDecimal getMaterialChangePrice() {
        return materialChangePrice;
    }

    public void setMaterialChangePrice(BigDecimal materialChangePrice) {
        this.materialChangePrice = materialChangePrice;
    }

    public BigDecimal getMaterialChangeTotalAmount() {
        return materialChangeTotalAmount;
    }

    public void setMaterialChangeTotalAmount(BigDecimal materialChangeTotalAmount) {
        this.materialChangeTotalAmount = materialChangeTotalAmount;
    }

    public String getTicketTaskCode() {
        return ticketTaskCode;
    }

    public void setTicketTaskCode(String ticketTaskCode) {
        this.ticketTaskCode = ticketTaskCode;
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public Date getChangeCreateAt() {
        return changeCreateAt;
    }

    public void setChangeCreateAt(Date changeCreateAt) {
        this.changeCreateAt = changeCreateAt;
    }

    public Date getChangeUpdateAt() {
        return changeUpdateAt;
    }

    public void setChangeUpdateAt(Date changeUpdateAt) {
        this.changeUpdateAt = changeUpdateAt;
    }

    public String getMaterialCategory() {
        return materialCategory;
    }

    public void setMaterialCategory(String materialCategory) {
        this.materialCategory = materialCategory;
    }

    public String getShowRequirementConfirm() {
        return showRequirementConfirm;
    }

    public void setShowRequirementConfirm(String showRequirementConfirm) {
        this.showRequirementConfirm = showRequirementConfirm;
    }
}
