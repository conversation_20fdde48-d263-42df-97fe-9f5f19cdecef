package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectCostStorageRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectCostStorageRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdIsNull() {
            addCriterion("summary_id is null");
            return (Criteria) this;
        }

        public Criteria andSummaryIdIsNotNull() {
            addCriterion("summary_id is not null");
            return (Criteria) this;
        }

        public Criteria andSummaryIdEqualTo(Long value) {
            addCriterion("summary_id =", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdNotEqualTo(Long value) {
            addCriterion("summary_id <>", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdGreaterThan(Long value) {
            addCriterion("summary_id >", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("summary_id >=", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdLessThan(Long value) {
            addCriterion("summary_id <", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdLessThanOrEqualTo(Long value) {
            addCriterion("summary_id <=", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdIn(List<Long> values) {
            addCriterion("summary_id in", values, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdNotIn(List<Long> values) {
            addCriterion("summary_id not in", values, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdBetween(Long value1, Long value2) {
            addCriterion("summary_id between", value1, value2, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdNotBetween(Long value1, Long value2) {
            addCriterion("summary_id not between", value1, value2, "summaryId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andSegment1IsNull() {
            addCriterion("segment1 is null");
            return (Criteria) this;
        }

        public Criteria andSegment1IsNotNull() {
            addCriterion("segment1 is not null");
            return (Criteria) this;
        }

        public Criteria andSegment1EqualTo(String value) {
            addCriterion("segment1 =", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1NotEqualTo(String value) {
            addCriterion("segment1 <>", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1GreaterThan(String value) {
            addCriterion("segment1 >", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1GreaterThanOrEqualTo(String value) {
            addCriterion("segment1 >=", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1LessThan(String value) {
            addCriterion("segment1 <", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1LessThanOrEqualTo(String value) {
            addCriterion("segment1 <=", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1Like(String value) {
            addCriterion("segment1 like", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1NotLike(String value) {
            addCriterion("segment1 not like", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1In(List<String> values) {
            addCriterion("segment1 in", values, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1NotIn(List<String> values) {
            addCriterion("segment1 not in", values, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1Between(String value1, String value2) {
            addCriterion("segment1 between", value1, value2, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1NotBetween(String value1, String value2) {
            addCriterion("segment1 not between", value1, value2, "segment1");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andTransactionQuantityIsNull() {
            addCriterion("transaction_quantity is null");
            return (Criteria) this;
        }

        public Criteria andTransactionQuantityIsNotNull() {
            addCriterion("transaction_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andTransactionQuantityEqualTo(BigDecimal value) {
            addCriterion("transaction_quantity =", value, "transactionQuantity");
            return (Criteria) this;
        }

        public Criteria andTransactionQuantityNotEqualTo(BigDecimal value) {
            addCriterion("transaction_quantity <>", value, "transactionQuantity");
            return (Criteria) this;
        }

        public Criteria andTransactionQuantityGreaterThan(BigDecimal value) {
            addCriterion("transaction_quantity >", value, "transactionQuantity");
            return (Criteria) this;
        }

        public Criteria andTransactionQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("transaction_quantity >=", value, "transactionQuantity");
            return (Criteria) this;
        }

        public Criteria andTransactionQuantityLessThan(BigDecimal value) {
            addCriterion("transaction_quantity <", value, "transactionQuantity");
            return (Criteria) this;
        }

        public Criteria andTransactionQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("transaction_quantity <=", value, "transactionQuantity");
            return (Criteria) this;
        }

        public Criteria andTransactionQuantityIn(List<BigDecimal> values) {
            addCriterion("transaction_quantity in", values, "transactionQuantity");
            return (Criteria) this;
        }

        public Criteria andTransactionQuantityNotIn(List<BigDecimal> values) {
            addCriterion("transaction_quantity not in", values, "transactionQuantity");
            return (Criteria) this;
        }

        public Criteria andTransactionQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transaction_quantity between", value1, value2, "transactionQuantity");
            return (Criteria) this;
        }

        public Criteria andTransactionQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transaction_quantity not between", value1, value2, "transactionQuantity");
            return (Criteria) this;
        }

        public Criteria andPrimaryUnitOfMeasureIsNull() {
            addCriterion("primary_unit_of_measure is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryUnitOfMeasureIsNotNull() {
            addCriterion("primary_unit_of_measure is not null");
            return (Criteria) this;
        }

        public Criteria andPrimaryUnitOfMeasureEqualTo(String value) {
            addCriterion("primary_unit_of_measure =", value, "primaryUnitOfMeasure");
            return (Criteria) this;
        }

        public Criteria andPrimaryUnitOfMeasureNotEqualTo(String value) {
            addCriterion("primary_unit_of_measure <>", value, "primaryUnitOfMeasure");
            return (Criteria) this;
        }

        public Criteria andPrimaryUnitOfMeasureGreaterThan(String value) {
            addCriterion("primary_unit_of_measure >", value, "primaryUnitOfMeasure");
            return (Criteria) this;
        }

        public Criteria andPrimaryUnitOfMeasureGreaterThanOrEqualTo(String value) {
            addCriterion("primary_unit_of_measure >=", value, "primaryUnitOfMeasure");
            return (Criteria) this;
        }

        public Criteria andPrimaryUnitOfMeasureLessThan(String value) {
            addCriterion("primary_unit_of_measure <", value, "primaryUnitOfMeasure");
            return (Criteria) this;
        }

        public Criteria andPrimaryUnitOfMeasureLessThanOrEqualTo(String value) {
            addCriterion("primary_unit_of_measure <=", value, "primaryUnitOfMeasure");
            return (Criteria) this;
        }

        public Criteria andPrimaryUnitOfMeasureLike(String value) {
            addCriterion("primary_unit_of_measure like", value, "primaryUnitOfMeasure");
            return (Criteria) this;
        }

        public Criteria andPrimaryUnitOfMeasureNotLike(String value) {
            addCriterion("primary_unit_of_measure not like", value, "primaryUnitOfMeasure");
            return (Criteria) this;
        }

        public Criteria andPrimaryUnitOfMeasureIn(List<String> values) {
            addCriterion("primary_unit_of_measure in", values, "primaryUnitOfMeasure");
            return (Criteria) this;
        }

        public Criteria andPrimaryUnitOfMeasureNotIn(List<String> values) {
            addCriterion("primary_unit_of_measure not in", values, "primaryUnitOfMeasure");
            return (Criteria) this;
        }

        public Criteria andPrimaryUnitOfMeasureBetween(String value1, String value2) {
            addCriterion("primary_unit_of_measure between", value1, value2, "primaryUnitOfMeasure");
            return (Criteria) this;
        }

        public Criteria andPrimaryUnitOfMeasureNotBetween(String value1, String value2) {
            addCriterion("primary_unit_of_measure not between", value1, value2, "primaryUnitOfMeasure");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNull() {
            addCriterion("item_cost is null");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNotNull() {
            addCriterion("item_cost is not null");
            return (Criteria) this;
        }

        public Criteria andItemCostEqualTo(BigDecimal value) {
            addCriterion("item_cost =", value, "itemCost");
            return (Criteria) this;
        }

        public Criteria andItemCostNotEqualTo(BigDecimal value) {
            addCriterion("item_cost <>", value, "itemCost");
            return (Criteria) this;
        }

        public Criteria andItemCostGreaterThan(BigDecimal value) {
            addCriterion("item_cost >", value, "itemCost");
            return (Criteria) this;
        }

        public Criteria andItemCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("item_cost >=", value, "itemCost");
            return (Criteria) this;
        }

        public Criteria andItemCostLessThan(BigDecimal value) {
            addCriterion("item_cost <", value, "itemCost");
            return (Criteria) this;
        }

        public Criteria andItemCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("item_cost <=", value, "itemCost");
            return (Criteria) this;
        }

        public Criteria andItemCostIn(List<BigDecimal> values) {
            addCriterion("item_cost in", values, "itemCost");
            return (Criteria) this;
        }

        public Criteria andItemCostNotIn(List<BigDecimal> values) {
            addCriterion("item_cost not in", values, "itemCost");
            return (Criteria) this;
        }

        public Criteria andItemCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("item_cost between", value1, value2, "itemCost");
            return (Criteria) this;
        }

        public Criteria andItemCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("item_cost not between", value1, value2, "itemCost");
            return (Criteria) this;
        }

        public Criteria andCostTypeIsNull() {
            addCriterion("cost_type is null");
            return (Criteria) this;
        }

        public Criteria andCostTypeIsNotNull() {
            addCriterion("cost_type is not null");
            return (Criteria) this;
        }

        public Criteria andCostTypeEqualTo(String value) {
            addCriterion("cost_type =", value, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeNotEqualTo(String value) {
            addCriterion("cost_type <>", value, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeGreaterThan(String value) {
            addCriterion("cost_type >", value, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeGreaterThanOrEqualTo(String value) {
            addCriterion("cost_type >=", value, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeLessThan(String value) {
            addCriterion("cost_type <", value, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeLessThanOrEqualTo(String value) {
            addCriterion("cost_type <=", value, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeLike(String value) {
            addCriterion("cost_type like", value, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeNotLike(String value) {
            addCriterion("cost_type not like", value, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeIn(List<String> values) {
            addCriterion("cost_type in", values, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeNotIn(List<String> values) {
            addCriterion("cost_type not in", values, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeBetween(String value1, String value2) {
            addCriterion("cost_type between", value1, value2, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeNotBetween(String value1, String value2) {
            addCriterion("cost_type not between", value1, value2, "costType");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeIsNull() {
            addCriterion("subinventory_code is null");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeIsNotNull() {
            addCriterion("subinventory_code is not null");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeEqualTo(String value) {
            addCriterion("subinventory_code =", value, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeNotEqualTo(String value) {
            addCriterion("subinventory_code <>", value, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeGreaterThan(String value) {
            addCriterion("subinventory_code >", value, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("subinventory_code >=", value, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeLessThan(String value) {
            addCriterion("subinventory_code <", value, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeLessThanOrEqualTo(String value) {
            addCriterion("subinventory_code <=", value, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeLike(String value) {
            addCriterion("subinventory_code like", value, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeNotLike(String value) {
            addCriterion("subinventory_code not like", value, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeIn(List<String> values) {
            addCriterion("subinventory_code in", values, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeNotIn(List<String> values) {
            addCriterion("subinventory_code not in", values, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeBetween(String value1, String value2) {
            addCriterion("subinventory_code between", value1, value2, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryCodeNotBetween(String value1, String value2) {
            addCriterion("subinventory_code not between", value1, value2, "subinventoryCode");
            return (Criteria) this;
        }

        public Criteria andSubinventoryDescriptionIsNull() {
            addCriterion("subinventory_description is null");
            return (Criteria) this;
        }

        public Criteria andSubinventoryDescriptionIsNotNull() {
            addCriterion("subinventory_description is not null");
            return (Criteria) this;
        }

        public Criteria andSubinventoryDescriptionEqualTo(String value) {
            addCriterion("subinventory_description =", value, "subinventoryDescription");
            return (Criteria) this;
        }

        public Criteria andSubinventoryDescriptionNotEqualTo(String value) {
            addCriterion("subinventory_description <>", value, "subinventoryDescription");
            return (Criteria) this;
        }

        public Criteria andSubinventoryDescriptionGreaterThan(String value) {
            addCriterion("subinventory_description >", value, "subinventoryDescription");
            return (Criteria) this;
        }

        public Criteria andSubinventoryDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("subinventory_description >=", value, "subinventoryDescription");
            return (Criteria) this;
        }

        public Criteria andSubinventoryDescriptionLessThan(String value) {
            addCriterion("subinventory_description <", value, "subinventoryDescription");
            return (Criteria) this;
        }

        public Criteria andSubinventoryDescriptionLessThanOrEqualTo(String value) {
            addCriterion("subinventory_description <=", value, "subinventoryDescription");
            return (Criteria) this;
        }

        public Criteria andSubinventoryDescriptionLike(String value) {
            addCriterion("subinventory_description like", value, "subinventoryDescription");
            return (Criteria) this;
        }

        public Criteria andSubinventoryDescriptionNotLike(String value) {
            addCriterion("subinventory_description not like", value, "subinventoryDescription");
            return (Criteria) this;
        }

        public Criteria andSubinventoryDescriptionIn(List<String> values) {
            addCriterion("subinventory_description in", values, "subinventoryDescription");
            return (Criteria) this;
        }

        public Criteria andSubinventoryDescriptionNotIn(List<String> values) {
            addCriterion("subinventory_description not in", values, "subinventoryDescription");
            return (Criteria) this;
        }

        public Criteria andSubinventoryDescriptionBetween(String value1, String value2) {
            addCriterion("subinventory_description between", value1, value2, "subinventoryDescription");
            return (Criteria) this;
        }

        public Criteria andSubinventoryDescriptionNotBetween(String value1, String value2) {
            addCriterion("subinventory_description not between", value1, value2, "subinventoryDescription");
            return (Criteria) this;
        }

        public Criteria andLocatorIsNull() {
            addCriterion("locator is null");
            return (Criteria) this;
        }

        public Criteria andLocatorIsNotNull() {
            addCriterion("locator is not null");
            return (Criteria) this;
        }

        public Criteria andLocatorEqualTo(String value) {
            addCriterion("locator =", value, "locator");
            return (Criteria) this;
        }

        public Criteria andLocatorNotEqualTo(String value) {
            addCriterion("locator <>", value, "locator");
            return (Criteria) this;
        }

        public Criteria andLocatorGreaterThan(String value) {
            addCriterion("locator >", value, "locator");
            return (Criteria) this;
        }

        public Criteria andLocatorGreaterThanOrEqualTo(String value) {
            addCriterion("locator >=", value, "locator");
            return (Criteria) this;
        }

        public Criteria andLocatorLessThan(String value) {
            addCriterion("locator <", value, "locator");
            return (Criteria) this;
        }

        public Criteria andLocatorLessThanOrEqualTo(String value) {
            addCriterion("locator <=", value, "locator");
            return (Criteria) this;
        }

        public Criteria andLocatorLike(String value) {
            addCriterion("locator like", value, "locator");
            return (Criteria) this;
        }

        public Criteria andLocatorNotLike(String value) {
            addCriterion("locator not like", value, "locator");
            return (Criteria) this;
        }

        public Criteria andLocatorIn(List<String> values) {
            addCriterion("locator in", values, "locator");
            return (Criteria) this;
        }

        public Criteria andLocatorNotIn(List<String> values) {
            addCriterion("locator not in", values, "locator");
            return (Criteria) this;
        }

        public Criteria andLocatorBetween(String value1, String value2) {
            addCriterion("locator between", value1, value2, "locator");
            return (Criteria) this;
        }

        public Criteria andLocatorNotBetween(String value1, String value2) {
            addCriterion("locator not between", value1, value2, "locator");
            return (Criteria) this;
        }

        public Criteria andLocatorDescriptionIsNull() {
            addCriterion("locator_description is null");
            return (Criteria) this;
        }

        public Criteria andLocatorDescriptionIsNotNull() {
            addCriterion("locator_description is not null");
            return (Criteria) this;
        }

        public Criteria andLocatorDescriptionEqualTo(String value) {
            addCriterion("locator_description =", value, "locatorDescription");
            return (Criteria) this;
        }

        public Criteria andLocatorDescriptionNotEqualTo(String value) {
            addCriterion("locator_description <>", value, "locatorDescription");
            return (Criteria) this;
        }

        public Criteria andLocatorDescriptionGreaterThan(String value) {
            addCriterion("locator_description >", value, "locatorDescription");
            return (Criteria) this;
        }

        public Criteria andLocatorDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("locator_description >=", value, "locatorDescription");
            return (Criteria) this;
        }

        public Criteria andLocatorDescriptionLessThan(String value) {
            addCriterion("locator_description <", value, "locatorDescription");
            return (Criteria) this;
        }

        public Criteria andLocatorDescriptionLessThanOrEqualTo(String value) {
            addCriterion("locator_description <=", value, "locatorDescription");
            return (Criteria) this;
        }

        public Criteria andLocatorDescriptionLike(String value) {
            addCriterion("locator_description like", value, "locatorDescription");
            return (Criteria) this;
        }

        public Criteria andLocatorDescriptionNotLike(String value) {
            addCriterion("locator_description not like", value, "locatorDescription");
            return (Criteria) this;
        }

        public Criteria andLocatorDescriptionIn(List<String> values) {
            addCriterion("locator_description in", values, "locatorDescription");
            return (Criteria) this;
        }

        public Criteria andLocatorDescriptionNotIn(List<String> values) {
            addCriterion("locator_description not in", values, "locatorDescription");
            return (Criteria) this;
        }

        public Criteria andLocatorDescriptionBetween(String value1, String value2) {
            addCriterion("locator_description between", value1, value2, "locatorDescription");
            return (Criteria) this;
        }

        public Criteria andLocatorDescriptionNotBetween(String value1, String value2) {
            addCriterion("locator_description not between", value1, value2, "locatorDescription");
            return (Criteria) this;
        }

        public Criteria andCostAmountIsNull() {
            addCriterion("cost_amount is null");
            return (Criteria) this;
        }

        public Criteria andCostAmountIsNotNull() {
            addCriterion("cost_amount is not null");
            return (Criteria) this;
        }

        public Criteria andCostAmountEqualTo(BigDecimal value) {
            addCriterion("cost_amount =", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountNotEqualTo(BigDecimal value) {
            addCriterion("cost_amount <>", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountGreaterThan(BigDecimal value) {
            addCriterion("cost_amount >", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cost_amount >=", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountLessThan(BigDecimal value) {
            addCriterion("cost_amount <", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cost_amount <=", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountIn(List<BigDecimal> values) {
            addCriterion("cost_amount in", values, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountNotIn(List<BigDecimal> values) {
            addCriterion("cost_amount not in", values, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost_amount between", value1, value2, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost_amount not between", value1, value2, "costAmount");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}