package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

public class ReportProjectChangeTraceSummaryDto extends LongIdEntity implements Serializable {

    private Integer number;

    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目经理")
    private Long projectManager;

    @ApiModelProperty(value = "基本信息变更次数")
    private Integer baseInfoCount;

    @ApiModelProperty(value = "基本信息变更类型")
    private String baseInfoType;

    @ApiModelProperty(value = "基本信息最后更新人")
    private Long baseInfoLastUpdater;

    @ApiModelProperty(value = "基本信息最后变更审批通过时间")
    private Date baseInfoLastApprovedTime;

    @ApiModelProperty(value = "里程碑变更次数")
    private Integer milepostCount;

    @ApiModelProperty(value = "里程碑变更类型")
    private String milepostType;

    @ApiModelProperty(value = "里程碑变更最后更新人")
    private Long milepostLastUpdater;

    @ApiModelProperty(value = "里程碑最后变更审批通过时间")
    private Date milepostLastApprovedTime;

    @ApiModelProperty(value = "预算变更次数")
    private Integer budgetCount;

    @ApiModelProperty(value = "预算变更类型")
    private String budgetType;

    @ApiModelProperty(value = "预算变更最后更新人")
    private Long budgetLastUpdater;

    @ApiModelProperty(value = "预算最后变更审批通过时间")
    private Date budgetLastApprovedTime;

    @ApiModelProperty(value = "是否删除")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "项目经理名称")
    private String projectManagerName;

    @ApiModelProperty(value = "基本信息最后更新者名称")
    private String baseInfoLastName;

    @ApiModelProperty(value = "里程碑最后更新者名称")
    private String milepostLastName;

    @ApiModelProperty(value = "预算最后更新者名称")
    private String budgetLastName;

    private static final long serialVersionUID = 1L;

    @Override
    public String toString() {
        return "ReportProjectChangeTraceSummaryDto{" +
                "number=" + number +
                ", reportId=" + reportId +
                ", executeId=" + executeId +
                ", projectCode='" + projectCode + '\'' +
                ", projectName='" + projectName + '\'' +
                ", projectManager=" + projectManager +
                ", baseInfoCount=" + baseInfoCount +
                ", baseInfoType='" + baseInfoType + '\'' +
                ", baseInfoLastUpdater=" + baseInfoLastUpdater +
                ", baseInfoLastApprovedTime=" + baseInfoLastApprovedTime +
                ", milepostCount=" + milepostCount +
                ", milepostType='" + milepostType + '\'' +
                ", milepostLastUpdater=" + milepostLastUpdater +
                ", milepostLastApprovedTime=" + milepostLastApprovedTime +
                ", budgetCount=" + budgetCount +
                ", budgetType='" + budgetType + '\'' +
                ", budgetLastUpdater=" + budgetLastUpdater +
                ", budgetLastApprovedTime=" + budgetLastApprovedTime +
                ", deletedFlag=" + deletedFlag +
                ", projectManagerName='" + projectManagerName + '\'' +
                ", baseInfoLastName='" + baseInfoLastName + '\'' +
                ", milepostLastName='" + milepostLastName + '\'' +
                ", budgetLastName='" + budgetLastName + '\'' +
                '}';
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Long getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(Long projectManager) {
        this.projectManager = projectManager;
    }

    public Integer getBaseInfoCount() {
        return baseInfoCount;
    }

    public void setBaseInfoCount(Integer baseInfoCount) {
        this.baseInfoCount = baseInfoCount;
    }

    public String getBaseInfoType() {
        return baseInfoType;
    }

    public void setBaseInfoType(String baseInfoType) {
        this.baseInfoType = baseInfoType;
    }

    public Long getBaseInfoLastUpdater() {
        return baseInfoLastUpdater;
    }

    public void setBaseInfoLastUpdater(Long baseInfoLastUpdater) {
        this.baseInfoLastUpdater = baseInfoLastUpdater;
    }

    public Date getBaseInfoLastApprovedTime() {
        return baseInfoLastApprovedTime;
    }

    public void setBaseInfoLastApprovedTime(Date baseInfoLastApprovedTime) {
        this.baseInfoLastApprovedTime = baseInfoLastApprovedTime;
    }

    public Integer getMilepostCount() {
        return milepostCount;
    }

    public void setMilepostCount(Integer milepostCount) {
        this.milepostCount = milepostCount;
    }

    public String getMilepostType() {
        return milepostType;
    }

    public void setMilepostType(String milepostType) {
        this.milepostType = milepostType;
    }

    public Long getMilepostLastUpdater() {
        return milepostLastUpdater;
    }

    public void setMilepostLastUpdater(Long milepostLastUpdater) {
        this.milepostLastUpdater = milepostLastUpdater;
    }

    public Date getMilepostLastApprovedTime() {
        return milepostLastApprovedTime;
    }

    public void setMilepostLastApprovedTime(Date milepostLastApprovedTime) {
        this.milepostLastApprovedTime = milepostLastApprovedTime;
    }

    public Integer getBudgetCount() {
        return budgetCount;
    }

    public void setBudgetCount(Integer budgetCount) {
        this.budgetCount = budgetCount;
    }

    public String getBudgetType() {
        return budgetType;
    }

    public void setBudgetType(String budgetType) {
        this.budgetType = budgetType;
    }

    public Long getBudgetLastUpdater() {
        return budgetLastUpdater;
    }

    public void setBudgetLastUpdater(Long budgetLastUpdater) {
        this.budgetLastUpdater = budgetLastUpdater;
    }

    public Date getBudgetLastApprovedTime() {
        return budgetLastApprovedTime;
    }

    public void setBudgetLastApprovedTime(Date budgetLastApprovedTime) {
        this.budgetLastApprovedTime = budgetLastApprovedTime;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public String getProjectManagerName() {
        return projectManagerName;
    }

    public void setProjectManagerName(String projectManagerName) {
        this.projectManagerName = projectManagerName;
    }

    public String getBaseInfoLastName() {
        return baseInfoLastName;
    }

    public void setBaseInfoLastName(String baseInfoLastName) {
        this.baseInfoLastName = baseInfoLastName;
    }

    public String getMilepostLastName() {
        return milepostLastName;
    }

    public void setMilepostLastName(String milepostLastName) {
        this.milepostLastName = milepostLastName;
    }

    public String getBudgetLastName() {
        return budgetLastName;
    }

    public void setBudgetLastName(String budgetLastName) {
        this.budgetLastName = budgetLastName;
    }
}
