package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目进度毛利跟踪表")
public class ReportProjectProcessProfit extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "子合同编号")
    private String contractCode;

    @ApiModelProperty(value = "业务分类")
    private String unitName;

    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "项目等级，1代表A级，2代表B级，3代表C级")
    private Integer projectLevel;

    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    @ApiModelProperty(value = "销售经理")
    private String salesManager;

    @ApiModelProperty(value = "财务人员")
    private String financial;

    @ApiModelProperty(value = "方案设计人")
    private String planDesignerName;

    @ApiModelProperty(value = "技术负责人")
    private String technologyLeaderName;

    @ApiModelProperty(value = "项目状态--审批驳回：-2, 财务驳回：-1,草稿：0,审批中：3,审批通过-项目进行中：4,项目变更中：9,已结项：10,预立项转正驳回：-3,预立项驳回：-4,审批撤回：11,预立项审批撤回：13,作废：12,预立项确认中：7,作废审批中：15")
    private Integer projectStatus;

    @ApiModelProperty(value = "当前里程碑")
    private String currentMilepost;

    @ApiModelProperty(value = "项目合同币种")
    private String currency;

    @ApiModelProperty(value = "项目金额（不含税）-原币")
    private BigDecimal projectAmount;

    @ApiModelProperty(value = "项目金额（不含税）-本位币")
    private BigDecimal projectContractStandardAmount;

    @ApiModelProperty(value = "项目开始日期")
    private Date startDate;

    @ApiModelProperty(value = "里程碑名称集合")
    private String milepostNameInfo;

    @ApiModelProperty(value = "里程碑状态集合")
    private String milepostStatusInfo;

    @ApiModelProperty(value = "里程碑计划结束日期集合")
    private String milepostEndDateInfo;

    @ApiModelProperty(value = "里程碑实际结束日期集合")
    private String milepostActualEndTimeInfo;

    @ApiModelProperty(value = "项目初始物料目标成本")
    private BigDecimal initMaterialTargetCost;

    @ApiModelProperty(value = "项目初始人力目标成本")
    private BigDecimal initHumanTargetCost;

    @ApiModelProperty(value = "项目初始差旅目标成本")
    private BigDecimal initTravelTargetCost;

    @ApiModelProperty(value = "项目初始其它费用目标成本")
    private BigDecimal initOtherTargetCost;

    @ApiModelProperty(value = "项目初始目标成本总额")
    private BigDecimal initTotalTargetCost;

    @ApiModelProperty(value = "目标毛利率")
    private BigDecimal targetCostGrassProfitRatio;

    @ApiModelProperty(value = "物料已发生成本")
    private BigDecimal materialCost;

    @ApiModelProperty(value = "人力已发生成本")
    private BigDecimal humanCost;

    @ApiModelProperty(value = "差旅已发生成本")
    private BigDecimal travelCost;

    @ApiModelProperty(value = "非差旅费用已发生成本")
    private BigDecimal feeCost;

    @ApiModelProperty(value = "已发生成本总额")
    private BigDecimal totalCost;

    @ApiModelProperty(value = "实际毛利率")
    private BigDecimal actualGrassProfitRatio;

    @ApiModelProperty(value = "目标毛利率偏差率")
    private BigDecimal targetCostDeviationRate;

    @ApiModelProperty(value = "当前预算总额")
    private BigDecimal budgetCost;

    @ApiModelProperty(value = "预算毛利率")
    private BigDecimal budgetProfitRatio;

    @ApiModelProperty(value = "预算毛利偏差率")
    private BigDecimal budgetDeviationRate;

    @ApiModelProperty(value = "项目收款金额-原币")
    private BigDecimal receiptClaimAmount;

    @ApiModelProperty(value = "项目收款金额-本位币")
    private BigDecimal standardReceiptClaimAmount;

    @ApiModelProperty(value = "项目付款金额")
    private BigDecimal paymentAmount;

    @ApiModelProperty(value = "项目现金流-本位币")
    private BigDecimal projectCash;

    @ApiModelProperty(value = "项目应收金额-原币")
    private BigDecimal receivableAmount;

    @ApiModelProperty(value = "项目逾期金额-原币")
    private BigDecimal previewAmount;

    @ApiModelProperty(value = "已结转收入（不含税）-原币")
    private BigDecimal carryoverIncomeAmount;

    @ApiModelProperty(value = "已结转收入（不含税）-本位币")
    private BigDecimal standardCarryoverIncomeAmount;

    @ApiModelProperty(value = "已结转收入百分比")
    private BigDecimal carryoverIncomeRatio;

    @ApiModelProperty(value = "已结转成本（不含税）")
    private BigDecimal carryoverCostAmount;

    @ApiModelProperty(value = "已结转毛利额-本位币")
    private BigDecimal carryoverCostGrassAmount;

    @ApiModelProperty(value = "开票金额(含税）-原币")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "开票金额(不含税）-原币")
    private BigDecimal exclusiveInvoiceAmount;

    @ApiModelProperty(value = "开票金额(含税）-本位币")
    private BigDecimal standardInvoiceAmount;

    @ApiModelProperty(value = "开票金额(不含税）-本位币")
    private BigDecimal standardExclusiveInvoiceAmount;

    @ApiModelProperty(value = "开票进度(%)")
    private BigDecimal invoiceRatio;

    @ApiModelProperty(value = "是否删除")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "交付及时率（考核数）")
    private BigDecimal assessmentNum;

    @ApiModelProperty(value = "交付及时率（百分比）")
    private BigDecimal assessmentPercent;

    @ApiModelProperty(value = "交付及时率-（延迟天数）集合")
    private String delayDayInfo;

    @ApiModelProperty(value = "交付及时率（考核数）汇总")
    private BigDecimal assessmentNumTotal;

    @ApiModelProperty(value = "交付及时率（百分比）汇总")
    private BigDecimal assessmentPercentTotal;

    @ApiModelProperty(value = "收款币种")
    private String receiptCurrency;

    @ApiModelProperty(value = "开票币种")
    private String invoiceCurrency;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode == null ? null : contractCode.trim();
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType == null ? null : projectType.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public Integer getProjectLevel() {
        return projectLevel;
    }

    public void setProjectLevel(Integer projectLevel) {
        this.projectLevel = projectLevel;
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public String getSalesManager() {
        return salesManager;
    }

    public void setSalesManager(String salesManager) {
        this.salesManager = salesManager == null ? null : salesManager.trim();
    }

    public String getFinancial() {
        return financial;
    }

    public void setFinancial(String financial) {
        this.financial = financial == null ? null : financial.trim();
    }

    public String getPlanDesignerName() {
        return planDesignerName;
    }

    public void setPlanDesignerName(String planDesignerName) {
        this.planDesignerName = planDesignerName == null ? null : planDesignerName.trim();
    }

    public String getTechnologyLeaderName() {
        return technologyLeaderName;
    }

    public void setTechnologyLeaderName(String technologyLeaderName) {
        this.technologyLeaderName = technologyLeaderName == null ? null : technologyLeaderName.trim();
    }

    public Integer getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(Integer projectStatus) {
        this.projectStatus = projectStatus;
    }

    public String getCurrentMilepost() {
        return currentMilepost;
    }

    public void setCurrentMilepost(String currentMilepost) {
        this.currentMilepost = currentMilepost == null ? null : currentMilepost.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public BigDecimal getProjectAmount() {
        return projectAmount;
    }

    public void setProjectAmount(BigDecimal projectAmount) {
        this.projectAmount = projectAmount;
    }

    public BigDecimal getProjectContractStandardAmount() {
        return projectContractStandardAmount;
    }

    public void setProjectContractStandardAmount(BigDecimal projectContractStandardAmount) {
        this.projectContractStandardAmount = projectContractStandardAmount;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public String getMilepostNameInfo() {
        return milepostNameInfo;
    }

    public void setMilepostNameInfo(String milepostNameInfo) {
        this.milepostNameInfo = milepostNameInfo == null ? null : milepostNameInfo.trim();
    }

    public String getMilepostStatusInfo() {
        return milepostStatusInfo;
    }

    public void setMilepostStatusInfo(String milepostStatusInfo) {
        this.milepostStatusInfo = milepostStatusInfo == null ? null : milepostStatusInfo.trim();
    }

    public String getMilepostEndDateInfo() {
        return milepostEndDateInfo;
    }

    public void setMilepostEndDateInfo(String milepostEndDateInfo) {
        this.milepostEndDateInfo = milepostEndDateInfo == null ? null : milepostEndDateInfo.trim();
    }

    public String getMilepostActualEndTimeInfo() {
        return milepostActualEndTimeInfo;
    }

    public void setMilepostActualEndTimeInfo(String milepostActualEndTimeInfo) {
        this.milepostActualEndTimeInfo = milepostActualEndTimeInfo == null ? null : milepostActualEndTimeInfo.trim();
    }

    public BigDecimal getInitMaterialTargetCost() {
        return initMaterialTargetCost;
    }

    public void setInitMaterialTargetCost(BigDecimal initMaterialTargetCost) {
        this.initMaterialTargetCost = initMaterialTargetCost;
    }

    public BigDecimal getInitHumanTargetCost() {
        return initHumanTargetCost;
    }

    public void setInitHumanTargetCost(BigDecimal initHumanTargetCost) {
        this.initHumanTargetCost = initHumanTargetCost;
    }

    public BigDecimal getInitTravelTargetCost() {
        return initTravelTargetCost;
    }

    public void setInitTravelTargetCost(BigDecimal initTravelTargetCost) {
        this.initTravelTargetCost = initTravelTargetCost;
    }

    public BigDecimal getInitOtherTargetCost() {
        return initOtherTargetCost;
    }

    public void setInitOtherTargetCost(BigDecimal initOtherTargetCost) {
        this.initOtherTargetCost = initOtherTargetCost;
    }

    public BigDecimal getInitTotalTargetCost() {
        return initTotalTargetCost;
    }

    public void setInitTotalTargetCost(BigDecimal initTotalTargetCost) {
        this.initTotalTargetCost = initTotalTargetCost;
    }

    public BigDecimal getTargetCostGrassProfitRatio() {
        return targetCostGrassProfitRatio;
    }

    public void setTargetCostGrassProfitRatio(BigDecimal targetCostGrassProfitRatio) {
        this.targetCostGrassProfitRatio = targetCostGrassProfitRatio;
    }

    public BigDecimal getMaterialCost() {
        return materialCost;
    }

    public void setMaterialCost(BigDecimal materialCost) {
        this.materialCost = materialCost;
    }

    public BigDecimal getHumanCost() {
        return humanCost;
    }

    public void setHumanCost(BigDecimal humanCost) {
        this.humanCost = humanCost;
    }

    public BigDecimal getTravelCost() {
        return travelCost;
    }

    public void setTravelCost(BigDecimal travelCost) {
        this.travelCost = travelCost;
    }

    public BigDecimal getFeeCost() {
        return feeCost;
    }

    public void setFeeCost(BigDecimal feeCost) {
        this.feeCost = feeCost;
    }

    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    public BigDecimal getActualGrassProfitRatio() {
        return actualGrassProfitRatio;
    }

    public void setActualGrassProfitRatio(BigDecimal actualGrassProfitRatio) {
        this.actualGrassProfitRatio = actualGrassProfitRatio;
    }

    public BigDecimal getTargetCostDeviationRate() {
        return targetCostDeviationRate;
    }

    public void setTargetCostDeviationRate(BigDecimal targetCostDeviationRate) {
        this.targetCostDeviationRate = targetCostDeviationRate;
    }

    public BigDecimal getBudgetCost() {
        return budgetCost;
    }

    public void setBudgetCost(BigDecimal budgetCost) {
        this.budgetCost = budgetCost;
    }

    public BigDecimal getBudgetProfitRatio() {
        return budgetProfitRatio;
    }

    public void setBudgetProfitRatio(BigDecimal budgetProfitRatio) {
        this.budgetProfitRatio = budgetProfitRatio;
    }

    public BigDecimal getBudgetDeviationRate() {
        return budgetDeviationRate;
    }

    public void setBudgetDeviationRate(BigDecimal budgetDeviationRate) {
        this.budgetDeviationRate = budgetDeviationRate;
    }

    public BigDecimal getReceiptClaimAmount() {
        return receiptClaimAmount;
    }

    public void setReceiptClaimAmount(BigDecimal receiptClaimAmount) {
        this.receiptClaimAmount = receiptClaimAmount;
    }

    public BigDecimal getStandardReceiptClaimAmount() {
        return standardReceiptClaimAmount;
    }

    public void setStandardReceiptClaimAmount(BigDecimal standardReceiptClaimAmount) {
        this.standardReceiptClaimAmount = standardReceiptClaimAmount;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public BigDecimal getProjectCash() {
        return projectCash;
    }

    public void setProjectCash(BigDecimal projectCash) {
        this.projectCash = projectCash;
    }

    public BigDecimal getReceivableAmount() {
        return receivableAmount;
    }

    public void setReceivableAmount(BigDecimal receivableAmount) {
        this.receivableAmount = receivableAmount;
    }

    public BigDecimal getPreviewAmount() {
        return previewAmount;
    }

    public void setPreviewAmount(BigDecimal previewAmount) {
        this.previewAmount = previewAmount;
    }

    public BigDecimal getCarryoverIncomeAmount() {
        return carryoverIncomeAmount;
    }

    public void setCarryoverIncomeAmount(BigDecimal carryoverIncomeAmount) {
        this.carryoverIncomeAmount = carryoverIncomeAmount;
    }

    public BigDecimal getStandardCarryoverIncomeAmount() {
        return standardCarryoverIncomeAmount;
    }

    public void setStandardCarryoverIncomeAmount(BigDecimal standardCarryoverIncomeAmount) {
        this.standardCarryoverIncomeAmount = standardCarryoverIncomeAmount;
    }

    public BigDecimal getCarryoverIncomeRatio() {
        return carryoverIncomeRatio;
    }

    public void setCarryoverIncomeRatio(BigDecimal carryoverIncomeRatio) {
        this.carryoverIncomeRatio = carryoverIncomeRatio;
    }

    public BigDecimal getCarryoverCostAmount() {
        return carryoverCostAmount;
    }

    public void setCarryoverCostAmount(BigDecimal carryoverCostAmount) {
        this.carryoverCostAmount = carryoverCostAmount;
    }

    public BigDecimal getCarryoverCostGrassAmount() {
        return carryoverCostGrassAmount;
    }

    public void setCarryoverCostGrassAmount(BigDecimal carryoverCostGrassAmount) {
        this.carryoverCostGrassAmount = carryoverCostGrassAmount;
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public BigDecimal getExclusiveInvoiceAmount() {
        return exclusiveInvoiceAmount;
    }

    public void setExclusiveInvoiceAmount(BigDecimal exclusiveInvoiceAmount) {
        this.exclusiveInvoiceAmount = exclusiveInvoiceAmount;
    }

    public BigDecimal getStandardInvoiceAmount() {
        return standardInvoiceAmount;
    }

    public void setStandardInvoiceAmount(BigDecimal standardInvoiceAmount) {
        this.standardInvoiceAmount = standardInvoiceAmount;
    }

    public BigDecimal getStandardExclusiveInvoiceAmount() {
        return standardExclusiveInvoiceAmount;
    }

    public void setStandardExclusiveInvoiceAmount(BigDecimal standardExclusiveInvoiceAmount) {
        this.standardExclusiveInvoiceAmount = standardExclusiveInvoiceAmount;
    }

    public BigDecimal getInvoiceRatio() {
        return invoiceRatio;
    }

    public void setInvoiceRatio(BigDecimal invoiceRatio) {
        this.invoiceRatio = invoiceRatio;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public BigDecimal getAssessmentNum() {
        return assessmentNum;
    }

    public void setAssessmentNum(BigDecimal assessmentNum) {
        this.assessmentNum = assessmentNum;
    }

    public BigDecimal getAssessmentPercent() {
        return assessmentPercent;
    }

    public void setAssessmentPercent(BigDecimal assessmentPercent) {
        this.assessmentPercent = assessmentPercent;
    }

    public String getDelayDayInfo() {
        return delayDayInfo;
    }

    public void setDelayDayInfo(String delayDayInfo) {
        this.delayDayInfo = delayDayInfo == null ? null : delayDayInfo.trim();
    }

    public BigDecimal getAssessmentNumTotal() {
        return assessmentNumTotal;
    }

    public void setAssessmentNumTotal(BigDecimal assessmentNumTotal) {
        this.assessmentNumTotal = assessmentNumTotal;
    }

    public BigDecimal getAssessmentPercentTotal() {
        return assessmentPercentTotal;
    }

    public void setAssessmentPercentTotal(BigDecimal assessmentPercentTotal) {
        this.assessmentPercentTotal = assessmentPercentTotal;
    }

    public String getReceiptCurrency() {
        return receiptCurrency;
    }

    public void setReceiptCurrency(String receiptCurrency) {
        this.receiptCurrency = receiptCurrency == null ? null : receiptCurrency.trim();
    }

    public String getInvoiceCurrency() {
        return invoiceCurrency;
    }

    public void setInvoiceCurrency(String invoiceCurrency) {
        this.invoiceCurrency = invoiceCurrency == null ? null : invoiceCurrency.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", contractCode=").append(contractCode);
        sb.append(", unitName=").append(unitName);
        sb.append(", projectType=").append(projectType);
        sb.append(", customerName=").append(customerName);
        sb.append(", projectLevel=").append(projectLevel);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", salesManager=").append(salesManager);
        sb.append(", financial=").append(financial);
        sb.append(", planDesignerName=").append(planDesignerName);
        sb.append(", technologyLeaderName=").append(technologyLeaderName);
        sb.append(", projectStatus=").append(projectStatus);
        sb.append(", currentMilepost=").append(currentMilepost);
        sb.append(", currency=").append(currency);
        sb.append(", projectAmount=").append(projectAmount);
        sb.append(", projectContractStandardAmount=").append(projectContractStandardAmount);
        sb.append(", startDate=").append(startDate);
        sb.append(", milepostNameInfo=").append(milepostNameInfo);
        sb.append(", milepostStatusInfo=").append(milepostStatusInfo);
        sb.append(", milepostEndDateInfo=").append(milepostEndDateInfo);
        sb.append(", milepostActualEndTimeInfo=").append(milepostActualEndTimeInfo);
        sb.append(", initMaterialTargetCost=").append(initMaterialTargetCost);
        sb.append(", initHumanTargetCost=").append(initHumanTargetCost);
        sb.append(", initTravelTargetCost=").append(initTravelTargetCost);
        sb.append(", initOtherTargetCost=").append(initOtherTargetCost);
        sb.append(", initTotalTargetCost=").append(initTotalTargetCost);
        sb.append(", targetCostGrassProfitRatio=").append(targetCostGrassProfitRatio);
        sb.append(", materialCost=").append(materialCost);
        sb.append(", humanCost=").append(humanCost);
        sb.append(", travelCost=").append(travelCost);
        sb.append(", feeCost=").append(feeCost);
        sb.append(", totalCost=").append(totalCost);
        sb.append(", actualGrassProfitRatio=").append(actualGrassProfitRatio);
        sb.append(", targetCostDeviationRate=").append(targetCostDeviationRate);
        sb.append(", budgetCost=").append(budgetCost);
        sb.append(", budgetProfitRatio=").append(budgetProfitRatio);
        sb.append(", budgetDeviationRate=").append(budgetDeviationRate);
        sb.append(", receiptClaimAmount=").append(receiptClaimAmount);
        sb.append(", standardReceiptClaimAmount=").append(standardReceiptClaimAmount);
        sb.append(", paymentAmount=").append(paymentAmount);
        sb.append(", projectCash=").append(projectCash);
        sb.append(", receivableAmount=").append(receivableAmount);
        sb.append(", previewAmount=").append(previewAmount);
        sb.append(", carryoverIncomeAmount=").append(carryoverIncomeAmount);
        sb.append(", standardCarryoverIncomeAmount=").append(standardCarryoverIncomeAmount);
        sb.append(", carryoverIncomeRatio=").append(carryoverIncomeRatio);
        sb.append(", carryoverCostAmount=").append(carryoverCostAmount);
        sb.append(", carryoverCostGrassAmount=").append(carryoverCostGrassAmount);
        sb.append(", invoiceAmount=").append(invoiceAmount);
        sb.append(", exclusiveInvoiceAmount=").append(exclusiveInvoiceAmount);
        sb.append(", standardInvoiceAmount=").append(standardInvoiceAmount);
        sb.append(", standardExclusiveInvoiceAmount=").append(standardExclusiveInvoiceAmount);
        sb.append(", invoiceRatio=").append(invoiceRatio);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", assessmentNum=").append(assessmentNum);
        sb.append(", assessmentPercent=").append(assessmentPercent);
        sb.append(", delayDayInfo=").append(delayDayInfo);
        sb.append(", assessmentNumTotal=").append(assessmentNumTotal);
        sb.append(", assessmentPercentTotal=").append(assessmentPercentTotal);
        sb.append(", receiptCurrency=").append(receiptCurrency);
        sb.append(", invoiceCurrency=").append(invoiceCurrency);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}