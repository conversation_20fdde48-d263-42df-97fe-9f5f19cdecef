package com.midea.pam.common.statistics.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportProjectChangeBaseInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportProjectChangeBaseInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByIsNull() {
            addCriterion("change_submit_by is null");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByIsNotNull() {
            addCriterion("change_submit_by is not null");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByEqualTo(String value) {
            addCriterion("change_submit_by =", value, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByNotEqualTo(String value) {
            addCriterion("change_submit_by <>", value, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByGreaterThan(String value) {
            addCriterion("change_submit_by >", value, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByGreaterThanOrEqualTo(String value) {
            addCriterion("change_submit_by >=", value, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByLessThan(String value) {
            addCriterion("change_submit_by <", value, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByLessThanOrEqualTo(String value) {
            addCriterion("change_submit_by <=", value, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByLike(String value) {
            addCriterion("change_submit_by like", value, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByNotLike(String value) {
            addCriterion("change_submit_by not like", value, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByIn(List<String> values) {
            addCriterion("change_submit_by in", values, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByNotIn(List<String> values) {
            addCriterion("change_submit_by not in", values, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByBetween(String value1, String value2) {
            addCriterion("change_submit_by between", value1, value2, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByNotBetween(String value1, String value2) {
            addCriterion("change_submit_by not between", value1, value2, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeTimeIsNull() {
            addCriterion("change_time is null");
            return (Criteria) this;
        }

        public Criteria andChangeTimeIsNotNull() {
            addCriterion("change_time is not null");
            return (Criteria) this;
        }

        public Criteria andChangeTimeEqualTo(Date value) {
            addCriterion("change_time =", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeNotEqualTo(Date value) {
            addCriterion("change_time <>", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeGreaterThan(Date value) {
            addCriterion("change_time >", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("change_time >=", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeLessThan(Date value) {
            addCriterion("change_time <", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeLessThanOrEqualTo(Date value) {
            addCriterion("change_time <=", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeIn(List<Date> values) {
            addCriterion("change_time in", values, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeNotIn(List<Date> values) {
            addCriterion("change_time not in", values, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeBetween(Date value1, Date value2) {
            addCriterion("change_time between", value1, value2, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeNotBetween(Date value1, Date value2) {
            addCriterion("change_time not between", value1, value2, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIsNull() {
            addCriterion("change_reason is null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIsNotNull() {
            addCriterion("change_reason is not null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonEqualTo(String value) {
            addCriterion("change_reason =", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotEqualTo(String value) {
            addCriterion("change_reason <>", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonGreaterThan(String value) {
            addCriterion("change_reason >", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonGreaterThanOrEqualTo(String value) {
            addCriterion("change_reason >=", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLessThan(String value) {
            addCriterion("change_reason <", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLessThanOrEqualTo(String value) {
            addCriterion("change_reason <=", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLike(String value) {
            addCriterion("change_reason like", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotLike(String value) {
            addCriterion("change_reason not like", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIn(List<String> values) {
            addCriterion("change_reason in", values, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotIn(List<String> values) {
            addCriterion("change_reason not in", values, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonBetween(String value1, String value2) {
            addCriterion("change_reason between", value1, value2, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotBetween(String value1, String value2) {
            addCriterion("change_reason not between", value1, value2, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeIsNull() {
            addCriterion("change_reason_describe is null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeIsNotNull() {
            addCriterion("change_reason_describe is not null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeEqualTo(String value) {
            addCriterion("change_reason_describe =", value, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeNotEqualTo(String value) {
            addCriterion("change_reason_describe <>", value, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeGreaterThan(String value) {
            addCriterion("change_reason_describe >", value, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeGreaterThanOrEqualTo(String value) {
            addCriterion("change_reason_describe >=", value, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeLessThan(String value) {
            addCriterion("change_reason_describe <", value, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeLessThanOrEqualTo(String value) {
            addCriterion("change_reason_describe <=", value, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeLike(String value) {
            addCriterion("change_reason_describe like", value, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeNotLike(String value) {
            addCriterion("change_reason_describe not like", value, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeIn(List<String> values) {
            addCriterion("change_reason_describe in", values, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeNotIn(List<String> values) {
            addCriterion("change_reason_describe not in", values, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeBetween(String value1, String value2) {
            addCriterion("change_reason_describe between", value1, value2, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeNotBetween(String value1, String value2) {
            addCriterion("change_reason_describe not between", value1, value2, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andAfterNameIsNull() {
            addCriterion("after_name is null");
            return (Criteria) this;
        }

        public Criteria andAfterNameIsNotNull() {
            addCriterion("after_name is not null");
            return (Criteria) this;
        }

        public Criteria andAfterNameEqualTo(String value) {
            addCriterion("after_name =", value, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameNotEqualTo(String value) {
            addCriterion("after_name <>", value, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameGreaterThan(String value) {
            addCriterion("after_name >", value, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameGreaterThanOrEqualTo(String value) {
            addCriterion("after_name >=", value, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameLessThan(String value) {
            addCriterion("after_name <", value, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameLessThanOrEqualTo(String value) {
            addCriterion("after_name <=", value, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameLike(String value) {
            addCriterion("after_name like", value, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameNotLike(String value) {
            addCriterion("after_name not like", value, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameIn(List<String> values) {
            addCriterion("after_name in", values, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameNotIn(List<String> values) {
            addCriterion("after_name not in", values, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameBetween(String value1, String value2) {
            addCriterion("after_name between", value1, value2, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameNotBetween(String value1, String value2) {
            addCriterion("after_name not between", value1, value2, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterBusinessTypeIsNull() {
            addCriterion("after_business_type is null");
            return (Criteria) this;
        }

        public Criteria andAfterBusinessTypeIsNotNull() {
            addCriterion("after_business_type is not null");
            return (Criteria) this;
        }

        public Criteria andAfterBusinessTypeEqualTo(String value) {
            addCriterion("after_business_type =", value, "afterBusinessType");
            return (Criteria) this;
        }

        public Criteria andAfterBusinessTypeNotEqualTo(String value) {
            addCriterion("after_business_type <>", value, "afterBusinessType");
            return (Criteria) this;
        }

        public Criteria andAfterBusinessTypeGreaterThan(String value) {
            addCriterion("after_business_type >", value, "afterBusinessType");
            return (Criteria) this;
        }

        public Criteria andAfterBusinessTypeGreaterThanOrEqualTo(String value) {
            addCriterion("after_business_type >=", value, "afterBusinessType");
            return (Criteria) this;
        }

        public Criteria andAfterBusinessTypeLessThan(String value) {
            addCriterion("after_business_type <", value, "afterBusinessType");
            return (Criteria) this;
        }

        public Criteria andAfterBusinessTypeLessThanOrEqualTo(String value) {
            addCriterion("after_business_type <=", value, "afterBusinessType");
            return (Criteria) this;
        }

        public Criteria andAfterBusinessTypeLike(String value) {
            addCriterion("after_business_type like", value, "afterBusinessType");
            return (Criteria) this;
        }

        public Criteria andAfterBusinessTypeNotLike(String value) {
            addCriterion("after_business_type not like", value, "afterBusinessType");
            return (Criteria) this;
        }

        public Criteria andAfterBusinessTypeIn(List<String> values) {
            addCriterion("after_business_type in", values, "afterBusinessType");
            return (Criteria) this;
        }

        public Criteria andAfterBusinessTypeNotIn(List<String> values) {
            addCriterion("after_business_type not in", values, "afterBusinessType");
            return (Criteria) this;
        }

        public Criteria andAfterBusinessTypeBetween(String value1, String value2) {
            addCriterion("after_business_type between", value1, value2, "afterBusinessType");
            return (Criteria) this;
        }

        public Criteria andAfterBusinessTypeNotBetween(String value1, String value2) {
            addCriterion("after_business_type not between", value1, value2, "afterBusinessType");
            return (Criteria) this;
        }

        public Criteria andAfterStartTimeIsNull() {
            addCriterion("after_start_time is null");
            return (Criteria) this;
        }

        public Criteria andAfterStartTimeIsNotNull() {
            addCriterion("after_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andAfterStartTimeEqualTo(Date value) {
            addCriterion("after_start_time =", value, "afterStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterStartTimeNotEqualTo(Date value) {
            addCriterion("after_start_time <>", value, "afterStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterStartTimeGreaterThan(Date value) {
            addCriterion("after_start_time >", value, "afterStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("after_start_time >=", value, "afterStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterStartTimeLessThan(Date value) {
            addCriterion("after_start_time <", value, "afterStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("after_start_time <=", value, "afterStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterStartTimeIn(List<Date> values) {
            addCriterion("after_start_time in", values, "afterStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterStartTimeNotIn(List<Date> values) {
            addCriterion("after_start_time not in", values, "afterStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterStartTimeBetween(Date value1, Date value2) {
            addCriterion("after_start_time between", value1, value2, "afterStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("after_start_time not between", value1, value2, "afterStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterEndTimeIsNull() {
            addCriterion("after_end_time is null");
            return (Criteria) this;
        }

        public Criteria andAfterEndTimeIsNotNull() {
            addCriterion("after_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andAfterEndTimeEqualTo(Date value) {
            addCriterion("after_end_time =", value, "afterEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterEndTimeNotEqualTo(Date value) {
            addCriterion("after_end_time <>", value, "afterEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterEndTimeGreaterThan(Date value) {
            addCriterion("after_end_time >", value, "afterEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("after_end_time >=", value, "afterEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterEndTimeLessThan(Date value) {
            addCriterion("after_end_time <", value, "afterEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("after_end_time <=", value, "afterEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterEndTimeIn(List<Date> values) {
            addCriterion("after_end_time in", values, "afterEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterEndTimeNotIn(List<Date> values) {
            addCriterion("after_end_time not in", values, "afterEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterEndTimeBetween(Date value1, Date value2) {
            addCriterion("after_end_time between", value1, value2, "afterEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("after_end_time not between", value1, value2, "afterEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterManagerIsNull() {
            addCriterion("after_manager is null");
            return (Criteria) this;
        }

        public Criteria andAfterManagerIsNotNull() {
            addCriterion("after_manager is not null");
            return (Criteria) this;
        }

        public Criteria andAfterManagerEqualTo(Long value) {
            addCriterion("after_manager =", value, "afterManager");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNotEqualTo(Long value) {
            addCriterion("after_manager <>", value, "afterManager");
            return (Criteria) this;
        }

        public Criteria andAfterManagerGreaterThan(Long value) {
            addCriterion("after_manager >", value, "afterManager");
            return (Criteria) this;
        }

        public Criteria andAfterManagerGreaterThanOrEqualTo(Long value) {
            addCriterion("after_manager >=", value, "afterManager");
            return (Criteria) this;
        }

        public Criteria andAfterManagerLessThan(Long value) {
            addCriterion("after_manager <", value, "afterManager");
            return (Criteria) this;
        }

        public Criteria andAfterManagerLessThanOrEqualTo(Long value) {
            addCriterion("after_manager <=", value, "afterManager");
            return (Criteria) this;
        }

        public Criteria andAfterManagerIn(List<Long> values) {
            addCriterion("after_manager in", values, "afterManager");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNotIn(List<Long> values) {
            addCriterion("after_manager not in", values, "afterManager");
            return (Criteria) this;
        }

        public Criteria andAfterManagerBetween(Long value1, Long value2) {
            addCriterion("after_manager between", value1, value2, "afterManager");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNotBetween(Long value1, Long value2) {
            addCriterion("after_manager not between", value1, value2, "afterManager");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialIsNull() {
            addCriterion("after_financial is null");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialIsNotNull() {
            addCriterion("after_financial is not null");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialEqualTo(Long value) {
            addCriterion("after_financial =", value, "afterFinancial");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNotEqualTo(Long value) {
            addCriterion("after_financial <>", value, "afterFinancial");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialGreaterThan(Long value) {
            addCriterion("after_financial >", value, "afterFinancial");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialGreaterThanOrEqualTo(Long value) {
            addCriterion("after_financial >=", value, "afterFinancial");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialLessThan(Long value) {
            addCriterion("after_financial <", value, "afterFinancial");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialLessThanOrEqualTo(Long value) {
            addCriterion("after_financial <=", value, "afterFinancial");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialIn(List<Long> values) {
            addCriterion("after_financial in", values, "afterFinancial");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNotIn(List<Long> values) {
            addCriterion("after_financial not in", values, "afterFinancial");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialBetween(Long value1, Long value2) {
            addCriterion("after_financial between", value1, value2, "afterFinancial");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNotBetween(Long value1, Long value2) {
            addCriterion("after_financial not between", value1, value2, "afterFinancial");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerIsNull() {
            addCriterion("after_sale_manager is null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerIsNotNull() {
            addCriterion("after_sale_manager is not null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerEqualTo(Long value) {
            addCriterion("after_sale_manager =", value, "afterSaleManager");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNotEqualTo(Long value) {
            addCriterion("after_sale_manager <>", value, "afterSaleManager");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerGreaterThan(Long value) {
            addCriterion("after_sale_manager >", value, "afterSaleManager");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerGreaterThanOrEqualTo(Long value) {
            addCriterion("after_sale_manager >=", value, "afterSaleManager");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerLessThan(Long value) {
            addCriterion("after_sale_manager <", value, "afterSaleManager");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerLessThanOrEqualTo(Long value) {
            addCriterion("after_sale_manager <=", value, "afterSaleManager");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerIn(List<Long> values) {
            addCriterion("after_sale_manager in", values, "afterSaleManager");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNotIn(List<Long> values) {
            addCriterion("after_sale_manager not in", values, "afterSaleManager");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerBetween(Long value1, Long value2) {
            addCriterion("after_sale_manager between", value1, value2, "afterSaleManager");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNotBetween(Long value1, Long value2) {
            addCriterion("after_sale_manager not between", value1, value2, "afterSaleManager");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerIsNull() {
            addCriterion("after_designer is null");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerIsNotNull() {
            addCriterion("after_designer is not null");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerEqualTo(Long value) {
            addCriterion("after_designer =", value, "afterDesigner");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNotEqualTo(Long value) {
            addCriterion("after_designer <>", value, "afterDesigner");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerGreaterThan(Long value) {
            addCriterion("after_designer >", value, "afterDesigner");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerGreaterThanOrEqualTo(Long value) {
            addCriterion("after_designer >=", value, "afterDesigner");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerLessThan(Long value) {
            addCriterion("after_designer <", value, "afterDesigner");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerLessThanOrEqualTo(Long value) {
            addCriterion("after_designer <=", value, "afterDesigner");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerIn(List<Long> values) {
            addCriterion("after_designer in", values, "afterDesigner");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNotIn(List<Long> values) {
            addCriterion("after_designer not in", values, "afterDesigner");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerBetween(Long value1, Long value2) {
            addCriterion("after_designer between", value1, value2, "afterDesigner");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNotBetween(Long value1, Long value2) {
            addCriterion("after_designer not between", value1, value2, "afterDesigner");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyIsNull() {
            addCriterion("after_technology is null");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyIsNotNull() {
            addCriterion("after_technology is not null");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyEqualTo(Long value) {
            addCriterion("after_technology =", value, "afterTechnology");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNotEqualTo(Long value) {
            addCriterion("after_technology <>", value, "afterTechnology");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyGreaterThan(Long value) {
            addCriterion("after_technology >", value, "afterTechnology");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyGreaterThanOrEqualTo(Long value) {
            addCriterion("after_technology >=", value, "afterTechnology");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyLessThan(Long value) {
            addCriterion("after_technology <", value, "afterTechnology");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyLessThanOrEqualTo(Long value) {
            addCriterion("after_technology <=", value, "afterTechnology");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyIn(List<Long> values) {
            addCriterion("after_technology in", values, "afterTechnology");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNotIn(List<Long> values) {
            addCriterion("after_technology not in", values, "afterTechnology");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyBetween(Long value1, Long value2) {
            addCriterion("after_technology between", value1, value2, "afterTechnology");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNotBetween(Long value1, Long value2) {
            addCriterion("after_technology not between", value1, value2, "afterTechnology");
            return (Criteria) this;
        }

        public Criteria andAfterProjectDescribeIsNull() {
            addCriterion("after_project_describe is null");
            return (Criteria) this;
        }

        public Criteria andAfterProjectDescribeIsNotNull() {
            addCriterion("after_project_describe is not null");
            return (Criteria) this;
        }

        public Criteria andAfterProjectDescribeEqualTo(String value) {
            addCriterion("after_project_describe =", value, "afterProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andAfterProjectDescribeNotEqualTo(String value) {
            addCriterion("after_project_describe <>", value, "afterProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andAfterProjectDescribeGreaterThan(String value) {
            addCriterion("after_project_describe >", value, "afterProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andAfterProjectDescribeGreaterThanOrEqualTo(String value) {
            addCriterion("after_project_describe >=", value, "afterProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andAfterProjectDescribeLessThan(String value) {
            addCriterion("after_project_describe <", value, "afterProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andAfterProjectDescribeLessThanOrEqualTo(String value) {
            addCriterion("after_project_describe <=", value, "afterProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andAfterProjectDescribeLike(String value) {
            addCriterion("after_project_describe like", value, "afterProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andAfterProjectDescribeNotLike(String value) {
            addCriterion("after_project_describe not like", value, "afterProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andAfterProjectDescribeIn(List<String> values) {
            addCriterion("after_project_describe in", values, "afterProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andAfterProjectDescribeNotIn(List<String> values) {
            addCriterion("after_project_describe not in", values, "afterProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andAfterProjectDescribeBetween(String value1, String value2) {
            addCriterion("after_project_describe between", value1, value2, "afterProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andAfterProjectDescribeNotBetween(String value1, String value2) {
            addCriterion("after_project_describe not between", value1, value2, "afterProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andBeforeBusinessTypeIsNull() {
            addCriterion("before_business_type is null");
            return (Criteria) this;
        }

        public Criteria andBeforeBusinessTypeIsNotNull() {
            addCriterion("before_business_type is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeBusinessTypeEqualTo(String value) {
            addCriterion("before_business_type =", value, "beforeBusinessType");
            return (Criteria) this;
        }

        public Criteria andBeforeBusinessTypeNotEqualTo(String value) {
            addCriterion("before_business_type <>", value, "beforeBusinessType");
            return (Criteria) this;
        }

        public Criteria andBeforeBusinessTypeGreaterThan(String value) {
            addCriterion("before_business_type >", value, "beforeBusinessType");
            return (Criteria) this;
        }

        public Criteria andBeforeBusinessTypeGreaterThanOrEqualTo(String value) {
            addCriterion("before_business_type >=", value, "beforeBusinessType");
            return (Criteria) this;
        }

        public Criteria andBeforeBusinessTypeLessThan(String value) {
            addCriterion("before_business_type <", value, "beforeBusinessType");
            return (Criteria) this;
        }

        public Criteria andBeforeBusinessTypeLessThanOrEqualTo(String value) {
            addCriterion("before_business_type <=", value, "beforeBusinessType");
            return (Criteria) this;
        }

        public Criteria andBeforeBusinessTypeLike(String value) {
            addCriterion("before_business_type like", value, "beforeBusinessType");
            return (Criteria) this;
        }

        public Criteria andBeforeBusinessTypeNotLike(String value) {
            addCriterion("before_business_type not like", value, "beforeBusinessType");
            return (Criteria) this;
        }

        public Criteria andBeforeBusinessTypeIn(List<String> values) {
            addCriterion("before_business_type in", values, "beforeBusinessType");
            return (Criteria) this;
        }

        public Criteria andBeforeBusinessTypeNotIn(List<String> values) {
            addCriterion("before_business_type not in", values, "beforeBusinessType");
            return (Criteria) this;
        }

        public Criteria andBeforeBusinessTypeBetween(String value1, String value2) {
            addCriterion("before_business_type between", value1, value2, "beforeBusinessType");
            return (Criteria) this;
        }

        public Criteria andBeforeBusinessTypeNotBetween(String value1, String value2) {
            addCriterion("before_business_type not between", value1, value2, "beforeBusinessType");
            return (Criteria) this;
        }

        public Criteria andBeforeStartTimeIsNull() {
            addCriterion("before_start_time is null");
            return (Criteria) this;
        }

        public Criteria andBeforeStartTimeIsNotNull() {
            addCriterion("before_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeStartTimeEqualTo(Date value) {
            addCriterion("before_start_time =", value, "beforeStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforeStartTimeNotEqualTo(Date value) {
            addCriterion("before_start_time <>", value, "beforeStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforeStartTimeGreaterThan(Date value) {
            addCriterion("before_start_time >", value, "beforeStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforeStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("before_start_time >=", value, "beforeStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforeStartTimeLessThan(Date value) {
            addCriterion("before_start_time <", value, "beforeStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforeStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("before_start_time <=", value, "beforeStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforeStartTimeIn(List<Date> values) {
            addCriterion("before_start_time in", values, "beforeStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforeStartTimeNotIn(List<Date> values) {
            addCriterion("before_start_time not in", values, "beforeStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforeStartTimeBetween(Date value1, Date value2) {
            addCriterion("before_start_time between", value1, value2, "beforeStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforeStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("before_start_time not between", value1, value2, "beforeStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforeEndTimeIsNull() {
            addCriterion("before_end_time is null");
            return (Criteria) this;
        }

        public Criteria andBeforeEndTimeIsNotNull() {
            addCriterion("before_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeEndTimeEqualTo(Date value) {
            addCriterion("before_end_time =", value, "beforeEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforeEndTimeNotEqualTo(Date value) {
            addCriterion("before_end_time <>", value, "beforeEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforeEndTimeGreaterThan(Date value) {
            addCriterion("before_end_time >", value, "beforeEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforeEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("before_end_time >=", value, "beforeEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforeEndTimeLessThan(Date value) {
            addCriterion("before_end_time <", value, "beforeEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforeEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("before_end_time <=", value, "beforeEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforeEndTimeIn(List<Date> values) {
            addCriterion("before_end_time in", values, "beforeEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforeEndTimeNotIn(List<Date> values) {
            addCriterion("before_end_time not in", values, "beforeEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforeEndTimeBetween(Date value1, Date value2) {
            addCriterion("before_end_time between", value1, value2, "beforeEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforeEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("before_end_time not between", value1, value2, "beforeEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerIsNull() {
            addCriterion("before_manager is null");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerIsNotNull() {
            addCriterion("before_manager is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerEqualTo(Long value) {
            addCriterion("before_manager =", value, "beforeManager");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNotEqualTo(Long value) {
            addCriterion("before_manager <>", value, "beforeManager");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerGreaterThan(Long value) {
            addCriterion("before_manager >", value, "beforeManager");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerGreaterThanOrEqualTo(Long value) {
            addCriterion("before_manager >=", value, "beforeManager");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerLessThan(Long value) {
            addCriterion("before_manager <", value, "beforeManager");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerLessThanOrEqualTo(Long value) {
            addCriterion("before_manager <=", value, "beforeManager");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerIn(List<Long> values) {
            addCriterion("before_manager in", values, "beforeManager");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNotIn(List<Long> values) {
            addCriterion("before_manager not in", values, "beforeManager");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerBetween(Long value1, Long value2) {
            addCriterion("before_manager between", value1, value2, "beforeManager");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNotBetween(Long value1, Long value2) {
            addCriterion("before_manager not between", value1, value2, "beforeManager");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialIsNull() {
            addCriterion("before_financial is null");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialIsNotNull() {
            addCriterion("before_financial is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialEqualTo(Long value) {
            addCriterion("before_financial =", value, "beforeFinancial");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNotEqualTo(Long value) {
            addCriterion("before_financial <>", value, "beforeFinancial");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialGreaterThan(Long value) {
            addCriterion("before_financial >", value, "beforeFinancial");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialGreaterThanOrEqualTo(Long value) {
            addCriterion("before_financial >=", value, "beforeFinancial");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialLessThan(Long value) {
            addCriterion("before_financial <", value, "beforeFinancial");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialLessThanOrEqualTo(Long value) {
            addCriterion("before_financial <=", value, "beforeFinancial");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialIn(List<Long> values) {
            addCriterion("before_financial in", values, "beforeFinancial");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNotIn(List<Long> values) {
            addCriterion("before_financial not in", values, "beforeFinancial");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialBetween(Long value1, Long value2) {
            addCriterion("before_financial between", value1, value2, "beforeFinancial");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNotBetween(Long value1, Long value2) {
            addCriterion("before_financial not between", value1, value2, "beforeFinancial");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerIsNull() {
            addCriterion("before_sale_manager is null");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerIsNotNull() {
            addCriterion("before_sale_manager is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerEqualTo(Long value) {
            addCriterion("before_sale_manager =", value, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNotEqualTo(Long value) {
            addCriterion("before_sale_manager <>", value, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerGreaterThan(Long value) {
            addCriterion("before_sale_manager >", value, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerGreaterThanOrEqualTo(Long value) {
            addCriterion("before_sale_manager >=", value, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerLessThan(Long value) {
            addCriterion("before_sale_manager <", value, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerLessThanOrEqualTo(Long value) {
            addCriterion("before_sale_manager <=", value, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerIn(List<Long> values) {
            addCriterion("before_sale_manager in", values, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNotIn(List<Long> values) {
            addCriterion("before_sale_manager not in", values, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerBetween(Long value1, Long value2) {
            addCriterion("before_sale_manager between", value1, value2, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNotBetween(Long value1, Long value2) {
            addCriterion("before_sale_manager not between", value1, value2, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerIsNull() {
            addCriterion("before_designer is null");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerIsNotNull() {
            addCriterion("before_designer is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerEqualTo(Long value) {
            addCriterion("before_designer =", value, "beforeDesigner");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNotEqualTo(Long value) {
            addCriterion("before_designer <>", value, "beforeDesigner");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerGreaterThan(Long value) {
            addCriterion("before_designer >", value, "beforeDesigner");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerGreaterThanOrEqualTo(Long value) {
            addCriterion("before_designer >=", value, "beforeDesigner");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerLessThan(Long value) {
            addCriterion("before_designer <", value, "beforeDesigner");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerLessThanOrEqualTo(Long value) {
            addCriterion("before_designer <=", value, "beforeDesigner");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerIn(List<Long> values) {
            addCriterion("before_designer in", values, "beforeDesigner");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNotIn(List<Long> values) {
            addCriterion("before_designer not in", values, "beforeDesigner");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerBetween(Long value1, Long value2) {
            addCriterion("before_designer between", value1, value2, "beforeDesigner");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNotBetween(Long value1, Long value2) {
            addCriterion("before_designer not between", value1, value2, "beforeDesigner");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyIsNull() {
            addCriterion("before_technology is null");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyIsNotNull() {
            addCriterion("before_technology is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyEqualTo(Long value) {
            addCriterion("before_technology =", value, "beforeTechnology");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNotEqualTo(Long value) {
            addCriterion("before_technology <>", value, "beforeTechnology");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyGreaterThan(Long value) {
            addCriterion("before_technology >", value, "beforeTechnology");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyGreaterThanOrEqualTo(Long value) {
            addCriterion("before_technology >=", value, "beforeTechnology");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyLessThan(Long value) {
            addCriterion("before_technology <", value, "beforeTechnology");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyLessThanOrEqualTo(Long value) {
            addCriterion("before_technology <=", value, "beforeTechnology");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyIn(List<Long> values) {
            addCriterion("before_technology in", values, "beforeTechnology");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNotIn(List<Long> values) {
            addCriterion("before_technology not in", values, "beforeTechnology");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyBetween(Long value1, Long value2) {
            addCriterion("before_technology between", value1, value2, "beforeTechnology");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNotBetween(Long value1, Long value2) {
            addCriterion("before_technology not between", value1, value2, "beforeTechnology");
            return (Criteria) this;
        }

        public Criteria andBeforeProjectDescribeIsNull() {
            addCriterion("before_project_describe is null");
            return (Criteria) this;
        }

        public Criteria andBeforeProjectDescribeIsNotNull() {
            addCriterion("before_project_describe is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeProjectDescribeEqualTo(String value) {
            addCriterion("before_project_describe =", value, "beforeProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andBeforeProjectDescribeNotEqualTo(String value) {
            addCriterion("before_project_describe <>", value, "beforeProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andBeforeProjectDescribeGreaterThan(String value) {
            addCriterion("before_project_describe >", value, "beforeProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andBeforeProjectDescribeGreaterThanOrEqualTo(String value) {
            addCriterion("before_project_describe >=", value, "beforeProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andBeforeProjectDescribeLessThan(String value) {
            addCriterion("before_project_describe <", value, "beforeProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andBeforeProjectDescribeLessThanOrEqualTo(String value) {
            addCriterion("before_project_describe <=", value, "beforeProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andBeforeProjectDescribeLike(String value) {
            addCriterion("before_project_describe like", value, "beforeProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andBeforeProjectDescribeNotLike(String value) {
            addCriterion("before_project_describe not like", value, "beforeProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andBeforeProjectDescribeIn(List<String> values) {
            addCriterion("before_project_describe in", values, "beforeProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andBeforeProjectDescribeNotIn(List<String> values) {
            addCriterion("before_project_describe not in", values, "beforeProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andBeforeProjectDescribeBetween(String value1, String value2) {
            addCriterion("before_project_describe between", value1, value2, "beforeProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andBeforeProjectDescribeNotBetween(String value1, String value2) {
            addCriterion("before_project_describe not between", value1, value2, "beforeProjectDescribe");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNameIsNull() {
            addCriterion("after_sale_manager_name is null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNameIsNotNull() {
            addCriterion("after_sale_manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNameEqualTo(String value) {
            addCriterion("after_sale_manager_name =", value, "afterSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNameNotEqualTo(String value) {
            addCriterion("after_sale_manager_name <>", value, "afterSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNameGreaterThan(String value) {
            addCriterion("after_sale_manager_name >", value, "afterSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("after_sale_manager_name >=", value, "afterSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNameLessThan(String value) {
            addCriterion("after_sale_manager_name <", value, "afterSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNameLessThanOrEqualTo(String value) {
            addCriterion("after_sale_manager_name <=", value, "afterSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNameLike(String value) {
            addCriterion("after_sale_manager_name like", value, "afterSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNameNotLike(String value) {
            addCriterion("after_sale_manager_name not like", value, "afterSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNameIn(List<String> values) {
            addCriterion("after_sale_manager_name in", values, "afterSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNameNotIn(List<String> values) {
            addCriterion("after_sale_manager_name not in", values, "afterSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNameBetween(String value1, String value2) {
            addCriterion("after_sale_manager_name between", value1, value2, "afterSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleManagerNameNotBetween(String value1, String value2) {
            addCriterion("after_sale_manager_name not between", value1, value2, "afterSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNameIsNull() {
            addCriterion("after_manager_name is null");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNameIsNotNull() {
            addCriterion("after_manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNameEqualTo(String value) {
            addCriterion("after_manager_name =", value, "afterManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNameNotEqualTo(String value) {
            addCriterion("after_manager_name <>", value, "afterManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNameGreaterThan(String value) {
            addCriterion("after_manager_name >", value, "afterManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("after_manager_name >=", value, "afterManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNameLessThan(String value) {
            addCriterion("after_manager_name <", value, "afterManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNameLessThanOrEqualTo(String value) {
            addCriterion("after_manager_name <=", value, "afterManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNameLike(String value) {
            addCriterion("after_manager_name like", value, "afterManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNameNotLike(String value) {
            addCriterion("after_manager_name not like", value, "afterManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNameIn(List<String> values) {
            addCriterion("after_manager_name in", values, "afterManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNameNotIn(List<String> values) {
            addCriterion("after_manager_name not in", values, "afterManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNameBetween(String value1, String value2) {
            addCriterion("after_manager_name between", value1, value2, "afterManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterManagerNameNotBetween(String value1, String value2) {
            addCriterion("after_manager_name not between", value1, value2, "afterManagerName");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNameIsNull() {
            addCriterion("after_financial_name is null");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNameIsNotNull() {
            addCriterion("after_financial_name is not null");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNameEqualTo(String value) {
            addCriterion("after_financial_name =", value, "afterFinancialName");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNameNotEqualTo(String value) {
            addCriterion("after_financial_name <>", value, "afterFinancialName");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNameGreaterThan(String value) {
            addCriterion("after_financial_name >", value, "afterFinancialName");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNameGreaterThanOrEqualTo(String value) {
            addCriterion("after_financial_name >=", value, "afterFinancialName");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNameLessThan(String value) {
            addCriterion("after_financial_name <", value, "afterFinancialName");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNameLessThanOrEqualTo(String value) {
            addCriterion("after_financial_name <=", value, "afterFinancialName");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNameLike(String value) {
            addCriterion("after_financial_name like", value, "afterFinancialName");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNameNotLike(String value) {
            addCriterion("after_financial_name not like", value, "afterFinancialName");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNameIn(List<String> values) {
            addCriterion("after_financial_name in", values, "afterFinancialName");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNameNotIn(List<String> values) {
            addCriterion("after_financial_name not in", values, "afterFinancialName");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNameBetween(String value1, String value2) {
            addCriterion("after_financial_name between", value1, value2, "afterFinancialName");
            return (Criteria) this;
        }

        public Criteria andAfterFinancialNameNotBetween(String value1, String value2) {
            addCriterion("after_financial_name not between", value1, value2, "afterFinancialName");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNameIsNull() {
            addCriterion("after_technology_name is null");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNameIsNotNull() {
            addCriterion("after_technology_name is not null");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNameEqualTo(String value) {
            addCriterion("after_technology_name =", value, "afterTechnologyName");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNameNotEqualTo(String value) {
            addCriterion("after_technology_name <>", value, "afterTechnologyName");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNameGreaterThan(String value) {
            addCriterion("after_technology_name >", value, "afterTechnologyName");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNameGreaterThanOrEqualTo(String value) {
            addCriterion("after_technology_name >=", value, "afterTechnologyName");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNameLessThan(String value) {
            addCriterion("after_technology_name <", value, "afterTechnologyName");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNameLessThanOrEqualTo(String value) {
            addCriterion("after_technology_name <=", value, "afterTechnologyName");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNameLike(String value) {
            addCriterion("after_technology_name like", value, "afterTechnologyName");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNameNotLike(String value) {
            addCriterion("after_technology_name not like", value, "afterTechnologyName");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNameIn(List<String> values) {
            addCriterion("after_technology_name in", values, "afterTechnologyName");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNameNotIn(List<String> values) {
            addCriterion("after_technology_name not in", values, "afterTechnologyName");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNameBetween(String value1, String value2) {
            addCriterion("after_technology_name between", value1, value2, "afterTechnologyName");
            return (Criteria) this;
        }

        public Criteria andAfterTechnologyNameNotBetween(String value1, String value2) {
            addCriterion("after_technology_name not between", value1, value2, "afterTechnologyName");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNameIsNull() {
            addCriterion("after_designer_name is null");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNameIsNotNull() {
            addCriterion("after_designer_name is not null");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNameEqualTo(String value) {
            addCriterion("after_designer_name =", value, "afterDesignerName");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNameNotEqualTo(String value) {
            addCriterion("after_designer_name <>", value, "afterDesignerName");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNameGreaterThan(String value) {
            addCriterion("after_designer_name >", value, "afterDesignerName");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNameGreaterThanOrEqualTo(String value) {
            addCriterion("after_designer_name >=", value, "afterDesignerName");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNameLessThan(String value) {
            addCriterion("after_designer_name <", value, "afterDesignerName");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNameLessThanOrEqualTo(String value) {
            addCriterion("after_designer_name <=", value, "afterDesignerName");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNameLike(String value) {
            addCriterion("after_designer_name like", value, "afterDesignerName");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNameNotLike(String value) {
            addCriterion("after_designer_name not like", value, "afterDesignerName");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNameIn(List<String> values) {
            addCriterion("after_designer_name in", values, "afterDesignerName");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNameNotIn(List<String> values) {
            addCriterion("after_designer_name not in", values, "afterDesignerName");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNameBetween(String value1, String value2) {
            addCriterion("after_designer_name between", value1, value2, "afterDesignerName");
            return (Criteria) this;
        }

        public Criteria andAfterDesignerNameNotBetween(String value1, String value2) {
            addCriterion("after_designer_name not between", value1, value2, "afterDesignerName");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNameIsNull() {
            addCriterion("before_sale_manager_name is null");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNameIsNotNull() {
            addCriterion("before_sale_manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNameEqualTo(String value) {
            addCriterion("before_sale_manager_name =", value, "beforeSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNameNotEqualTo(String value) {
            addCriterion("before_sale_manager_name <>", value, "beforeSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNameGreaterThan(String value) {
            addCriterion("before_sale_manager_name >", value, "beforeSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("before_sale_manager_name >=", value, "beforeSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNameLessThan(String value) {
            addCriterion("before_sale_manager_name <", value, "beforeSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNameLessThanOrEqualTo(String value) {
            addCriterion("before_sale_manager_name <=", value, "beforeSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNameLike(String value) {
            addCriterion("before_sale_manager_name like", value, "beforeSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNameNotLike(String value) {
            addCriterion("before_sale_manager_name not like", value, "beforeSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNameIn(List<String> values) {
            addCriterion("before_sale_manager_name in", values, "beforeSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNameNotIn(List<String> values) {
            addCriterion("before_sale_manager_name not in", values, "beforeSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNameBetween(String value1, String value2) {
            addCriterion("before_sale_manager_name between", value1, value2, "beforeSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNameNotBetween(String value1, String value2) {
            addCriterion("before_sale_manager_name not between", value1, value2, "beforeSaleManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNameIsNull() {
            addCriterion("before_manager_name is null");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNameIsNotNull() {
            addCriterion("before_manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNameEqualTo(String value) {
            addCriterion("before_manager_name =", value, "beforeManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNameNotEqualTo(String value) {
            addCriterion("before_manager_name <>", value, "beforeManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNameGreaterThan(String value) {
            addCriterion("before_manager_name >", value, "beforeManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("before_manager_name >=", value, "beforeManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNameLessThan(String value) {
            addCriterion("before_manager_name <", value, "beforeManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNameLessThanOrEqualTo(String value) {
            addCriterion("before_manager_name <=", value, "beforeManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNameLike(String value) {
            addCriterion("before_manager_name like", value, "beforeManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNameNotLike(String value) {
            addCriterion("before_manager_name not like", value, "beforeManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNameIn(List<String> values) {
            addCriterion("before_manager_name in", values, "beforeManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNameNotIn(List<String> values) {
            addCriterion("before_manager_name not in", values, "beforeManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNameBetween(String value1, String value2) {
            addCriterion("before_manager_name between", value1, value2, "beforeManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeManagerNameNotBetween(String value1, String value2) {
            addCriterion("before_manager_name not between", value1, value2, "beforeManagerName");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNameIsNull() {
            addCriterion("before_financial_name is null");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNameIsNotNull() {
            addCriterion("before_financial_name is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNameEqualTo(String value) {
            addCriterion("before_financial_name =", value, "beforeFinancialName");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNameNotEqualTo(String value) {
            addCriterion("before_financial_name <>", value, "beforeFinancialName");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNameGreaterThan(String value) {
            addCriterion("before_financial_name >", value, "beforeFinancialName");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNameGreaterThanOrEqualTo(String value) {
            addCriterion("before_financial_name >=", value, "beforeFinancialName");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNameLessThan(String value) {
            addCriterion("before_financial_name <", value, "beforeFinancialName");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNameLessThanOrEqualTo(String value) {
            addCriterion("before_financial_name <=", value, "beforeFinancialName");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNameLike(String value) {
            addCriterion("before_financial_name like", value, "beforeFinancialName");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNameNotLike(String value) {
            addCriterion("before_financial_name not like", value, "beforeFinancialName");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNameIn(List<String> values) {
            addCriterion("before_financial_name in", values, "beforeFinancialName");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNameNotIn(List<String> values) {
            addCriterion("before_financial_name not in", values, "beforeFinancialName");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNameBetween(String value1, String value2) {
            addCriterion("before_financial_name between", value1, value2, "beforeFinancialName");
            return (Criteria) this;
        }

        public Criteria andBeforeFinancialNameNotBetween(String value1, String value2) {
            addCriterion("before_financial_name not between", value1, value2, "beforeFinancialName");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNameIsNull() {
            addCriterion("before_designer_name is null");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNameIsNotNull() {
            addCriterion("before_designer_name is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNameEqualTo(String value) {
            addCriterion("before_designer_name =", value, "beforeDesignerName");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNameNotEqualTo(String value) {
            addCriterion("before_designer_name <>", value, "beforeDesignerName");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNameGreaterThan(String value) {
            addCriterion("before_designer_name >", value, "beforeDesignerName");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNameGreaterThanOrEqualTo(String value) {
            addCriterion("before_designer_name >=", value, "beforeDesignerName");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNameLessThan(String value) {
            addCriterion("before_designer_name <", value, "beforeDesignerName");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNameLessThanOrEqualTo(String value) {
            addCriterion("before_designer_name <=", value, "beforeDesignerName");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNameLike(String value) {
            addCriterion("before_designer_name like", value, "beforeDesignerName");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNameNotLike(String value) {
            addCriterion("before_designer_name not like", value, "beforeDesignerName");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNameIn(List<String> values) {
            addCriterion("before_designer_name in", values, "beforeDesignerName");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNameNotIn(List<String> values) {
            addCriterion("before_designer_name not in", values, "beforeDesignerName");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNameBetween(String value1, String value2) {
            addCriterion("before_designer_name between", value1, value2, "beforeDesignerName");
            return (Criteria) this;
        }

        public Criteria andBeforeDesignerNameNotBetween(String value1, String value2) {
            addCriterion("before_designer_name not between", value1, value2, "beforeDesignerName");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNameIsNull() {
            addCriterion("before_technology_name is null");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNameIsNotNull() {
            addCriterion("before_technology_name is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNameEqualTo(String value) {
            addCriterion("before_technology_name =", value, "beforeTechnologyName");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNameNotEqualTo(String value) {
            addCriterion("before_technology_name <>", value, "beforeTechnologyName");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNameGreaterThan(String value) {
            addCriterion("before_technology_name >", value, "beforeTechnologyName");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNameGreaterThanOrEqualTo(String value) {
            addCriterion("before_technology_name >=", value, "beforeTechnologyName");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNameLessThan(String value) {
            addCriterion("before_technology_name <", value, "beforeTechnologyName");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNameLessThanOrEqualTo(String value) {
            addCriterion("before_technology_name <=", value, "beforeTechnologyName");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNameLike(String value) {
            addCriterion("before_technology_name like", value, "beforeTechnologyName");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNameNotLike(String value) {
            addCriterion("before_technology_name not like", value, "beforeTechnologyName");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNameIn(List<String> values) {
            addCriterion("before_technology_name in", values, "beforeTechnologyName");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNameNotIn(List<String> values) {
            addCriterion("before_technology_name not in", values, "beforeTechnologyName");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNameBetween(String value1, String value2) {
            addCriterion("before_technology_name between", value1, value2, "beforeTechnologyName");
            return (Criteria) this;
        }

        public Criteria andBeforeTechnologyNameNotBetween(String value1, String value2) {
            addCriterion("before_technology_name not between", value1, value2, "beforeTechnologyName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}