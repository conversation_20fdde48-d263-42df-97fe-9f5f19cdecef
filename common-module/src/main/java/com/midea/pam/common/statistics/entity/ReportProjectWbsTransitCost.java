package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "在途成本明细表")
public class ReportProjectWbsTransitCost extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "执行时间,项目详情中项目成本页面的更新时间")
    private Date executeTime;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "wbs编码")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "采购订单号/采购合同编号/EC单号/罚扣编号")
    private String num;

    @ApiModelProperty(value = "合同名称")
    private String purchaseContractName;

    @ApiModelProperty(value = "供应商名称/人员")
    private String vendorName;

    @ApiModelProperty(value = "PAM编码")
    private String pamCode;

    @ApiModelProperty(value = "物料编码")
    private String erpCode;

    @ApiModelProperty(value = "物料描述/角色")
    private String materielDescr;

    @ApiModelProperty(value = "下达数量/已填报待审批的工时（h）")
    private BigDecimal orderNum;

    @ApiModelProperty(value = "入库数量/标准费率（d）")
    private BigDecimal storageCount;

    @ApiModelProperty(value = "取消数量")
    private BigDecimal cancelNum;

    @ApiModelProperty(value = "折后金额(不含税)")
    private BigDecimal discountPrice;

    @ApiModelProperty(value = "总价（不含税）")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "累计进度执行金额（不含税）")
    private BigDecimal budgetExecuteAmountTotal;

    @ApiModelProperty(value = "已对账工时金额（不含税）")
    private BigDecimal billMhAmount;

    @ApiModelProperty(value = "已对账费用（不含税）")
    private BigDecimal billCostAmount;

    @ApiModelProperty(value = "在途成本")
    private BigDecimal onTheWayCost;

    @ApiModelProperty(value = "采购订单创建时间/采购合同创建时间/工时创建时间/费用入账日期")
    private Date dataTime;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Date getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(Date executeTime) {
        this.executeTime = executeTime;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num == null ? null : num.trim();
    }

    public String getPurchaseContractName() {
        return purchaseContractName;
    }

    public void setPurchaseContractName(String purchaseContractName) {
        this.purchaseContractName = purchaseContractName == null ? null : purchaseContractName.trim();
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName == null ? null : vendorName.trim();
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode == null ? null : pamCode.trim();
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode == null ? null : erpCode.trim();
    }

    public String getMaterielDescr() {
        return materielDescr;
    }

    public void setMaterielDescr(String materielDescr) {
        this.materielDescr = materielDescr == null ? null : materielDescr.trim();
    }

    public BigDecimal getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(BigDecimal orderNum) {
        this.orderNum = orderNum;
    }

    public BigDecimal getStorageCount() {
        return storageCount;
    }

    public void setStorageCount(BigDecimal storageCount) {
        this.storageCount = storageCount;
    }

    public BigDecimal getCancelNum() {
        return cancelNum;
    }

    public void setCancelNum(BigDecimal cancelNum) {
        this.cancelNum = cancelNum;
    }

    public BigDecimal getDiscountPrice() {
        return discountPrice;
    }

    public void setDiscountPrice(BigDecimal discountPrice) {
        this.discountPrice = discountPrice;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getBudgetExecuteAmountTotal() {
        return budgetExecuteAmountTotal;
    }

    public void setBudgetExecuteAmountTotal(BigDecimal budgetExecuteAmountTotal) {
        this.budgetExecuteAmountTotal = budgetExecuteAmountTotal;
    }

    public BigDecimal getBillMhAmount() {
        return billMhAmount;
    }

    public void setBillMhAmount(BigDecimal billMhAmount) {
        this.billMhAmount = billMhAmount;
    }

    public BigDecimal getBillCostAmount() {
        return billCostAmount;
    }

    public void setBillCostAmount(BigDecimal billCostAmount) {
        this.billCostAmount = billCostAmount;
    }

    public BigDecimal getOnTheWayCost() {
        return onTheWayCost;
    }

    public void setOnTheWayCost(BigDecimal onTheWayCost) {
        this.onTheWayCost = onTheWayCost;
    }

    public Date getDataTime() {
        return dataTime;
    }

    public void setDataTime(Date dataTime) {
        this.dataTime = dataTime;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", executeTime=").append(executeTime);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", type=").append(type);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", num=").append(num);
        sb.append(", purchaseContractName=").append(purchaseContractName);
        sb.append(", vendorName=").append(vendorName);
        sb.append(", pamCode=").append(pamCode);
        sb.append(", erpCode=").append(erpCode);
        sb.append(", materielDescr=").append(materielDescr);
        sb.append(", orderNum=").append(orderNum);
        sb.append(", storageCount=").append(storageCount);
        sb.append(", cancelNum=").append(cancelNum);
        sb.append(", discountPrice=").append(discountPrice);
        sb.append(", totalPrice=").append(totalPrice);
        sb.append(", budgetExecuteAmountTotal=").append(budgetExecuteAmountTotal);
        sb.append(", billMhAmount=").append(billMhAmount);
        sb.append(", billCostAmount=").append(billCostAmount);
        sb.append(", onTheWayCost=").append(onTheWayCost);
        sb.append(", dataTime=").append(dataTime);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}