package com.midea.pam.common.statistics.excelVo;


import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022-11-25
 * @description 已发生成本-点工采购合同已对账金额
 */
@Getter
@Setter
public class HroPurchaseContractCompleteExcelVO {

    @Excel(name = "序号", width = 5)
    private Integer number;

    @Excel(name = "WBS", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项", width = 15)
    private String activityCode;

    @Excel(name = "采购合同编号", width = 25)
    private String purchaseContractCode;

    @Excel(name = "角色", width = 25)
    private String roleName;

    @Excel(name = "已对账工时金额（不含税）", width = 15)
    private BigDecimal billMhAmount;

    @Excel(name = "已对账费用（含税）", width = 15)
    private BigDecimal billCostAmount;

    @Excel(name = "点工采购合同已对账金额", width = 15)
    private BigDecimal surplusAmount;

}
