package com.midea.pam.common.statistics.excelVo;


import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-7-25
 * @description 需求预算-物料外包（整包）需求预算
 */
@Getter
@Setter
public class RequirementBudgetOutsourceExcelVO {

    @Excel(name = "序号", width = 5)
    private Integer number;

    @Excel(name = "WBS", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项", width = 15)
    private String activityCode;

    @Excel(name = "需求发布单据编号", width = 25)
    private String requirementCode;

    @Excel(name = "需求发布单据情况", width = 15)
    private String receiptsType;

    @Excel(name = "需求预算", width = 15)
    private BigDecimal budgetOccupiedAmount;

    @Excel(name = "需求单据中已下采购合同金额", width = 15)
    private BigDecimal downAmount;

    @Excel(name = "需求预算剩余金额", width = 15)
    private BigDecimal remainingCostAmount;

    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date dataTime;
}
