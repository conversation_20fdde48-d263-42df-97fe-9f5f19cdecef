package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WorkingHourSubmitErrorTemporaryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WorkingHourSubmitErrorTemporaryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdIsNull() {
            addCriterion("working_hour_id is null");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdIsNotNull() {
            addCriterion("working_hour_id is not null");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdEqualTo(Long value) {
            addCriterion("working_hour_id =", value, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdNotEqualTo(Long value) {
            addCriterion("working_hour_id <>", value, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdGreaterThan(Long value) {
            addCriterion("working_hour_id >", value, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdGreaterThanOrEqualTo(Long value) {
            addCriterion("working_hour_id >=", value, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdLessThan(Long value) {
            addCriterion("working_hour_id <", value, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdLessThanOrEqualTo(Long value) {
            addCriterion("working_hour_id <=", value, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdIn(List<Long> values) {
            addCriterion("working_hour_id in", values, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdNotIn(List<Long> values) {
            addCriterion("working_hour_id not in", values, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdBetween(Long value1, Long value2) {
            addCriterion("working_hour_id between", value1, value2, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdNotBetween(Long value1, Long value2) {
            addCriterion("working_hour_id not between", value1, value2, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andUserMipIsNull() {
            addCriterion("user_mip is null");
            return (Criteria) this;
        }

        public Criteria andUserMipIsNotNull() {
            addCriterion("user_mip is not null");
            return (Criteria) this;
        }

        public Criteria andUserMipEqualTo(String value) {
            addCriterion("user_mip =", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipNotEqualTo(String value) {
            addCriterion("user_mip <>", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipGreaterThan(String value) {
            addCriterion("user_mip >", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipGreaterThanOrEqualTo(String value) {
            addCriterion("user_mip >=", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipLessThan(String value) {
            addCriterion("user_mip <", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipLessThanOrEqualTo(String value) {
            addCriterion("user_mip <=", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipLike(String value) {
            addCriterion("user_mip like", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipNotLike(String value) {
            addCriterion("user_mip not like", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipIn(List<String> values) {
            addCriterion("user_mip in", values, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipNotIn(List<String> values) {
            addCriterion("user_mip not in", values, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipBetween(String value1, String value2) {
            addCriterion("user_mip between", value1, value2, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipNotBetween(String value1, String value2) {
            addCriterion("user_mip not between", value1, value2, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNull() {
            addCriterion("user_name is null");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNotNull() {
            addCriterion("user_name is not null");
            return (Criteria) this;
        }

        public Criteria andUserNameEqualTo(String value) {
            addCriterion("user_name =", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotEqualTo(String value) {
            addCriterion("user_name <>", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThan(String value) {
            addCriterion("user_name >", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("user_name >=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThan(String value) {
            addCriterion("user_name <", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThanOrEqualTo(String value) {
            addCriterion("user_name <=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLike(String value) {
            addCriterion("user_name like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotLike(String value) {
            addCriterion("user_name not like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameIn(List<String> values) {
            addCriterion("user_name in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotIn(List<String> values) {
            addCriterion("user_name not in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameBetween(String value1, String value2) {
            addCriterion("user_name between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotBetween(String value1, String value2) {
            addCriterion("user_name not between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNull() {
            addCriterion("apply_date is null");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNotNull() {
            addCriterion("apply_date is not null");
            return (Criteria) this;
        }

        public Criteria andApplyDateEqualTo(Date value) {
            addCriterion("apply_date =", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotEqualTo(Date value) {
            addCriterion("apply_date <>", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThan(Date value) {
            addCriterion("apply_date >", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThanOrEqualTo(Date value) {
            addCriterion("apply_date >=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThan(Date value) {
            addCriterion("apply_date <", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThanOrEqualTo(Date value) {
            addCriterion("apply_date <=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateIn(List<Date> values) {
            addCriterion("apply_date in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotIn(List<Date> values) {
            addCriterion("apply_date not in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateBetween(Date value1, Date value2) {
            addCriterion("apply_date between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotBetween(Date value1, Date value2) {
            addCriterion("apply_date not between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursIsNull() {
            addCriterion("apply_working_hours is null");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursIsNotNull() {
            addCriterion("apply_working_hours is not null");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursEqualTo(BigDecimal value) {
            addCriterion("apply_working_hours =", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursNotEqualTo(BigDecimal value) {
            addCriterion("apply_working_hours <>", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursGreaterThan(BigDecimal value) {
            addCriterion("apply_working_hours >", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_working_hours >=", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursLessThan(BigDecimal value) {
            addCriterion("apply_working_hours <", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_working_hours <=", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursIn(List<BigDecimal> values) {
            addCriterion("apply_working_hours in", values, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursNotIn(List<BigDecimal> values) {
            addCriterion("apply_working_hours not in", values, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_working_hours between", value1, value2, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_working_hours not between", value1, value2, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdIsNull() {
            addCriterion("biz_unit_id is null");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdIsNotNull() {
            addCriterion("biz_unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdEqualTo(Long value) {
            addCriterion("biz_unit_id =", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdNotEqualTo(Long value) {
            addCriterion("biz_unit_id <>", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdGreaterThan(Long value) {
            addCriterion("biz_unit_id >", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("biz_unit_id >=", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdLessThan(Long value) {
            addCriterion("biz_unit_id <", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("biz_unit_id <=", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdIn(List<Long> values) {
            addCriterion("biz_unit_id in", values, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdNotIn(List<Long> values) {
            addCriterion("biz_unit_id not in", values, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdBetween(Long value1, Long value2) {
            addCriterion("biz_unit_id between", value1, value2, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("biz_unit_id not between", value1, value2, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOperatingUnitNameIsNull() {
            addCriterion("operating_unit_name is null");
            return (Criteria) this;
        }

        public Criteria andOperatingUnitNameIsNotNull() {
            addCriterion("operating_unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andOperatingUnitNameEqualTo(String value) {
            addCriterion("operating_unit_name =", value, "operatingUnitName");
            return (Criteria) this;
        }

        public Criteria andOperatingUnitNameNotEqualTo(String value) {
            addCriterion("operating_unit_name <>", value, "operatingUnitName");
            return (Criteria) this;
        }

        public Criteria andOperatingUnitNameGreaterThan(String value) {
            addCriterion("operating_unit_name >", value, "operatingUnitName");
            return (Criteria) this;
        }

        public Criteria andOperatingUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("operating_unit_name >=", value, "operatingUnitName");
            return (Criteria) this;
        }

        public Criteria andOperatingUnitNameLessThan(String value) {
            addCriterion("operating_unit_name <", value, "operatingUnitName");
            return (Criteria) this;
        }

        public Criteria andOperatingUnitNameLessThanOrEqualTo(String value) {
            addCriterion("operating_unit_name <=", value, "operatingUnitName");
            return (Criteria) this;
        }

        public Criteria andOperatingUnitNameLike(String value) {
            addCriterion("operating_unit_name like", value, "operatingUnitName");
            return (Criteria) this;
        }

        public Criteria andOperatingUnitNameNotLike(String value) {
            addCriterion("operating_unit_name not like", value, "operatingUnitName");
            return (Criteria) this;
        }

        public Criteria andOperatingUnitNameIn(List<String> values) {
            addCriterion("operating_unit_name in", values, "operatingUnitName");
            return (Criteria) this;
        }

        public Criteria andOperatingUnitNameNotIn(List<String> values) {
            addCriterion("operating_unit_name not in", values, "operatingUnitName");
            return (Criteria) this;
        }

        public Criteria andOperatingUnitNameBetween(String value1, String value2) {
            addCriterion("operating_unit_name between", value1, value2, "operatingUnitName");
            return (Criteria) this;
        }

        public Criteria andOperatingUnitNameNotBetween(String value1, String value2) {
            addCriterion("operating_unit_name not between", value1, value2, "operatingUnitName");
            return (Criteria) this;
        }

        public Criteria andErpMessagesIsNull() {
            addCriterion("erp_messages is null");
            return (Criteria) this;
        }

        public Criteria andErpMessagesIsNotNull() {
            addCriterion("erp_messages is not null");
            return (Criteria) this;
        }

        public Criteria andErpMessagesEqualTo(String value) {
            addCriterion("erp_messages =", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesNotEqualTo(String value) {
            addCriterion("erp_messages <>", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesGreaterThan(String value) {
            addCriterion("erp_messages >", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesGreaterThanOrEqualTo(String value) {
            addCriterion("erp_messages >=", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesLessThan(String value) {
            addCriterion("erp_messages <", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesLessThanOrEqualTo(String value) {
            addCriterion("erp_messages <=", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesLike(String value) {
            addCriterion("erp_messages like", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesNotLike(String value) {
            addCriterion("erp_messages not like", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesIn(List<String> values) {
            addCriterion("erp_messages in", values, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesNotIn(List<String> values) {
            addCriterion("erp_messages not in", values, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesBetween(String value1, String value2) {
            addCriterion("erp_messages between", value1, value2, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesNotBetween(String value1, String value2) {
            addCriterion("erp_messages not between", value1, value2, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagIsNull() {
            addCriterion("invoice_apply_flag is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagIsNotNull() {
            addCriterion("invoice_apply_flag is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagEqualTo(Integer value) {
            addCriterion("invoice_apply_flag =", value, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagNotEqualTo(Integer value) {
            addCriterion("invoice_apply_flag <>", value, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagGreaterThan(Integer value) {
            addCriterion("invoice_apply_flag >", value, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("invoice_apply_flag >=", value, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagLessThan(Integer value) {
            addCriterion("invoice_apply_flag <", value, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagLessThanOrEqualTo(Integer value) {
            addCriterion("invoice_apply_flag <=", value, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagIn(List<Integer> values) {
            addCriterion("invoice_apply_flag in", values, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagNotIn(List<Integer> values) {
            addCriterion("invoice_apply_flag not in", values, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagBetween(Integer value1, Integer value2) {
            addCriterion("invoice_apply_flag between", value1, value2, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("invoice_apply_flag not between", value1, value2, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagIsNull() {
            addCriterion("cost_collection_flag is null");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagIsNotNull() {
            addCriterion("cost_collection_flag is not null");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagEqualTo(Integer value) {
            addCriterion("cost_collection_flag =", value, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagNotEqualTo(Integer value) {
            addCriterion("cost_collection_flag <>", value, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagGreaterThan(Integer value) {
            addCriterion("cost_collection_flag >", value, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("cost_collection_flag >=", value, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagLessThan(Integer value) {
            addCriterion("cost_collection_flag <", value, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagLessThanOrEqualTo(Integer value) {
            addCriterion("cost_collection_flag <=", value, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagIn(List<Integer> values) {
            addCriterion("cost_collection_flag in", values, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagNotIn(List<Integer> values) {
            addCriterion("cost_collection_flag not in", values, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagBetween(Integer value1, Integer value2) {
            addCriterion("cost_collection_flag between", value1, value2, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("cost_collection_flag not between", value1, value2, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andLeverIsNull() {
            addCriterion("lever is null");
            return (Criteria) this;
        }

        public Criteria andLeverIsNotNull() {
            addCriterion("lever is not null");
            return (Criteria) this;
        }

        public Criteria andLeverEqualTo(String value) {
            addCriterion("lever =", value, "lever");
            return (Criteria) this;
        }

        public Criteria andLeverNotEqualTo(String value) {
            addCriterion("lever <>", value, "lever");
            return (Criteria) this;
        }

        public Criteria andLeverGreaterThan(String value) {
            addCriterion("lever >", value, "lever");
            return (Criteria) this;
        }

        public Criteria andLeverGreaterThanOrEqualTo(String value) {
            addCriterion("lever >=", value, "lever");
            return (Criteria) this;
        }

        public Criteria andLeverLessThan(String value) {
            addCriterion("lever <", value, "lever");
            return (Criteria) this;
        }

        public Criteria andLeverLessThanOrEqualTo(String value) {
            addCriterion("lever <=", value, "lever");
            return (Criteria) this;
        }

        public Criteria andLeverLike(String value) {
            addCriterion("lever like", value, "lever");
            return (Criteria) this;
        }

        public Criteria andLeverNotLike(String value) {
            addCriterion("lever not like", value, "lever");
            return (Criteria) this;
        }

        public Criteria andLeverIn(List<String> values) {
            addCriterion("lever in", values, "lever");
            return (Criteria) this;
        }

        public Criteria andLeverNotIn(List<String> values) {
            addCriterion("lever not in", values, "lever");
            return (Criteria) this;
        }

        public Criteria andLeverBetween(String value1, String value2) {
            addCriterion("lever between", value1, value2, "lever");
            return (Criteria) this;
        }

        public Criteria andLeverNotBetween(String value1, String value2) {
            addCriterion("lever not between", value1, value2, "lever");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeIsNull() {
            addCriterion("labor_cost_type is null");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeIsNotNull() {
            addCriterion("labor_cost_type is not null");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeEqualTo(String value) {
            addCriterion("labor_cost_type =", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeNotEqualTo(String value) {
            addCriterion("labor_cost_type <>", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeGreaterThan(String value) {
            addCriterion("labor_cost_type >", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeGreaterThanOrEqualTo(String value) {
            addCriterion("labor_cost_type >=", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeLessThan(String value) {
            addCriterion("labor_cost_type <", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeLessThanOrEqualTo(String value) {
            addCriterion("labor_cost_type <=", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeLike(String value) {
            addCriterion("labor_cost_type like", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeNotLike(String value) {
            addCriterion("labor_cost_type not like", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeIn(List<String> values) {
            addCriterion("labor_cost_type in", values, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeNotIn(List<String> values) {
            addCriterion("labor_cost_type not in", values, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeBetween(String value1, String value2) {
            addCriterion("labor_cost_type between", value1, value2, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeNotBetween(String value1, String value2) {
            addCriterion("labor_cost_type not between", value1, value2, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyIsNull() {
            addCriterion("actual_cost_money is null");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyIsNotNull() {
            addCriterion("actual_cost_money is not null");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyEqualTo(BigDecimal value) {
            addCriterion("actual_cost_money =", value, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyNotEqualTo(BigDecimal value) {
            addCriterion("actual_cost_money <>", value, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyGreaterThan(BigDecimal value) {
            addCriterion("actual_cost_money >", value, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_cost_money >=", value, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyLessThan(BigDecimal value) {
            addCriterion("actual_cost_money <", value, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_cost_money <=", value, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyIn(List<BigDecimal> values) {
            addCriterion("actual_cost_money in", values, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyNotIn(List<BigDecimal> values) {
            addCriterion("actual_cost_money not in", values, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_cost_money between", value1, value2, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_cost_money not between", value1, value2, "actualCostMoney");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}