package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "")
public class RequirementBudget extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "详细设计单据id")
    private Long projectWbsReceiptsId;

    @ApiModelProperty(value = "详细设计单据编号")
    private String requirementCode;

    @ApiModelProperty(value = "未下达量")
    private Integer unreleasedQuantity;

    @ApiModelProperty(value = "需求发布单据情况")
    private String receiptsType;

    @ApiModelProperty(value = "需求预算")
    private BigDecimal budgetOccupiedAmount;

    @ApiModelProperty(value = "已下金额")
    private BigDecimal downAmount;

    @ApiModelProperty(value = "剩余预算金额/占用金额")
    private BigDecimal remainingCostAmount;

    @ApiModelProperty(value = "预算类型 0-物料采购需求预算、1-物料外包（整包）需求预算、2-审批中的需求发布单据")
    private Integer budgetType;

    @ApiModelProperty(value = "wbs编码")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "业务生成时间")
    private Date dataTime;

    private static final long serialVersionUID = 1L;

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public Long getProjectWbsReceiptsId() {
        return projectWbsReceiptsId;
    }

    public void setProjectWbsReceiptsId(Long projectWbsReceiptsId) {
        this.projectWbsReceiptsId = projectWbsReceiptsId;
    }

    public String getRequirementCode() {
        return requirementCode;
    }

    public void setRequirementCode(String requirementCode) {
        this.requirementCode = requirementCode == null ? null : requirementCode.trim();
    }

    public Integer getUnreleasedQuantity() {
        return unreleasedQuantity;
    }

    public void setUnreleasedQuantity(Integer unreleasedQuantity) {
        this.unreleasedQuantity = unreleasedQuantity;
    }

    public String getReceiptsType() {
        return receiptsType;
    }

    public void setReceiptsType(String receiptsType) {
        this.receiptsType = receiptsType == null ? null : receiptsType.trim();
    }

    public BigDecimal getBudgetOccupiedAmount() {
        return budgetOccupiedAmount;
    }

    public void setBudgetOccupiedAmount(BigDecimal budgetOccupiedAmount) {
        this.budgetOccupiedAmount = budgetOccupiedAmount;
    }

    public BigDecimal getDownAmount() {
        return downAmount;
    }

    public void setDownAmount(BigDecimal downAmount) {
        this.downAmount = downAmount;
    }

    public BigDecimal getRemainingCostAmount() {
        return remainingCostAmount;
    }

    public void setRemainingCostAmount(BigDecimal remainingCostAmount) {
        this.remainingCostAmount = remainingCostAmount;
    }

    public Integer getBudgetType() {
        return budgetType;
    }

    public void setBudgetType(Integer budgetType) {
        this.budgetType = budgetType;
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Date getDataTime() {
        return dataTime;
    }

    public void setDataTime(Date dataTime) {
        this.dataTime = dataTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectWbsReceiptsId=").append(projectWbsReceiptsId);
        sb.append(", requirementCode=").append(requirementCode);
        sb.append(", unreleasedQuantity=").append(unreleasedQuantity);
        sb.append(", receiptsType=").append(receiptsType);
        sb.append(", budgetOccupiedAmount=").append(budgetOccupiedAmount);
        sb.append(", downAmount=").append(downAmount);
        sb.append(", remainingCostAmount=").append(remainingCostAmount);
        sb.append(", budgetType=").append(budgetType);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", dataTime=").append(dataTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}