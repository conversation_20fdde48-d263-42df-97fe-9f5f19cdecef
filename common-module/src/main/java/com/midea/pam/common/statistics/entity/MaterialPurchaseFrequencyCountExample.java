package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MaterialPurchaseFrequencyCountExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public MaterialPurchaseFrequencyCountExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andPurchaseNumIsNull() {
            addCriterion("purchase_num is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseNumIsNotNull() {
            addCriterion("purchase_num is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseNumEqualTo(Long value) {
            addCriterion("purchase_num =", value, "purchaseNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseNumNotEqualTo(Long value) {
            addCriterion("purchase_num <>", value, "purchaseNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseNumGreaterThan(Long value) {
            addCriterion("purchase_num >", value, "purchaseNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseNumGreaterThanOrEqualTo(Long value) {
            addCriterion("purchase_num >=", value, "purchaseNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseNumLessThan(Long value) {
            addCriterion("purchase_num <", value, "purchaseNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseNumLessThanOrEqualTo(Long value) {
            addCriterion("purchase_num <=", value, "purchaseNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseNumIn(List<Long> values) {
            addCriterion("purchase_num in", values, "purchaseNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseNumNotIn(List<Long> values) {
            addCriterion("purchase_num not in", values, "purchaseNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseNumBetween(Long value1, Long value2) {
            addCriterion("purchase_num between", value1, value2, "purchaseNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseNumNotBetween(Long value1, Long value2) {
            addCriterion("purchase_num not between", value1, value2, "purchaseNum");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNull() {
            addCriterion("pam_code is null");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNotNull() {
            addCriterion("pam_code is not null");
            return (Criteria) this;
        }

        public Criteria andPamCodeEqualTo(String value) {
            addCriterion("pam_code =", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotEqualTo(String value) {
            addCriterion("pam_code <>", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThan(String value) {
            addCriterion("pam_code >", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("pam_code >=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThan(String value) {
            addCriterion("pam_code <", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThanOrEqualTo(String value) {
            addCriterion("pam_code <=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLike(String value) {
            addCriterion("pam_code like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotLike(String value) {
            addCriterion("pam_code not like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeIn(List<String> values) {
            addCriterion("pam_code in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotIn(List<String> values) {
            addCriterion("pam_code not in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeBetween(String value1, String value2) {
            addCriterion("pam_code between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotBetween(String value1, String value2) {
            addCriterion("pam_code not between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNull() {
            addCriterion("erp_code is null");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNotNull() {
            addCriterion("erp_code is not null");
            return (Criteria) this;
        }

        public Criteria andErpCodeEqualTo(String value) {
            addCriterion("erp_code =", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotEqualTo(String value) {
            addCriterion("erp_code <>", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThan(String value) {
            addCriterion("erp_code >", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("erp_code >=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThan(String value) {
            addCriterion("erp_code <", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThanOrEqualTo(String value) {
            addCriterion("erp_code <=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLike(String value) {
            addCriterion("erp_code like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotLike(String value) {
            addCriterion("erp_code not like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIn(List<String> values) {
            addCriterion("erp_code in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotIn(List<String> values) {
            addCriterion("erp_code not in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeBetween(String value1, String value2) {
            addCriterion("erp_code between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotBetween(String value1, String value2) {
            addCriterion("erp_code not between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andItemInfoIsNull() {
            addCriterion("item_info is null");
            return (Criteria) this;
        }

        public Criteria andItemInfoIsNotNull() {
            addCriterion("item_info is not null");
            return (Criteria) this;
        }

        public Criteria andItemInfoEqualTo(String value) {
            addCriterion("item_info =", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoNotEqualTo(String value) {
            addCriterion("item_info <>", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoGreaterThan(String value) {
            addCriterion("item_info >", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoGreaterThanOrEqualTo(String value) {
            addCriterion("item_info >=", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoLessThan(String value) {
            addCriterion("item_info <", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoLessThanOrEqualTo(String value) {
            addCriterion("item_info <=", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoLike(String value) {
            addCriterion("item_info like", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoNotLike(String value) {
            addCriterion("item_info not like", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoIn(List<String> values) {
            addCriterion("item_info in", values, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoNotIn(List<String> values) {
            addCriterion("item_info not in", values, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoBetween(String value1, String value2) {
            addCriterion("item_info between", value1, value2, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoNotBetween(String value1, String value2) {
            addCriterion("item_info not between", value1, value2, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andItemStatusIsNull() {
            addCriterion("item_status is null");
            return (Criteria) this;
        }

        public Criteria andItemStatusIsNotNull() {
            addCriterion("item_status is not null");
            return (Criteria) this;
        }

        public Criteria andItemStatusEqualTo(String value) {
            addCriterion("item_status =", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusNotEqualTo(String value) {
            addCriterion("item_status <>", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusGreaterThan(String value) {
            addCriterion("item_status >", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusGreaterThanOrEqualTo(String value) {
            addCriterion("item_status >=", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusLessThan(String value) {
            addCriterion("item_status <", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusLessThanOrEqualTo(String value) {
            addCriterion("item_status <=", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusLike(String value) {
            addCriterion("item_status like", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusNotLike(String value) {
            addCriterion("item_status not like", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusIn(List<String> values) {
            addCriterion("item_status in", values, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusNotIn(List<String> values) {
            addCriterion("item_status not in", values, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusBetween(String value1, String value2) {
            addCriterion("item_status between", value1, value2, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusNotBetween(String value1, String value2) {
            addCriterion("item_status not between", value1, value2, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusNameIsNull() {
            addCriterion("item_status_name is null");
            return (Criteria) this;
        }

        public Criteria andItemStatusNameIsNotNull() {
            addCriterion("item_status_name is not null");
            return (Criteria) this;
        }

        public Criteria andItemStatusNameEqualTo(String value) {
            addCriterion("item_status_name =", value, "itemStatusName");
            return (Criteria) this;
        }

        public Criteria andItemStatusNameNotEqualTo(String value) {
            addCriterion("item_status_name <>", value, "itemStatusName");
            return (Criteria) this;
        }

        public Criteria andItemStatusNameGreaterThan(String value) {
            addCriterion("item_status_name >", value, "itemStatusName");
            return (Criteria) this;
        }

        public Criteria andItemStatusNameGreaterThanOrEqualTo(String value) {
            addCriterion("item_status_name >=", value, "itemStatusName");
            return (Criteria) this;
        }

        public Criteria andItemStatusNameLessThan(String value) {
            addCriterion("item_status_name <", value, "itemStatusName");
            return (Criteria) this;
        }

        public Criteria andItemStatusNameLessThanOrEqualTo(String value) {
            addCriterion("item_status_name <=", value, "itemStatusName");
            return (Criteria) this;
        }

        public Criteria andItemStatusNameLike(String value) {
            addCriterion("item_status_name like", value, "itemStatusName");
            return (Criteria) this;
        }

        public Criteria andItemStatusNameNotLike(String value) {
            addCriterion("item_status_name not like", value, "itemStatusName");
            return (Criteria) this;
        }

        public Criteria andItemStatusNameIn(List<String> values) {
            addCriterion("item_status_name in", values, "itemStatusName");
            return (Criteria) this;
        }

        public Criteria andItemStatusNameNotIn(List<String> values) {
            addCriterion("item_status_name not in", values, "itemStatusName");
            return (Criteria) this;
        }

        public Criteria andItemStatusNameBetween(String value1, String value2) {
            addCriterion("item_status_name between", value1, value2, "itemStatusName");
            return (Criteria) this;
        }

        public Criteria andItemStatusNameNotBetween(String value1, String value2) {
            addCriterion("item_status_name not between", value1, value2, "itemStatusName");
            return (Criteria) this;
        }

        public Criteria andDelistFlagIsNull() {
            addCriterion("delist_flag is null");
            return (Criteria) this;
        }

        public Criteria andDelistFlagIsNotNull() {
            addCriterion("delist_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDelistFlagEqualTo(Byte value) {
            addCriterion("delist_flag =", value, "delistFlag");
            return (Criteria) this;
        }

        public Criteria andDelistFlagNotEqualTo(Byte value) {
            addCriterion("delist_flag <>", value, "delistFlag");
            return (Criteria) this;
        }

        public Criteria andDelistFlagGreaterThan(Byte value) {
            addCriterion("delist_flag >", value, "delistFlag");
            return (Criteria) this;
        }

        public Criteria andDelistFlagGreaterThanOrEqualTo(Byte value) {
            addCriterion("delist_flag >=", value, "delistFlag");
            return (Criteria) this;
        }

        public Criteria andDelistFlagLessThan(Byte value) {
            addCriterion("delist_flag <", value, "delistFlag");
            return (Criteria) this;
        }

        public Criteria andDelistFlagLessThanOrEqualTo(Byte value) {
            addCriterion("delist_flag <=", value, "delistFlag");
            return (Criteria) this;
        }

        public Criteria andDelistFlagIn(List<Byte> values) {
            addCriterion("delist_flag in", values, "delistFlag");
            return (Criteria) this;
        }

        public Criteria andDelistFlagNotIn(List<Byte> values) {
            addCriterion("delist_flag not in", values, "delistFlag");
            return (Criteria) this;
        }

        public Criteria andDelistFlagBetween(Byte value1, Byte value2) {
            addCriterion("delist_flag between", value1, value2, "delistFlag");
            return (Criteria) this;
        }

        public Criteria andDelistFlagNotBetween(Byte value1, Byte value2) {
            addCriterion("delist_flag not between", value1, value2, "delistFlag");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationIsNull() {
            addCriterion("material_classification is null");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationIsNotNull() {
            addCriterion("material_classification is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationEqualTo(String value) {
            addCriterion("material_classification =", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotEqualTo(String value) {
            addCriterion("material_classification <>", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationGreaterThan(String value) {
            addCriterion("material_classification >", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationGreaterThanOrEqualTo(String value) {
            addCriterion("material_classification >=", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationLessThan(String value) {
            addCriterion("material_classification <", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationLessThanOrEqualTo(String value) {
            addCriterion("material_classification <=", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationLike(String value) {
            addCriterion("material_classification like", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotLike(String value) {
            addCriterion("material_classification not like", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationIn(List<String> values) {
            addCriterion("material_classification in", values, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotIn(List<String> values) {
            addCriterion("material_classification not in", values, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationBetween(String value1, String value2) {
            addCriterion("material_classification between", value1, value2, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotBetween(String value1, String value2) {
            addCriterion("material_classification not between", value1, value2, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassIsNull() {
            addCriterion("coding_middleclass is null");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassIsNotNull() {
            addCriterion("coding_middleclass is not null");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassEqualTo(String value) {
            addCriterion("coding_middleclass =", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassNotEqualTo(String value) {
            addCriterion("coding_middleclass <>", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassGreaterThan(String value) {
            addCriterion("coding_middleclass >", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassGreaterThanOrEqualTo(String value) {
            addCriterion("coding_middleclass >=", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassLessThan(String value) {
            addCriterion("coding_middleclass <", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassLessThanOrEqualTo(String value) {
            addCriterion("coding_middleclass <=", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassLike(String value) {
            addCriterion("coding_middleclass like", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassNotLike(String value) {
            addCriterion("coding_middleclass not like", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassIn(List<String> values) {
            addCriterion("coding_middleclass in", values, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassNotIn(List<String> values) {
            addCriterion("coding_middleclass not in", values, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassBetween(String value1, String value2) {
            addCriterion("coding_middleclass between", value1, value2, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassNotBetween(String value1, String value2) {
            addCriterion("coding_middleclass not between", value1, value2, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIsNull() {
            addCriterion("material_type is null");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIsNotNull() {
            addCriterion("material_type is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeEqualTo(String value) {
            addCriterion("material_type =", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotEqualTo(String value) {
            addCriterion("material_type <>", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeGreaterThan(String value) {
            addCriterion("material_type >", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeGreaterThanOrEqualTo(String value) {
            addCriterion("material_type >=", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLessThan(String value) {
            addCriterion("material_type <", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLessThanOrEqualTo(String value) {
            addCriterion("material_type <=", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLike(String value) {
            addCriterion("material_type like", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotLike(String value) {
            addCriterion("material_type not like", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIn(List<String> values) {
            addCriterion("material_type in", values, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotIn(List<String> values) {
            addCriterion("material_type not in", values, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeBetween(String value1, String value2) {
            addCriterion("material_type between", value1, value2, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotBetween(String value1, String value2) {
            addCriterion("material_type not between", value1, value2, "materialType");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeIsNull() {
            addCriterion("machining_part_type is null");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeIsNotNull() {
            addCriterion("machining_part_type is not null");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeEqualTo(String value) {
            addCriterion("machining_part_type =", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNotEqualTo(String value) {
            addCriterion("machining_part_type <>", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeGreaterThan(String value) {
            addCriterion("machining_part_type >", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeGreaterThanOrEqualTo(String value) {
            addCriterion("machining_part_type >=", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeLessThan(String value) {
            addCriterion("machining_part_type <", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeLessThanOrEqualTo(String value) {
            addCriterion("machining_part_type <=", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeLike(String value) {
            addCriterion("machining_part_type like", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNotLike(String value) {
            addCriterion("machining_part_type not like", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeIn(List<String> values) {
            addCriterion("machining_part_type in", values, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNotIn(List<String> values) {
            addCriterion("machining_part_type not in", values, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeBetween(String value1, String value2) {
            addCriterion("machining_part_type between", value1, value2, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNotBetween(String value1, String value2) {
            addCriterion("machining_part_type not between", value1, value2, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMaterialIsNull() {
            addCriterion("material is null");
            return (Criteria) this;
        }

        public Criteria andMaterialIsNotNull() {
            addCriterion("material is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialEqualTo(String value) {
            addCriterion("material =", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotEqualTo(String value) {
            addCriterion("material <>", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialGreaterThan(String value) {
            addCriterion("material >", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialGreaterThanOrEqualTo(String value) {
            addCriterion("material >=", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLessThan(String value) {
            addCriterion("material <", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLessThanOrEqualTo(String value) {
            addCriterion("material <=", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLike(String value) {
            addCriterion("material like", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotLike(String value) {
            addCriterion("material not like", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialIn(List<String> values) {
            addCriterion("material in", values, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotIn(List<String> values) {
            addCriterion("material not in", values, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialBetween(String value1, String value2) {
            addCriterion("material between", value1, value2, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotBetween(String value1, String value2) {
            addCriterion("material not between", value1, value2, "material");
            return (Criteria) this;
        }

        public Criteria andUnitWeightIsNull() {
            addCriterion("unit_weight is null");
            return (Criteria) this;
        }

        public Criteria andUnitWeightIsNotNull() {
            addCriterion("unit_weight is not null");
            return (Criteria) this;
        }

        public Criteria andUnitWeightEqualTo(BigDecimal value) {
            addCriterion("unit_weight =", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNotEqualTo(BigDecimal value) {
            addCriterion("unit_weight <>", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightGreaterThan(BigDecimal value) {
            addCriterion("unit_weight >", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("unit_weight >=", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightLessThan(BigDecimal value) {
            addCriterion("unit_weight <", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightLessThanOrEqualTo(BigDecimal value) {
            addCriterion("unit_weight <=", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightIn(List<BigDecimal> values) {
            addCriterion("unit_weight in", values, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNotIn(List<BigDecimal> values) {
            addCriterion("unit_weight not in", values, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unit_weight between", value1, value2, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unit_weight not between", value1, value2, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingIsNull() {
            addCriterion("material_processing is null");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingIsNotNull() {
            addCriterion("material_processing is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingEqualTo(String value) {
            addCriterion("material_processing =", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingNotEqualTo(String value) {
            addCriterion("material_processing <>", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingGreaterThan(String value) {
            addCriterion("material_processing >", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingGreaterThanOrEqualTo(String value) {
            addCriterion("material_processing >=", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingLessThan(String value) {
            addCriterion("material_processing <", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingLessThanOrEqualTo(String value) {
            addCriterion("material_processing <=", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingLike(String value) {
            addCriterion("material_processing like", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingNotLike(String value) {
            addCriterion("material_processing not like", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingIn(List<String> values) {
            addCriterion("material_processing in", values, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingNotIn(List<String> values) {
            addCriterion("material_processing not in", values, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingBetween(String value1, String value2) {
            addCriterion("material_processing between", value1, value2, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingNotBetween(String value1, String value2) {
            addCriterion("material_processing not between", value1, value2, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMinimumOrderQuantityIsNull() {
            addCriterion("minimum_order_quantity is null");
            return (Criteria) this;
        }

        public Criteria andMinimumOrderQuantityIsNotNull() {
            addCriterion("minimum_order_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andMinimumOrderQuantityEqualTo(Long value) {
            addCriterion("minimum_order_quantity =", value, "minimumOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andMinimumOrderQuantityNotEqualTo(Long value) {
            addCriterion("minimum_order_quantity <>", value, "minimumOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andMinimumOrderQuantityGreaterThan(Long value) {
            addCriterion("minimum_order_quantity >", value, "minimumOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andMinimumOrderQuantityGreaterThanOrEqualTo(Long value) {
            addCriterion("minimum_order_quantity >=", value, "minimumOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andMinimumOrderQuantityLessThan(Long value) {
            addCriterion("minimum_order_quantity <", value, "minimumOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andMinimumOrderQuantityLessThanOrEqualTo(Long value) {
            addCriterion("minimum_order_quantity <=", value, "minimumOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andMinimumOrderQuantityIn(List<Long> values) {
            addCriterion("minimum_order_quantity in", values, "minimumOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andMinimumOrderQuantityNotIn(List<Long> values) {
            addCriterion("minimum_order_quantity not in", values, "minimumOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andMinimumOrderQuantityBetween(Long value1, Long value2) {
            addCriterion("minimum_order_quantity between", value1, value2, "minimumOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andMinimumOrderQuantityNotBetween(Long value1, Long value2) {
            addCriterion("minimum_order_quantity not between", value1, value2, "minimumOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andRecevingSubinventoryIsNull() {
            addCriterion("receving_subinventory is null");
            return (Criteria) this;
        }

        public Criteria andRecevingSubinventoryIsNotNull() {
            addCriterion("receving_subinventory is not null");
            return (Criteria) this;
        }

        public Criteria andRecevingSubinventoryEqualTo(String value) {
            addCriterion("receving_subinventory =", value, "recevingSubinventory");
            return (Criteria) this;
        }

        public Criteria andRecevingSubinventoryNotEqualTo(String value) {
            addCriterion("receving_subinventory <>", value, "recevingSubinventory");
            return (Criteria) this;
        }

        public Criteria andRecevingSubinventoryGreaterThan(String value) {
            addCriterion("receving_subinventory >", value, "recevingSubinventory");
            return (Criteria) this;
        }

        public Criteria andRecevingSubinventoryGreaterThanOrEqualTo(String value) {
            addCriterion("receving_subinventory >=", value, "recevingSubinventory");
            return (Criteria) this;
        }

        public Criteria andRecevingSubinventoryLessThan(String value) {
            addCriterion("receving_subinventory <", value, "recevingSubinventory");
            return (Criteria) this;
        }

        public Criteria andRecevingSubinventoryLessThanOrEqualTo(String value) {
            addCriterion("receving_subinventory <=", value, "recevingSubinventory");
            return (Criteria) this;
        }

        public Criteria andRecevingSubinventoryLike(String value) {
            addCriterion("receving_subinventory like", value, "recevingSubinventory");
            return (Criteria) this;
        }

        public Criteria andRecevingSubinventoryNotLike(String value) {
            addCriterion("receving_subinventory not like", value, "recevingSubinventory");
            return (Criteria) this;
        }

        public Criteria andRecevingSubinventoryIn(List<String> values) {
            addCriterion("receving_subinventory in", values, "recevingSubinventory");
            return (Criteria) this;
        }

        public Criteria andRecevingSubinventoryNotIn(List<String> values) {
            addCriterion("receving_subinventory not in", values, "recevingSubinventory");
            return (Criteria) this;
        }

        public Criteria andRecevingSubinventoryBetween(String value1, String value2) {
            addCriterion("receving_subinventory between", value1, value2, "recevingSubinventory");
            return (Criteria) this;
        }

        public Criteria andRecevingSubinventoryNotBetween(String value1, String value2) {
            addCriterion("receving_subinventory not between", value1, value2, "recevingSubinventory");
            return (Criteria) this;
        }

        public Criteria andShelvesIsNull() {
            addCriterion("shelves is null");
            return (Criteria) this;
        }

        public Criteria andShelvesIsNotNull() {
            addCriterion("shelves is not null");
            return (Criteria) this;
        }

        public Criteria andShelvesEqualTo(String value) {
            addCriterion("shelves =", value, "shelves");
            return (Criteria) this;
        }

        public Criteria andShelvesNotEqualTo(String value) {
            addCriterion("shelves <>", value, "shelves");
            return (Criteria) this;
        }

        public Criteria andShelvesGreaterThan(String value) {
            addCriterion("shelves >", value, "shelves");
            return (Criteria) this;
        }

        public Criteria andShelvesGreaterThanOrEqualTo(String value) {
            addCriterion("shelves >=", value, "shelves");
            return (Criteria) this;
        }

        public Criteria andShelvesLessThan(String value) {
            addCriterion("shelves <", value, "shelves");
            return (Criteria) this;
        }

        public Criteria andShelvesLessThanOrEqualTo(String value) {
            addCriterion("shelves <=", value, "shelves");
            return (Criteria) this;
        }

        public Criteria andShelvesLike(String value) {
            addCriterion("shelves like", value, "shelves");
            return (Criteria) this;
        }

        public Criteria andShelvesNotLike(String value) {
            addCriterion("shelves not like", value, "shelves");
            return (Criteria) this;
        }

        public Criteria andShelvesIn(List<String> values) {
            addCriterion("shelves in", values, "shelves");
            return (Criteria) this;
        }

        public Criteria andShelvesNotIn(List<String> values) {
            addCriterion("shelves not in", values, "shelves");
            return (Criteria) this;
        }

        public Criteria andShelvesBetween(String value1, String value2) {
            addCriterion("shelves between", value1, value2, "shelves");
            return (Criteria) this;
        }

        public Criteria andShelvesNotBetween(String value1, String value2) {
            addCriterion("shelves not between", value1, value2, "shelves");
            return (Criteria) this;
        }

        public Criteria andSourcerIsNull() {
            addCriterion("sourcer is null");
            return (Criteria) this;
        }

        public Criteria andSourcerIsNotNull() {
            addCriterion("sourcer is not null");
            return (Criteria) this;
        }

        public Criteria andSourcerEqualTo(String value) {
            addCriterion("sourcer =", value, "sourcer");
            return (Criteria) this;
        }

        public Criteria andSourcerNotEqualTo(String value) {
            addCriterion("sourcer <>", value, "sourcer");
            return (Criteria) this;
        }

        public Criteria andSourcerGreaterThan(String value) {
            addCriterion("sourcer >", value, "sourcer");
            return (Criteria) this;
        }

        public Criteria andSourcerGreaterThanOrEqualTo(String value) {
            addCriterion("sourcer >=", value, "sourcer");
            return (Criteria) this;
        }

        public Criteria andSourcerLessThan(String value) {
            addCriterion("sourcer <", value, "sourcer");
            return (Criteria) this;
        }

        public Criteria andSourcerLessThanOrEqualTo(String value) {
            addCriterion("sourcer <=", value, "sourcer");
            return (Criteria) this;
        }

        public Criteria andSourcerLike(String value) {
            addCriterion("sourcer like", value, "sourcer");
            return (Criteria) this;
        }

        public Criteria andSourcerNotLike(String value) {
            addCriterion("sourcer not like", value, "sourcer");
            return (Criteria) this;
        }

        public Criteria andSourcerIn(List<String> values) {
            addCriterion("sourcer in", values, "sourcer");
            return (Criteria) this;
        }

        public Criteria andSourcerNotIn(List<String> values) {
            addCriterion("sourcer not in", values, "sourcer");
            return (Criteria) this;
        }

        public Criteria andSourcerBetween(String value1, String value2) {
            addCriterion("sourcer between", value1, value2, "sourcer");
            return (Criteria) this;
        }

        public Criteria andSourcerNotBetween(String value1, String value2) {
            addCriterion("sourcer not between", value1, value2, "sourcer");
            return (Criteria) this;
        }

        public Criteria andSafetyStockQuantityIsNull() {
            addCriterion("safety_stock_quantity is null");
            return (Criteria) this;
        }

        public Criteria andSafetyStockQuantityIsNotNull() {
            addCriterion("safety_stock_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andSafetyStockQuantityEqualTo(Long value) {
            addCriterion("safety_stock_quantity =", value, "safetyStockQuantity");
            return (Criteria) this;
        }

        public Criteria andSafetyStockQuantityNotEqualTo(Long value) {
            addCriterion("safety_stock_quantity <>", value, "safetyStockQuantity");
            return (Criteria) this;
        }

        public Criteria andSafetyStockQuantityGreaterThan(Long value) {
            addCriterion("safety_stock_quantity >", value, "safetyStockQuantity");
            return (Criteria) this;
        }

        public Criteria andSafetyStockQuantityGreaterThanOrEqualTo(Long value) {
            addCriterion("safety_stock_quantity >=", value, "safetyStockQuantity");
            return (Criteria) this;
        }

        public Criteria andSafetyStockQuantityLessThan(Long value) {
            addCriterion("safety_stock_quantity <", value, "safetyStockQuantity");
            return (Criteria) this;
        }

        public Criteria andSafetyStockQuantityLessThanOrEqualTo(Long value) {
            addCriterion("safety_stock_quantity <=", value, "safetyStockQuantity");
            return (Criteria) this;
        }

        public Criteria andSafetyStockQuantityIn(List<Long> values) {
            addCriterion("safety_stock_quantity in", values, "safetyStockQuantity");
            return (Criteria) this;
        }

        public Criteria andSafetyStockQuantityNotIn(List<Long> values) {
            addCriterion("safety_stock_quantity not in", values, "safetyStockQuantity");
            return (Criteria) this;
        }

        public Criteria andSafetyStockQuantityBetween(Long value1, Long value2) {
            addCriterion("safety_stock_quantity between", value1, value2, "safetyStockQuantity");
            return (Criteria) this;
        }

        public Criteria andSafetyStockQuantityNotBetween(Long value1, Long value2) {
            addCriterion("safety_stock_quantity not between", value1, value2, "safetyStockQuantity");
            return (Criteria) this;
        }

        public Criteria andItemTypeIsNull() {
            addCriterion("item_type is null");
            return (Criteria) this;
        }

        public Criteria andItemTypeIsNotNull() {
            addCriterion("item_type is not null");
            return (Criteria) this;
        }

        public Criteria andItemTypeEqualTo(String value) {
            addCriterion("item_type =", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotEqualTo(String value) {
            addCriterion("item_type <>", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeGreaterThan(String value) {
            addCriterion("item_type >", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeGreaterThanOrEqualTo(String value) {
            addCriterion("item_type >=", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeLessThan(String value) {
            addCriterion("item_type <", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeLessThanOrEqualTo(String value) {
            addCriterion("item_type <=", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeLike(String value) {
            addCriterion("item_type like", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotLike(String value) {
            addCriterion("item_type not like", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeIn(List<String> values) {
            addCriterion("item_type in", values, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotIn(List<String> values) {
            addCriterion("item_type not in", values, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeBetween(String value1, String value2) {
            addCriterion("item_type between", value1, value2, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotBetween(String value1, String value2) {
            addCriterion("item_type not between", value1, value2, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNameIsNull() {
            addCriterion("item_type_name is null");
            return (Criteria) this;
        }

        public Criteria andItemTypeNameIsNotNull() {
            addCriterion("item_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andItemTypeNameEqualTo(String value) {
            addCriterion("item_type_name =", value, "itemTypeName");
            return (Criteria) this;
        }

        public Criteria andItemTypeNameNotEqualTo(String value) {
            addCriterion("item_type_name <>", value, "itemTypeName");
            return (Criteria) this;
        }

        public Criteria andItemTypeNameGreaterThan(String value) {
            addCriterion("item_type_name >", value, "itemTypeName");
            return (Criteria) this;
        }

        public Criteria andItemTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("item_type_name >=", value, "itemTypeName");
            return (Criteria) this;
        }

        public Criteria andItemTypeNameLessThan(String value) {
            addCriterion("item_type_name <", value, "itemTypeName");
            return (Criteria) this;
        }

        public Criteria andItemTypeNameLessThanOrEqualTo(String value) {
            addCriterion("item_type_name <=", value, "itemTypeName");
            return (Criteria) this;
        }

        public Criteria andItemTypeNameLike(String value) {
            addCriterion("item_type_name like", value, "itemTypeName");
            return (Criteria) this;
        }

        public Criteria andItemTypeNameNotLike(String value) {
            addCriterion("item_type_name not like", value, "itemTypeName");
            return (Criteria) this;
        }

        public Criteria andItemTypeNameIn(List<String> values) {
            addCriterion("item_type_name in", values, "itemTypeName");
            return (Criteria) this;
        }

        public Criteria andItemTypeNameNotIn(List<String> values) {
            addCriterion("item_type_name not in", values, "itemTypeName");
            return (Criteria) this;
        }

        public Criteria andItemTypeNameBetween(String value1, String value2) {
            addCriterion("item_type_name between", value1, value2, "itemTypeName");
            return (Criteria) this;
        }

        public Criteria andItemTypeNameNotBetween(String value1, String value2) {
            addCriterion("item_type_name not between", value1, value2, "itemTypeName");
            return (Criteria) this;
        }

        public Criteria andBuyerNumberIsNull() {
            addCriterion("buyer_number is null");
            return (Criteria) this;
        }

        public Criteria andBuyerNumberIsNotNull() {
            addCriterion("buyer_number is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerNumberEqualTo(String value) {
            addCriterion("buyer_number =", value, "buyerNumber");
            return (Criteria) this;
        }

        public Criteria andBuyerNumberNotEqualTo(String value) {
            addCriterion("buyer_number <>", value, "buyerNumber");
            return (Criteria) this;
        }

        public Criteria andBuyerNumberGreaterThan(String value) {
            addCriterion("buyer_number >", value, "buyerNumber");
            return (Criteria) this;
        }

        public Criteria andBuyerNumberGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_number >=", value, "buyerNumber");
            return (Criteria) this;
        }

        public Criteria andBuyerNumberLessThan(String value) {
            addCriterion("buyer_number <", value, "buyerNumber");
            return (Criteria) this;
        }

        public Criteria andBuyerNumberLessThanOrEqualTo(String value) {
            addCriterion("buyer_number <=", value, "buyerNumber");
            return (Criteria) this;
        }

        public Criteria andBuyerNumberLike(String value) {
            addCriterion("buyer_number like", value, "buyerNumber");
            return (Criteria) this;
        }

        public Criteria andBuyerNumberNotLike(String value) {
            addCriterion("buyer_number not like", value, "buyerNumber");
            return (Criteria) this;
        }

        public Criteria andBuyerNumberIn(List<String> values) {
            addCriterion("buyer_number in", values, "buyerNumber");
            return (Criteria) this;
        }

        public Criteria andBuyerNumberNotIn(List<String> values) {
            addCriterion("buyer_number not in", values, "buyerNumber");
            return (Criteria) this;
        }

        public Criteria andBuyerNumberBetween(String value1, String value2) {
            addCriterion("buyer_number between", value1, value2, "buyerNumber");
            return (Criteria) this;
        }

        public Criteria andBuyerNumberNotBetween(String value1, String value2) {
            addCriterion("buyer_number not between", value1, value2, "buyerNumber");
            return (Criteria) this;
        }

        public Criteria andBuyerIdIsNull() {
            addCriterion("buyer_id is null");
            return (Criteria) this;
        }

        public Criteria andBuyerIdIsNotNull() {
            addCriterion("buyer_id is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerIdEqualTo(String value) {
            addCriterion("buyer_id =", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdNotEqualTo(String value) {
            addCriterion("buyer_id <>", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdGreaterThan(String value) {
            addCriterion("buyer_id >", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_id >=", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdLessThan(String value) {
            addCriterion("buyer_id <", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdLessThanOrEqualTo(String value) {
            addCriterion("buyer_id <=", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdLike(String value) {
            addCriterion("buyer_id like", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdNotLike(String value) {
            addCriterion("buyer_id not like", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdIn(List<String> values) {
            addCriterion("buyer_id in", values, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdNotIn(List<String> values) {
            addCriterion("buyer_id not in", values, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdBetween(String value1, String value2) {
            addCriterion("buyer_id between", value1, value2, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdNotBetween(String value1, String value2) {
            addCriterion("buyer_id not between", value1, value2, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerRoundIsNull() {
            addCriterion("buyer_round is null");
            return (Criteria) this;
        }

        public Criteria andBuyerRoundIsNotNull() {
            addCriterion("buyer_round is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerRoundEqualTo(String value) {
            addCriterion("buyer_round =", value, "buyerRound");
            return (Criteria) this;
        }

        public Criteria andBuyerRoundNotEqualTo(String value) {
            addCriterion("buyer_round <>", value, "buyerRound");
            return (Criteria) this;
        }

        public Criteria andBuyerRoundGreaterThan(String value) {
            addCriterion("buyer_round >", value, "buyerRound");
            return (Criteria) this;
        }

        public Criteria andBuyerRoundGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_round >=", value, "buyerRound");
            return (Criteria) this;
        }

        public Criteria andBuyerRoundLessThan(String value) {
            addCriterion("buyer_round <", value, "buyerRound");
            return (Criteria) this;
        }

        public Criteria andBuyerRoundLessThanOrEqualTo(String value) {
            addCriterion("buyer_round <=", value, "buyerRound");
            return (Criteria) this;
        }

        public Criteria andBuyerRoundLike(String value) {
            addCriterion("buyer_round like", value, "buyerRound");
            return (Criteria) this;
        }

        public Criteria andBuyerRoundNotLike(String value) {
            addCriterion("buyer_round not like", value, "buyerRound");
            return (Criteria) this;
        }

        public Criteria andBuyerRoundIn(List<String> values) {
            addCriterion("buyer_round in", values, "buyerRound");
            return (Criteria) this;
        }

        public Criteria andBuyerRoundNotIn(List<String> values) {
            addCriterion("buyer_round not in", values, "buyerRound");
            return (Criteria) this;
        }

        public Criteria andBuyerRoundBetween(String value1, String value2) {
            addCriterion("buyer_round between", value1, value2, "buyerRound");
            return (Criteria) this;
        }

        public Criteria andBuyerRoundNotBetween(String value1, String value2) {
            addCriterion("buyer_round not between", value1, value2, "buyerRound");
            return (Criteria) this;
        }

        public Criteria andPurTypeIsNull() {
            addCriterion("pur_type is null");
            return (Criteria) this;
        }

        public Criteria andPurTypeIsNotNull() {
            addCriterion("pur_type is not null");
            return (Criteria) this;
        }

        public Criteria andPurTypeEqualTo(String value) {
            addCriterion("pur_type =", value, "purType");
            return (Criteria) this;
        }

        public Criteria andPurTypeNotEqualTo(String value) {
            addCriterion("pur_type <>", value, "purType");
            return (Criteria) this;
        }

        public Criteria andPurTypeGreaterThan(String value) {
            addCriterion("pur_type >", value, "purType");
            return (Criteria) this;
        }

        public Criteria andPurTypeGreaterThanOrEqualTo(String value) {
            addCriterion("pur_type >=", value, "purType");
            return (Criteria) this;
        }

        public Criteria andPurTypeLessThan(String value) {
            addCriterion("pur_type <", value, "purType");
            return (Criteria) this;
        }

        public Criteria andPurTypeLessThanOrEqualTo(String value) {
            addCriterion("pur_type <=", value, "purType");
            return (Criteria) this;
        }

        public Criteria andPurTypeLike(String value) {
            addCriterion("pur_type like", value, "purType");
            return (Criteria) this;
        }

        public Criteria andPurTypeNotLike(String value) {
            addCriterion("pur_type not like", value, "purType");
            return (Criteria) this;
        }

        public Criteria andPurTypeIn(List<String> values) {
            addCriterion("pur_type in", values, "purType");
            return (Criteria) this;
        }

        public Criteria andPurTypeNotIn(List<String> values) {
            addCriterion("pur_type not in", values, "purType");
            return (Criteria) this;
        }

        public Criteria andPurTypeBetween(String value1, String value2) {
            addCriterion("pur_type between", value1, value2, "purType");
            return (Criteria) this;
        }

        public Criteria andPurTypeNotBetween(String value1, String value2) {
            addCriterion("pur_type not between", value1, value2, "purType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeIsNull() {
            addCriterion("inventory_type is null");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeIsNotNull() {
            addCriterion("inventory_type is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeEqualTo(String value) {
            addCriterion("inventory_type =", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeNotEqualTo(String value) {
            addCriterion("inventory_type <>", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeGreaterThan(String value) {
            addCriterion("inventory_type >", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeGreaterThanOrEqualTo(String value) {
            addCriterion("inventory_type >=", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeLessThan(String value) {
            addCriterion("inventory_type <", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeLessThanOrEqualTo(String value) {
            addCriterion("inventory_type <=", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeLike(String value) {
            addCriterion("inventory_type like", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeNotLike(String value) {
            addCriterion("inventory_type not like", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeIn(List<String> values) {
            addCriterion("inventory_type in", values, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeNotIn(List<String> values) {
            addCriterion("inventory_type not in", values, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeBetween(String value1, String value2) {
            addCriterion("inventory_type between", value1, value2, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeNotBetween(String value1, String value2) {
            addCriterion("inventory_type not between", value1, value2, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdIsNull() {
            addCriterion("organization_id is null");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdIsNotNull() {
            addCriterion("organization_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdEqualTo(Long value) {
            addCriterion("organization_id =", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdNotEqualTo(Long value) {
            addCriterion("organization_id <>", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdGreaterThan(Long value) {
            addCriterion("organization_id >", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdGreaterThanOrEqualTo(Long value) {
            addCriterion("organization_id >=", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdLessThan(Long value) {
            addCriterion("organization_id <", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdLessThanOrEqualTo(Long value) {
            addCriterion("organization_id <=", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdIn(List<Long> values) {
            addCriterion("organization_id in", values, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdNotIn(List<Long> values) {
            addCriterion("organization_id not in", values, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdBetween(Long value1, Long value2) {
            addCriterion("organization_id between", value1, value2, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdNotBetween(Long value1, Long value2) {
            addCriterion("organization_id not between", value1, value2, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationNameIsNull() {
            addCriterion("organization_name is null");
            return (Criteria) this;
        }

        public Criteria andOrganizationNameIsNotNull() {
            addCriterion("organization_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrganizationNameEqualTo(String value) {
            addCriterion("organization_name =", value, "organizationName");
            return (Criteria) this;
        }

        public Criteria andOrganizationNameNotEqualTo(String value) {
            addCriterion("organization_name <>", value, "organizationName");
            return (Criteria) this;
        }

        public Criteria andOrganizationNameGreaterThan(String value) {
            addCriterion("organization_name >", value, "organizationName");
            return (Criteria) this;
        }

        public Criteria andOrganizationNameGreaterThanOrEqualTo(String value) {
            addCriterion("organization_name >=", value, "organizationName");
            return (Criteria) this;
        }

        public Criteria andOrganizationNameLessThan(String value) {
            addCriterion("organization_name <", value, "organizationName");
            return (Criteria) this;
        }

        public Criteria andOrganizationNameLessThanOrEqualTo(String value) {
            addCriterion("organization_name <=", value, "organizationName");
            return (Criteria) this;
        }

        public Criteria andOrganizationNameLike(String value) {
            addCriterion("organization_name like", value, "organizationName");
            return (Criteria) this;
        }

        public Criteria andOrganizationNameNotLike(String value) {
            addCriterion("organization_name not like", value, "organizationName");
            return (Criteria) this;
        }

        public Criteria andOrganizationNameIn(List<String> values) {
            addCriterion("organization_name in", values, "organizationName");
            return (Criteria) this;
        }

        public Criteria andOrganizationNameNotIn(List<String> values) {
            addCriterion("organization_name not in", values, "organizationName");
            return (Criteria) this;
        }

        public Criteria andOrganizationNameBetween(String value1, String value2) {
            addCriterion("organization_name between", value1, value2, "organizationName");
            return (Criteria) this;
        }

        public Criteria andOrganizationNameNotBetween(String value1, String value2) {
            addCriterion("organization_name not between", value1, value2, "organizationName");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIsNull() {
            addCriterion("creator_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIsNotNull() {
            addCriterion("creator_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorNameEqualTo(String value) {
            addCriterion("creator_name =", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotEqualTo(String value) {
            addCriterion("creator_name <>", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameGreaterThan(String value) {
            addCriterion("creator_name >", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameGreaterThanOrEqualTo(String value) {
            addCriterion("creator_name >=", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLessThan(String value) {
            addCriterion("creator_name <", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLessThanOrEqualTo(String value) {
            addCriterion("creator_name <=", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLike(String value) {
            addCriterion("creator_name like", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotLike(String value) {
            addCriterion("creator_name not like", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIn(List<String> values) {
            addCriterion("creator_name in", values, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotIn(List<String> values) {
            addCriterion("creator_name not in", values, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameBetween(String value1, String value2) {
            addCriterion("creator_name between", value1, value2, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotBetween(String value1, String value2) {
            addCriterion("creator_name not between", value1, value2, "creatorName");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameIsNull() {
            addCriterion("updater_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameIsNotNull() {
            addCriterion("updater_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameEqualTo(String value) {
            addCriterion("updater_name =", value, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameNotEqualTo(String value) {
            addCriterion("updater_name <>", value, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameGreaterThan(String value) {
            addCriterion("updater_name >", value, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameGreaterThanOrEqualTo(String value) {
            addCriterion("updater_name >=", value, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameLessThan(String value) {
            addCriterion("updater_name <", value, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameLessThanOrEqualTo(String value) {
            addCriterion("updater_name <=", value, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameLike(String value) {
            addCriterion("updater_name like", value, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameNotLike(String value) {
            addCriterion("updater_name not like", value, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameIn(List<String> values) {
            addCriterion("updater_name in", values, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameNotIn(List<String> values) {
            addCriterion("updater_name not in", values, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameBetween(String value1, String value2) {
            addCriterion("updater_name between", value1, value2, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameNotBetween(String value1, String value2) {
            addCriterion("updater_name not between", value1, value2, "updaterName");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIsNull() {
            addCriterion("figure_number is null");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIsNotNull() {
            addCriterion("figure_number is not null");
            return (Criteria) this;
        }

        public Criteria andFigureNumberEqualTo(String value) {
            addCriterion("figure_number =", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotEqualTo(String value) {
            addCriterion("figure_number <>", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberGreaterThan(String value) {
            addCriterion("figure_number >", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberGreaterThanOrEqualTo(String value) {
            addCriterion("figure_number >=", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLessThan(String value) {
            addCriterion("figure_number <", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLessThanOrEqualTo(String value) {
            addCriterion("figure_number <=", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLike(String value) {
            addCriterion("figure_number like", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotLike(String value) {
            addCriterion("figure_number not like", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIn(List<String> values) {
            addCriterion("figure_number in", values, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotIn(List<String> values) {
            addCriterion("figure_number not in", values, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberBetween(String value1, String value2) {
            addCriterion("figure_number between", value1, value2, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotBetween(String value1, String value2) {
            addCriterion("figure_number not between", value1, value2, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andChartVersionIsNull() {
            addCriterion("chart_version is null");
            return (Criteria) this;
        }

        public Criteria andChartVersionIsNotNull() {
            addCriterion("chart_version is not null");
            return (Criteria) this;
        }

        public Criteria andChartVersionEqualTo(String value) {
            addCriterion("chart_version =", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotEqualTo(String value) {
            addCriterion("chart_version <>", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionGreaterThan(String value) {
            addCriterion("chart_version >", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionGreaterThanOrEqualTo(String value) {
            addCriterion("chart_version >=", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionLessThan(String value) {
            addCriterion("chart_version <", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionLessThanOrEqualTo(String value) {
            addCriterion("chart_version <=", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionLike(String value) {
            addCriterion("chart_version like", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotLike(String value) {
            addCriterion("chart_version not like", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionIn(List<String> values) {
            addCriterion("chart_version in", values, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotIn(List<String> values) {
            addCriterion("chart_version not in", values, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionBetween(String value1, String value2) {
            addCriterion("chart_version between", value1, value2, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotBetween(String value1, String value2) {
            addCriterion("chart_version not between", value1, value2, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeIsNull() {
            addCriterion("brand_material_code is null");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeIsNotNull() {
            addCriterion("brand_material_code is not null");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeEqualTo(String value) {
            addCriterion("brand_material_code =", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeNotEqualTo(String value) {
            addCriterion("brand_material_code <>", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeGreaterThan(String value) {
            addCriterion("brand_material_code >", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeGreaterThanOrEqualTo(String value) {
            addCriterion("brand_material_code >=", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeLessThan(String value) {
            addCriterion("brand_material_code <", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeLessThanOrEqualTo(String value) {
            addCriterion("brand_material_code <=", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeLike(String value) {
            addCriterion("brand_material_code like", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeNotLike(String value) {
            addCriterion("brand_material_code not like", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeIn(List<String> values) {
            addCriterion("brand_material_code in", values, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeNotIn(List<String> values) {
            addCriterion("brand_material_code not in", values, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeBetween(String value1, String value2) {
            addCriterion("brand_material_code between", value1, value2, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeNotBetween(String value1, String value2) {
            addCriterion("brand_material_code not between", value1, value2, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCreatedDateIsNull() {
            addCriterion("purchase_order_created_date is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCreatedDateIsNotNull() {
            addCriterion("purchase_order_created_date is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCreatedDateEqualTo(Date value) {
            addCriterion("purchase_order_created_date =", value, "purchaseOrderCreatedDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCreatedDateNotEqualTo(Date value) {
            addCriterion("purchase_order_created_date <>", value, "purchaseOrderCreatedDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCreatedDateGreaterThan(Date value) {
            addCriterion("purchase_order_created_date >", value, "purchaseOrderCreatedDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("purchase_order_created_date >=", value, "purchaseOrderCreatedDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCreatedDateLessThan(Date value) {
            addCriterion("purchase_order_created_date <", value, "purchaseOrderCreatedDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("purchase_order_created_date <=", value, "purchaseOrderCreatedDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCreatedDateIn(List<Date> values) {
            addCriterion("purchase_order_created_date in", values, "purchaseOrderCreatedDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCreatedDateNotIn(List<Date> values) {
            addCriterion("purchase_order_created_date not in", values, "purchaseOrderCreatedDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCreatedDateBetween(Date value1, Date value2) {
            addCriterion("purchase_order_created_date between", value1, value2, "purchaseOrderCreatedDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("purchase_order_created_date not between", value1, value2, "purchaseOrderCreatedDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderStartDateIsNull() {
            addCriterion("purchase_order_start_date is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderStartDateIsNotNull() {
            addCriterion("purchase_order_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderStartDateEqualTo(Date value) {
            addCriterion("purchase_order_start_date =", value, "purchaseOrderStartDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderStartDateNotEqualTo(Date value) {
            addCriterion("purchase_order_start_date <>", value, "purchaseOrderStartDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderStartDateGreaterThan(Date value) {
            addCriterion("purchase_order_start_date >", value, "purchaseOrderStartDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("purchase_order_start_date >=", value, "purchaseOrderStartDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderStartDateLessThan(Date value) {
            addCriterion("purchase_order_start_date <", value, "purchaseOrderStartDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderStartDateLessThanOrEqualTo(Date value) {
            addCriterion("purchase_order_start_date <=", value, "purchaseOrderStartDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderStartDateIn(List<Date> values) {
            addCriterion("purchase_order_start_date in", values, "purchaseOrderStartDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderStartDateNotIn(List<Date> values) {
            addCriterion("purchase_order_start_date not in", values, "purchaseOrderStartDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderStartDateBetween(Date value1, Date value2) {
            addCriterion("purchase_order_start_date between", value1, value2, "purchaseOrderStartDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderStartDateNotBetween(Date value1, Date value2) {
            addCriterion("purchase_order_start_date not between", value1, value2, "purchaseOrderStartDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderEndDateIsNull() {
            addCriterion("purchase_order_end_date is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderEndDateIsNotNull() {
            addCriterion("purchase_order_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderEndDateEqualTo(Date value) {
            addCriterion("purchase_order_end_date =", value, "purchaseOrderEndDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderEndDateNotEqualTo(Date value) {
            addCriterion("purchase_order_end_date <>", value, "purchaseOrderEndDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderEndDateGreaterThan(Date value) {
            addCriterion("purchase_order_end_date >", value, "purchaseOrderEndDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("purchase_order_end_date >=", value, "purchaseOrderEndDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderEndDateLessThan(Date value) {
            addCriterion("purchase_order_end_date <", value, "purchaseOrderEndDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderEndDateLessThanOrEqualTo(Date value) {
            addCriterion("purchase_order_end_date <=", value, "purchaseOrderEndDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderEndDateIn(List<Date> values) {
            addCriterion("purchase_order_end_date in", values, "purchaseOrderEndDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderEndDateNotIn(List<Date> values) {
            addCriterion("purchase_order_end_date not in", values, "purchaseOrderEndDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderEndDateBetween(Date value1, Date value2) {
            addCriterion("purchase_order_end_date between", value1, value2, "purchaseOrderEndDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderEndDateNotBetween(Date value1, Date value2) {
            addCriterion("purchase_order_end_date not between", value1, value2, "purchaseOrderEndDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedDateIsNull() {
            addCriterion("material_created_date is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedDateIsNotNull() {
            addCriterion("material_created_date is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedDateEqualTo(Date value) {
            addCriterion("material_created_date =", value, "materialCreatedDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedDateNotEqualTo(Date value) {
            addCriterion("material_created_date <>", value, "materialCreatedDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedDateGreaterThan(Date value) {
            addCriterion("material_created_date >", value, "materialCreatedDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("material_created_date >=", value, "materialCreatedDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedDateLessThan(Date value) {
            addCriterion("material_created_date <", value, "materialCreatedDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("material_created_date <=", value, "materialCreatedDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedDateIn(List<Date> values) {
            addCriterion("material_created_date in", values, "materialCreatedDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedDateNotIn(List<Date> values) {
            addCriterion("material_created_date not in", values, "materialCreatedDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedDateBetween(Date value1, Date value2) {
            addCriterion("material_created_date between", value1, value2, "materialCreatedDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("material_created_date not between", value1, value2, "materialCreatedDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedStartDateIsNull() {
            addCriterion("material_created_start_date is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedStartDateIsNotNull() {
            addCriterion("material_created_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedStartDateEqualTo(Date value) {
            addCriterion("material_created_start_date =", value, "materialCreatedStartDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedStartDateNotEqualTo(Date value) {
            addCriterion("material_created_start_date <>", value, "materialCreatedStartDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedStartDateGreaterThan(Date value) {
            addCriterion("material_created_start_date >", value, "materialCreatedStartDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("material_created_start_date >=", value, "materialCreatedStartDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedStartDateLessThan(Date value) {
            addCriterion("material_created_start_date <", value, "materialCreatedStartDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedStartDateLessThanOrEqualTo(Date value) {
            addCriterion("material_created_start_date <=", value, "materialCreatedStartDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedStartDateIn(List<Date> values) {
            addCriterion("material_created_start_date in", values, "materialCreatedStartDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedStartDateNotIn(List<Date> values) {
            addCriterion("material_created_start_date not in", values, "materialCreatedStartDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedStartDateBetween(Date value1, Date value2) {
            addCriterion("material_created_start_date between", value1, value2, "materialCreatedStartDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedStartDateNotBetween(Date value1, Date value2) {
            addCriterion("material_created_start_date not between", value1, value2, "materialCreatedStartDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedEndDateIsNull() {
            addCriterion("material_created_end_date is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedEndDateIsNotNull() {
            addCriterion("material_created_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedEndDateEqualTo(Date value) {
            addCriterion("material_created_end_date =", value, "materialCreatedEndDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedEndDateNotEqualTo(Date value) {
            addCriterion("material_created_end_date <>", value, "materialCreatedEndDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedEndDateGreaterThan(Date value) {
            addCriterion("material_created_end_date >", value, "materialCreatedEndDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("material_created_end_date >=", value, "materialCreatedEndDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedEndDateLessThan(Date value) {
            addCriterion("material_created_end_date <", value, "materialCreatedEndDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedEndDateLessThanOrEqualTo(Date value) {
            addCriterion("material_created_end_date <=", value, "materialCreatedEndDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedEndDateIn(List<Date> values) {
            addCriterion("material_created_end_date in", values, "materialCreatedEndDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedEndDateNotIn(List<Date> values) {
            addCriterion("material_created_end_date not in", values, "materialCreatedEndDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedEndDateBetween(Date value1, Date value2) {
            addCriterion("material_created_end_date between", value1, value2, "materialCreatedEndDate");
            return (Criteria) this;
        }

        public Criteria andMaterialCreatedEndDateNotBetween(Date value1, Date value2) {
            addCriterion("material_created_end_date not between", value1, value2, "materialCreatedEndDate");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNull() {
            addCriterion("delete_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNotNull() {
            addCriterion("delete_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagEqualTo(Byte value) {
            addCriterion("delete_flag =", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotEqualTo(Byte value) {
            addCriterion("delete_flag <>", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThan(Byte value) {
            addCriterion("delete_flag >", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThanOrEqualTo(Byte value) {
            addCriterion("delete_flag >=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThan(Byte value) {
            addCriterion("delete_flag <", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThanOrEqualTo(Byte value) {
            addCriterion("delete_flag <=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIn(List<Byte> values) {
            addCriterion("delete_flag in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotIn(List<Byte> values) {
            addCriterion("delete_flag not in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagBetween(Byte value1, Byte value2) {
            addCriterion("delete_flag between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotBetween(Byte value1, Byte value2) {
            addCriterion("delete_flag not between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}