package com.midea.pam.common.statistics.excelVo;

import com.midea.pam.common.util.BigDecimalUtils;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022-12-14
 * 项目物料领料进度表ExcelVO
 */
@Getter
@Setter
public class ProjectMaterialGetExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer number;

    @Excel(name = "项目编号", width = 15)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "业务分类", width = 20)
    private String unitName;

    @Excel(name = "项目经理", width = 15)
    private String managerName;

    @Excel(name = "项目状态", width = 15, replace = {"审批驳回_-2", "财务驳回_-1", "草稿_0", "审批中_3", "项目进行中_4", "项目变更中_9", "结项_10",
            "预立项转正驳回_-3", "预立项审批驳回_-4", "审批撤回_11", "预立项审批撤回_13", "作废_12", "预立项转正审批中_7",
            "预立项转正作废_-13", "预立项转正删除_23", "项目终止审批中_15", "终止_16", "项目流程删除_17", "_null"})
    private Integer status;

    @Excel(name = "业务实体", width = 30)
    private String ouName;

    @Excel(name = "ERP物料编码", width = 20)
    private String erpCode;

    @Excel(name = "物料描述", width = 30)
    private String materielDescr;

    @Excel(name = "品牌", width = 20)
    private String brand;

    @Excel(name = "型号", width = 20)
    private String model;

    @Excel(name = "名称", width = 20)
    private String name;

    @Excel(name = "单位", width = 10)
    private String unit;

    @Excel(name = "剩余领料量", width = 15)
    private BigDecimal surplusAmount;

    @Excel(name = "库存量", width = 15)
    private BigDecimal existAmount;

    @Excel(name = "下单数量", width = 15)
    private BigDecimal orderNum;

    @Excel(name = "子库", width = 20)
    private String subinventoryCode;

    @Excel(name = "子库描述", width = 30)
    private String subinventoryDescription;

    @Excel(name = "货位", width = 30)
    private String locationCode;
    
    @Excel(name = "货架", width = 30)
    private String shelves;


    public BigDecimal getSurplusAmount() {
        return BigDecimalUtils.scale(surplusAmount);
    }

    public BigDecimal getExistAmount() {
        return BigDecimalUtils.scale(existAmount);
    }

    public BigDecimal getOrderNum() {
        return BigDecimalUtils.scale(orderNum);
    }

}