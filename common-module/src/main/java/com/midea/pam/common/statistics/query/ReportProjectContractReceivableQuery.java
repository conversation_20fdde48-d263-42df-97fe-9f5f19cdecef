package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * Description
 * Created by chenchong
 * Date 2024-04-17 15:48
 */
@Getter
@Setter
@ApiModel(value = "ReportProjectContractReceivableQuery", description = "合同资产账龄报表")
public class ReportProjectContractReceivableQuery {

    private Long id;

    private Long personal;

    private Long companyId;

    private Long createBy;

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    @ApiModelProperty(value = "报表ID")
    private Long reportId;

    @ApiModelProperty(value = "业务实体")
    private List<Long> ouIdList;

    @ApiModelProperty(value = "是否显示余额为0的数据：true/false")
    private Boolean displayZeroOrNot;

    @ApiModelProperty(value = "截止期间")
    private Date expireDate;

    @ApiModelProperty(value = "计算逻辑:1收入-开票/2收入-max（开票、收款）")
    private Integer calculateRule;

    @ApiModelProperty(value = "账龄规则id")
    private Long agingSegmentHeaderId;

    @ApiModelProperty(value = "规则范围拼接")
    private String ruleScope;

    @ApiModelProperty(value = "规则名称拼接")
    private String ruleName;

    @Override
    public String toString() {
        return "ReportProjectContractReceivableQuery{" +
                "id=" + id +
                ", personal=" + personal +
                ", companyId=" + companyId +
                ", createBy=" + createBy +
                ", executeId=" + executeId +
                ", reportId=" + reportId +
                ", ouIdList=" + ouIdList +
                ", displayZeroOrNot=" + displayZeroOrNot +
                ", expireDate=" + expireDate +
                ", calculateRule=" + calculateRule +
                ", agingSegmentHeaderId=" + agingSegmentHeaderId +
                ", ruleScope='" + ruleScope + '\'' +
                ", ruleName='" + ruleName + '\'' +
                '}';
    }
}
