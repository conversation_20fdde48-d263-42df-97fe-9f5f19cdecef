package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: common-module
 * @description: 实际成本归集-物料外包成本明细导出实体【按合同进度入账结转】
 * @author:zhongpeng
 * @create:2020-03-24 16:32
 **/
@Getter
@Setter
public class ProgressOutsourcingCostCollectionExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer num;

    @Excel(name = "合同进度完成日期", width = 20, format = "yyyy-MM-dd")
    private Date progressEndTime;

    @Excel(name = "供应商名称", width = 30)
    private String vendorName;

    @Excel(name = "供应商地点", width = 30)
    private String vendorSiteCode;

    @Excel(name = "合同编号", width = 30)
    private String purchaseContractCode;

    @Excel(name = "合同名称", width = 30)
    private String purchaseContractName;

    @Excel(name = "币种", width = 10)
    private String purchaseContractCurrency;

    @Excel(name = "合同进度金额（不含税）", width = 20)
    private BigDecimal executeAmount;

    @Excel(name = "结转状态", width = 20, replace = {"未结转_0", "已结转_1", "未结转_null"})
    private Integer carryStatus;

    @Excel(name = "结转期间", width = 20)
    private String glPeriod;

    @Excel(name = "项目编号",width = 30)
    private String projectCode;

    @Excel(name = "WBS号", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项编码", width = 20)
    private String activityCode;
}
