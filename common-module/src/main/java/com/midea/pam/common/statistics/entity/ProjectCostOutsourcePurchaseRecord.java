package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "项目外包采购成本明细")
public class ProjectCostOutsourcePurchaseRecord extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "执行ID")
    private Long executeId;

    @ApiModelProperty(value = "汇总ID")
    private Long summaryId;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "采购合同编码")
    private String contractCode;

    @ApiModelProperty(value = "采购合同名称")
    private String contractName;

    @ApiModelProperty(value = "合同金额(含税)-原币")
    private BigDecimal contractAmount;

    @ApiModelProperty(value = "合同金额(不含税)-原币")
    private BigDecimal excludingTaxAmount;

    @ApiModelProperty(value = "合同币种")
    private String currency;

    @ApiModelProperty(value = "合同汇率")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "本位币")
    private String localCurrency;

    @ApiModelProperty(value = "合同金额(不含税)-本位币")
    private BigDecimal localExcludingTaxAmount;

    @ApiModelProperty(value = "生效罚扣金额(不含税)-本位币")
    private BigDecimal localPunishmentAmount;

    @ApiModelProperty(value = "开始日期")
    private Date startTime;

    @ApiModelProperty(value = "结束日期")
    private Date endTime;

    @ApiModelProperty(value = "采购跟进人")
    private String buyerName;

    @ApiModelProperty(value = "合同类型")
    private String typeName;

    @ApiModelProperty(value = "供应商编号")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "供应商地点")
    private String vendorSiteCode;

    @ApiModelProperty(value = "合同状态")
    private String status;

    @ApiModelProperty(value = "是否被删除标志")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getSummaryId() {
        return summaryId;
    }

    public void setSummaryId(Long summaryId) {
        this.summaryId = summaryId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode == null ? null : contractCode.trim();
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName == null ? null : contractName.trim();
    }

    public BigDecimal getContractAmount() {
        return contractAmount;
    }

    public void setContractAmount(BigDecimal contractAmount) {
        this.contractAmount = contractAmount;
    }

    public BigDecimal getExcludingTaxAmount() {
        return excludingTaxAmount;
    }

    public void setExcludingTaxAmount(BigDecimal excludingTaxAmount) {
        this.excludingTaxAmount = excludingTaxAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public String getLocalCurrency() {
        return localCurrency;
    }

    public void setLocalCurrency(String localCurrency) {
        this.localCurrency = localCurrency == null ? null : localCurrency.trim();
    }

    public BigDecimal getLocalExcludingTaxAmount() {
        return localExcludingTaxAmount;
    }

    public void setLocalExcludingTaxAmount(BigDecimal localExcludingTaxAmount) {
        this.localExcludingTaxAmount = localExcludingTaxAmount;
    }

    public BigDecimal getLocalPunishmentAmount() {
        return localPunishmentAmount;
    }

    public void setLocalPunishmentAmount(BigDecimal localPunishmentAmount) {
        this.localPunishmentAmount = localPunishmentAmount;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName == null ? null : buyerName.trim();
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName == null ? null : typeName.trim();
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode == null ? null : vendorCode.trim();
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName == null ? null : vendorName.trim();
    }

    public String getVendorSiteCode() {
        return vendorSiteCode;
    }

    public void setVendorSiteCode(String vendorSiteCode) {
        this.vendorSiteCode = vendorSiteCode == null ? null : vendorSiteCode.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", executeId=").append(executeId);
        sb.append(", summaryId=").append(summaryId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", contractCode=").append(contractCode);
        sb.append(", contractName=").append(contractName);
        sb.append(", contractAmount=").append(contractAmount);
        sb.append(", excludingTaxAmount=").append(excludingTaxAmount);
        sb.append(", currency=").append(currency);
        sb.append(", conversionRate=").append(conversionRate);
        sb.append(", localCurrency=").append(localCurrency);
        sb.append(", localExcludingTaxAmount=").append(localExcludingTaxAmount);
        sb.append(", localPunishmentAmount=").append(localPunishmentAmount);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", buyerName=").append(buyerName);
        sb.append(", typeName=").append(typeName);
        sb.append(", vendorCode=").append(vendorCode);
        sb.append(", vendorName=").append(vendorName);
        sb.append(", vendorSiteCode=").append(vendorSiteCode);
        sb.append(", status=").append(status);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}