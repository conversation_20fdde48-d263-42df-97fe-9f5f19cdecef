package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectCostEaDetailRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectCostEaDetailRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdIsNull() {
            addCriterion("summary_id is null");
            return (Criteria) this;
        }

        public Criteria andSummaryIdIsNotNull() {
            addCriterion("summary_id is not null");
            return (Criteria) this;
        }

        public Criteria andSummaryIdEqualTo(Long value) {
            addCriterion("summary_id =", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdNotEqualTo(Long value) {
            addCriterion("summary_id <>", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdGreaterThan(Long value) {
            addCriterion("summary_id >", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("summary_id >=", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdLessThan(Long value) {
            addCriterion("summary_id <", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdLessThanOrEqualTo(Long value) {
            addCriterion("summary_id <=", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdIn(List<Long> values) {
            addCriterion("summary_id in", values, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdNotIn(List<Long> values) {
            addCriterion("summary_id not in", values, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdBetween(Long value1, Long value2) {
            addCriterion("summary_id between", value1, value2, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdNotBetween(Long value1, Long value2) {
            addCriterion("summary_id not between", value1, value2, "summaryId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andFeeApplyCodeIsNull() {
            addCriterion("fee_apply_code is null");
            return (Criteria) this;
        }

        public Criteria andFeeApplyCodeIsNotNull() {
            addCriterion("fee_apply_code is not null");
            return (Criteria) this;
        }

        public Criteria andFeeApplyCodeEqualTo(String value) {
            addCriterion("fee_apply_code =", value, "feeApplyCode");
            return (Criteria) this;
        }

        public Criteria andFeeApplyCodeNotEqualTo(String value) {
            addCriterion("fee_apply_code <>", value, "feeApplyCode");
            return (Criteria) this;
        }

        public Criteria andFeeApplyCodeGreaterThan(String value) {
            addCriterion("fee_apply_code >", value, "feeApplyCode");
            return (Criteria) this;
        }

        public Criteria andFeeApplyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("fee_apply_code >=", value, "feeApplyCode");
            return (Criteria) this;
        }

        public Criteria andFeeApplyCodeLessThan(String value) {
            addCriterion("fee_apply_code <", value, "feeApplyCode");
            return (Criteria) this;
        }

        public Criteria andFeeApplyCodeLessThanOrEqualTo(String value) {
            addCriterion("fee_apply_code <=", value, "feeApplyCode");
            return (Criteria) this;
        }

        public Criteria andFeeApplyCodeLike(String value) {
            addCriterion("fee_apply_code like", value, "feeApplyCode");
            return (Criteria) this;
        }

        public Criteria andFeeApplyCodeNotLike(String value) {
            addCriterion("fee_apply_code not like", value, "feeApplyCode");
            return (Criteria) this;
        }

        public Criteria andFeeApplyCodeIn(List<String> values) {
            addCriterion("fee_apply_code in", values, "feeApplyCode");
            return (Criteria) this;
        }

        public Criteria andFeeApplyCodeNotIn(List<String> values) {
            addCriterion("fee_apply_code not in", values, "feeApplyCode");
            return (Criteria) this;
        }

        public Criteria andFeeApplyCodeBetween(String value1, String value2) {
            addCriterion("fee_apply_code between", value1, value2, "feeApplyCode");
            return (Criteria) this;
        }

        public Criteria andFeeApplyCodeNotBetween(String value1, String value2) {
            addCriterion("fee_apply_code not between", value1, value2, "feeApplyCode");
            return (Criteria) this;
        }

        public Criteria andSubmitedTimeIsNull() {
            addCriterion("submited_time is null");
            return (Criteria) this;
        }

        public Criteria andSubmitedTimeIsNotNull() {
            addCriterion("submited_time is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitedTimeEqualTo(Date value) {
            addCriterion("submited_time =", value, "submitedTime");
            return (Criteria) this;
        }

        public Criteria andSubmitedTimeNotEqualTo(Date value) {
            addCriterion("submited_time <>", value, "submitedTime");
            return (Criteria) this;
        }

        public Criteria andSubmitedTimeGreaterThan(Date value) {
            addCriterion("submited_time >", value, "submitedTime");
            return (Criteria) this;
        }

        public Criteria andSubmitedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("submited_time >=", value, "submitedTime");
            return (Criteria) this;
        }

        public Criteria andSubmitedTimeLessThan(Date value) {
            addCriterion("submited_time <", value, "submitedTime");
            return (Criteria) this;
        }

        public Criteria andSubmitedTimeLessThanOrEqualTo(Date value) {
            addCriterion("submited_time <=", value, "submitedTime");
            return (Criteria) this;
        }

        public Criteria andSubmitedTimeIn(List<Date> values) {
            addCriterion("submited_time in", values, "submitedTime");
            return (Criteria) this;
        }

        public Criteria andSubmitedTimeNotIn(List<Date> values) {
            addCriterion("submited_time not in", values, "submitedTime");
            return (Criteria) this;
        }

        public Criteria andSubmitedTimeBetween(Date value1, Date value2) {
            addCriterion("submited_time between", value1, value2, "submitedTime");
            return (Criteria) this;
        }

        public Criteria andSubmitedTimeNotBetween(Date value1, Date value2) {
            addCriterion("submited_time not between", value1, value2, "submitedTime");
            return (Criteria) this;
        }

        public Criteria andApplyNameIsNull() {
            addCriterion("apply_name is null");
            return (Criteria) this;
        }

        public Criteria andApplyNameIsNotNull() {
            addCriterion("apply_name is not null");
            return (Criteria) this;
        }

        public Criteria andApplyNameEqualTo(String value) {
            addCriterion("apply_name =", value, "applyName");
            return (Criteria) this;
        }

        public Criteria andApplyNameNotEqualTo(String value) {
            addCriterion("apply_name <>", value, "applyName");
            return (Criteria) this;
        }

        public Criteria andApplyNameGreaterThan(String value) {
            addCriterion("apply_name >", value, "applyName");
            return (Criteria) this;
        }

        public Criteria andApplyNameGreaterThanOrEqualTo(String value) {
            addCriterion("apply_name >=", value, "applyName");
            return (Criteria) this;
        }

        public Criteria andApplyNameLessThan(String value) {
            addCriterion("apply_name <", value, "applyName");
            return (Criteria) this;
        }

        public Criteria andApplyNameLessThanOrEqualTo(String value) {
            addCriterion("apply_name <=", value, "applyName");
            return (Criteria) this;
        }

        public Criteria andApplyNameLike(String value) {
            addCriterion("apply_name like", value, "applyName");
            return (Criteria) this;
        }

        public Criteria andApplyNameNotLike(String value) {
            addCriterion("apply_name not like", value, "applyName");
            return (Criteria) this;
        }

        public Criteria andApplyNameIn(List<String> values) {
            addCriterion("apply_name in", values, "applyName");
            return (Criteria) this;
        }

        public Criteria andApplyNameNotIn(List<String> values) {
            addCriterion("apply_name not in", values, "applyName");
            return (Criteria) this;
        }

        public Criteria andApplyNameBetween(String value1, String value2) {
            addCriterion("apply_name between", value1, value2, "applyName");
            return (Criteria) this;
        }

        public Criteria andApplyNameNotBetween(String value1, String value2) {
            addCriterion("apply_name not between", value1, value2, "applyName");
            return (Criteria) this;
        }

        public Criteria andFeeItemNameIsNull() {
            addCriterion("fee_item_name is null");
            return (Criteria) this;
        }

        public Criteria andFeeItemNameIsNotNull() {
            addCriterion("fee_item_name is not null");
            return (Criteria) this;
        }

        public Criteria andFeeItemNameEqualTo(String value) {
            addCriterion("fee_item_name =", value, "feeItemName");
            return (Criteria) this;
        }

        public Criteria andFeeItemNameNotEqualTo(String value) {
            addCriterion("fee_item_name <>", value, "feeItemName");
            return (Criteria) this;
        }

        public Criteria andFeeItemNameGreaterThan(String value) {
            addCriterion("fee_item_name >", value, "feeItemName");
            return (Criteria) this;
        }

        public Criteria andFeeItemNameGreaterThanOrEqualTo(String value) {
            addCriterion("fee_item_name >=", value, "feeItemName");
            return (Criteria) this;
        }

        public Criteria andFeeItemNameLessThan(String value) {
            addCriterion("fee_item_name <", value, "feeItemName");
            return (Criteria) this;
        }

        public Criteria andFeeItemNameLessThanOrEqualTo(String value) {
            addCriterion("fee_item_name <=", value, "feeItemName");
            return (Criteria) this;
        }

        public Criteria andFeeItemNameLike(String value) {
            addCriterion("fee_item_name like", value, "feeItemName");
            return (Criteria) this;
        }

        public Criteria andFeeItemNameNotLike(String value) {
            addCriterion("fee_item_name not like", value, "feeItemName");
            return (Criteria) this;
        }

        public Criteria andFeeItemNameIn(List<String> values) {
            addCriterion("fee_item_name in", values, "feeItemName");
            return (Criteria) this;
        }

        public Criteria andFeeItemNameNotIn(List<String> values) {
            addCriterion("fee_item_name not in", values, "feeItemName");
            return (Criteria) this;
        }

        public Criteria andFeeItemNameBetween(String value1, String value2) {
            addCriterion("fee_item_name between", value1, value2, "feeItemName");
            return (Criteria) this;
        }

        public Criteria andFeeItemNameNotBetween(String value1, String value2) {
            addCriterion("fee_item_name not between", value1, value2, "feeItemName");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNameIsNull() {
            addCriterion("fee_type_name is null");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNameIsNotNull() {
            addCriterion("fee_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNameEqualTo(String value) {
            addCriterion("fee_type_name =", value, "feeTypeName");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNameNotEqualTo(String value) {
            addCriterion("fee_type_name <>", value, "feeTypeName");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNameGreaterThan(String value) {
            addCriterion("fee_type_name >", value, "feeTypeName");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("fee_type_name >=", value, "feeTypeName");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNameLessThan(String value) {
            addCriterion("fee_type_name <", value, "feeTypeName");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNameLessThanOrEqualTo(String value) {
            addCriterion("fee_type_name <=", value, "feeTypeName");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNameLike(String value) {
            addCriterion("fee_type_name like", value, "feeTypeName");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNameNotLike(String value) {
            addCriterion("fee_type_name not like", value, "feeTypeName");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNameIn(List<String> values) {
            addCriterion("fee_type_name in", values, "feeTypeName");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNameNotIn(List<String> values) {
            addCriterion("fee_type_name not in", values, "feeTypeName");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNameBetween(String value1, String value2) {
            addCriterion("fee_type_name between", value1, value2, "feeTypeName");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNameNotBetween(String value1, String value2) {
            addCriterion("fee_type_name not between", value1, value2, "feeTypeName");
            return (Criteria) this;
        }

        public Criteria andApproveAmountIsNull() {
            addCriterion("approve_amount is null");
            return (Criteria) this;
        }

        public Criteria andApproveAmountIsNotNull() {
            addCriterion("approve_amount is not null");
            return (Criteria) this;
        }

        public Criteria andApproveAmountEqualTo(BigDecimal value) {
            addCriterion("approve_amount =", value, "approveAmount");
            return (Criteria) this;
        }

        public Criteria andApproveAmountNotEqualTo(BigDecimal value) {
            addCriterion("approve_amount <>", value, "approveAmount");
            return (Criteria) this;
        }

        public Criteria andApproveAmountGreaterThan(BigDecimal value) {
            addCriterion("approve_amount >", value, "approveAmount");
            return (Criteria) this;
        }

        public Criteria andApproveAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("approve_amount >=", value, "approveAmount");
            return (Criteria) this;
        }

        public Criteria andApproveAmountLessThan(BigDecimal value) {
            addCriterion("approve_amount <", value, "approveAmount");
            return (Criteria) this;
        }

        public Criteria andApproveAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("approve_amount <=", value, "approveAmount");
            return (Criteria) this;
        }

        public Criteria andApproveAmountIn(List<BigDecimal> values) {
            addCriterion("approve_amount in", values, "approveAmount");
            return (Criteria) this;
        }

        public Criteria andApproveAmountNotIn(List<BigDecimal> values) {
            addCriterion("approve_amount not in", values, "approveAmount");
            return (Criteria) this;
        }

        public Criteria andApproveAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("approve_amount between", value1, value2, "approveAmount");
            return (Criteria) this;
        }

        public Criteria andApproveAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("approve_amount not between", value1, value2, "approveAmount");
            return (Criteria) this;
        }

        public Criteria andOverplusEaAmountIsNull() {
            addCriterion("overplus_ea_amount is null");
            return (Criteria) this;
        }

        public Criteria andOverplusEaAmountIsNotNull() {
            addCriterion("overplus_ea_amount is not null");
            return (Criteria) this;
        }

        public Criteria andOverplusEaAmountEqualTo(BigDecimal value) {
            addCriterion("overplus_ea_amount =", value, "overplusEaAmount");
            return (Criteria) this;
        }

        public Criteria andOverplusEaAmountNotEqualTo(BigDecimal value) {
            addCriterion("overplus_ea_amount <>", value, "overplusEaAmount");
            return (Criteria) this;
        }

        public Criteria andOverplusEaAmountGreaterThan(BigDecimal value) {
            addCriterion("overplus_ea_amount >", value, "overplusEaAmount");
            return (Criteria) this;
        }

        public Criteria andOverplusEaAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("overplus_ea_amount >=", value, "overplusEaAmount");
            return (Criteria) this;
        }

        public Criteria andOverplusEaAmountLessThan(BigDecimal value) {
            addCriterion("overplus_ea_amount <", value, "overplusEaAmount");
            return (Criteria) this;
        }

        public Criteria andOverplusEaAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("overplus_ea_amount <=", value, "overplusEaAmount");
            return (Criteria) this;
        }

        public Criteria andOverplusEaAmountIn(List<BigDecimal> values) {
            addCriterion("overplus_ea_amount in", values, "overplusEaAmount");
            return (Criteria) this;
        }

        public Criteria andOverplusEaAmountNotIn(List<BigDecimal> values) {
            addCriterion("overplus_ea_amount not in", values, "overplusEaAmount");
            return (Criteria) this;
        }

        public Criteria andOverplusEaAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("overplus_ea_amount between", value1, value2, "overplusEaAmount");
            return (Criteria) this;
        }

        public Criteria andOverplusEaAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("overplus_ea_amount not between", value1, value2, "overplusEaAmount");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameIsNull() {
            addCriterion("currency_name is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameIsNotNull() {
            addCriterion("currency_name is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameEqualTo(String value) {
            addCriterion("currency_name =", value, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameNotEqualTo(String value) {
            addCriterion("currency_name <>", value, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameGreaterThan(String value) {
            addCriterion("currency_name >", value, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameGreaterThanOrEqualTo(String value) {
            addCriterion("currency_name >=", value, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameLessThan(String value) {
            addCriterion("currency_name <", value, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameLessThanOrEqualTo(String value) {
            addCriterion("currency_name <=", value, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameLike(String value) {
            addCriterion("currency_name like", value, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameNotLike(String value) {
            addCriterion("currency_name not like", value, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameIn(List<String> values) {
            addCriterion("currency_name in", values, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameNotIn(List<String> values) {
            addCriterion("currency_name not in", values, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameBetween(String value1, String value2) {
            addCriterion("currency_name between", value1, value2, "currencyName");
            return (Criteria) this;
        }

        public Criteria andCurrencyNameNotBetween(String value1, String value2) {
            addCriterion("currency_name not between", value1, value2, "currencyName");
            return (Criteria) this;
        }

        public Criteria andSensitiveInfoIsNull() {
            addCriterion("sensitive_info is null");
            return (Criteria) this;
        }

        public Criteria andSensitiveInfoIsNotNull() {
            addCriterion("sensitive_info is not null");
            return (Criteria) this;
        }

        public Criteria andSensitiveInfoEqualTo(String value) {
            addCriterion("sensitive_info =", value, "sensitiveInfo");
            return (Criteria) this;
        }

        public Criteria andSensitiveInfoNotEqualTo(String value) {
            addCriterion("sensitive_info <>", value, "sensitiveInfo");
            return (Criteria) this;
        }

        public Criteria andSensitiveInfoGreaterThan(String value) {
            addCriterion("sensitive_info >", value, "sensitiveInfo");
            return (Criteria) this;
        }

        public Criteria andSensitiveInfoGreaterThanOrEqualTo(String value) {
            addCriterion("sensitive_info >=", value, "sensitiveInfo");
            return (Criteria) this;
        }

        public Criteria andSensitiveInfoLessThan(String value) {
            addCriterion("sensitive_info <", value, "sensitiveInfo");
            return (Criteria) this;
        }

        public Criteria andSensitiveInfoLessThanOrEqualTo(String value) {
            addCriterion("sensitive_info <=", value, "sensitiveInfo");
            return (Criteria) this;
        }

        public Criteria andSensitiveInfoLike(String value) {
            addCriterion("sensitive_info like", value, "sensitiveInfo");
            return (Criteria) this;
        }

        public Criteria andSensitiveInfoNotLike(String value) {
            addCriterion("sensitive_info not like", value, "sensitiveInfo");
            return (Criteria) this;
        }

        public Criteria andSensitiveInfoIn(List<String> values) {
            addCriterion("sensitive_info in", values, "sensitiveInfo");
            return (Criteria) this;
        }

        public Criteria andSensitiveInfoNotIn(List<String> values) {
            addCriterion("sensitive_info not in", values, "sensitiveInfo");
            return (Criteria) this;
        }

        public Criteria andSensitiveInfoBetween(String value1, String value2) {
            addCriterion("sensitive_info between", value1, value2, "sensitiveInfo");
            return (Criteria) this;
        }

        public Criteria andSensitiveInfoNotBetween(String value1, String value2) {
            addCriterion("sensitive_info not between", value1, value2, "sensitiveInfo");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNull() {
            addCriterion("order_status is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNotNull() {
            addCriterion("order_status is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualTo(String value) {
            addCriterion("order_status =", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualTo(String value) {
            addCriterion("order_status <>", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThan(String value) {
            addCriterion("order_status >", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualTo(String value) {
            addCriterion("order_status >=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThan(String value) {
            addCriterion("order_status <", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualTo(String value) {
            addCriterion("order_status <=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLike(String value) {
            addCriterion("order_status like", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotLike(String value) {
            addCriterion("order_status not like", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIn(List<String> values) {
            addCriterion("order_status in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotIn(List<String> values) {
            addCriterion("order_status not in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusBetween(String value1, String value2) {
            addCriterion("order_status between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotBetween(String value1, String value2) {
            addCriterion("order_status not between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}