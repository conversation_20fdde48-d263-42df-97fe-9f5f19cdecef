package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "wbs项目已确认成本汇总表")
public class ProjectCurrentIncomeSummary extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "已确认收入总额（原币）")
    private BigDecimal confirmedIncomeTotalAmount;

    @ApiModelProperty(value = "已确认收入总额（本位币）")
    private BigDecimal standardConfirmedIncomeTotalAmount;

    @ApiModelProperty(value = "已确认成本总额")
    private BigDecimal confirmedCostTotalAmount;

    @ApiModelProperty(value = "已确认毛利率")
    private BigDecimal confirmedGrossProfitRatio;

    @ApiModelProperty(value = "已确认汇兑损益")
    private BigDecimal confirmedExchangeAmount;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public BigDecimal getConfirmedIncomeTotalAmount() {
        return confirmedIncomeTotalAmount;
    }

    public void setConfirmedIncomeTotalAmount(BigDecimal confirmedIncomeTotalAmount) {
        this.confirmedIncomeTotalAmount = confirmedIncomeTotalAmount;
    }

    public BigDecimal getStandardConfirmedIncomeTotalAmount() {
        return standardConfirmedIncomeTotalAmount;
    }

    public void setStandardConfirmedIncomeTotalAmount(BigDecimal standardConfirmedIncomeTotalAmount) {
        this.standardConfirmedIncomeTotalAmount = standardConfirmedIncomeTotalAmount;
    }

    public BigDecimal getConfirmedCostTotalAmount() {
        return confirmedCostTotalAmount;
    }

    public void setConfirmedCostTotalAmount(BigDecimal confirmedCostTotalAmount) {
        this.confirmedCostTotalAmount = confirmedCostTotalAmount;
    }

    public BigDecimal getConfirmedGrossProfitRatio() {
        return confirmedGrossProfitRatio;
    }

    public void setConfirmedGrossProfitRatio(BigDecimal confirmedGrossProfitRatio) {
        this.confirmedGrossProfitRatio = confirmedGrossProfitRatio;
    }

    public BigDecimal getConfirmedExchangeAmount() {
        return confirmedExchangeAmount;
    }

    public void setConfirmedExchangeAmount(BigDecimal confirmedExchangeAmount) {
        this.confirmedExchangeAmount = confirmedExchangeAmount;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", confirmedIncomeTotalAmount=").append(confirmedIncomeTotalAmount);
        sb.append(", standardConfirmedIncomeTotalAmount=").append(standardConfirmedIncomeTotalAmount);
        sb.append(", confirmedCostTotalAmount=").append(confirmedCostTotalAmount);
        sb.append(", confirmedGrossProfitRatio=").append(confirmedGrossProfitRatio);
        sb.append(", confirmedExchangeAmount=").append(confirmedExchangeAmount);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}