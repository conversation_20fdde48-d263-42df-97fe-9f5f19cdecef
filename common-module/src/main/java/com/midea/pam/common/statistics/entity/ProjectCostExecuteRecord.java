package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "项目成本执行表")
public class ProjectCostExecuteRecord extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "OU")
    private Long ouId;

    @ApiModelProperty(value = "批次ID")
    private Long batchNum;

    @ApiModelProperty(value = "执行开始时间")
    private Date startTime;

    @ApiModelProperty(value = "执行项目总数")
    private Integer num;

    @ApiModelProperty(value = "成功数量")
    private Integer successNum;

    @ApiModelProperty(value = "失败数量")
    private Integer failureNum;

    @ApiModelProperty(value = "执行完成")
    private Date endTime;

    @ApiModelProperty(value = "执行花费时间，单位（s）")
    private Long costTime;

    @ApiModelProperty(value = "状态：1：新建；2：执行成功：3：执行失败")
    private Integer status;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "是否为WBS项目（0否1是）")
    private Integer orNotWbs;

    private static final long serialVersionUID = 1L;

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public Long getBatchNum() {
        return batchNum;
    }

    public void setBatchNum(Long batchNum) {
        this.batchNum = batchNum;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Integer getSuccessNum() {
        return successNum;
    }

    public void setSuccessNum(Integer successNum) {
        this.successNum = successNum;
    }

    public Integer getFailureNum() {
        return failureNum;
    }

    public void setFailureNum(Integer failureNum) {
        this.failureNum = failureNum;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getCostTime() {
        return costTime;
    }

    public void setCostTime(Long costTime) {
        this.costTime = costTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Integer getOrNotWbs() {
        return orNotWbs;
    }

    public void setOrNotWbs(Integer orNotWbs) {
        this.orNotWbs = orNotWbs;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", ouId=").append(ouId);
        sb.append(", batchNum=").append(batchNum);
        sb.append(", startTime=").append(startTime);
        sb.append(", num=").append(num);
        sb.append(", successNum=").append(successNum);
        sb.append(", failureNum=").append(failureNum);
        sb.append(", endTime=").append(endTime);
        sb.append(", costTime=").append(costTime);
        sb.append(", status=").append(status);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", orNotWbs=").append(orNotWbs);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}