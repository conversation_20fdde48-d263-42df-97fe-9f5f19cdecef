package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "工时明细表")
public class WorkingHourDetail extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "执行ID")
    private Long executeId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目状态")
    private Integer projectStatus;

    @ApiModelProperty(value = "事业部id")
    private Long departmentId;

    @ApiModelProperty(value = "事业部名称")
    private String departmentName;

    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    @ApiModelProperty(value = "填报人姓名")
    private String fillByName;

    @ApiModelProperty(value = "填报人mip")
    private String fillByMip;

    @ApiModelProperty(value = "填报人部门")
    private String applyOrg;

    @ApiModelProperty(value = "出勤日期")
    private Date attendanceDate;

    @ApiModelProperty(value = "填报日期")
    private Date fillDate;

    @ApiModelProperty(value = "填报工时")
    private BigDecimal fillWorkingHours;

    @ApiModelProperty(value = "考勤时长")
    private BigDecimal ihrAttendHours;

    @ApiModelProperty(value = "审批人姓名")
    private String approveByName;

    @ApiModelProperty(value = "审批人mip")
    private String approveByMip;

    @ApiModelProperty(value = "审批日期")
    private Date approveDate;

    @ApiModelProperty(value = "审批工时")
    private BigDecimal approveWorkingHours;

    @ApiModelProperty(value = "工时状态")
    private Byte status;

    @ApiModelProperty(value = "RDM确认工时")
    private BigDecimal rdmWorkingHours;

    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "业务分类id")
    private Long unitId;

    @ApiModelProperty(value = "业务分类名称")
    private String unitName;

    @ApiModelProperty(value = "业务实体id")
    private Long ouId;

    @ApiModelProperty(value = "业务实体名称")
    private String ouName;

    @ApiModelProperty(value = "是否删除")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "是否结转，0:未，1:已")
    private Boolean costCollectionFlag;

    @ApiModelProperty(value = "是否开票，0:未，1:已")
    private Boolean invoiceApplyFlag;

    @ApiModelProperty(value = "供应商编号")
    private String vendorCode;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "是否跨单位工时 0否/1是")
    private Boolean crossUnitLaborCost;

    @ApiModelProperty(value = "wbs预算编码")
    private String wbsBudgetCode;

    @ApiModelProperty(value = "wbs业务角色名称")
    private String laborWbsCostName;

    private static final long serialVersionUID = 1L;

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public Integer getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(Integer projectStatus) {
        this.projectStatus = projectStatus;
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName == null ? null : departmentName.trim();
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public String getFillByName() {
        return fillByName;
    }

    public void setFillByName(String fillByName) {
        this.fillByName = fillByName == null ? null : fillByName.trim();
    }

    public String getFillByMip() {
        return fillByMip;
    }

    public void setFillByMip(String fillByMip) {
        this.fillByMip = fillByMip == null ? null : fillByMip.trim();
    }

    public String getApplyOrg() {
        return applyOrg;
    }

    public void setApplyOrg(String applyOrg) {
        this.applyOrg = applyOrg == null ? null : applyOrg.trim();
    }

    public Date getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(Date attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public Date getFillDate() {
        return fillDate;
    }

    public void setFillDate(Date fillDate) {
        this.fillDate = fillDate;
    }

    public BigDecimal getFillWorkingHours() {
        return fillWorkingHours;
    }

    public void setFillWorkingHours(BigDecimal fillWorkingHours) {
        this.fillWorkingHours = fillWorkingHours;
    }

    public BigDecimal getIhrAttendHours() {
        return ihrAttendHours;
    }

    public void setIhrAttendHours(BigDecimal ihrAttendHours) {
        this.ihrAttendHours = ihrAttendHours;
    }

    public String getApproveByName() {
        return approveByName;
    }

    public void setApproveByName(String approveByName) {
        this.approveByName = approveByName == null ? null : approveByName.trim();
    }

    public String getApproveByMip() {
        return approveByMip;
    }

    public void setApproveByMip(String approveByMip) {
        this.approveByMip = approveByMip == null ? null : approveByMip.trim();
    }

    public Date getApproveDate() {
        return approveDate;
    }

    public void setApproveDate(Date approveDate) {
        this.approveDate = approveDate;
    }

    public BigDecimal getApproveWorkingHours() {
        return approveWorkingHours;
    }

    public void setApproveWorkingHours(BigDecimal approveWorkingHours) {
        this.approveWorkingHours = approveWorkingHours;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public BigDecimal getRdmWorkingHours() {
        return rdmWorkingHours;
    }

    public void setRdmWorkingHours(BigDecimal rdmWorkingHours) {
        this.rdmWorkingHours = rdmWorkingHours;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType == null ? null : projectType.trim();
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Boolean getCostCollectionFlag() {
        return costCollectionFlag;
    }

    public void setCostCollectionFlag(Boolean costCollectionFlag) {
        this.costCollectionFlag = costCollectionFlag;
    }

    public Boolean getInvoiceApplyFlag() {
        return invoiceApplyFlag;
    }

    public void setInvoiceApplyFlag(Boolean invoiceApplyFlag) {
        this.invoiceApplyFlag = invoiceApplyFlag;
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode == null ? null : vendorCode.trim();
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName == null ? null : roleName.trim();
    }

    public Boolean getCrossUnitLaborCost() {
        return crossUnitLaborCost;
    }

    public void setCrossUnitLaborCost(Boolean crossUnitLaborCost) {
        this.crossUnitLaborCost = crossUnitLaborCost;
    }

    public String getWbsBudgetCode() {
        return wbsBudgetCode;
    }

    public void setWbsBudgetCode(String wbsBudgetCode) {
        this.wbsBudgetCode = wbsBudgetCode == null ? null : wbsBudgetCode.trim();
    }

    public String getLaborWbsCostName() {
        return laborWbsCostName;
    }

    public void setLaborWbsCostName(String laborWbsCostName) {
        this.laborWbsCostName = laborWbsCostName == null ? null : laborWbsCostName.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", executeId=").append(executeId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectStatus=").append(projectStatus);
        sb.append(", departmentId=").append(departmentId);
        sb.append(", departmentName=").append(departmentName);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", fillByName=").append(fillByName);
        sb.append(", fillByMip=").append(fillByMip);
        sb.append(", applyOrg=").append(applyOrg);
        sb.append(", attendanceDate=").append(attendanceDate);
        sb.append(", fillDate=").append(fillDate);
        sb.append(", fillWorkingHours=").append(fillWorkingHours);
        sb.append(", ihrAttendHours=").append(ihrAttendHours);
        sb.append(", approveByName=").append(approveByName);
        sb.append(", approveByMip=").append(approveByMip);
        sb.append(", approveDate=").append(approveDate);
        sb.append(", approveWorkingHours=").append(approveWorkingHours);
        sb.append(", status=").append(status);
        sb.append(", rdmWorkingHours=").append(rdmWorkingHours);
        sb.append(", projectType=").append(projectType);
        sb.append(", unitId=").append(unitId);
        sb.append(", unitName=").append(unitName);
        sb.append(", ouId=").append(ouId);
        sb.append(", ouName=").append(ouName);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", costCollectionFlag=").append(costCollectionFlag);
        sb.append(", invoiceApplyFlag=").append(invoiceApplyFlag);
        sb.append(", vendorCode=").append(vendorCode);
        sb.append(", roleName=").append(roleName);
        sb.append(", crossUnitLaborCost=").append(crossUnitLaborCost);
        sb.append(", wbsBudgetCode=").append(wbsBudgetCode);
        sb.append(", laborWbsCostName=").append(laborWbsCostName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}