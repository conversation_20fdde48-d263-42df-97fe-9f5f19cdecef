package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProjectCostFeeItemRecord extends LongIdEntity implements Serializable {
    private Long id;

    private Long executeId;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private Long summaryId;

    private Long feeItemId;

    private String feeItemName;

    private BigDecimal totalCost;

    private BigDecimal incurredCost;

    private BigDecimal pendingCost;

    private BigDecimal eaAvailableAmount;

    private BigDecimal budget;

    private BigDecimal remainderBudget;

    private BigDecimal incurredRatio;

    @ApiModelProperty(value = "类型 1:物料成本,2:外部人力成本(费用),3:差旅成本,4:费用成本(包含工单)")
    private Integer type;

    private Boolean deletedFlag;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public Long getSummaryId() {
        return summaryId;
    }

    public void setSummaryId(Long summaryId) {
        this.summaryId = summaryId;
    }

    public Long getFeeItemId() {
        return feeItemId;
    }

    public void setFeeItemId(Long feeItemId) {
        this.feeItemId = feeItemId;
    }

    public String getFeeItemName() {
        return feeItemName;
    }

    public void setFeeItemName(String feeItemName) {
        this.feeItemName = feeItemName == null ? null : feeItemName.trim();
    }

    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    public BigDecimal getIncurredCost() {
        return incurredCost;
    }

    public void setIncurredCost(BigDecimal incurredCost) {
        this.incurredCost = incurredCost;
    }

    public BigDecimal getPendingCost() {
        return pendingCost;
    }

    public void setPendingCost(BigDecimal pendingCost) {
        this.pendingCost = pendingCost;
    }

    public BigDecimal getEaAvailableAmount() {
        return eaAvailableAmount;
    }

    public void setEaAvailableAmount(BigDecimal eaAvailableAmount) {
        this.eaAvailableAmount = eaAvailableAmount;
    }

    public BigDecimal getBudget() {
        return budget;
    }

    public void setBudget(BigDecimal budget) {
        this.budget = budget;
    }

    public BigDecimal getRemainderBudget() {
        return remainderBudget;
    }

    public void setRemainderBudget(BigDecimal remainderBudget) {
        this.remainderBudget = remainderBudget;
    }

    public BigDecimal getIncurredRatio() {
        return incurredRatio;
    }

    public void setIncurredRatio(BigDecimal incurredRatio) {
        this.incurredRatio = incurredRatio;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", summaryId=").append(summaryId);
        sb.append(", feeItemId=").append(feeItemId);
        sb.append(", feeItemName=").append(feeItemName);
        sb.append(", totalCost=").append(totalCost);
        sb.append(", incurredCost=").append(incurredCost);
        sb.append(", pendingCost=").append(pendingCost);
        sb.append(", eaAvailableAmount=").append(eaAvailableAmount);
        sb.append(", budget=").append(budget);
        sb.append(", remainderBudget=").append(remainderBudget);
        sb.append(", incurredRatio=").append(incurredRatio);
        sb.append(", type=").append(type);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}