package com.midea.pam.common.statistics.excelVo;

import com.midea.pam.common.util.DateUtils;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 **/
public class ReportProjectProfitBudgetExcelVO {
    // 项目预算
    @Excel(name = "物料预算",width = 20)
    private BigDecimal projectBudgetHardwareWithoutTax;

    @Excel(name = "人力预算",width = 20)
    private BigDecimal projectBudgetLabourWithoutTax;

    @Excel(name = "差旅费预算",width = 20)
    private BigDecimal projectBudgetTravelWithoutTax;

    @Excel(name = "非差旅费预算",width = 20)
    private BigDecimal projectBudgetOtherWithoutTax;

    public BigDecimal getProjectBudgetHardwareWithoutTax() {
        return projectBudgetHardwareWithoutTax;
    }

    public void setProjectBudgetHardwareWithoutTax(BigDecimal projectBudgetHardwareWithoutTax) {
        this.projectBudgetHardwareWithoutTax = projectBudgetHardwareWithoutTax;
    }

    public BigDecimal getProjectBudgetLabourWithoutTax() {
        return projectBudgetLabourWithoutTax;
    }

    public void setProjectBudgetLabourWithoutTax(BigDecimal projectBudgetLabourWithoutTax) {
        this.projectBudgetLabourWithoutTax = projectBudgetLabourWithoutTax;
    }

    public BigDecimal getProjectBudgetTravelWithoutTax() {
        return projectBudgetTravelWithoutTax;
    }

    public void setProjectBudgetTravelWithoutTax(BigDecimal projectBudgetTravelWithoutTax) {
        this.projectBudgetTravelWithoutTax = projectBudgetTravelWithoutTax;
    }

    public BigDecimal getProjectBudgetOtherWithoutTax() {
        return projectBudgetOtherWithoutTax;
    }

    public void setProjectBudgetOtherWithoutTax(BigDecimal projectBudgetOtherWithoutTax) {
        this.projectBudgetOtherWithoutTax = projectBudgetOtherWithoutTax;
    }
}