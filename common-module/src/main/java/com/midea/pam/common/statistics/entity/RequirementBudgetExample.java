package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RequirementBudgetExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public RequirementBudgetExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdIsNull() {
            addCriterion("project_wbs_receipts_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdIsNotNull() {
            addCriterion("project_wbs_receipts_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdEqualTo(Long value) {
            addCriterion("project_wbs_receipts_id =", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdNotEqualTo(Long value) {
            addCriterion("project_wbs_receipts_id <>", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdGreaterThan(Long value) {
            addCriterion("project_wbs_receipts_id >", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_wbs_receipts_id >=", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdLessThan(Long value) {
            addCriterion("project_wbs_receipts_id <", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdLessThanOrEqualTo(Long value) {
            addCriterion("project_wbs_receipts_id <=", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdIn(List<Long> values) {
            addCriterion("project_wbs_receipts_id in", values, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdNotIn(List<Long> values) {
            addCriterion("project_wbs_receipts_id not in", values, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdBetween(Long value1, Long value2) {
            addCriterion("project_wbs_receipts_id between", value1, value2, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdNotBetween(Long value1, Long value2) {
            addCriterion("project_wbs_receipts_id not between", value1, value2, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIsNull() {
            addCriterion("requirement_code is null");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIsNotNull() {
            addCriterion("requirement_code is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeEqualTo(String value) {
            addCriterion("requirement_code =", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotEqualTo(String value) {
            addCriterion("requirement_code <>", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeGreaterThan(String value) {
            addCriterion("requirement_code >", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeGreaterThanOrEqualTo(String value) {
            addCriterion("requirement_code >=", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLessThan(String value) {
            addCriterion("requirement_code <", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLessThanOrEqualTo(String value) {
            addCriterion("requirement_code <=", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLike(String value) {
            addCriterion("requirement_code like", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotLike(String value) {
            addCriterion("requirement_code not like", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIn(List<String> values) {
            addCriterion("requirement_code in", values, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotIn(List<String> values) {
            addCriterion("requirement_code not in", values, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeBetween(String value1, String value2) {
            addCriterion("requirement_code between", value1, value2, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotBetween(String value1, String value2) {
            addCriterion("requirement_code not between", value1, value2, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityIsNull() {
            addCriterion("unreleased_quantity is null");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityIsNotNull() {
            addCriterion("unreleased_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityEqualTo(Integer value) {
            addCriterion("unreleased_quantity =", value, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityNotEqualTo(Integer value) {
            addCriterion("unreleased_quantity <>", value, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityGreaterThan(Integer value) {
            addCriterion("unreleased_quantity >", value, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("unreleased_quantity >=", value, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityLessThan(Integer value) {
            addCriterion("unreleased_quantity <", value, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("unreleased_quantity <=", value, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityIn(List<Integer> values) {
            addCriterion("unreleased_quantity in", values, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityNotIn(List<Integer> values) {
            addCriterion("unreleased_quantity not in", values, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityBetween(Integer value1, Integer value2) {
            addCriterion("unreleased_quantity between", value1, value2, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("unreleased_quantity not between", value1, value2, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiptsTypeIsNull() {
            addCriterion("receipts_type is null");
            return (Criteria) this;
        }

        public Criteria andReceiptsTypeIsNotNull() {
            addCriterion("receipts_type is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptsTypeEqualTo(String value) {
            addCriterion("receipts_type =", value, "receiptsType");
            return (Criteria) this;
        }

        public Criteria andReceiptsTypeNotEqualTo(String value) {
            addCriterion("receipts_type <>", value, "receiptsType");
            return (Criteria) this;
        }

        public Criteria andReceiptsTypeGreaterThan(String value) {
            addCriterion("receipts_type >", value, "receiptsType");
            return (Criteria) this;
        }

        public Criteria andReceiptsTypeGreaterThanOrEqualTo(String value) {
            addCriterion("receipts_type >=", value, "receiptsType");
            return (Criteria) this;
        }

        public Criteria andReceiptsTypeLessThan(String value) {
            addCriterion("receipts_type <", value, "receiptsType");
            return (Criteria) this;
        }

        public Criteria andReceiptsTypeLessThanOrEqualTo(String value) {
            addCriterion("receipts_type <=", value, "receiptsType");
            return (Criteria) this;
        }

        public Criteria andReceiptsTypeLike(String value) {
            addCriterion("receipts_type like", value, "receiptsType");
            return (Criteria) this;
        }

        public Criteria andReceiptsTypeNotLike(String value) {
            addCriterion("receipts_type not like", value, "receiptsType");
            return (Criteria) this;
        }

        public Criteria andReceiptsTypeIn(List<String> values) {
            addCriterion("receipts_type in", values, "receiptsType");
            return (Criteria) this;
        }

        public Criteria andReceiptsTypeNotIn(List<String> values) {
            addCriterion("receipts_type not in", values, "receiptsType");
            return (Criteria) this;
        }

        public Criteria andReceiptsTypeBetween(String value1, String value2) {
            addCriterion("receipts_type between", value1, value2, "receiptsType");
            return (Criteria) this;
        }

        public Criteria andReceiptsTypeNotBetween(String value1, String value2) {
            addCriterion("receipts_type not between", value1, value2, "receiptsType");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountIsNull() {
            addCriterion("budget_occupied_amount is null");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountIsNotNull() {
            addCriterion("budget_occupied_amount is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountEqualTo(BigDecimal value) {
            addCriterion("budget_occupied_amount =", value, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountNotEqualTo(BigDecimal value) {
            addCriterion("budget_occupied_amount <>", value, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountGreaterThan(BigDecimal value) {
            addCriterion("budget_occupied_amount >", value, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_occupied_amount >=", value, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountLessThan(BigDecimal value) {
            addCriterion("budget_occupied_amount <", value, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_occupied_amount <=", value, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountIn(List<BigDecimal> values) {
            addCriterion("budget_occupied_amount in", values, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountNotIn(List<BigDecimal> values) {
            addCriterion("budget_occupied_amount not in", values, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_occupied_amount between", value1, value2, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_occupied_amount not between", value1, value2, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andDownAmountIsNull() {
            addCriterion("down_amount is null");
            return (Criteria) this;
        }

        public Criteria andDownAmountIsNotNull() {
            addCriterion("down_amount is not null");
            return (Criteria) this;
        }

        public Criteria andDownAmountEqualTo(BigDecimal value) {
            addCriterion("down_amount =", value, "downAmount");
            return (Criteria) this;
        }

        public Criteria andDownAmountNotEqualTo(BigDecimal value) {
            addCriterion("down_amount <>", value, "downAmount");
            return (Criteria) this;
        }

        public Criteria andDownAmountGreaterThan(BigDecimal value) {
            addCriterion("down_amount >", value, "downAmount");
            return (Criteria) this;
        }

        public Criteria andDownAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("down_amount >=", value, "downAmount");
            return (Criteria) this;
        }

        public Criteria andDownAmountLessThan(BigDecimal value) {
            addCriterion("down_amount <", value, "downAmount");
            return (Criteria) this;
        }

        public Criteria andDownAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("down_amount <=", value, "downAmount");
            return (Criteria) this;
        }

        public Criteria andDownAmountIn(List<BigDecimal> values) {
            addCriterion("down_amount in", values, "downAmount");
            return (Criteria) this;
        }

        public Criteria andDownAmountNotIn(List<BigDecimal> values) {
            addCriterion("down_amount not in", values, "downAmount");
            return (Criteria) this;
        }

        public Criteria andDownAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("down_amount between", value1, value2, "downAmount");
            return (Criteria) this;
        }

        public Criteria andDownAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("down_amount not between", value1, value2, "downAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingCostAmountIsNull() {
            addCriterion("remaining_cost_amount is null");
            return (Criteria) this;
        }

        public Criteria andRemainingCostAmountIsNotNull() {
            addCriterion("remaining_cost_amount is not null");
            return (Criteria) this;
        }

        public Criteria andRemainingCostAmountEqualTo(BigDecimal value) {
            addCriterion("remaining_cost_amount =", value, "remainingCostAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingCostAmountNotEqualTo(BigDecimal value) {
            addCriterion("remaining_cost_amount <>", value, "remainingCostAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingCostAmountGreaterThan(BigDecimal value) {
            addCriterion("remaining_cost_amount >", value, "remainingCostAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingCostAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("remaining_cost_amount >=", value, "remainingCostAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingCostAmountLessThan(BigDecimal value) {
            addCriterion("remaining_cost_amount <", value, "remainingCostAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingCostAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("remaining_cost_amount <=", value, "remainingCostAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingCostAmountIn(List<BigDecimal> values) {
            addCriterion("remaining_cost_amount in", values, "remainingCostAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingCostAmountNotIn(List<BigDecimal> values) {
            addCriterion("remaining_cost_amount not in", values, "remainingCostAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingCostAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("remaining_cost_amount between", value1, value2, "remainingCostAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingCostAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("remaining_cost_amount not between", value1, value2, "remainingCostAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeIsNull() {
            addCriterion("budget_type is null");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeIsNotNull() {
            addCriterion("budget_type is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeEqualTo(Integer value) {
            addCriterion("budget_type =", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeNotEqualTo(Integer value) {
            addCriterion("budget_type <>", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeGreaterThan(Integer value) {
            addCriterion("budget_type >", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("budget_type >=", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeLessThan(Integer value) {
            addCriterion("budget_type <", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeLessThanOrEqualTo(Integer value) {
            addCriterion("budget_type <=", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeIn(List<Integer> values) {
            addCriterion("budget_type in", values, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeNotIn(List<Integer> values) {
            addCriterion("budget_type not in", values, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeBetween(Integer value1, Integer value2) {
            addCriterion("budget_type between", value1, value2, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("budget_type not between", value1, value2, "budgetType");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNull() {
            addCriterion("wbs_summary_code is null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNotNull() {
            addCriterion("wbs_summary_code is not null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeEqualTo(String value) {
            addCriterion("wbs_summary_code =", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotEqualTo(String value) {
            addCriterion("wbs_summary_code <>", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThan(String value) {
            addCriterion("wbs_summary_code >", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code >=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThan(String value) {
            addCriterion("wbs_summary_code <", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code <=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLike(String value) {
            addCriterion("wbs_summary_code like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotLike(String value) {
            addCriterion("wbs_summary_code not like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIn(List<String> values) {
            addCriterion("wbs_summary_code in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotIn(List<String> values) {
            addCriterion("wbs_summary_code not in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeBetween(String value1, String value2) {
            addCriterion("wbs_summary_code between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotBetween(String value1, String value2) {
            addCriterion("wbs_summary_code not between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNull() {
            addCriterion("activity_code is null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNotNull() {
            addCriterion("activity_code is not null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeEqualTo(String value) {
            addCriterion("activity_code =", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotEqualTo(String value) {
            addCriterion("activity_code <>", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThan(String value) {
            addCriterion("activity_code >", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("activity_code >=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThan(String value) {
            addCriterion("activity_code <", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThanOrEqualTo(String value) {
            addCriterion("activity_code <=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLike(String value) {
            addCriterion("activity_code like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotLike(String value) {
            addCriterion("activity_code not like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIn(List<String> values) {
            addCriterion("activity_code in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotIn(List<String> values) {
            addCriterion("activity_code not in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeBetween(String value1, String value2) {
            addCriterion("activity_code between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotBetween(String value1, String value2) {
            addCriterion("activity_code not between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDataTimeIsNull() {
            addCriterion("data_time is null");
            return (Criteria) this;
        }

        public Criteria andDataTimeIsNotNull() {
            addCriterion("data_time is not null");
            return (Criteria) this;
        }

        public Criteria andDataTimeEqualTo(Date value) {
            addCriterion("data_time =", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotEqualTo(Date value) {
            addCriterion("data_time <>", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeGreaterThan(Date value) {
            addCriterion("data_time >", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("data_time >=", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeLessThan(Date value) {
            addCriterion("data_time <", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeLessThanOrEqualTo(Date value) {
            addCriterion("data_time <=", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeIn(List<Date> values) {
            addCriterion("data_time in", values, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotIn(List<Date> values) {
            addCriterion("data_time not in", values, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeBetween(Date value1, Date value2) {
            addCriterion("data_time between", value1, value2, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotBetween(Date value1, Date value2) {
            addCriterion("data_time not between", value1, value2, "dataTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}