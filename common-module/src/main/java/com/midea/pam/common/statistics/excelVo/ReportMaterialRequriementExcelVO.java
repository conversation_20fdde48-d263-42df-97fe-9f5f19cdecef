package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/4/23
 * 任务缺料报表ExcelVO
 */
public class ReportMaterialRequriementExcelVO {

    @Excel(name = "工单任务号", width = 15)
    private String ticketTaskCode;

    @Excel(name = "项目号")
    private String projectCode;

    @Excel(name = "开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date ticketPlanTime;

    @Excel(name = "装配件编码")
    private String theassemblyCode;

    @Excel(name = "装配件名称")
    private String theassemblyDes;

    @Excel(name = "装配件单位")
    private String theassemblyUnit;

    @Excel(name = "模组编码")
    private String moduleCode;

    @Excel(name = "模组名称")
    private String moduleName;

    @Excel(name = "任务状态", replace = {"未发布_0", "已发布_1"})
    private Integer taskStatus;

    @Excel(name = "计划数量")
    private BigDecimal planNum;

    @Excel(name = "组件编码")
    private String erpCode;

    @Excel(name = "组件名称")
    private String materielDescr;

    @Excel(name = "单位")
    private String materialUnit;

    @Excel(name = "供应子库")
    private String subinventoryCode;

    @Excel(name = "任务需求量")
    private BigDecimal taskRequirementNum;

    @Excel(name = "本期需求总数量")
    private BigDecimal totalRequirementNum;

    @Excel(name = "已发料数量")
    private BigDecimal issuedMaterialNum;

    @Excel(name = "未发料数量")
    private BigDecimal unissuedMaterialNum;

    @Excel(name = "现有量")
    private BigDecimal storageCurrentNum;

    @Excel(name = "可用量")
    private BigDecimal availableNum;

    @Excel(name = "本任务缺料数量")
    private BigDecimal taskRequriementNum;

    @Excel(name = "本期总缺料数量")
    private BigDecimal totalRequriementNum;

    @Excel(name = "在途po总数")
    private BigDecimal deliveredPoTotalNum;

    @Excel(name = "发运日期")
    private String needByDate;

    @Excel(name = "在途订单明细", width = 16)
    private String deliveredPoDetail;

    @Excel(name = "采购员")
    private String buyer;

    @Excel(name = "物料类型")
    private String materialCategory;

    public String getTicketTaskCode() {
        return ticketTaskCode;
    }

    public void setTicketTaskCode(String ticketTaskCode) {
        this.ticketTaskCode = ticketTaskCode;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public Date getTicketPlanTime() {
        return ticketPlanTime;
    }

    public void setTicketPlanTime(Date ticketPlanTime) {
        this.ticketPlanTime = ticketPlanTime;
    }

    public String getTheassemblyCode() {
        return theassemblyCode;
    }

    public void setTheassemblyCode(String theassemblyCode) {
        this.theassemblyCode = theassemblyCode;
    }

    public String getTheassemblyDes() {
        return theassemblyDes;
    }

    public void setTheassemblyDes(String theassemblyDes) {
        this.theassemblyDes = theassemblyDes;
    }

    public String getTheassemblyUnit() {
        return theassemblyUnit;
    }

    public void setTheassemblyUnit(String theassemblyUnit) {
        this.theassemblyUnit = theassemblyUnit;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public BigDecimal getPlanNum() {
        return planNum;
    }

    public void setPlanNum(BigDecimal planNum) {
        this.planNum = planNum;
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode;
    }

    public String getMaterielDescr() {
        return materielDescr;
    }

    public void setMaterielDescr(String materielDescr) {
        this.materielDescr = materielDescr;
    }

    public String getMaterialUnit() {
        return materialUnit;
    }

    public void setMaterialUnit(String materialUnit) {
        this.materialUnit = materialUnit;
    }

    public String getSubinventoryCode() {
        return subinventoryCode;
    }

    public void setSubinventoryCode(String subinventoryCode) {
        this.subinventoryCode = subinventoryCode;
    }

    public BigDecimal getTaskRequirementNum() {
        return taskRequirementNum;
    }

    public void setTaskRequirementNum(BigDecimal taskRequirementNum) {
        this.taskRequirementNum = taskRequirementNum;
    }

    public BigDecimal getTotalRequirementNum() {
        return totalRequirementNum;
    }

    public void setTotalRequirementNum(BigDecimal totalRequirementNum) {
        this.totalRequirementNum = totalRequirementNum;
    }

    public BigDecimal getIssuedMaterialNum() {
        return issuedMaterialNum;
    }

    public void setIssuedMaterialNum(BigDecimal issuedMaterialNum) {
        this.issuedMaterialNum = issuedMaterialNum;
    }

    public BigDecimal getStorageCurrentNum() {
        return storageCurrentNum;
    }

    public void setStorageCurrentNum(BigDecimal storageCurrentNum) {
        this.storageCurrentNum = storageCurrentNum;
    }

    public BigDecimal getAvailableNum() {
        return availableNum;
    }

    public void setAvailableNum(BigDecimal availableNum) {
        this.availableNum = availableNum;
    }

    public BigDecimal getTaskRequriementNum() {
        return taskRequriementNum;
    }

    public void setTaskRequriementNum(BigDecimal taskRequriementNum) {
        this.taskRequriementNum = taskRequriementNum;
    }

    public BigDecimal getTotalRequriementNum() {
        return totalRequriementNum;
    }

    public void setTotalRequriementNum(BigDecimal totalRequriementNum) {
        this.totalRequriementNum = totalRequriementNum;
    }

    public BigDecimal getDeliveredPoTotalNum() {
        return deliveredPoTotalNum;
    }

    public void setDeliveredPoTotalNum(BigDecimal deliveredPoTotalNum) {
        this.deliveredPoTotalNum = deliveredPoTotalNum;
    }

    public String getNeedByDate() {
        return needByDate;
    }

    public void setNeedByDate(String needByDate) {
        this.needByDate = needByDate;
    }

    public String getDeliveredPoDetail() {
        return deliveredPoDetail;
    }

    public void setDeliveredPoDetail(String deliveredPoDetail) {
        this.deliveredPoDetail = deliveredPoDetail;
    }

    public String getBuyer() {
        return buyer;
    }

    public void setBuyer(String buyer) {
        this.buyer = buyer;
    }

    public BigDecimal getUnissuedMaterialNum() {
        return unissuedMaterialNum;
    }

    public void setUnissuedMaterialNum(BigDecimal unissuedMaterialNum) {
        this.unissuedMaterialNum = unissuedMaterialNum;
    }

    public String getMaterialCategory() {
        return materialCategory;
    }

    public void setMaterialCategory(String materialCategory) {
        this.materialCategory = materialCategory;
    }
}
