package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ReportMilepostDesignPlanChange extends LongIdEntity implements Serializable {
    private Long id;

    private Long reportId;

    private Long executeId;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private Long projectTypeId;

    private String projectTypeName;

    private Long unitId;

    private String unitName;

    private String managerName;

    private Long managerId;

    private String modelName;

    private Long modelId;

    private String modelPamCode;

    private String materialName;

    private Long materialId;

    private String materialPamCode;

    private String materialErpCode;

    private String materialDesc;

    private String materialUnit;

    private String materialUnitCode;

    private BigDecimal modelNum;

    private BigDecimal materialChangeNum;

    private BigDecimal materialChangeTotalNum;

    private BigDecimal materialChangePrice;

    private BigDecimal materialChangeTotalAmount;

    private Integer materialCostType;

    private Long changeRecordId;

    private Date changeTime;

    private Long changeUserId;

    private String changeUserName;

    private String changeReason;

    private String changeRemark;

    private Integer changeType;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public Long getProjectTypeId() {
        return projectTypeId;
    }

    public void setProjectTypeId(Long projectTypeId) {
        this.projectTypeId = projectTypeId;
    }

    public String getProjectTypeName() {
        return projectTypeName;
    }

    public void setProjectTypeName(String projectTypeName) {
        this.projectTypeName = projectTypeName == null ? null : projectTypeName.trim();
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName == null ? null : managerName.trim();
    }

    public Long getManagerId() {
        return managerId;
    }

    public void setManagerId(Long managerId) {
        this.managerId = managerId;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName == null ? null : modelName.trim();
    }

    public Long getModelId() {
        return modelId;
    }

    public void setModelId(Long modelId) {
        this.modelId = modelId;
    }

    public String getModelPamCode() {
        return modelPamCode;
    }

    public void setModelPamCode(String modelPamCode) {
        this.modelPamCode = modelPamCode == null ? null : modelPamCode.trim();
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName == null ? null : materialName.trim();
    }

    public Long getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Long materialId) {
        this.materialId = materialId;
    }

    public String getMaterialPamCode() {
        return materialPamCode;
    }

    public void setMaterialPamCode(String materialPamCode) {
        this.materialPamCode = materialPamCode == null ? null : materialPamCode.trim();
    }

    public String getMaterialErpCode() {
        return materialErpCode;
    }

    public void setMaterialErpCode(String materialErpCode) {
        this.materialErpCode = materialErpCode == null ? null : materialErpCode.trim();
    }

    public String getMaterialDesc() {
        return materialDesc;
    }

    public void setMaterialDesc(String materialDesc) {
        this.materialDesc = materialDesc == null ? null : materialDesc.trim();
    }

    public String getMaterialUnit() {
        return materialUnit;
    }

    public void setMaterialUnit(String materialUnit) {
        this.materialUnit = materialUnit == null ? null : materialUnit.trim();
    }

    public String getMaterialUnitCode() {
        return materialUnitCode;
    }

    public void setMaterialUnitCode(String materialUnitCode) {
        this.materialUnitCode = materialUnitCode == null ? null : materialUnitCode.trim();
    }

    public BigDecimal getModelNum() {
        return modelNum;
    }

    public void setModelNum(BigDecimal modelNum) {
        this.modelNum = modelNum;
    }

    public BigDecimal getMaterialChangeNum() {
        return materialChangeNum;
    }

    public void setMaterialChangeNum(BigDecimal materialChangeNum) {
        this.materialChangeNum = materialChangeNum;
    }

    public BigDecimal getMaterialChangeTotalNum() {
        return materialChangeTotalNum;
    }

    public void setMaterialChangeTotalNum(BigDecimal materialChangeTotalNum) {
        this.materialChangeTotalNum = materialChangeTotalNum;
    }

    public BigDecimal getMaterialChangePrice() {
        return materialChangePrice;
    }

    public void setMaterialChangePrice(BigDecimal materialChangePrice) {
        this.materialChangePrice = materialChangePrice;
    }

    public BigDecimal getMaterialChangeTotalAmount() {
        return materialChangeTotalAmount;
    }

    public void setMaterialChangeTotalAmount(BigDecimal materialChangeTotalAmount) {
        this.materialChangeTotalAmount = materialChangeTotalAmount;
    }

    public Integer getMaterialCostType() {
        return materialCostType;
    }

    public void setMaterialCostType(Integer materialCostType) {
        this.materialCostType = materialCostType;
    }

    public Long getChangeRecordId() {
        return changeRecordId;
    }

    public void setChangeRecordId(Long changeRecordId) {
        this.changeRecordId = changeRecordId;
    }

    public Date getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(Date changeTime) {
        this.changeTime = changeTime;
    }

    public Long getChangeUserId() {
        return changeUserId;
    }

    public void setChangeUserId(Long changeUserId) {
        this.changeUserId = changeUserId;
    }

    public String getChangeUserName() {
        return changeUserName;
    }

    public void setChangeUserName(String changeUserName) {
        this.changeUserName = changeUserName == null ? null : changeUserName.trim();
    }

    public String getChangeReason() {
        return changeReason;
    }

    public void setChangeReason(String changeReason) {
        this.changeReason = changeReason == null ? null : changeReason.trim();
    }

    public String getChangeRemark() {
        return changeRemark;
    }

    public void setChangeRemark(String changeRemark) {
        this.changeRemark = changeRemark == null ? null : changeRemark.trim();
    }

    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectTypeId=").append(projectTypeId);
        sb.append(", projectTypeName=").append(projectTypeName);
        sb.append(", unitId=").append(unitId);
        sb.append(", unitName=").append(unitName);
        sb.append(", managerName=").append(managerName);
        sb.append(", managerId=").append(managerId);
        sb.append(", modelName=").append(modelName);
        sb.append(", modelId=").append(modelId);
        sb.append(", modelPamCode=").append(modelPamCode);
        sb.append(", materialName=").append(materialName);
        sb.append(", materialId=").append(materialId);
        sb.append(", materialPamCode=").append(materialPamCode);
        sb.append(", materialErpCode=").append(materialErpCode);
        sb.append(", materialDesc=").append(materialDesc);
        sb.append(", materialUnit=").append(materialUnit);
        sb.append(", materialUnitCode=").append(materialUnitCode);
        sb.append(", modelNum=").append(modelNum);
        sb.append(", materialChangeNum=").append(materialChangeNum);
        sb.append(", materialChangeTotalNum=").append(materialChangeTotalNum);
        sb.append(", materialChangePrice=").append(materialChangePrice);
        sb.append(", materialChangeTotalAmount=").append(materialChangeTotalAmount);
        sb.append(", materialCostType=").append(materialCostType);
        sb.append(", changeRecordId=").append(changeRecordId);
        sb.append(", changeTime=").append(changeTime);
        sb.append(", changeUserId=").append(changeUserId);
        sb.append(", changeUserName=").append(changeUserName);
        sb.append(", changeReason=").append(changeReason);
        sb.append(", changeRemark=").append(changeRemark);
        sb.append(", changeType=").append(changeType);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}