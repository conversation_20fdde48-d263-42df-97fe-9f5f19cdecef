package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProjectCostGetreturnMaterialRecord extends LongIdEntity implements Serializable {
    private Long id;

    private Long executeId;

    private Long summaryId;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private String erpCode;

    private String itemInfo;

    private String brand;

    private String model;

    private String name;

    private BigDecimal totalApplyAmount;

    private BigDecimal totalActualAmount;

    private Date getReturnTime;

    private String orderType;

    private String getReturnCode;

    private String status;

    private BigDecimal materialCost;

    private BigDecimal getReturnAmount;

    private BigDecimal actualGetReturnAccount;

    private Boolean deletedFlag;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private Integer itemCostIsNull;

    private String source;

    private Long transitionId;

    private String headRemark;

    private String detailRemark;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getSummaryId() {
        return summaryId;
    }

    public void setSummaryId(Long summaryId) {
        this.summaryId = summaryId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode == null ? null : erpCode.trim();
    }

    public String getItemInfo() {
        return itemInfo;
    }

    public void setItemInfo(String itemInfo) {
        this.itemInfo = itemInfo == null ? null : itemInfo.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public BigDecimal getTotalApplyAmount() {
        return totalApplyAmount;
    }

    public void setTotalApplyAmount(BigDecimal totalApplyAmount) {
        this.totalApplyAmount = totalApplyAmount;
    }

    public BigDecimal getTotalActualAmount() {
        return totalActualAmount;
    }

    public void setTotalActualAmount(BigDecimal totalActualAmount) {
        this.totalActualAmount = totalActualAmount;
    }

    public Date getGetReturnTime() {
        return getReturnTime;
    }

    public void setGetReturnTime(Date getReturnTime) {
        this.getReturnTime = getReturnTime;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType == null ? null : orderType.trim();
    }

    public String getGetReturnCode() {
        return getReturnCode;
    }

    public void setGetReturnCode(String getReturnCode) {
        this.getReturnCode = getReturnCode == null ? null : getReturnCode.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public BigDecimal getMaterialCost() {
        return materialCost;
    }

    public void setMaterialCost(BigDecimal materialCost) {
        this.materialCost = materialCost;
    }

    public BigDecimal getGetReturnAmount() {
        return getReturnAmount;
    }

    public void setGetReturnAmount(BigDecimal getReturnAmount) {
        this.getReturnAmount = getReturnAmount;
    }

    public BigDecimal getActualGetReturnAccount() {
        return actualGetReturnAccount;
    }

    public void setActualGetReturnAccount(BigDecimal actualGetReturnAccount) {
        this.actualGetReturnAccount = actualGetReturnAccount;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    public Integer getItemCostIsNull() {
        return itemCostIsNull;
    }

    public void setItemCostIsNull(Integer itemCostIsNull) {
        this.itemCostIsNull = itemCostIsNull;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Long getTransitionId() {
        return transitionId;
    }

    public void setTransitionId(Long transitionId) {
        this.transitionId = transitionId;
    }

    public String getHeadRemark() {
        return headRemark;
    }

    public void setHeadRemark(String headRemark) {
        this.headRemark = headRemark == null ? null : headRemark.trim();
    }

    public String getDetailRemark() {
        return detailRemark;
    }

    public void setDetailRemark(String detailRemark) {
        this.detailRemark = detailRemark == null ? null : detailRemark.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", executeId=").append(executeId);
        sb.append(", summaryId=").append(summaryId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", erpCode=").append(erpCode);
        sb.append(", itemInfo=").append(itemInfo);
        sb.append(", brand=").append(brand);
        sb.append(", model=").append(model);
        sb.append(", name=").append(name);
        sb.append(", totalApplyAmount=").append(totalApplyAmount);
        sb.append(", totalActualAmount=").append(totalActualAmount);
        sb.append(", getReturnTime=").append(getReturnTime);
        sb.append(", orderType=").append(orderType);
        sb.append(", getReturnCode=").append(getReturnCode);
        sb.append(", status=").append(status);
        sb.append(", materialCost=").append(materialCost);
        sb.append(", getReturnAmount=").append(getReturnAmount);
        sb.append(", actualGetReturnAccount=").append(actualGetReturnAccount);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", itemCostIsNull=").append(itemCostIsNull);
        sb.append(", source=").append(source);
        sb.append(", transitionId=").append(transitionId);
        sb.append(", headRemark=").append(headRemark);
        sb.append(", detailRemark=").append(detailRemark);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}