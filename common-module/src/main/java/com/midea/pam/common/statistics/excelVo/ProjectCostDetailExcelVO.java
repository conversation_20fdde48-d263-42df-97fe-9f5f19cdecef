package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

public class ProjectCostDetailExcelVO {

    @Excel(name = "序号",width = 10)
    private Integer num;

    @Excel(name = "项目编号",width = 20)
    private String projectCode;

    @Excel(name = "项目名称",width = 20)
    private String projectName;

    @Excel(name = "事业部",width = 20)
    private String departmentName;

    @Excel(name = "业务分类",width = 20)
    private String unitName;

    @Excel(name = "项目类型",width = 20)
    private String projectType;

    @Excel(name = "项目属性",width = 20,replace = {"内部_1","外部_2","研发_3"})
    private String projectAttribute;

    @Excel(name = "项目经理",width = 20)
    private String projectManager;


    @Excel(name = "项目里程碑",width = 20,replace = {"-_null"})
    private String projectMilepostName;

    @Excel(name = "项目状态",width = 20,replace = {"审批驳回_-2","财务驳回_-1","草稿_0","审批中_3","项目进行中_4","项目变更中_9","结项_10","预立项转正驳回_-3","预立项审批驳回_-4","审批撤回_11","预立项审批撤回_13","作废_12","预立项转正审批中_7","终止_16"})
    private Integer projectStatus;

    @Excel(name = "项目开始时间",width = 20,format = "yyyy-MM-dd")
    private Date projectStartDate;

    @Excel(name = "项目结束时间",width = 20,format = "yyyy-MM-dd")
    private Date projectEndDate;

    @Excel(name = "项目金额(不含税)",width = 20)
    private BigDecimal noTaxAmount;

    @Excel(name = "项目总预算",width = 20)
    private BigDecimal budgetAmount;

    @Excel(name = "物料预算",width = 20)
    private BigDecimal materialBudget;

    @Excel(name = "人力预算",width = 20)
    private BigDecimal humanBudget;

    @Excel(name = "差旅预算",width = 20)
    private BigDecimal travelBudget;

    @Excel(name = "非差旅预算",width = 20)
    private BigDecimal noTravelBudget;

    @Excel(name = "预算毛利",width = 20)
    private BigDecimal budgetProfit;

    @Excel(name = "预算毛利率",width = 20,suffix = "%")
    private BigDecimal budgetProfitRate;

    @Excel(name = "项目总成本",width = 20)
    private BigDecimal projectCostAmount;

    @Excel(name = "物料成本-已处理成本",width = 20)
    private BigDecimal materialCostActual;

    @Excel(name = "物料成本-待处理成本",width = 20)
    private BigDecimal materialCostRemaining;

    @Excel(name = "内部人力成本-已审核成本",width = 20)
    private BigDecimal inHumanCostActual;

    @Excel(name = "内部人力成本-待审核成本",width = 20)
    private BigDecimal inHumanCostRemaining;

    @Excel(name = "外部人力成本-已审核成本",width = 20)
    private BigDecimal outHumanCostActual;

    @Excel(name = "外部人力成本-待审核成本",width = 20)
    private BigDecimal outHumanCostRemaining;

    @Excel(name = "差旅费-已处理成本",width = 20)
    private BigDecimal travelCostActual;

    @Excel(name = "差旅费-待处理成本",width = 20)
    private BigDecimal travelCostRemaining;

    @Excel(name = "差旅费-EA可用金额",width = 20)
    private BigDecimal travelEaAvailableAmount;

    @Excel(name = "非差旅费-已处理成本",width = 20)
    private BigDecimal noTravelCostActual;

    @Excel(name = "非差旅费-待处理成本",width = 20)
    private BigDecimal noTravelCostRemaining;

    @Excel(name = "非差旅费-EA可用金额",width = 20)
    private BigDecimal noTravelEaAvailableAmount;

    @Excel(name = "差异分摊-已处理成本",width = 20)
    private BigDecimal shareCostActual;

    @Excel(name = "已发生成本比例",width = 20,suffix = "%")
    private BigDecimal actualCostProportion;

    @Excel(name = "实际毛利",width = 20)
    private BigDecimal actualProfits;

    @Excel(name = "实际毛利率",width = 20,suffix = "%")
    private BigDecimal actualProfitsRate;

    @Excel(name = "PAM子合同编号",width = 20)
    private String contractCode;

    @Excel(name = "业务实体",width = 20)
    private String ouName;

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public String getProjectAttribute() {
        return projectAttribute;
    }

    public void setProjectAttribute(String projectAttribute) {
        this.projectAttribute = projectAttribute;
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager;
    }

    public String getProjectMilepostName() {
        return projectMilepostName;
    }

    public void setProjectMilepostName(String projectMilepostName) {
        this.projectMilepostName = projectMilepostName;
    }

    public Integer getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(Integer projectStatus) {
        this.projectStatus = projectStatus;
    }

    public Date getProjectStartDate() {
        return projectStartDate;
    }

    public void setProjectStartDate(Date projectStartDate) {
        this.projectStartDate = projectStartDate;
    }

    public Date getProjectEndDate() {
        return projectEndDate;
    }

    public void setProjectEndDate(Date projectEndDate) {
        this.projectEndDate = projectEndDate;
    }

    public BigDecimal getNoTaxAmount() {
        return noTaxAmount;
    }

    public void setNoTaxAmount(BigDecimal noTaxAmount) {
        this.noTaxAmount = noTaxAmount;
    }

    public BigDecimal getBudgetAmount() {
        return budgetAmount;
    }

    public void setBudgetAmount(BigDecimal budgetAmount) {
        this.budgetAmount = budgetAmount;
    }

    public BigDecimal getMaterialBudget() {
        return materialBudget;
    }

    public void setMaterialBudget(BigDecimal materialBudget) {
        this.materialBudget = materialBudget;
    }

    public BigDecimal getHumanBudget() {
        return humanBudget;
    }

    public void setHumanBudget(BigDecimal humanBudget) {
        this.humanBudget = humanBudget;
    }

    public BigDecimal getTravelBudget() {
        return travelBudget;
    }

    public void setTravelBudget(BigDecimal travelBudget) {
        this.travelBudget = travelBudget;
    }

    public BigDecimal getNoTravelBudget() {
        return noTravelBudget;
    }

    public void setNoTravelBudget(BigDecimal noTravelBudget) {
        this.noTravelBudget = noTravelBudget;
    }

    public BigDecimal getBudgetProfit() {
        return budgetProfit;
    }

    public void setBudgetProfit(BigDecimal budgetProfit) {
        this.budgetProfit = budgetProfit;
    }

    public BigDecimal getBudgetProfitRate() {
        return budgetProfitRate;
    }

    public void setBudgetProfitRate(BigDecimal budgetProfitRate) {
        this.budgetProfitRate = budgetProfitRate;
    }

    public BigDecimal getProjectCostAmount() {
        return projectCostAmount;
    }

    public void setProjectCostAmount(BigDecimal projectCostAmount) {
        this.projectCostAmount = projectCostAmount;
    }

    public BigDecimal getMaterialCostActual() {
        return materialCostActual;
    }

    public void setMaterialCostActual(BigDecimal materialCostActual) {
        this.materialCostActual = materialCostActual;
    }

    public BigDecimal getMaterialCostRemaining() {
        return materialCostRemaining;
    }

    public void setMaterialCostRemaining(BigDecimal materialCostRemaining) {
        this.materialCostRemaining = materialCostRemaining;
    }

    public BigDecimal getInHumanCostActual() {
        return inHumanCostActual;
    }

    public void setInHumanCostActual(BigDecimal inHumanCostActual) {
        this.inHumanCostActual = inHumanCostActual;
    }

    public BigDecimal getInHumanCostRemaining() {
        return inHumanCostRemaining;
    }

    public void setInHumanCostRemaining(BigDecimal inHumanCostRemaining) {
        this.inHumanCostRemaining = inHumanCostRemaining;
    }

    public BigDecimal getOutHumanCostActual() {
        return outHumanCostActual;
    }

    public void setOutHumanCostActual(BigDecimal outHumanCostActual) {
        this.outHumanCostActual = outHumanCostActual;
    }

    public BigDecimal getOutHumanCostRemaining() {
        return outHumanCostRemaining;
    }

    public void setOutHumanCostRemaining(BigDecimal outHumanCostRemaining) {
        this.outHumanCostRemaining = outHumanCostRemaining;
    }

    public BigDecimal getTravelCostActual() {
        return travelCostActual;
    }

    public void setTravelCostActual(BigDecimal travelCostActual) {
        this.travelCostActual = travelCostActual;
    }

    public BigDecimal getTravelCostRemaining() {
        return travelCostRemaining;
    }

    public void setTravelCostRemaining(BigDecimal travelCostRemaining) {
        this.travelCostRemaining = travelCostRemaining;
    }

    public BigDecimal getNoTravelCostActual() {
        return noTravelCostActual;
    }

    public void setNoTravelCostActual(BigDecimal noTravelCostActual) {
        this.noTravelCostActual = noTravelCostActual;
    }

    public BigDecimal getNoTravelCostRemaining() {
        return noTravelCostRemaining;
    }

    public void setNoTravelCostRemaining(BigDecimal noTravelCostRemaining) {
        this.noTravelCostRemaining = noTravelCostRemaining;
    }

    public BigDecimal getShareCostActual() {
        return shareCostActual;
    }

    public void setShareCostActual(BigDecimal shareCostActual) {
        this.shareCostActual = shareCostActual;
    }

    public BigDecimal getActualCostProportion() {
        return actualCostProportion;
    }

    public void setActualCostProportion(BigDecimal actualCostProportion) {
        this.actualCostProportion = actualCostProportion;
    }

    public BigDecimal getActualProfits() {
        return actualProfits;
    }

    public void setActualProfits(BigDecimal actualProfits) {
        this.actualProfits = actualProfits;
    }

    public BigDecimal getActualProfitsRate() {
        return actualProfitsRate;
    }

    public void setActualProfitsRate(BigDecimal actualProfitsRate) {
        this.actualProfitsRate = actualProfitsRate;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName;
    }

    public BigDecimal getTravelEaAvailableAmount() {
        return travelEaAvailableAmount;
    }

    public void setTravelEaAvailableAmount(BigDecimal travelEaAvailableAmount) {
        this.travelEaAvailableAmount = travelEaAvailableAmount;
    }

    public BigDecimal getNoTravelEaAvailableAmount() {
        return noTravelEaAvailableAmount;
    }

    public void setNoTravelEaAvailableAmount(BigDecimal noTravelEaAvailableAmount) {
        this.noTravelEaAvailableAmount = noTravelEaAvailableAmount;
    }
}
