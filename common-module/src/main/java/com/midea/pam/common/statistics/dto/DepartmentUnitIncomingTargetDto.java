package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.DepartmentUnitIncomingTarget;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "DepartmentUnitIncomingTargetDto", description = "事业部或产品的收入目标和利润目标记录")
public class DepartmentUnitIncomingTargetDto extends DepartmentUnitIncomingTarget {

    @ApiModelProperty(value = "使用单位的名称")
    String companyName;

    @ApiModelProperty(value = "事业部或产品的名称")
    String unitName;

    @ApiModelProperty(value = "更新人名称")
    String updaterName;

    @ApiModelProperty(value = "内部或外部")
    String innerFlagName;

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public String getInnerFlagName() {
        return innerFlagName;
    }

    public void setInnerFlagName(String innerFlagName) {
        this.innerFlagName = innerFlagName;
    }
}