package com.midea.pam.common.statistics.excelVo;

import com.google.common.base.Joiner;
import com.midea.pam.common.enums.ProjectStatus;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/18
 * 剩余待确认收入明细报表ExcelVO
 */
public class ReportUnconfirmedIncomeExcelVO {

    private Long contractId;

    @Excel(name = "PAM合同编号", width = 15)
    private String contractCode;

    @Excel(name = "合同名称", width = 30)
    private String contractName;

    @Excel(name = "接单年度")
    private String orderYear;

    @Excel(name = "接单月份")
    private String orderMonth;

    private Long unitId;

    @Excel(name = "销售部门")
    private String unitName;

    private Long customerId;

    @Excel(name = "客户CRM编码", width = 15)
    private String customerCode;

    @Excel(name = "客户名称", width = 30)
    private String customerName;

    @Excel(name = "客户属性", width = 15,replace = {"外部客户_0","内部客户_1","_null"})
    private String customerType;

    @Excel(name = "期初订单金额（不含税）")
    private BigDecimal initialPeriodOrderAmount;

    @Excel(name = "历史年度确认收入")
    private BigDecimal hisYearConfirmedIncome;

    @Excel(name = "期初OB")
    private BigDecimal initialPeriodOb;

    @Excel(name = "本年订单金额")
    private BigDecimal curYearOrderAmount;

    @Excel(name = "本年确认收入")
    private BigDecimal curYearConfirmedIncome;

    @Excel(name = "剩余订单金额（期末OB）")
    private BigDecimal remainingOrderAmount;

    @Excel(name = "合同状态", replace = {"待归档_0", "草稿_1", "审核中_2", "驳回_3", "待生效_4", "生效_5", "冻结_6", "失效_7", "已结束_8", "作废_9", "变更中_10", "合同终止_11", "终止审批中_12"})
    private String contractStatus;

    private Long projectId;

    @Excel(name = "项目号", width = 15)
    private String projectCode;

    @Excel(name = "项目名称", width = 25)
    private String projectName;

    @Excel(name = "项目类型")
    private String projectTypeName;

    @Excel(name = "项目状态")
    private String projectStatus;

    private Long ouId;

    @Excel(name = "业务实体", width = 30)
    private String ouName;

    @Excel(name = "累计确认收入金额")
    private BigDecimal totalConfirmedIncomeAmount;

    @Excel(name = "收入达成率")
    private String incomeAchivementRate;

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    public String getOrderYear() {
        return orderYear;
    }

    public void setOrderYear(String orderYear) {
        this.orderYear = orderYear;
    }

    public String getOrderMonth() {
        return orderMonth;
    }

    public void setOrderMonth(String orderMonth) {
        this.orderMonth = orderMonth;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public BigDecimal getInitialPeriodOrderAmount() {
        return initialPeriodOrderAmount;
    }

    public void setInitialPeriodOrderAmount(BigDecimal initialPeriodOrderAmount) {
        if (initialPeriodOrderAmount != null) {
            initialPeriodOrderAmount = initialPeriodOrderAmount.setScale(2,BigDecimal.ROUND_HALF_UP);
        }
        this.initialPeriodOrderAmount = initialPeriodOrderAmount;
    }

    public BigDecimal getHisYearConfirmedIncome() {
        return hisYearConfirmedIncome;
    }

    public void setHisYearConfirmedIncome(BigDecimal hisYearConfirmedIncome) {
        if (hisYearConfirmedIncome != null) {
            hisYearConfirmedIncome = hisYearConfirmedIncome.setScale(2,BigDecimal.ROUND_HALF_UP);
        }
        this.hisYearConfirmedIncome = hisYearConfirmedIncome;
    }

    public BigDecimal getInitialPeriodOb() {
        return initialPeriodOb;
    }

    public void setInitialPeriodOb(BigDecimal initialPeriodOb) {
        if (initialPeriodOb != null) {
            initialPeriodOb = initialPeriodOb.setScale(2,BigDecimal.ROUND_HALF_UP);
        }
        this.initialPeriodOb = initialPeriodOb;
    }

    public BigDecimal getCurYearOrderAmount() {
        return curYearOrderAmount;
    }

    public void setCurYearOrderAmount(BigDecimal curYearOrderAmount) {
        if (curYearOrderAmount != null) {
            curYearOrderAmount = curYearOrderAmount.setScale(2,BigDecimal.ROUND_HALF_UP);
        }
        this.curYearOrderAmount = curYearOrderAmount;
    }

    public BigDecimal getCurYearConfirmedIncome() {
        return curYearConfirmedIncome;
    }

    public void setCurYearConfirmedIncome(BigDecimal curYearConfirmedIncome) {
        if (curYearConfirmedIncome != null) {
            curYearConfirmedIncome = curYearConfirmedIncome.setScale(2,BigDecimal.ROUND_HALF_UP);
        }
        this.curYearConfirmedIncome = curYearConfirmedIncome;
    }

    public BigDecimal getRemainingOrderAmount() {
        return remainingOrderAmount;
    }

    public void setRemainingOrderAmount(BigDecimal remainingOrderAmount) {
        if (remainingOrderAmount != null) {
            remainingOrderAmount = remainingOrderAmount.setScale(2,BigDecimal.ROUND_HALF_UP);
        }
        this.remainingOrderAmount = remainingOrderAmount;
    }

    public String getContractStatus() {
        return contractStatus;
    }

    public void setContractStatus(String contractStatus) {
        this.contractStatus = contractStatus;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectTypeName() {
        return projectTypeName;
    }

    public void setProjectTypeName(String projectTypeName) {
        this.projectTypeName = projectTypeName;
    }

    public String getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(String projectStatus) {
        if (projectStatus != null) {
            try {
                List<Integer> statusList = Arrays.stream(projectStatus.split("、")).map(Integer::parseInt).collect(Collectors.toList());
                ArrayList<String> statusStrList = new ArrayList<>(statusList.size());
                for (Integer status : statusList) {
                    statusStrList.add(ProjectStatus.getValue(status));
                }
                projectStatus = Joiner.on("、").skipNulls().join(statusStrList);
            } catch (Exception e) {
                // 发生异常就不转换
            }
        }
        this.projectStatus = projectStatus;
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName;
    }

    public BigDecimal getTotalConfirmedIncomeAmount() {
        return totalConfirmedIncomeAmount;
    }

    public void setTotalConfirmedIncomeAmount(BigDecimal totalConfirmedIncomeAmount) {
        if (totalConfirmedIncomeAmount != null) {
            totalConfirmedIncomeAmount = totalConfirmedIncomeAmount.setScale(2,BigDecimal.ROUND_HALF_UP);
        }
        this.totalConfirmedIncomeAmount = totalConfirmedIncomeAmount;
    }

    public String getIncomeAchivementRate() {
        return incomeAchivementRate;
    }

    public void setIncomeAchivementRate(String incomeAchivementRate) {
        // 会转换两次，转换一次后就不需做转换
        if (incomeAchivementRate != null && !incomeAchivementRate.endsWith("%")) {
            incomeAchivementRate += "%";
        }
        this.incomeAchivementRate = incomeAchivementRate;
    }
}
