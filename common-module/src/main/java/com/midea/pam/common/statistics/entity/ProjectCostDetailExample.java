package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class ProjectCostDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectCostDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNull() {
            addCriterion("department_id is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNotNull() {
            addCriterion("department_id is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdEqualTo(Long value) {
            addCriterion("department_id =", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotEqualTo(Long value) {
            addCriterion("department_id <>", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThan(Long value) {
            addCriterion("department_id >", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("department_id >=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThan(Long value) {
            addCriterion("department_id <", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThanOrEqualTo(Long value) {
            addCriterion("department_id <=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIn(List<Long> values) {
            addCriterion("department_id in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotIn(List<Long> values) {
            addCriterion("department_id not in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdBetween(Long value1, Long value2) {
            addCriterion("department_id between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotBetween(Long value1, Long value2) {
            addCriterion("department_id not between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIsNull() {
            addCriterion("department_name is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIsNotNull() {
            addCriterion("department_name is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameEqualTo(String value) {
            addCriterion("department_name =", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotEqualTo(String value) {
            addCriterion("department_name <>", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameGreaterThan(String value) {
            addCriterion("department_name >", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameGreaterThanOrEqualTo(String value) {
            addCriterion("department_name >=", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLessThan(String value) {
            addCriterion("department_name <", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLessThanOrEqualTo(String value) {
            addCriterion("department_name <=", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLike(String value) {
            addCriterion("department_name like", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotLike(String value) {
            addCriterion("department_name not like", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIn(List<String> values) {
            addCriterion("department_name in", values, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotIn(List<String> values) {
            addCriterion("department_name not in", values, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameBetween(String value1, String value2) {
            addCriterion("department_name between", value1, value2, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotBetween(String value1, String value2) {
            addCriterion("department_name not between", value1, value2, "departmentName");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Long value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Long value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Long value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Long value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Long> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Long> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Long value1, Long value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNull() {
            addCriterion("unit_name is null");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNotNull() {
            addCriterion("unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andUnitNameEqualTo(String value) {
            addCriterion("unit_name =", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotEqualTo(String value) {
            addCriterion("unit_name <>", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThan(String value) {
            addCriterion("unit_name >", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("unit_name >=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThan(String value) {
            addCriterion("unit_name <", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThanOrEqualTo(String value) {
            addCriterion("unit_name <=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLike(String value) {
            addCriterion("unit_name like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotLike(String value) {
            addCriterion("unit_name not like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIn(List<String> values) {
            addCriterion("unit_name in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotIn(List<String> values) {
            addCriterion("unit_name not in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameBetween(String value1, String value2) {
            addCriterion("unit_name between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotBetween(String value1, String value2) {
            addCriterion("unit_name not between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNull() {
            addCriterion("project_type is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNotNull() {
            addCriterion("project_type is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeEqualTo(String value) {
            addCriterion("project_type =", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotEqualTo(String value) {
            addCriterion("project_type <>", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThan(String value) {
            addCriterion("project_type >", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThanOrEqualTo(String value) {
            addCriterion("project_type >=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThan(String value) {
            addCriterion("project_type <", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThanOrEqualTo(String value) {
            addCriterion("project_type <=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLike(String value) {
            addCriterion("project_type like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotLike(String value) {
            addCriterion("project_type not like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIn(List<String> values) {
            addCriterion("project_type in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotIn(List<String> values) {
            addCriterion("project_type not in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeBetween(String value1, String value2) {
            addCriterion("project_type between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotBetween(String value1, String value2) {
            addCriterion("project_type not between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectAttributeIsNull() {
            addCriterion("project_attribute is null");
            return (Criteria) this;
        }

        public Criteria andProjectAttributeIsNotNull() {
            addCriterion("project_attribute is not null");
            return (Criteria) this;
        }

        public Criteria andProjectAttributeEqualTo(String value) {
            addCriterion("project_attribute =", value, "projectAttribute");
            return (Criteria) this;
        }

        public Criteria andProjectAttributeNotEqualTo(String value) {
            addCriterion("project_attribute <>", value, "projectAttribute");
            return (Criteria) this;
        }

        public Criteria andProjectAttributeGreaterThan(String value) {
            addCriterion("project_attribute >", value, "projectAttribute");
            return (Criteria) this;
        }

        public Criteria andProjectAttributeGreaterThanOrEqualTo(String value) {
            addCriterion("project_attribute >=", value, "projectAttribute");
            return (Criteria) this;
        }

        public Criteria andProjectAttributeLessThan(String value) {
            addCriterion("project_attribute <", value, "projectAttribute");
            return (Criteria) this;
        }

        public Criteria andProjectAttributeLessThanOrEqualTo(String value) {
            addCriterion("project_attribute <=", value, "projectAttribute");
            return (Criteria) this;
        }

        public Criteria andProjectAttributeLike(String value) {
            addCriterion("project_attribute like", value, "projectAttribute");
            return (Criteria) this;
        }

        public Criteria andProjectAttributeNotLike(String value) {
            addCriterion("project_attribute not like", value, "projectAttribute");
            return (Criteria) this;
        }

        public Criteria andProjectAttributeIn(List<String> values) {
            addCriterion("project_attribute in", values, "projectAttribute");
            return (Criteria) this;
        }

        public Criteria andProjectAttributeNotIn(List<String> values) {
            addCriterion("project_attribute not in", values, "projectAttribute");
            return (Criteria) this;
        }

        public Criteria andProjectAttributeBetween(String value1, String value2) {
            addCriterion("project_attribute between", value1, value2, "projectAttribute");
            return (Criteria) this;
        }

        public Criteria andProjectAttributeNotBetween(String value1, String value2) {
            addCriterion("project_attribute not between", value1, value2, "projectAttribute");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNull() {
            addCriterion("project_manager is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNotNull() {
            addCriterion("project_manager is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerEqualTo(String value) {
            addCriterion("project_manager =", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotEqualTo(String value) {
            addCriterion("project_manager <>", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThan(String value) {
            addCriterion("project_manager >", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThanOrEqualTo(String value) {
            addCriterion("project_manager >=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThan(String value) {
            addCriterion("project_manager <", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThanOrEqualTo(String value) {
            addCriterion("project_manager <=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLike(String value) {
            addCriterion("project_manager like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotLike(String value) {
            addCriterion("project_manager not like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIn(List<String> values) {
            addCriterion("project_manager in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotIn(List<String> values) {
            addCriterion("project_manager not in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerBetween(String value1, String value2) {
            addCriterion("project_manager between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotBetween(String value1, String value2) {
            addCriterion("project_manager not between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostIdIsNull() {
            addCriterion("project_milepost_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostIdIsNotNull() {
            addCriterion("project_milepost_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostIdEqualTo(Long value) {
            addCriterion("project_milepost_id =", value, "projectMilepostId");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostIdNotEqualTo(Long value) {
            addCriterion("project_milepost_id <>", value, "projectMilepostId");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostIdGreaterThan(Long value) {
            addCriterion("project_milepost_id >", value, "projectMilepostId");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_milepost_id >=", value, "projectMilepostId");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostIdLessThan(Long value) {
            addCriterion("project_milepost_id <", value, "projectMilepostId");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostIdLessThanOrEqualTo(Long value) {
            addCriterion("project_milepost_id <=", value, "projectMilepostId");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostIdIn(List<Long> values) {
            addCriterion("project_milepost_id in", values, "projectMilepostId");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostIdNotIn(List<Long> values) {
            addCriterion("project_milepost_id not in", values, "projectMilepostId");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostIdBetween(Long value1, Long value2) {
            addCriterion("project_milepost_id between", value1, value2, "projectMilepostId");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostIdNotBetween(Long value1, Long value2) {
            addCriterion("project_milepost_id not between", value1, value2, "projectMilepostId");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameIsNull() {
            addCriterion("project_milepost_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameIsNotNull() {
            addCriterion("project_milepost_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameEqualTo(String value) {
            addCriterion("project_milepost_name =", value, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameNotEqualTo(String value) {
            addCriterion("project_milepost_name <>", value, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameGreaterThan(String value) {
            addCriterion("project_milepost_name >", value, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_milepost_name >=", value, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameLessThan(String value) {
            addCriterion("project_milepost_name <", value, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameLessThanOrEqualTo(String value) {
            addCriterion("project_milepost_name <=", value, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameLike(String value) {
            addCriterion("project_milepost_name like", value, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameNotLike(String value) {
            addCriterion("project_milepost_name not like", value, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameIn(List<String> values) {
            addCriterion("project_milepost_name in", values, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameNotIn(List<String> values) {
            addCriterion("project_milepost_name not in", values, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameBetween(String value1, String value2) {
            addCriterion("project_milepost_name between", value1, value2, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameNotBetween(String value1, String value2) {
            addCriterion("project_milepost_name not between", value1, value2, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNull() {
            addCriterion("project_status is null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNotNull() {
            addCriterion("project_status is not null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusEqualTo(Integer value) {
            addCriterion("project_status =", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotEqualTo(Integer value) {
            addCriterion("project_status <>", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThan(Integer value) {
            addCriterion("project_status >", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("project_status >=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThan(Integer value) {
            addCriterion("project_status <", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThanOrEqualTo(Integer value) {
            addCriterion("project_status <=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIn(List<Integer> values) {
            addCriterion("project_status in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotIn(List<Integer> values) {
            addCriterion("project_status not in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusBetween(Integer value1, Integer value2) {
            addCriterion("project_status between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("project_status not between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStartDateIsNull() {
            addCriterion("project_start_date is null");
            return (Criteria) this;
        }

        public Criteria andProjectStartDateIsNotNull() {
            addCriterion("project_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andProjectStartDateEqualTo(Date value) {
            addCriterionForJDBCDate("project_start_date =", value, "projectStartDate");
            return (Criteria) this;
        }

        public Criteria andProjectStartDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("project_start_date <>", value, "projectStartDate");
            return (Criteria) this;
        }

        public Criteria andProjectStartDateGreaterThan(Date value) {
            addCriterionForJDBCDate("project_start_date >", value, "projectStartDate");
            return (Criteria) this;
        }

        public Criteria andProjectStartDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("project_start_date >=", value, "projectStartDate");
            return (Criteria) this;
        }

        public Criteria andProjectStartDateLessThan(Date value) {
            addCriterionForJDBCDate("project_start_date <", value, "projectStartDate");
            return (Criteria) this;
        }

        public Criteria andProjectStartDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("project_start_date <=", value, "projectStartDate");
            return (Criteria) this;
        }

        public Criteria andProjectStartDateIn(List<Date> values) {
            addCriterionForJDBCDate("project_start_date in", values, "projectStartDate");
            return (Criteria) this;
        }

        public Criteria andProjectStartDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("project_start_date not in", values, "projectStartDate");
            return (Criteria) this;
        }

        public Criteria andProjectStartDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("project_start_date between", value1, value2, "projectStartDate");
            return (Criteria) this;
        }

        public Criteria andProjectStartDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("project_start_date not between", value1, value2, "projectStartDate");
            return (Criteria) this;
        }

        public Criteria andProjectEndDateIsNull() {
            addCriterion("project_end_date is null");
            return (Criteria) this;
        }

        public Criteria andProjectEndDateIsNotNull() {
            addCriterion("project_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andProjectEndDateEqualTo(Date value) {
            addCriterionForJDBCDate("project_end_date =", value, "projectEndDate");
            return (Criteria) this;
        }

        public Criteria andProjectEndDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("project_end_date <>", value, "projectEndDate");
            return (Criteria) this;
        }

        public Criteria andProjectEndDateGreaterThan(Date value) {
            addCriterionForJDBCDate("project_end_date >", value, "projectEndDate");
            return (Criteria) this;
        }

        public Criteria andProjectEndDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("project_end_date >=", value, "projectEndDate");
            return (Criteria) this;
        }

        public Criteria andProjectEndDateLessThan(Date value) {
            addCriterionForJDBCDate("project_end_date <", value, "projectEndDate");
            return (Criteria) this;
        }

        public Criteria andProjectEndDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("project_end_date <=", value, "projectEndDate");
            return (Criteria) this;
        }

        public Criteria andProjectEndDateIn(List<Date> values) {
            addCriterionForJDBCDate("project_end_date in", values, "projectEndDate");
            return (Criteria) this;
        }

        public Criteria andProjectEndDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("project_end_date not in", values, "projectEndDate");
            return (Criteria) this;
        }

        public Criteria andProjectEndDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("project_end_date between", value1, value2, "projectEndDate");
            return (Criteria) this;
        }

        public Criteria andProjectEndDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("project_end_date not between", value1, value2, "projectEndDate");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountIsNull() {
            addCriterion("no_tax_amount is null");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountIsNotNull() {
            addCriterion("no_tax_amount is not null");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountEqualTo(BigDecimal value) {
            addCriterion("no_tax_amount =", value, "noTaxAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountNotEqualTo(BigDecimal value) {
            addCriterion("no_tax_amount <>", value, "noTaxAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountGreaterThan(BigDecimal value) {
            addCriterion("no_tax_amount >", value, "noTaxAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("no_tax_amount >=", value, "noTaxAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountLessThan(BigDecimal value) {
            addCriterion("no_tax_amount <", value, "noTaxAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("no_tax_amount <=", value, "noTaxAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountIn(List<BigDecimal> values) {
            addCriterion("no_tax_amount in", values, "noTaxAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountNotIn(List<BigDecimal> values) {
            addCriterion("no_tax_amount not in", values, "noTaxAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_tax_amount between", value1, value2, "noTaxAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_tax_amount not between", value1, value2, "noTaxAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountIsNull() {
            addCriterion("budget_amount is null");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountIsNotNull() {
            addCriterion("budget_amount is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountEqualTo(BigDecimal value) {
            addCriterion("budget_amount =", value, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountNotEqualTo(BigDecimal value) {
            addCriterion("budget_amount <>", value, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountGreaterThan(BigDecimal value) {
            addCriterion("budget_amount >", value, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_amount >=", value, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountLessThan(BigDecimal value) {
            addCriterion("budget_amount <", value, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_amount <=", value, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountIn(List<BigDecimal> values) {
            addCriterion("budget_amount in", values, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountNotIn(List<BigDecimal> values) {
            addCriterion("budget_amount not in", values, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_amount between", value1, value2, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_amount not between", value1, value2, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetIsNull() {
            addCriterion("material_budget is null");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetIsNotNull() {
            addCriterion("material_budget is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetEqualTo(BigDecimal value) {
            addCriterion("material_budget =", value, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetNotEqualTo(BigDecimal value) {
            addCriterion("material_budget <>", value, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetGreaterThan(BigDecimal value) {
            addCriterion("material_budget >", value, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_budget >=", value, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetLessThan(BigDecimal value) {
            addCriterion("material_budget <", value, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_budget <=", value, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetIn(List<BigDecimal> values) {
            addCriterion("material_budget in", values, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetNotIn(List<BigDecimal> values) {
            addCriterion("material_budget not in", values, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_budget between", value1, value2, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_budget not between", value1, value2, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetIsNull() {
            addCriterion("human_budget is null");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetIsNotNull() {
            addCriterion("human_budget is not null");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetEqualTo(BigDecimal value) {
            addCriterion("human_budget =", value, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetNotEqualTo(BigDecimal value) {
            addCriterion("human_budget <>", value, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetGreaterThan(BigDecimal value) {
            addCriterion("human_budget >", value, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("human_budget >=", value, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetLessThan(BigDecimal value) {
            addCriterion("human_budget <", value, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("human_budget <=", value, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetIn(List<BigDecimal> values) {
            addCriterion("human_budget in", values, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetNotIn(List<BigDecimal> values) {
            addCriterion("human_budget not in", values, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("human_budget between", value1, value2, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("human_budget not between", value1, value2, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetIsNull() {
            addCriterion("travel_budget is null");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetIsNotNull() {
            addCriterion("travel_budget is not null");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetEqualTo(BigDecimal value) {
            addCriterion("travel_budget =", value, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetNotEqualTo(BigDecimal value) {
            addCriterion("travel_budget <>", value, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetGreaterThan(BigDecimal value) {
            addCriterion("travel_budget >", value, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_budget >=", value, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetLessThan(BigDecimal value) {
            addCriterion("travel_budget <", value, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_budget <=", value, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetIn(List<BigDecimal> values) {
            addCriterion("travel_budget in", values, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetNotIn(List<BigDecimal> values) {
            addCriterion("travel_budget not in", values, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_budget between", value1, value2, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_budget not between", value1, value2, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetIsNull() {
            addCriterion("no_travel_budget is null");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetIsNotNull() {
            addCriterion("no_travel_budget is not null");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetEqualTo(BigDecimal value) {
            addCriterion("no_travel_budget =", value, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetNotEqualTo(BigDecimal value) {
            addCriterion("no_travel_budget <>", value, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetGreaterThan(BigDecimal value) {
            addCriterion("no_travel_budget >", value, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("no_travel_budget >=", value, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetLessThan(BigDecimal value) {
            addCriterion("no_travel_budget <", value, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("no_travel_budget <=", value, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetIn(List<BigDecimal> values) {
            addCriterion("no_travel_budget in", values, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetNotIn(List<BigDecimal> values) {
            addCriterion("no_travel_budget not in", values, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_travel_budget between", value1, value2, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_travel_budget not between", value1, value2, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitIsNull() {
            addCriterion("budget_profit is null");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitIsNotNull() {
            addCriterion("budget_profit is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitEqualTo(BigDecimal value) {
            addCriterion("budget_profit =", value, "budgetProfit");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitNotEqualTo(BigDecimal value) {
            addCriterion("budget_profit <>", value, "budgetProfit");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitGreaterThan(BigDecimal value) {
            addCriterion("budget_profit >", value, "budgetProfit");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_profit >=", value, "budgetProfit");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitLessThan(BigDecimal value) {
            addCriterion("budget_profit <", value, "budgetProfit");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_profit <=", value, "budgetProfit");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitIn(List<BigDecimal> values) {
            addCriterion("budget_profit in", values, "budgetProfit");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitNotIn(List<BigDecimal> values) {
            addCriterion("budget_profit not in", values, "budgetProfit");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_profit between", value1, value2, "budgetProfit");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_profit not between", value1, value2, "budgetProfit");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateIsNull() {
            addCriterion("budget_profit_rate is null");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateIsNotNull() {
            addCriterion("budget_profit_rate is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateEqualTo(BigDecimal value) {
            addCriterion("budget_profit_rate =", value, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateNotEqualTo(BigDecimal value) {
            addCriterion("budget_profit_rate <>", value, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateGreaterThan(BigDecimal value) {
            addCriterion("budget_profit_rate >", value, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_profit_rate >=", value, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateLessThan(BigDecimal value) {
            addCriterion("budget_profit_rate <", value, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_profit_rate <=", value, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateIn(List<BigDecimal> values) {
            addCriterion("budget_profit_rate in", values, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateNotIn(List<BigDecimal> values) {
            addCriterion("budget_profit_rate not in", values, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_profit_rate between", value1, value2, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_profit_rate not between", value1, value2, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountIsNull() {
            addCriterion("project_cost_amount is null");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountIsNotNull() {
            addCriterion("project_cost_amount is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountEqualTo(BigDecimal value) {
            addCriterion("project_cost_amount =", value, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountNotEqualTo(BigDecimal value) {
            addCriterion("project_cost_amount <>", value, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountGreaterThan(BigDecimal value) {
            addCriterion("project_cost_amount >", value, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_cost_amount >=", value, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountLessThan(BigDecimal value) {
            addCriterion("project_cost_amount <", value, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_cost_amount <=", value, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountIn(List<BigDecimal> values) {
            addCriterion("project_cost_amount in", values, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountNotIn(List<BigDecimal> values) {
            addCriterion("project_cost_amount not in", values, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_cost_amount between", value1, value2, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_cost_amount not between", value1, value2, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualIsNull() {
            addCriterion("material_cost_actual is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualIsNotNull() {
            addCriterion("material_cost_actual is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualEqualTo(BigDecimal value) {
            addCriterion("material_cost_actual =", value, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualNotEqualTo(BigDecimal value) {
            addCriterion("material_cost_actual <>", value, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualGreaterThan(BigDecimal value) {
            addCriterion("material_cost_actual >", value, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_cost_actual >=", value, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualLessThan(BigDecimal value) {
            addCriterion("material_cost_actual <", value, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_cost_actual <=", value, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualIn(List<BigDecimal> values) {
            addCriterion("material_cost_actual in", values, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualNotIn(List<BigDecimal> values) {
            addCriterion("material_cost_actual not in", values, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_cost_actual between", value1, value2, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_cost_actual not between", value1, value2, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostRemainingIsNull() {
            addCriterion("material_cost_remaining is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCostRemainingIsNotNull() {
            addCriterion("material_cost_remaining is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCostRemainingEqualTo(BigDecimal value) {
            addCriterion("material_cost_remaining =", value, "materialCostRemaining");
            return (Criteria) this;
        }

        public Criteria andMaterialCostRemainingNotEqualTo(BigDecimal value) {
            addCriterion("material_cost_remaining <>", value, "materialCostRemaining");
            return (Criteria) this;
        }

        public Criteria andMaterialCostRemainingGreaterThan(BigDecimal value) {
            addCriterion("material_cost_remaining >", value, "materialCostRemaining");
            return (Criteria) this;
        }

        public Criteria andMaterialCostRemainingGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_cost_remaining >=", value, "materialCostRemaining");
            return (Criteria) this;
        }

        public Criteria andMaterialCostRemainingLessThan(BigDecimal value) {
            addCriterion("material_cost_remaining <", value, "materialCostRemaining");
            return (Criteria) this;
        }

        public Criteria andMaterialCostRemainingLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_cost_remaining <=", value, "materialCostRemaining");
            return (Criteria) this;
        }

        public Criteria andMaterialCostRemainingIn(List<BigDecimal> values) {
            addCriterion("material_cost_remaining in", values, "materialCostRemaining");
            return (Criteria) this;
        }

        public Criteria andMaterialCostRemainingNotIn(List<BigDecimal> values) {
            addCriterion("material_cost_remaining not in", values, "materialCostRemaining");
            return (Criteria) this;
        }

        public Criteria andMaterialCostRemainingBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_cost_remaining between", value1, value2, "materialCostRemaining");
            return (Criteria) this;
        }

        public Criteria andMaterialCostRemainingNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_cost_remaining not between", value1, value2, "materialCostRemaining");
            return (Criteria) this;
        }

        public Criteria andInHumanCostActualIsNull() {
            addCriterion("in_human_cost_actual is null");
            return (Criteria) this;
        }

        public Criteria andInHumanCostActualIsNotNull() {
            addCriterion("in_human_cost_actual is not null");
            return (Criteria) this;
        }

        public Criteria andInHumanCostActualEqualTo(BigDecimal value) {
            addCriterion("in_human_cost_actual =", value, "inHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andInHumanCostActualNotEqualTo(BigDecimal value) {
            addCriterion("in_human_cost_actual <>", value, "inHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andInHumanCostActualGreaterThan(BigDecimal value) {
            addCriterion("in_human_cost_actual >", value, "inHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andInHumanCostActualGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("in_human_cost_actual >=", value, "inHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andInHumanCostActualLessThan(BigDecimal value) {
            addCriterion("in_human_cost_actual <", value, "inHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andInHumanCostActualLessThanOrEqualTo(BigDecimal value) {
            addCriterion("in_human_cost_actual <=", value, "inHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andInHumanCostActualIn(List<BigDecimal> values) {
            addCriterion("in_human_cost_actual in", values, "inHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andInHumanCostActualNotIn(List<BigDecimal> values) {
            addCriterion("in_human_cost_actual not in", values, "inHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andInHumanCostActualBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_human_cost_actual between", value1, value2, "inHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andInHumanCostActualNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_human_cost_actual not between", value1, value2, "inHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andInHumanCostRemainingIsNull() {
            addCriterion("in_human_cost_remaining is null");
            return (Criteria) this;
        }

        public Criteria andInHumanCostRemainingIsNotNull() {
            addCriterion("in_human_cost_remaining is not null");
            return (Criteria) this;
        }

        public Criteria andInHumanCostRemainingEqualTo(BigDecimal value) {
            addCriterion("in_human_cost_remaining =", value, "inHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andInHumanCostRemainingNotEqualTo(BigDecimal value) {
            addCriterion("in_human_cost_remaining <>", value, "inHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andInHumanCostRemainingGreaterThan(BigDecimal value) {
            addCriterion("in_human_cost_remaining >", value, "inHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andInHumanCostRemainingGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("in_human_cost_remaining >=", value, "inHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andInHumanCostRemainingLessThan(BigDecimal value) {
            addCriterion("in_human_cost_remaining <", value, "inHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andInHumanCostRemainingLessThanOrEqualTo(BigDecimal value) {
            addCriterion("in_human_cost_remaining <=", value, "inHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andInHumanCostRemainingIn(List<BigDecimal> values) {
            addCriterion("in_human_cost_remaining in", values, "inHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andInHumanCostRemainingNotIn(List<BigDecimal> values) {
            addCriterion("in_human_cost_remaining not in", values, "inHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andInHumanCostRemainingBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_human_cost_remaining between", value1, value2, "inHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andInHumanCostRemainingNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_human_cost_remaining not between", value1, value2, "inHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostActualIsNull() {
            addCriterion("out_human_cost_actual is null");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostActualIsNotNull() {
            addCriterion("out_human_cost_actual is not null");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostActualEqualTo(BigDecimal value) {
            addCriterion("out_human_cost_actual =", value, "outHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostActualNotEqualTo(BigDecimal value) {
            addCriterion("out_human_cost_actual <>", value, "outHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostActualGreaterThan(BigDecimal value) {
            addCriterion("out_human_cost_actual >", value, "outHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostActualGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("out_human_cost_actual >=", value, "outHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostActualLessThan(BigDecimal value) {
            addCriterion("out_human_cost_actual <", value, "outHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostActualLessThanOrEqualTo(BigDecimal value) {
            addCriterion("out_human_cost_actual <=", value, "outHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostActualIn(List<BigDecimal> values) {
            addCriterion("out_human_cost_actual in", values, "outHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostActualNotIn(List<BigDecimal> values) {
            addCriterion("out_human_cost_actual not in", values, "outHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostActualBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("out_human_cost_actual between", value1, value2, "outHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostActualNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("out_human_cost_actual not between", value1, value2, "outHumanCostActual");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostRemainingIsNull() {
            addCriterion("out_human_cost_remaining is null");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostRemainingIsNotNull() {
            addCriterion("out_human_cost_remaining is not null");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostRemainingEqualTo(BigDecimal value) {
            addCriterion("out_human_cost_remaining =", value, "outHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostRemainingNotEqualTo(BigDecimal value) {
            addCriterion("out_human_cost_remaining <>", value, "outHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostRemainingGreaterThan(BigDecimal value) {
            addCriterion("out_human_cost_remaining >", value, "outHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostRemainingGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("out_human_cost_remaining >=", value, "outHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostRemainingLessThan(BigDecimal value) {
            addCriterion("out_human_cost_remaining <", value, "outHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostRemainingLessThanOrEqualTo(BigDecimal value) {
            addCriterion("out_human_cost_remaining <=", value, "outHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostRemainingIn(List<BigDecimal> values) {
            addCriterion("out_human_cost_remaining in", values, "outHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostRemainingNotIn(List<BigDecimal> values) {
            addCriterion("out_human_cost_remaining not in", values, "outHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostRemainingBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("out_human_cost_remaining between", value1, value2, "outHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andOutHumanCostRemainingNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("out_human_cost_remaining not between", value1, value2, "outHumanCostRemaining");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualIsNull() {
            addCriterion("travel_cost_actual is null");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualIsNotNull() {
            addCriterion("travel_cost_actual is not null");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualEqualTo(BigDecimal value) {
            addCriterion("travel_cost_actual =", value, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualNotEqualTo(BigDecimal value) {
            addCriterion("travel_cost_actual <>", value, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualGreaterThan(BigDecimal value) {
            addCriterion("travel_cost_actual >", value, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_cost_actual >=", value, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualLessThan(BigDecimal value) {
            addCriterion("travel_cost_actual <", value, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualLessThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_cost_actual <=", value, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualIn(List<BigDecimal> values) {
            addCriterion("travel_cost_actual in", values, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualNotIn(List<BigDecimal> values) {
            addCriterion("travel_cost_actual not in", values, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_cost_actual between", value1, value2, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_cost_actual not between", value1, value2, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostRemainingIsNull() {
            addCriterion("travel_cost_remaining is null");
            return (Criteria) this;
        }

        public Criteria andTravelCostRemainingIsNotNull() {
            addCriterion("travel_cost_remaining is not null");
            return (Criteria) this;
        }

        public Criteria andTravelCostRemainingEqualTo(BigDecimal value) {
            addCriterion("travel_cost_remaining =", value, "travelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andTravelCostRemainingNotEqualTo(BigDecimal value) {
            addCriterion("travel_cost_remaining <>", value, "travelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andTravelCostRemainingGreaterThan(BigDecimal value) {
            addCriterion("travel_cost_remaining >", value, "travelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andTravelCostRemainingGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_cost_remaining >=", value, "travelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andTravelCostRemainingLessThan(BigDecimal value) {
            addCriterion("travel_cost_remaining <", value, "travelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andTravelCostRemainingLessThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_cost_remaining <=", value, "travelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andTravelCostRemainingIn(List<BigDecimal> values) {
            addCriterion("travel_cost_remaining in", values, "travelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andTravelCostRemainingNotIn(List<BigDecimal> values) {
            addCriterion("travel_cost_remaining not in", values, "travelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andTravelCostRemainingBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_cost_remaining between", value1, value2, "travelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andTravelCostRemainingNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_cost_remaining not between", value1, value2, "travelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andTravelEaAvailableAmountIsNull() {
            addCriterion("travel_ea_available_amount is null");
            return (Criteria) this;
        }

        public Criteria andTravelEaAvailableAmountIsNotNull() {
            addCriterion("travel_ea_available_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTravelEaAvailableAmountEqualTo(BigDecimal value) {
            addCriterion("travel_ea_available_amount =", value, "travelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andTravelEaAvailableAmountNotEqualTo(BigDecimal value) {
            addCriterion("travel_ea_available_amount <>", value, "travelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andTravelEaAvailableAmountGreaterThan(BigDecimal value) {
            addCriterion("travel_ea_available_amount >", value, "travelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andTravelEaAvailableAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_ea_available_amount >=", value, "travelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andTravelEaAvailableAmountLessThan(BigDecimal value) {
            addCriterion("travel_ea_available_amount <", value, "travelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andTravelEaAvailableAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_ea_available_amount <=", value, "travelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andTravelEaAvailableAmountIn(List<BigDecimal> values) {
            addCriterion("travel_ea_available_amount in", values, "travelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andTravelEaAvailableAmountNotIn(List<BigDecimal> values) {
            addCriterion("travel_ea_available_amount not in", values, "travelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andTravelEaAvailableAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_ea_available_amount between", value1, value2, "travelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andTravelEaAvailableAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_ea_available_amount not between", value1, value2, "travelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualIsNull() {
            addCriterion("no_travel_cost_actual is null");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualIsNotNull() {
            addCriterion("no_travel_cost_actual is not null");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualEqualTo(BigDecimal value) {
            addCriterion("no_travel_cost_actual =", value, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualNotEqualTo(BigDecimal value) {
            addCriterion("no_travel_cost_actual <>", value, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualGreaterThan(BigDecimal value) {
            addCriterion("no_travel_cost_actual >", value, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("no_travel_cost_actual >=", value, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualLessThan(BigDecimal value) {
            addCriterion("no_travel_cost_actual <", value, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualLessThanOrEqualTo(BigDecimal value) {
            addCriterion("no_travel_cost_actual <=", value, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualIn(List<BigDecimal> values) {
            addCriterion("no_travel_cost_actual in", values, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualNotIn(List<BigDecimal> values) {
            addCriterion("no_travel_cost_actual not in", values, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_travel_cost_actual between", value1, value2, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_travel_cost_actual not between", value1, value2, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostRemainingIsNull() {
            addCriterion("no_travel_cost_remaining is null");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostRemainingIsNotNull() {
            addCriterion("no_travel_cost_remaining is not null");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostRemainingEqualTo(BigDecimal value) {
            addCriterion("no_travel_cost_remaining =", value, "noTravelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostRemainingNotEqualTo(BigDecimal value) {
            addCriterion("no_travel_cost_remaining <>", value, "noTravelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostRemainingGreaterThan(BigDecimal value) {
            addCriterion("no_travel_cost_remaining >", value, "noTravelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostRemainingGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("no_travel_cost_remaining >=", value, "noTravelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostRemainingLessThan(BigDecimal value) {
            addCriterion("no_travel_cost_remaining <", value, "noTravelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostRemainingLessThanOrEqualTo(BigDecimal value) {
            addCriterion("no_travel_cost_remaining <=", value, "noTravelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostRemainingIn(List<BigDecimal> values) {
            addCriterion("no_travel_cost_remaining in", values, "noTravelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostRemainingNotIn(List<BigDecimal> values) {
            addCriterion("no_travel_cost_remaining not in", values, "noTravelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostRemainingBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_travel_cost_remaining between", value1, value2, "noTravelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostRemainingNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_travel_cost_remaining not between", value1, value2, "noTravelCostRemaining");
            return (Criteria) this;
        }

        public Criteria andNoTravelEaAvailableAmountIsNull() {
            addCriterion("no_travel_ea_available_amount is null");
            return (Criteria) this;
        }

        public Criteria andNoTravelEaAvailableAmountIsNotNull() {
            addCriterion("no_travel_ea_available_amount is not null");
            return (Criteria) this;
        }

        public Criteria andNoTravelEaAvailableAmountEqualTo(BigDecimal value) {
            addCriterion("no_travel_ea_available_amount =", value, "noTravelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andNoTravelEaAvailableAmountNotEqualTo(BigDecimal value) {
            addCriterion("no_travel_ea_available_amount <>", value, "noTravelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andNoTravelEaAvailableAmountGreaterThan(BigDecimal value) {
            addCriterion("no_travel_ea_available_amount >", value, "noTravelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andNoTravelEaAvailableAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("no_travel_ea_available_amount >=", value, "noTravelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andNoTravelEaAvailableAmountLessThan(BigDecimal value) {
            addCriterion("no_travel_ea_available_amount <", value, "noTravelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andNoTravelEaAvailableAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("no_travel_ea_available_amount <=", value, "noTravelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andNoTravelEaAvailableAmountIn(List<BigDecimal> values) {
            addCriterion("no_travel_ea_available_amount in", values, "noTravelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andNoTravelEaAvailableAmountNotIn(List<BigDecimal> values) {
            addCriterion("no_travel_ea_available_amount not in", values, "noTravelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andNoTravelEaAvailableAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_travel_ea_available_amount between", value1, value2, "noTravelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andNoTravelEaAvailableAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_travel_ea_available_amount not between", value1, value2, "noTravelEaAvailableAmount");
            return (Criteria) this;
        }

        public Criteria andShareCostActualIsNull() {
            addCriterion("Share_cost_actual is null");
            return (Criteria) this;
        }

        public Criteria andShareCostActualIsNotNull() {
            addCriterion("Share_cost_actual is not null");
            return (Criteria) this;
        }

        public Criteria andShareCostActualEqualTo(BigDecimal value) {
            addCriterion("Share_cost_actual =", value, "shareCostActual");
            return (Criteria) this;
        }

        public Criteria andShareCostActualNotEqualTo(BigDecimal value) {
            addCriterion("Share_cost_actual <>", value, "shareCostActual");
            return (Criteria) this;
        }

        public Criteria andShareCostActualGreaterThan(BigDecimal value) {
            addCriterion("Share_cost_actual >", value, "shareCostActual");
            return (Criteria) this;
        }

        public Criteria andShareCostActualGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("Share_cost_actual >=", value, "shareCostActual");
            return (Criteria) this;
        }

        public Criteria andShareCostActualLessThan(BigDecimal value) {
            addCriterion("Share_cost_actual <", value, "shareCostActual");
            return (Criteria) this;
        }

        public Criteria andShareCostActualLessThanOrEqualTo(BigDecimal value) {
            addCriterion("Share_cost_actual <=", value, "shareCostActual");
            return (Criteria) this;
        }

        public Criteria andShareCostActualIn(List<BigDecimal> values) {
            addCriterion("Share_cost_actual in", values, "shareCostActual");
            return (Criteria) this;
        }

        public Criteria andShareCostActualNotIn(List<BigDecimal> values) {
            addCriterion("Share_cost_actual not in", values, "shareCostActual");
            return (Criteria) this;
        }

        public Criteria andShareCostActualBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("Share_cost_actual between", value1, value2, "shareCostActual");
            return (Criteria) this;
        }

        public Criteria andShareCostActualNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("Share_cost_actual not between", value1, value2, "shareCostActual");
            return (Criteria) this;
        }

        public Criteria andActualCostProportionIsNull() {
            addCriterion("actual_cost_proportion is null");
            return (Criteria) this;
        }

        public Criteria andActualCostProportionIsNotNull() {
            addCriterion("actual_cost_proportion is not null");
            return (Criteria) this;
        }

        public Criteria andActualCostProportionEqualTo(BigDecimal value) {
            addCriterion("actual_cost_proportion =", value, "actualCostProportion");
            return (Criteria) this;
        }

        public Criteria andActualCostProportionNotEqualTo(BigDecimal value) {
            addCriterion("actual_cost_proportion <>", value, "actualCostProportion");
            return (Criteria) this;
        }

        public Criteria andActualCostProportionGreaterThan(BigDecimal value) {
            addCriterion("actual_cost_proportion >", value, "actualCostProportion");
            return (Criteria) this;
        }

        public Criteria andActualCostProportionGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_cost_proportion >=", value, "actualCostProportion");
            return (Criteria) this;
        }

        public Criteria andActualCostProportionLessThan(BigDecimal value) {
            addCriterion("actual_cost_proportion <", value, "actualCostProportion");
            return (Criteria) this;
        }

        public Criteria andActualCostProportionLessThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_cost_proportion <=", value, "actualCostProportion");
            return (Criteria) this;
        }

        public Criteria andActualCostProportionIn(List<BigDecimal> values) {
            addCriterion("actual_cost_proportion in", values, "actualCostProportion");
            return (Criteria) this;
        }

        public Criteria andActualCostProportionNotIn(List<BigDecimal> values) {
            addCriterion("actual_cost_proportion not in", values, "actualCostProportion");
            return (Criteria) this;
        }

        public Criteria andActualCostProportionBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_cost_proportion between", value1, value2, "actualCostProportion");
            return (Criteria) this;
        }

        public Criteria andActualCostProportionNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_cost_proportion not between", value1, value2, "actualCostProportion");
            return (Criteria) this;
        }

        public Criteria andActualProfitsIsNull() {
            addCriterion("actual_profits is null");
            return (Criteria) this;
        }

        public Criteria andActualProfitsIsNotNull() {
            addCriterion("actual_profits is not null");
            return (Criteria) this;
        }

        public Criteria andActualProfitsEqualTo(BigDecimal value) {
            addCriterion("actual_profits =", value, "actualProfits");
            return (Criteria) this;
        }

        public Criteria andActualProfitsNotEqualTo(BigDecimal value) {
            addCriterion("actual_profits <>", value, "actualProfits");
            return (Criteria) this;
        }

        public Criteria andActualProfitsGreaterThan(BigDecimal value) {
            addCriterion("actual_profits >", value, "actualProfits");
            return (Criteria) this;
        }

        public Criteria andActualProfitsGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_profits >=", value, "actualProfits");
            return (Criteria) this;
        }

        public Criteria andActualProfitsLessThan(BigDecimal value) {
            addCriterion("actual_profits <", value, "actualProfits");
            return (Criteria) this;
        }

        public Criteria andActualProfitsLessThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_profits <=", value, "actualProfits");
            return (Criteria) this;
        }

        public Criteria andActualProfitsIn(List<BigDecimal> values) {
            addCriterion("actual_profits in", values, "actualProfits");
            return (Criteria) this;
        }

        public Criteria andActualProfitsNotIn(List<BigDecimal> values) {
            addCriterion("actual_profits not in", values, "actualProfits");
            return (Criteria) this;
        }

        public Criteria andActualProfitsBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_profits between", value1, value2, "actualProfits");
            return (Criteria) this;
        }

        public Criteria andActualProfitsNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_profits not between", value1, value2, "actualProfits");
            return (Criteria) this;
        }

        public Criteria andActualProfitsRateIsNull() {
            addCriterion("actual_profits_rate is null");
            return (Criteria) this;
        }

        public Criteria andActualProfitsRateIsNotNull() {
            addCriterion("actual_profits_rate is not null");
            return (Criteria) this;
        }

        public Criteria andActualProfitsRateEqualTo(BigDecimal value) {
            addCriterion("actual_profits_rate =", value, "actualProfitsRate");
            return (Criteria) this;
        }

        public Criteria andActualProfitsRateNotEqualTo(BigDecimal value) {
            addCriterion("actual_profits_rate <>", value, "actualProfitsRate");
            return (Criteria) this;
        }

        public Criteria andActualProfitsRateGreaterThan(BigDecimal value) {
            addCriterion("actual_profits_rate >", value, "actualProfitsRate");
            return (Criteria) this;
        }

        public Criteria andActualProfitsRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_profits_rate >=", value, "actualProfitsRate");
            return (Criteria) this;
        }

        public Criteria andActualProfitsRateLessThan(BigDecimal value) {
            addCriterion("actual_profits_rate <", value, "actualProfitsRate");
            return (Criteria) this;
        }

        public Criteria andActualProfitsRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_profits_rate <=", value, "actualProfitsRate");
            return (Criteria) this;
        }

        public Criteria andActualProfitsRateIn(List<BigDecimal> values) {
            addCriterion("actual_profits_rate in", values, "actualProfitsRate");
            return (Criteria) this;
        }

        public Criteria andActualProfitsRateNotIn(List<BigDecimal> values) {
            addCriterion("actual_profits_rate not in", values, "actualProfitsRate");
            return (Criteria) this;
        }

        public Criteria andActualProfitsRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_profits_rate between", value1, value2, "actualProfitsRate");
            return (Criteria) this;
        }

        public Criteria andActualProfitsRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_profits_rate not between", value1, value2, "actualProfitsRate");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNull() {
            addCriterion("contract_code is null");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNotNull() {
            addCriterion("contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andContractCodeEqualTo(String value) {
            addCriterion("contract_code =", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotEqualTo(String value) {
            addCriterion("contract_code <>", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThan(String value) {
            addCriterion("contract_code >", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_code >=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThan(String value) {
            addCriterion("contract_code <", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThanOrEqualTo(String value) {
            addCriterion("contract_code <=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLike(String value) {
            addCriterion("contract_code like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotLike(String value) {
            addCriterion("contract_code not like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeIn(List<String> values) {
            addCriterion("contract_code in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotIn(List<String> values) {
            addCriterion("contract_code not in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeBetween(String value1, String value2) {
            addCriterion("contract_code between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotBetween(String value1, String value2) {
            addCriterion("contract_code not between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNull() {
            addCriterion("ou_name is null");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNotNull() {
            addCriterion("ou_name is not null");
            return (Criteria) this;
        }

        public Criteria andOuNameEqualTo(String value) {
            addCriterion("ou_name =", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotEqualTo(String value) {
            addCriterion("ou_name <>", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThan(String value) {
            addCriterion("ou_name >", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThanOrEqualTo(String value) {
            addCriterion("ou_name >=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThan(String value) {
            addCriterion("ou_name <", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThanOrEqualTo(String value) {
            addCriterion("ou_name <=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLike(String value) {
            addCriterion("ou_name like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotLike(String value) {
            addCriterion("ou_name not like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameIn(List<String> values) {
            addCriterion("ou_name in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotIn(List<String> values) {
            addCriterion("ou_name not in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameBetween(String value1, String value2) {
            addCriterion("ou_name between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotBetween(String value1, String value2) {
            addCriterion("ou_name not between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}