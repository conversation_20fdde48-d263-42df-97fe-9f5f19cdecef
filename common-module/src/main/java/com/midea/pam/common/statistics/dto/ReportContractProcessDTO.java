package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.ReportContractProcess;

import java.math.BigDecimal;

public class ReportContractProcessDTO extends ReportContractProcess {

    private Long contractId;

    private Long changewayId;

    private String newOuName;

    private String newCustomerCode;

    private String newCustomerName;

    private Integer newCustomerType;

    private String newUnitName;

    private BigDecimal newExcludingTaxAmount;

    public BigDecimal getNewExcludingTaxAmount() {
        return newExcludingTaxAmount;
    }

    public void setNewExcludingTaxAmount(BigDecimal newExcludingTaxAmount) {
        this.newExcludingTaxAmount = newExcludingTaxAmount;
    }

    public String getNewOuName() {
        return newOuName;
    }

    public void setNewOuName(String newOuName) {
        this.newOuName = newOuName;
    }

    public String getNewCustomerCode() {
        return newCustomerCode;
    }

    public void setNewCustomerCode(String newCustomerCode) {
        this.newCustomerCode = newCustomerCode;
    }

    public String getNewCustomerName() {
        return newCustomerName;
    }

    public void setNewCustomerName(String newCustomerName) {
        this.newCustomerName = newCustomerName;
    }

    public Integer getNewCustomerType() {
        return newCustomerType;
    }

    public void setNewCustomerType(Integer newCustomerType) {
        this.newCustomerType = newCustomerType;
    }

    public String getNewUnitName() {
        return newUnitName;
    }

    public void setNewUnitName(String newUnitName) {
        this.newUnitName = newUnitName;
    }

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public Long getChangewayId() {
        return changewayId;
    }

    public void setChangewayId(Long changewayId) {
        this.changewayId = changewayId;
    }
}