package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectCostSummaryItemRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectCostSummaryItemRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andItemNameIsNull() {
            addCriterion("item_name is null");
            return (Criteria) this;
        }

        public Criteria andItemNameIsNotNull() {
            addCriterion("item_name is not null");
            return (Criteria) this;
        }

        public Criteria andItemNameEqualTo(String value) {
            addCriterion("item_name =", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotEqualTo(String value) {
            addCriterion("item_name <>", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameGreaterThan(String value) {
            addCriterion("item_name >", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameGreaterThanOrEqualTo(String value) {
            addCriterion("item_name >=", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLessThan(String value) {
            addCriterion("item_name <", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLessThanOrEqualTo(String value) {
            addCriterion("item_name <=", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLike(String value) {
            addCriterion("item_name like", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotLike(String value) {
            addCriterion("item_name not like", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameIn(List<String> values) {
            addCriterion("item_name in", values, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotIn(List<String> values) {
            addCriterion("item_name not in", values, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameBetween(String value1, String value2) {
            addCriterion("item_name between", value1, value2, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotBetween(String value1, String value2) {
            addCriterion("item_name not between", value1, value2, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemTypeIsNull() {
            addCriterion("item_type is null");
            return (Criteria) this;
        }

        public Criteria andItemTypeIsNotNull() {
            addCriterion("item_type is not null");
            return (Criteria) this;
        }

        public Criteria andItemTypeEqualTo(Byte value) {
            addCriterion("item_type =", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotEqualTo(Byte value) {
            addCriterion("item_type <>", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeGreaterThan(Byte value) {
            addCriterion("item_type >", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("item_type >=", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeLessThan(Byte value) {
            addCriterion("item_type <", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeLessThanOrEqualTo(Byte value) {
            addCriterion("item_type <=", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeIn(List<Byte> values) {
            addCriterion("item_type in", values, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotIn(List<Byte> values) {
            addCriterion("item_type not in", values, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeBetween(Byte value1, Byte value2) {
            addCriterion("item_type between", value1, value2, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("item_type not between", value1, value2, "itemType");
            return (Criteria) this;
        }

        public Criteria andWinBudgetIsNull() {
            addCriterion("win_budget is null");
            return (Criteria) this;
        }

        public Criteria andWinBudgetIsNotNull() {
            addCriterion("win_budget is not null");
            return (Criteria) this;
        }

        public Criteria andWinBudgetEqualTo(BigDecimal value) {
            addCriterion("win_budget =", value, "winBudget");
            return (Criteria) this;
        }

        public Criteria andWinBudgetNotEqualTo(BigDecimal value) {
            addCriterion("win_budget <>", value, "winBudget");
            return (Criteria) this;
        }

        public Criteria andWinBudgetGreaterThan(BigDecimal value) {
            addCriterion("win_budget >", value, "winBudget");
            return (Criteria) this;
        }

        public Criteria andWinBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("win_budget >=", value, "winBudget");
            return (Criteria) this;
        }

        public Criteria andWinBudgetLessThan(BigDecimal value) {
            addCriterion("win_budget <", value, "winBudget");
            return (Criteria) this;
        }

        public Criteria andWinBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("win_budget <=", value, "winBudget");
            return (Criteria) this;
        }

        public Criteria andWinBudgetIn(List<BigDecimal> values) {
            addCriterion("win_budget in", values, "winBudget");
            return (Criteria) this;
        }

        public Criteria andWinBudgetNotIn(List<BigDecimal> values) {
            addCriterion("win_budget not in", values, "winBudget");
            return (Criteria) this;
        }

        public Criteria andWinBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("win_budget between", value1, value2, "winBudget");
            return (Criteria) this;
        }

        public Criteria andWinBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("win_budget not between", value1, value2, "winBudget");
            return (Criteria) this;
        }

        public Criteria andCurrentBudgetIsNull() {
            addCriterion("current_budget is null");
            return (Criteria) this;
        }

        public Criteria andCurrentBudgetIsNotNull() {
            addCriterion("current_budget is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentBudgetEqualTo(BigDecimal value) {
            addCriterion("current_budget =", value, "currentBudget");
            return (Criteria) this;
        }

        public Criteria andCurrentBudgetNotEqualTo(BigDecimal value) {
            addCriterion("current_budget <>", value, "currentBudget");
            return (Criteria) this;
        }

        public Criteria andCurrentBudgetGreaterThan(BigDecimal value) {
            addCriterion("current_budget >", value, "currentBudget");
            return (Criteria) this;
        }

        public Criteria andCurrentBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("current_budget >=", value, "currentBudget");
            return (Criteria) this;
        }

        public Criteria andCurrentBudgetLessThan(BigDecimal value) {
            addCriterion("current_budget <", value, "currentBudget");
            return (Criteria) this;
        }

        public Criteria andCurrentBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("current_budget <=", value, "currentBudget");
            return (Criteria) this;
        }

        public Criteria andCurrentBudgetIn(List<BigDecimal> values) {
            addCriterion("current_budget in", values, "currentBudget");
            return (Criteria) this;
        }

        public Criteria andCurrentBudgetNotIn(List<BigDecimal> values) {
            addCriterion("current_budget not in", values, "currentBudget");
            return (Criteria) this;
        }

        public Criteria andCurrentBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_budget between", value1, value2, "currentBudget");
            return (Criteria) this;
        }

        public Criteria andCurrentBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_budget not between", value1, value2, "currentBudget");
            return (Criteria) this;
        }

        public Criteria andIncurredCostIsNull() {
            addCriterion("incurred_cost is null");
            return (Criteria) this;
        }

        public Criteria andIncurredCostIsNotNull() {
            addCriterion("incurred_cost is not null");
            return (Criteria) this;
        }

        public Criteria andIncurredCostEqualTo(BigDecimal value) {
            addCriterion("incurred_cost =", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostNotEqualTo(BigDecimal value) {
            addCriterion("incurred_cost <>", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostGreaterThan(BigDecimal value) {
            addCriterion("incurred_cost >", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_cost >=", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLessThan(BigDecimal value) {
            addCriterion("incurred_cost <", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_cost <=", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostIn(List<BigDecimal> values) {
            addCriterion("incurred_cost in", values, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostNotIn(List<BigDecimal> values) {
            addCriterion("incurred_cost not in", values, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_cost between", value1, value2, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_cost not between", value1, value2, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andPendingCostIsNull() {
            addCriterion("pending_cost is null");
            return (Criteria) this;
        }

        public Criteria andPendingCostIsNotNull() {
            addCriterion("pending_cost is not null");
            return (Criteria) this;
        }

        public Criteria andPendingCostEqualTo(BigDecimal value) {
            addCriterion("pending_cost =", value, "pendingCost");
            return (Criteria) this;
        }

        public Criteria andPendingCostNotEqualTo(BigDecimal value) {
            addCriterion("pending_cost <>", value, "pendingCost");
            return (Criteria) this;
        }

        public Criteria andPendingCostGreaterThan(BigDecimal value) {
            addCriterion("pending_cost >", value, "pendingCost");
            return (Criteria) this;
        }

        public Criteria andPendingCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("pending_cost >=", value, "pendingCost");
            return (Criteria) this;
        }

        public Criteria andPendingCostLessThan(BigDecimal value) {
            addCriterion("pending_cost <", value, "pendingCost");
            return (Criteria) this;
        }

        public Criteria andPendingCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("pending_cost <=", value, "pendingCost");
            return (Criteria) this;
        }

        public Criteria andPendingCostIn(List<BigDecimal> values) {
            addCriterion("pending_cost in", values, "pendingCost");
            return (Criteria) this;
        }

        public Criteria andPendingCostNotIn(List<BigDecimal> values) {
            addCriterion("pending_cost not in", values, "pendingCost");
            return (Criteria) this;
        }

        public Criteria andPendingCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pending_cost between", value1, value2, "pendingCost");
            return (Criteria) this;
        }

        public Criteria andPendingCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pending_cost not between", value1, value2, "pendingCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostIsNull() {
            addCriterion("total_cost is null");
            return (Criteria) this;
        }

        public Criteria andTotalCostIsNotNull() {
            addCriterion("total_cost is not null");
            return (Criteria) this;
        }

        public Criteria andTotalCostEqualTo(BigDecimal value) {
            addCriterion("total_cost =", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostNotEqualTo(BigDecimal value) {
            addCriterion("total_cost <>", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostGreaterThan(BigDecimal value) {
            addCriterion("total_cost >", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_cost >=", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostLessThan(BigDecimal value) {
            addCriterion("total_cost <", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_cost <=", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostIn(List<BigDecimal> values) {
            addCriterion("total_cost in", values, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostNotIn(List<BigDecimal> values) {
            addCriterion("total_cost not in", values, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_cost between", value1, value2, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_cost not between", value1, value2, "totalCost");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetIsNull() {
            addCriterion("remainder_budget is null");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetIsNotNull() {
            addCriterion("remainder_budget is not null");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetEqualTo(BigDecimal value) {
            addCriterion("remainder_budget =", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetNotEqualTo(BigDecimal value) {
            addCriterion("remainder_budget <>", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetGreaterThan(BigDecimal value) {
            addCriterion("remainder_budget >", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("remainder_budget >=", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetLessThan(BigDecimal value) {
            addCriterion("remainder_budget <", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("remainder_budget <=", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetIn(List<BigDecimal> values) {
            addCriterion("remainder_budget in", values, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetNotIn(List<BigDecimal> values) {
            addCriterion("remainder_budget not in", values, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("remainder_budget between", value1, value2, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("remainder_budget not between", value1, value2, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateIsNull() {
            addCriterion("target_cost_deviation_rate is null");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateIsNotNull() {
            addCriterion("target_cost_deviation_rate is not null");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateEqualTo(BigDecimal value) {
            addCriterion("target_cost_deviation_rate =", value, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateNotEqualTo(BigDecimal value) {
            addCriterion("target_cost_deviation_rate <>", value, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateGreaterThan(BigDecimal value) {
            addCriterion("target_cost_deviation_rate >", value, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("target_cost_deviation_rate >=", value, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateLessThan(BigDecimal value) {
            addCriterion("target_cost_deviation_rate <", value, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("target_cost_deviation_rate <=", value, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateIn(List<BigDecimal> values) {
            addCriterion("target_cost_deviation_rate in", values, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateNotIn(List<BigDecimal> values) {
            addCriterion("target_cost_deviation_rate not in", values, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("target_cost_deviation_rate between", value1, value2, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("target_cost_deviation_rate not between", value1, value2, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationIsNull() {
            addCriterion("target_cost_deviation is null");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationIsNotNull() {
            addCriterion("target_cost_deviation is not null");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationEqualTo(BigDecimal value) {
            addCriterion("target_cost_deviation =", value, "targetCostDeviation");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationNotEqualTo(BigDecimal value) {
            addCriterion("target_cost_deviation <>", value, "targetCostDeviation");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationGreaterThan(BigDecimal value) {
            addCriterion("target_cost_deviation >", value, "targetCostDeviation");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("target_cost_deviation >=", value, "targetCostDeviation");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationLessThan(BigDecimal value) {
            addCriterion("target_cost_deviation <", value, "targetCostDeviation");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationLessThanOrEqualTo(BigDecimal value) {
            addCriterion("target_cost_deviation <=", value, "targetCostDeviation");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationIn(List<BigDecimal> values) {
            addCriterion("target_cost_deviation in", values, "targetCostDeviation");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationNotIn(List<BigDecimal> values) {
            addCriterion("target_cost_deviation not in", values, "targetCostDeviation");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("target_cost_deviation between", value1, value2, "targetCostDeviation");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("target_cost_deviation not between", value1, value2, "targetCostDeviation");
            return (Criteria) this;
        }

        public Criteria andRemainTargetCostIsNull() {
            addCriterion("remain_target_cost is null");
            return (Criteria) this;
        }

        public Criteria andRemainTargetCostIsNotNull() {
            addCriterion("remain_target_cost is not null");
            return (Criteria) this;
        }

        public Criteria andRemainTargetCostEqualTo(BigDecimal value) {
            addCriterion("remain_target_cost =", value, "remainTargetCost");
            return (Criteria) this;
        }

        public Criteria andRemainTargetCostNotEqualTo(BigDecimal value) {
            addCriterion("remain_target_cost <>", value, "remainTargetCost");
            return (Criteria) this;
        }

        public Criteria andRemainTargetCostGreaterThan(BigDecimal value) {
            addCriterion("remain_target_cost >", value, "remainTargetCost");
            return (Criteria) this;
        }

        public Criteria andRemainTargetCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("remain_target_cost >=", value, "remainTargetCost");
            return (Criteria) this;
        }

        public Criteria andRemainTargetCostLessThan(BigDecimal value) {
            addCriterion("remain_target_cost <", value, "remainTargetCost");
            return (Criteria) this;
        }

        public Criteria andRemainTargetCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("remain_target_cost <=", value, "remainTargetCost");
            return (Criteria) this;
        }

        public Criteria andRemainTargetCostIn(List<BigDecimal> values) {
            addCriterion("remain_target_cost in", values, "remainTargetCost");
            return (Criteria) this;
        }

        public Criteria andRemainTargetCostNotIn(List<BigDecimal> values) {
            addCriterion("remain_target_cost not in", values, "remainTargetCost");
            return (Criteria) this;
        }

        public Criteria andRemainTargetCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("remain_target_cost between", value1, value2, "remainTargetCost");
            return (Criteria) this;
        }

        public Criteria andRemainTargetCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("remain_target_cost not between", value1, value2, "remainTargetCost");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetBudgetIsNull() {
            addCriterion("outer_adjust_target_budget is null");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetBudgetIsNotNull() {
            addCriterion("outer_adjust_target_budget is not null");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetBudgetEqualTo(BigDecimal value) {
            addCriterion("outer_adjust_target_budget =", value, "outerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetBudgetNotEqualTo(BigDecimal value) {
            addCriterion("outer_adjust_target_budget <>", value, "outerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetBudgetGreaterThan(BigDecimal value) {
            addCriterion("outer_adjust_target_budget >", value, "outerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("outer_adjust_target_budget >=", value, "outerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetBudgetLessThan(BigDecimal value) {
            addCriterion("outer_adjust_target_budget <", value, "outerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("outer_adjust_target_budget <=", value, "outerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetBudgetIn(List<BigDecimal> values) {
            addCriterion("outer_adjust_target_budget in", values, "outerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetBudgetNotIn(List<BigDecimal> values) {
            addCriterion("outer_adjust_target_budget not in", values, "outerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outer_adjust_target_budget between", value1, value2, "outerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outer_adjust_target_budget not between", value1, value2, "outerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetBudgetIsNull() {
            addCriterion("inner_adjust_target_budget is null");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetBudgetIsNotNull() {
            addCriterion("inner_adjust_target_budget is not null");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetBudgetEqualTo(BigDecimal value) {
            addCriterion("inner_adjust_target_budget =", value, "innerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetBudgetNotEqualTo(BigDecimal value) {
            addCriterion("inner_adjust_target_budget <>", value, "innerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetBudgetGreaterThan(BigDecimal value) {
            addCriterion("inner_adjust_target_budget >", value, "innerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_adjust_target_budget >=", value, "innerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetBudgetLessThan(BigDecimal value) {
            addCriterion("inner_adjust_target_budget <", value, "innerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_adjust_target_budget <=", value, "innerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetBudgetIn(List<BigDecimal> values) {
            addCriterion("inner_adjust_target_budget in", values, "innerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetBudgetNotIn(List<BigDecimal> values) {
            addCriterion("inner_adjust_target_budget not in", values, "innerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_adjust_target_budget between", value1, value2, "innerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_adjust_target_budget not between", value1, value2, "innerAdjustTargetBudget");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostIsNull() {
            addCriterion("current_target_cost is null");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostIsNotNull() {
            addCriterion("current_target_cost is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostEqualTo(BigDecimal value) {
            addCriterion("current_target_cost =", value, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostNotEqualTo(BigDecimal value) {
            addCriterion("current_target_cost <>", value, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostGreaterThan(BigDecimal value) {
            addCriterion("current_target_cost >", value, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("current_target_cost >=", value, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostLessThan(BigDecimal value) {
            addCriterion("current_target_cost <", value, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("current_target_cost <=", value, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostIn(List<BigDecimal> values) {
            addCriterion("current_target_cost in", values, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostNotIn(List<BigDecimal> values) {
            addCriterion("current_target_cost not in", values, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_target_cost between", value1, value2, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andCurrentTargetCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("current_target_cost not between", value1, value2, "currentTargetCost");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetCostIsNull() {
            addCriterion("outer_adjust_target_cost is null");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetCostIsNotNull() {
            addCriterion("outer_adjust_target_cost is not null");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetCostEqualTo(BigDecimal value) {
            addCriterion("outer_adjust_target_cost =", value, "outerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetCostNotEqualTo(BigDecimal value) {
            addCriterion("outer_adjust_target_cost <>", value, "outerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetCostGreaterThan(BigDecimal value) {
            addCriterion("outer_adjust_target_cost >", value, "outerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("outer_adjust_target_cost >=", value, "outerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetCostLessThan(BigDecimal value) {
            addCriterion("outer_adjust_target_cost <", value, "outerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("outer_adjust_target_cost <=", value, "outerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetCostIn(List<BigDecimal> values) {
            addCriterion("outer_adjust_target_cost in", values, "outerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetCostNotIn(List<BigDecimal> values) {
            addCriterion("outer_adjust_target_cost not in", values, "outerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outer_adjust_target_cost between", value1, value2, "outerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andOuterAdjustTargetCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outer_adjust_target_cost not between", value1, value2, "outerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetCostIsNull() {
            addCriterion("inner_adjust_target_cost is null");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetCostIsNotNull() {
            addCriterion("inner_adjust_target_cost is not null");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetCostEqualTo(BigDecimal value) {
            addCriterion("inner_adjust_target_cost =", value, "innerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetCostNotEqualTo(BigDecimal value) {
            addCriterion("inner_adjust_target_cost <>", value, "innerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetCostGreaterThan(BigDecimal value) {
            addCriterion("inner_adjust_target_cost >", value, "innerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_adjust_target_cost >=", value, "innerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetCostLessThan(BigDecimal value) {
            addCriterion("inner_adjust_target_cost <", value, "innerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_adjust_target_cost <=", value, "innerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetCostIn(List<BigDecimal> values) {
            addCriterion("inner_adjust_target_cost in", values, "innerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetCostNotIn(List<BigDecimal> values) {
            addCriterion("inner_adjust_target_cost not in", values, "innerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_adjust_target_cost between", value1, value2, "innerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andInnerAdjustTargetCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_adjust_target_cost not between", value1, value2, "innerAdjustTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTargetCostIsNull() {
            addCriterion("init_target_cost is null");
            return (Criteria) this;
        }

        public Criteria andInitTargetCostIsNotNull() {
            addCriterion("init_target_cost is not null");
            return (Criteria) this;
        }

        public Criteria andInitTargetCostEqualTo(BigDecimal value) {
            addCriterion("init_target_cost =", value, "initTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTargetCostNotEqualTo(BigDecimal value) {
            addCriterion("init_target_cost <>", value, "initTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTargetCostGreaterThan(BigDecimal value) {
            addCriterion("init_target_cost >", value, "initTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTargetCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("init_target_cost >=", value, "initTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTargetCostLessThan(BigDecimal value) {
            addCriterion("init_target_cost <", value, "initTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTargetCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("init_target_cost <=", value, "initTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTargetCostIn(List<BigDecimal> values) {
            addCriterion("init_target_cost in", values, "initTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTargetCostNotIn(List<BigDecimal> values) {
            addCriterion("init_target_cost not in", values, "initTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTargetCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("init_target_cost between", value1, value2, "initTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTargetCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("init_target_cost not between", value1, value2, "initTargetCost");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioIsNull() {
            addCriterion("incurred_ratio is null");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioIsNotNull() {
            addCriterion("incurred_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioEqualTo(BigDecimal value) {
            addCriterion("incurred_ratio =", value, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioNotEqualTo(BigDecimal value) {
            addCriterion("incurred_ratio <>", value, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioGreaterThan(BigDecimal value) {
            addCriterion("incurred_ratio >", value, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_ratio >=", value, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioLessThan(BigDecimal value) {
            addCriterion("incurred_ratio <", value, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_ratio <=", value, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioIn(List<BigDecimal> values) {
            addCriterion("incurred_ratio in", values, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioNotIn(List<BigDecimal> values) {
            addCriterion("incurred_ratio not in", values, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_ratio between", value1, value2, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_ratio not between", value1, value2, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}