package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportMilepostDesignPlanDetailFollowExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportMilepostDesignPlanDetailFollowExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignDetailIdIsNull() {
            addCriterion("milepost_design_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignDetailIdIsNotNull() {
            addCriterion("milepost_design_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignDetailIdEqualTo(Long value) {
            addCriterion("milepost_design_detail_id =", value, "milepostDesignDetailId");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignDetailIdNotEqualTo(Long value) {
            addCriterion("milepost_design_detail_id <>", value, "milepostDesignDetailId");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignDetailIdGreaterThan(Long value) {
            addCriterion("milepost_design_detail_id >", value, "milepostDesignDetailId");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("milepost_design_detail_id >=", value, "milepostDesignDetailId");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignDetailIdLessThan(Long value) {
            addCriterion("milepost_design_detail_id <", value, "milepostDesignDetailId");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("milepost_design_detail_id <=", value, "milepostDesignDetailId");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignDetailIdIn(List<Long> values) {
            addCriterion("milepost_design_detail_id in", values, "milepostDesignDetailId");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignDetailIdNotIn(List<Long> values) {
            addCriterion("milepost_design_detail_id not in", values, "milepostDesignDetailId");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignDetailIdBetween(Long value1, Long value2) {
            addCriterion("milepost_design_detail_id between", value1, value2, "milepostDesignDetailId");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("milepost_design_detail_id not between", value1, value2, "milepostDesignDetailId");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNull() {
            addCriterion("wbs_summary_code is null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNotNull() {
            addCriterion("wbs_summary_code is not null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeEqualTo(String value) {
            addCriterion("wbs_summary_code =", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotEqualTo(String value) {
            addCriterion("wbs_summary_code <>", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThan(String value) {
            addCriterion("wbs_summary_code >", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code >=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThan(String value) {
            addCriterion("wbs_summary_code <", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code <=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLike(String value) {
            addCriterion("wbs_summary_code like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotLike(String value) {
            addCriterion("wbs_summary_code not like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIn(List<String> values) {
            addCriterion("wbs_summary_code in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotIn(List<String> values) {
            addCriterion("wbs_summary_code not in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeBetween(String value1, String value2) {
            addCriterion("wbs_summary_code between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotBetween(String value1, String value2) {
            addCriterion("wbs_summary_code not between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryIsNull() {
            addCriterion("material_category is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryIsNotNull() {
            addCriterion("material_category is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryEqualTo(String value) {
            addCriterion("material_category =", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryNotEqualTo(String value) {
            addCriterion("material_category <>", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryGreaterThan(String value) {
            addCriterion("material_category >", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("material_category >=", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryLessThan(String value) {
            addCriterion("material_category <", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryLessThanOrEqualTo(String value) {
            addCriterion("material_category <=", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryLike(String value) {
            addCriterion("material_category like", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryNotLike(String value) {
            addCriterion("material_category not like", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryIn(List<String> values) {
            addCriterion("material_category in", values, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryNotIn(List<String> values) {
            addCriterion("material_category not in", values, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryBetween(String value1, String value2) {
            addCriterion("material_category between", value1, value2, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryNotBetween(String value1, String value2) {
            addCriterion("material_category not between", value1, value2, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andSerialNumberIsNull() {
            addCriterion("serial_number is null");
            return (Criteria) this;
        }

        public Criteria andSerialNumberIsNotNull() {
            addCriterion("serial_number is not null");
            return (Criteria) this;
        }

        public Criteria andSerialNumberEqualTo(String value) {
            addCriterion("serial_number =", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberNotEqualTo(String value) {
            addCriterion("serial_number <>", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberGreaterThan(String value) {
            addCriterion("serial_number >", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberGreaterThanOrEqualTo(String value) {
            addCriterion("serial_number >=", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberLessThan(String value) {
            addCriterion("serial_number <", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberLessThanOrEqualTo(String value) {
            addCriterion("serial_number <=", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberLike(String value) {
            addCriterion("serial_number like", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberNotLike(String value) {
            addCriterion("serial_number not like", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberIn(List<String> values) {
            addCriterion("serial_number in", values, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberNotIn(List<String> values) {
            addCriterion("serial_number not in", values, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberBetween(String value1, String value2) {
            addCriterion("serial_number between", value1, value2, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberNotBetween(String value1, String value2) {
            addCriterion("serial_number not between", value1, value2, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNull() {
            addCriterion("materiel_descr is null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNotNull() {
            addCriterion("materiel_descr is not null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrEqualTo(String value) {
            addCriterion("materiel_descr =", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotEqualTo(String value) {
            addCriterion("materiel_descr <>", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThan(String value) {
            addCriterion("materiel_descr >", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThanOrEqualTo(String value) {
            addCriterion("materiel_descr >=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThan(String value) {
            addCriterion("materiel_descr <", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThanOrEqualTo(String value) {
            addCriterion("materiel_descr <=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLike(String value) {
            addCriterion("materiel_descr like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotLike(String value) {
            addCriterion("materiel_descr not like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIn(List<String> values) {
            addCriterion("materiel_descr in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotIn(List<String> values) {
            addCriterion("materiel_descr not in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrBetween(String value1, String value2) {
            addCriterion("materiel_descr between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotBetween(String value1, String value2) {
            addCriterion("materiel_descr not between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNull() {
            addCriterion("pam_code is null");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNotNull() {
            addCriterion("pam_code is not null");
            return (Criteria) this;
        }

        public Criteria andPamCodeEqualTo(String value) {
            addCriterion("pam_code =", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotEqualTo(String value) {
            addCriterion("pam_code <>", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThan(String value) {
            addCriterion("pam_code >", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("pam_code >=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThan(String value) {
            addCriterion("pam_code <", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThanOrEqualTo(String value) {
            addCriterion("pam_code <=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLike(String value) {
            addCriterion("pam_code like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotLike(String value) {
            addCriterion("pam_code not like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeIn(List<String> values) {
            addCriterion("pam_code in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotIn(List<String> values) {
            addCriterion("pam_code not in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeBetween(String value1, String value2) {
            addCriterion("pam_code between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotBetween(String value1, String value2) {
            addCriterion("pam_code not between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNull() {
            addCriterion("erp_code is null");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNotNull() {
            addCriterion("erp_code is not null");
            return (Criteria) this;
        }

        public Criteria andErpCodeEqualTo(String value) {
            addCriterion("erp_code =", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotEqualTo(String value) {
            addCriterion("erp_code <>", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThan(String value) {
            addCriterion("erp_code >", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("erp_code >=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThan(String value) {
            addCriterion("erp_code <", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThanOrEqualTo(String value) {
            addCriterion("erp_code <=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLike(String value) {
            addCriterion("erp_code like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotLike(String value) {
            addCriterion("erp_code not like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIn(List<String> values) {
            addCriterion("erp_code in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotIn(List<String> values) {
            addCriterion("erp_code not in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeBetween(String value1, String value2) {
            addCriterion("erp_code between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotBetween(String value1, String value2) {
            addCriterion("erp_code not between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andTotalNumIsNull() {
            addCriterion("total_num is null");
            return (Criteria) this;
        }

        public Criteria andTotalNumIsNotNull() {
            addCriterion("total_num is not null");
            return (Criteria) this;
        }

        public Criteria andTotalNumEqualTo(BigDecimal value) {
            addCriterion("total_num =", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumNotEqualTo(BigDecimal value) {
            addCriterion("total_num <>", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumGreaterThan(BigDecimal value) {
            addCriterion("total_num >", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_num >=", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumLessThan(BigDecimal value) {
            addCriterion("total_num <", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_num <=", value, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumIn(List<BigDecimal> values) {
            addCriterion("total_num in", values, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumNotIn(List<BigDecimal> values) {
            addCriterion("total_num not in", values, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_num between", value1, value2, "totalNum");
            return (Criteria) this;
        }

        public Criteria andTotalNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_num not between", value1, value2, "totalNum");
            return (Criteria) this;
        }

        public Criteria andNumberIsNull() {
            addCriterion("number is null");
            return (Criteria) this;
        }

        public Criteria andNumberIsNotNull() {
            addCriterion("number is not null");
            return (Criteria) this;
        }

        public Criteria andNumberEqualTo(BigDecimal value) {
            addCriterion("number =", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotEqualTo(BigDecimal value) {
            addCriterion("number <>", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberGreaterThan(BigDecimal value) {
            addCriterion("number >", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("number >=", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberLessThan(BigDecimal value) {
            addCriterion("number <", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberLessThanOrEqualTo(BigDecimal value) {
            addCriterion("number <=", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberIn(List<BigDecimal> values) {
            addCriterion("number in", values, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotIn(List<BigDecimal> values) {
            addCriterion("number not in", values, "number");
            return (Criteria) this;
        }

        public Criteria andNumberBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("number between", value1, value2, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("number not between", value1, value2, "number");
            return (Criteria) this;
        }

        public Criteria andRequirementNumIsNull() {
            addCriterion("requirement_num is null");
            return (Criteria) this;
        }

        public Criteria andRequirementNumIsNotNull() {
            addCriterion("requirement_num is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementNumEqualTo(BigDecimal value) {
            addCriterion("requirement_num =", value, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumNotEqualTo(BigDecimal value) {
            addCriterion("requirement_num <>", value, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumGreaterThan(BigDecimal value) {
            addCriterion("requirement_num >", value, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("requirement_num >=", value, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumLessThan(BigDecimal value) {
            addCriterion("requirement_num <", value, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("requirement_num <=", value, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumIn(List<BigDecimal> values) {
            addCriterion("requirement_num in", values, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumNotIn(List<BigDecimal> values) {
            addCriterion("requirement_num not in", values, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("requirement_num between", value1, value2, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andRequirementNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("requirement_num not between", value1, value2, "requirementNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNull() {
            addCriterion("delivery_time is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNotNull() {
            addCriterion("delivery_time is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeEqualTo(Date value) {
            addCriterion("delivery_time =", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotEqualTo(Date value) {
            addCriterion("delivery_time <>", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThan(Date value) {
            addCriterion("delivery_time >", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("delivery_time >=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThan(Date value) {
            addCriterion("delivery_time <", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThanOrEqualTo(Date value) {
            addCriterion("delivery_time <=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIn(List<Date> values) {
            addCriterion("delivery_time in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotIn(List<Date> values) {
            addCriterion("delivery_time not in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeBetween(Date value1, Date value2) {
            addCriterion("delivery_time between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotBetween(Date value1, Date value2) {
            addCriterion("delivery_time not between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignPlanDetailTotalNumIsNull() {
            addCriterion("milepost_design_plan_detail_total_num is null");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignPlanDetailTotalNumIsNotNull() {
            addCriterion("milepost_design_plan_detail_total_num is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignPlanDetailTotalNumEqualTo(BigDecimal value) {
            addCriterion("milepost_design_plan_detail_total_num =", value, "milepostDesignPlanDetailTotalNum");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignPlanDetailTotalNumNotEqualTo(BigDecimal value) {
            addCriterion("milepost_design_plan_detail_total_num <>", value, "milepostDesignPlanDetailTotalNum");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignPlanDetailTotalNumGreaterThan(BigDecimal value) {
            addCriterion("milepost_design_plan_detail_total_num >", value, "milepostDesignPlanDetailTotalNum");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignPlanDetailTotalNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("milepost_design_plan_detail_total_num >=", value, "milepostDesignPlanDetailTotalNum");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignPlanDetailTotalNumLessThan(BigDecimal value) {
            addCriterion("milepost_design_plan_detail_total_num <", value, "milepostDesignPlanDetailTotalNum");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignPlanDetailTotalNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("milepost_design_plan_detail_total_num <=", value, "milepostDesignPlanDetailTotalNum");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignPlanDetailTotalNumIn(List<BigDecimal> values) {
            addCriterion("milepost_design_plan_detail_total_num in", values, "milepostDesignPlanDetailTotalNum");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignPlanDetailTotalNumNotIn(List<BigDecimal> values) {
            addCriterion("milepost_design_plan_detail_total_num not in", values, "milepostDesignPlanDetailTotalNum");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignPlanDetailTotalNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("milepost_design_plan_detail_total_num between", value1, value2, "milepostDesignPlanDetailTotalNum");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignPlanDetailTotalNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("milepost_design_plan_detail_total_num not between", value1, value2, "milepostDesignPlanDetailTotalNum");
            return (Criteria) this;
        }

        public Criteria andNeedTotalIsNull() {
            addCriterion("need_total is null");
            return (Criteria) this;
        }

        public Criteria andNeedTotalIsNotNull() {
            addCriterion("need_total is not null");
            return (Criteria) this;
        }

        public Criteria andNeedTotalEqualTo(BigDecimal value) {
            addCriterion("need_total =", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalNotEqualTo(BigDecimal value) {
            addCriterion("need_total <>", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalGreaterThan(BigDecimal value) {
            addCriterion("need_total >", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("need_total >=", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalLessThan(BigDecimal value) {
            addCriterion("need_total <", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("need_total <=", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalIn(List<BigDecimal> values) {
            addCriterion("need_total in", values, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalNotIn(List<BigDecimal> values) {
            addCriterion("need_total not in", values, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("need_total between", value1, value2, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("need_total not between", value1, value2, "needTotal");
            return (Criteria) this;
        }

        public Criteria andUnreleasedAmountIsNull() {
            addCriterion("unreleased_amount is null");
            return (Criteria) this;
        }

        public Criteria andUnreleasedAmountIsNotNull() {
            addCriterion("unreleased_amount is not null");
            return (Criteria) this;
        }

        public Criteria andUnreleasedAmountEqualTo(BigDecimal value) {
            addCriterion("unreleased_amount =", value, "unreleasedAmount");
            return (Criteria) this;
        }

        public Criteria andUnreleasedAmountNotEqualTo(BigDecimal value) {
            addCriterion("unreleased_amount <>", value, "unreleasedAmount");
            return (Criteria) this;
        }

        public Criteria andUnreleasedAmountGreaterThan(BigDecimal value) {
            addCriterion("unreleased_amount >", value, "unreleasedAmount");
            return (Criteria) this;
        }

        public Criteria andUnreleasedAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("unreleased_amount >=", value, "unreleasedAmount");
            return (Criteria) this;
        }

        public Criteria andUnreleasedAmountLessThan(BigDecimal value) {
            addCriterion("unreleased_amount <", value, "unreleasedAmount");
            return (Criteria) this;
        }

        public Criteria andUnreleasedAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("unreleased_amount <=", value, "unreleasedAmount");
            return (Criteria) this;
        }

        public Criteria andUnreleasedAmountIn(List<BigDecimal> values) {
            addCriterion("unreleased_amount in", values, "unreleasedAmount");
            return (Criteria) this;
        }

        public Criteria andUnreleasedAmountNotIn(List<BigDecimal> values) {
            addCriterion("unreleased_amount not in", values, "unreleasedAmount");
            return (Criteria) this;
        }

        public Criteria andUnreleasedAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unreleased_amount between", value1, value2, "unreleasedAmount");
            return (Criteria) this;
        }

        public Criteria andUnreleasedAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unreleased_amount not between", value1, value2, "unreleasedAmount");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityIsNull() {
            addCriterion("released_quantity is null");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityIsNotNull() {
            addCriterion("released_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityEqualTo(BigDecimal value) {
            addCriterion("released_quantity =", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityNotEqualTo(BigDecimal value) {
            addCriterion("released_quantity <>", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityGreaterThan(BigDecimal value) {
            addCriterion("released_quantity >", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("released_quantity >=", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityLessThan(BigDecimal value) {
            addCriterion("released_quantity <", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("released_quantity <=", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityIn(List<BigDecimal> values) {
            addCriterion("released_quantity in", values, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityNotIn(List<BigDecimal> values) {
            addCriterion("released_quantity not in", values, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("released_quantity between", value1, value2, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("released_quantity not between", value1, value2, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityIsNull() {
            addCriterion("closed_quantity is null");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityIsNotNull() {
            addCriterion("closed_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityEqualTo(BigDecimal value) {
            addCriterion("closed_quantity =", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityNotEqualTo(BigDecimal value) {
            addCriterion("closed_quantity <>", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityGreaterThan(BigDecimal value) {
            addCriterion("closed_quantity >", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("closed_quantity >=", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityLessThan(BigDecimal value) {
            addCriterion("closed_quantity <", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("closed_quantity <=", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityIn(List<BigDecimal> values) {
            addCriterion("closed_quantity in", values, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityNotIn(List<BigDecimal> values) {
            addCriterion("closed_quantity not in", values, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("closed_quantity between", value1, value2, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("closed_quantity not between", value1, value2, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNumIsNull() {
            addCriterion("purchase_order_num is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNumIsNotNull() {
            addCriterion("purchase_order_num is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNumEqualTo(String value) {
            addCriterion("purchase_order_num =", value, "purchaseOrderNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNumNotEqualTo(String value) {
            addCriterion("purchase_order_num <>", value, "purchaseOrderNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNumGreaterThan(String value) {
            addCriterion("purchase_order_num >", value, "purchaseOrderNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNumGreaterThanOrEqualTo(String value) {
            addCriterion("purchase_order_num >=", value, "purchaseOrderNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNumLessThan(String value) {
            addCriterion("purchase_order_num <", value, "purchaseOrderNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNumLessThanOrEqualTo(String value) {
            addCriterion("purchase_order_num <=", value, "purchaseOrderNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNumLike(String value) {
            addCriterion("purchase_order_num like", value, "purchaseOrderNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNumNotLike(String value) {
            addCriterion("purchase_order_num not like", value, "purchaseOrderNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNumIn(List<String> values) {
            addCriterion("purchase_order_num in", values, "purchaseOrderNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNumNotIn(List<String> values) {
            addCriterion("purchase_order_num not in", values, "purchaseOrderNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNumBetween(String value1, String value2) {
            addCriterion("purchase_order_num between", value1, value2, "purchaseOrderNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNumNotBetween(String value1, String value2) {
            addCriterion("purchase_order_num not between", value1, value2, "purchaseOrderNum");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCancelNumberIsNull() {
            addCriterion("purchase_order_cancel_number is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCancelNumberIsNotNull() {
            addCriterion("purchase_order_cancel_number is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCancelNumberEqualTo(BigDecimal value) {
            addCriterion("purchase_order_cancel_number =", value, "purchaseOrderCancelNumber");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCancelNumberNotEqualTo(BigDecimal value) {
            addCriterion("purchase_order_cancel_number <>", value, "purchaseOrderCancelNumber");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCancelNumberGreaterThan(BigDecimal value) {
            addCriterion("purchase_order_cancel_number >", value, "purchaseOrderCancelNumber");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCancelNumberGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("purchase_order_cancel_number >=", value, "purchaseOrderCancelNumber");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCancelNumberLessThan(BigDecimal value) {
            addCriterion("purchase_order_cancel_number <", value, "purchaseOrderCancelNumber");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCancelNumberLessThanOrEqualTo(BigDecimal value) {
            addCriterion("purchase_order_cancel_number <=", value, "purchaseOrderCancelNumber");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCancelNumberIn(List<BigDecimal> values) {
            addCriterion("purchase_order_cancel_number in", values, "purchaseOrderCancelNumber");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCancelNumberNotIn(List<BigDecimal> values) {
            addCriterion("purchase_order_cancel_number not in", values, "purchaseOrderCancelNumber");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCancelNumberBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("purchase_order_cancel_number between", value1, value2, "purchaseOrderCancelNumber");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderCancelNumberNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("purchase_order_cancel_number not between", value1, value2, "purchaseOrderCancelNumber");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderTransactionQuantityIsNull() {
            addCriterion("purchase_order_transaction_quantity is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderTransactionQuantityIsNotNull() {
            addCriterion("purchase_order_transaction_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderTransactionQuantityEqualTo(BigDecimal value) {
            addCriterion("purchase_order_transaction_quantity =", value, "purchaseOrderTransactionQuantity");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderTransactionQuantityNotEqualTo(BigDecimal value) {
            addCriterion("purchase_order_transaction_quantity <>", value, "purchaseOrderTransactionQuantity");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderTransactionQuantityGreaterThan(BigDecimal value) {
            addCriterion("purchase_order_transaction_quantity >", value, "purchaseOrderTransactionQuantity");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderTransactionQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("purchase_order_transaction_quantity >=", value, "purchaseOrderTransactionQuantity");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderTransactionQuantityLessThan(BigDecimal value) {
            addCriterion("purchase_order_transaction_quantity <", value, "purchaseOrderTransactionQuantity");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderTransactionQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("purchase_order_transaction_quantity <=", value, "purchaseOrderTransactionQuantity");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderTransactionQuantityIn(List<BigDecimal> values) {
            addCriterion("purchase_order_transaction_quantity in", values, "purchaseOrderTransactionQuantity");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderTransactionQuantityNotIn(List<BigDecimal> values) {
            addCriterion("purchase_order_transaction_quantity not in", values, "purchaseOrderTransactionQuantity");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderTransactionQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("purchase_order_transaction_quantity between", value1, value2, "purchaseOrderTransactionQuantity");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderTransactionQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("purchase_order_transaction_quantity not between", value1, value2, "purchaseOrderTransactionQuantity");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIsNull() {
            addCriterion("figure_number is null");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIsNotNull() {
            addCriterion("figure_number is not null");
            return (Criteria) this;
        }

        public Criteria andFigureNumberEqualTo(String value) {
            addCriterion("figure_number =", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotEqualTo(String value) {
            addCriterion("figure_number <>", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberGreaterThan(String value) {
            addCriterion("figure_number >", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberGreaterThanOrEqualTo(String value) {
            addCriterion("figure_number >=", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLessThan(String value) {
            addCriterion("figure_number <", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLessThanOrEqualTo(String value) {
            addCriterion("figure_number <=", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLike(String value) {
            addCriterion("figure_number like", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotLike(String value) {
            addCriterion("figure_number not like", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIn(List<String> values) {
            addCriterion("figure_number in", values, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotIn(List<String> values) {
            addCriterion("figure_number not in", values, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberBetween(String value1, String value2) {
            addCriterion("figure_number between", value1, value2, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotBetween(String value1, String value2) {
            addCriterion("figure_number not between", value1, value2, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationIsNull() {
            addCriterion("material_classification is null");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationIsNotNull() {
            addCriterion("material_classification is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationEqualTo(String value) {
            addCriterion("material_classification =", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotEqualTo(String value) {
            addCriterion("material_classification <>", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationGreaterThan(String value) {
            addCriterion("material_classification >", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationGreaterThanOrEqualTo(String value) {
            addCriterion("material_classification >=", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationLessThan(String value) {
            addCriterion("material_classification <", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationLessThanOrEqualTo(String value) {
            addCriterion("material_classification <=", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationLike(String value) {
            addCriterion("material_classification like", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotLike(String value) {
            addCriterion("material_classification not like", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationIn(List<String> values) {
            addCriterion("material_classification in", values, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotIn(List<String> values) {
            addCriterion("material_classification not in", values, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationBetween(String value1, String value2) {
            addCriterion("material_classification between", value1, value2, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotBetween(String value1, String value2) {
            addCriterion("material_classification not between", value1, value2, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassIsNull() {
            addCriterion("coding_middle_class is null");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassIsNotNull() {
            addCriterion("coding_middle_class is not null");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassEqualTo(String value) {
            addCriterion("coding_middle_class =", value, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassNotEqualTo(String value) {
            addCriterion("coding_middle_class <>", value, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassGreaterThan(String value) {
            addCriterion("coding_middle_class >", value, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassGreaterThanOrEqualTo(String value) {
            addCriterion("coding_middle_class >=", value, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassLessThan(String value) {
            addCriterion("coding_middle_class <", value, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassLessThanOrEqualTo(String value) {
            addCriterion("coding_middle_class <=", value, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassLike(String value) {
            addCriterion("coding_middle_class like", value, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassNotLike(String value) {
            addCriterion("coding_middle_class not like", value, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassIn(List<String> values) {
            addCriterion("coding_middle_class in", values, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassNotIn(List<String> values) {
            addCriterion("coding_middle_class not in", values, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassBetween(String value1, String value2) {
            addCriterion("coding_middle_class between", value1, value2, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassNotBetween(String value1, String value2) {
            addCriterion("coding_middle_class not between", value1, value2, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeIsNull() {
            addCriterion("materiel_type is null");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeIsNotNull() {
            addCriterion("materiel_type is not null");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeEqualTo(String value) {
            addCriterion("materiel_type =", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeNotEqualTo(String value) {
            addCriterion("materiel_type <>", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeGreaterThan(String value) {
            addCriterion("materiel_type >", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeGreaterThanOrEqualTo(String value) {
            addCriterion("materiel_type >=", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeLessThan(String value) {
            addCriterion("materiel_type <", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeLessThanOrEqualTo(String value) {
            addCriterion("materiel_type <=", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeLike(String value) {
            addCriterion("materiel_type like", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeNotLike(String value) {
            addCriterion("materiel_type not like", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeIn(List<String> values) {
            addCriterion("materiel_type in", values, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeNotIn(List<String> values) {
            addCriterion("materiel_type not in", values, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeBetween(String value1, String value2) {
            addCriterion("materiel_type between", value1, value2, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeNotBetween(String value1, String value2) {
            addCriterion("materiel_type not between", value1, value2, "materielType");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}