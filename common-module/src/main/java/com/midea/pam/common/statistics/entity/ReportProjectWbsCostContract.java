package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "开票回款信息")
public class ReportProjectWbsCostContract extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "执行时间,项目详情中项目成本页面的更新时间")
    private Date executeTime;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "合同编号")
    private String code;

    @ApiModelProperty(value = "合同名称")
    private String name;

    @ApiModelProperty(value = "合同状态(0:待归档,1:草稿,2:审核中,3:驳回,4:待生效,5:生效,6:冻结,7:失效,8:已结束,9:作废,10:变更中,11:合同终止)")
    private Integer status;

    @ApiModelProperty(value = "合同总额")
    private BigDecimal amount;

    @ApiModelProperty(value = "不含税金额")
    private BigDecimal excludingTaxAmount;

    @ApiModelProperty(value = "已开票")
    private BigDecimal billed;

    @ApiModelProperty(value = "已回款")
    private BigDecimal moneyBacked;

    @ApiModelProperty(value = "剩余开票")
    private BigDecimal surplusBill;

    @ApiModelProperty(value = "剩余回款")
    private BigDecimal surplusMoneyBack;

    @ApiModelProperty(value = "开票率")
    private BigDecimal billRate;

    @ApiModelProperty(value = "回款率")
    private BigDecimal moneyBackRate;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Date getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(Date executeTime) {
        this.executeTime = executeTime;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getExcludingTaxAmount() {
        return excludingTaxAmount;
    }

    public void setExcludingTaxAmount(BigDecimal excludingTaxAmount) {
        this.excludingTaxAmount = excludingTaxAmount;
    }

    public BigDecimal getBilled() {
        return billed;
    }

    public void setBilled(BigDecimal billed) {
        this.billed = billed;
    }

    public BigDecimal getMoneyBacked() {
        return moneyBacked;
    }

    public void setMoneyBacked(BigDecimal moneyBacked) {
        this.moneyBacked = moneyBacked;
    }

    public BigDecimal getSurplusBill() {
        return surplusBill;
    }

    public void setSurplusBill(BigDecimal surplusBill) {
        this.surplusBill = surplusBill;
    }

    public BigDecimal getSurplusMoneyBack() {
        return surplusMoneyBack;
    }

    public void setSurplusMoneyBack(BigDecimal surplusMoneyBack) {
        this.surplusMoneyBack = surplusMoneyBack;
    }

    public BigDecimal getBillRate() {
        return billRate;
    }

    public void setBillRate(BigDecimal billRate) {
        this.billRate = billRate;
    }

    public BigDecimal getMoneyBackRate() {
        return moneyBackRate;
    }

    public void setMoneyBackRate(BigDecimal moneyBackRate) {
        this.moneyBackRate = moneyBackRate;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", executeTime=").append(executeTime);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", code=").append(code);
        sb.append(", name=").append(name);
        sb.append(", status=").append(status);
        sb.append(", amount=").append(amount);
        sb.append(", excludingTaxAmount=").append(excludingTaxAmount);
        sb.append(", billed=").append(billed);
        sb.append(", moneyBacked=").append(moneyBacked);
        sb.append(", surplusBill=").append(surplusBill);
        sb.append(", surplusMoneyBack=").append(surplusMoneyBack);
        sb.append(", billRate=").append(billRate);
        sb.append(", moneyBackRate=").append(moneyBackRate);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}