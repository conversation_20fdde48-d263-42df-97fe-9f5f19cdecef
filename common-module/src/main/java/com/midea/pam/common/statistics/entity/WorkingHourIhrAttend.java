package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目工时与考勤工时汇总表")
public class WorkingHourIhrAttend extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "目成员mip姓名")
    private String userMipName;

    @ApiModelProperty(value = "项目成员id")
    private Long userId;

    @ApiModelProperty(value = "项目成员mip")
    private String userMip;

    @ApiModelProperty(value = "部门ID")
    private Long orgId;

    @ApiModelProperty(value = "部门名称")
    private String orgName;

    @ApiModelProperty(value = "填报人部门")
    private String applyOrg;

    @ApiModelProperty(value = "申请日期")
    private Date applyDate;

    @ApiModelProperty(value = "hr工时数(实际考勤工时)")
    private BigDecimal ihrAttendHours;

    @ApiModelProperty(value = "项目工时数(填报项目工时)")
    private BigDecimal actualWorkingHours;

    @ApiModelProperty(value = "待审工时数")
    private BigDecimal pendingWorkingHours;

    @ApiModelProperty(value = "工时差异(实际考勤工时-填报项目工时)")
    private BigDecimal hoursDifferent;

    @ApiModelProperty(value = "使用单位ID")
    private Long parentUnitId;

    @ApiModelProperty(value = "使用名称")
    private String parentUnitName;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getUserMipName() {
        return userMipName;
    }

    public void setUserMipName(String userMipName) {
        this.userMipName = userMipName == null ? null : userMipName.trim();
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserMip() {
        return userMip;
    }

    public void setUserMip(String userMip) {
        this.userMip = userMip == null ? null : userMip.trim();
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }

    public String getApplyOrg() {
        return applyOrg;
    }

    public void setApplyOrg(String applyOrg) {
        this.applyOrg = applyOrg == null ? null : applyOrg.trim();
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public BigDecimal getIhrAttendHours() {
        return ihrAttendHours;
    }

    public void setIhrAttendHours(BigDecimal ihrAttendHours) {
        this.ihrAttendHours = ihrAttendHours;
    }

    public BigDecimal getActualWorkingHours() {
        return actualWorkingHours;
    }

    public void setActualWorkingHours(BigDecimal actualWorkingHours) {
        this.actualWorkingHours = actualWorkingHours;
    }

    public BigDecimal getPendingWorkingHours() {
        return pendingWorkingHours;
    }

    public void setPendingWorkingHours(BigDecimal pendingWorkingHours) {
        this.pendingWorkingHours = pendingWorkingHours;
    }

    public BigDecimal getHoursDifferent() {
        return hoursDifferent;
    }

    public void setHoursDifferent(BigDecimal hoursDifferent) {
        this.hoursDifferent = hoursDifferent;
    }

    public Long getParentUnitId() {
        return parentUnitId;
    }

    public void setParentUnitId(Long parentUnitId) {
        this.parentUnitId = parentUnitId;
    }

    public String getParentUnitName() {
        return parentUnitName;
    }

    public void setParentUnitName(String parentUnitName) {
        this.parentUnitName = parentUnitName == null ? null : parentUnitName.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", userMipName=").append(userMipName);
        sb.append(", userId=").append(userId);
        sb.append(", userMip=").append(userMip);
        sb.append(", orgId=").append(orgId);
        sb.append(", orgName=").append(orgName);
        sb.append(", applyOrg=").append(applyOrg);
        sb.append(", applyDate=").append(applyDate);
        sb.append(", ihrAttendHours=").append(ihrAttendHours);
        sb.append(", actualWorkingHours=").append(actualWorkingHours);
        sb.append(", pendingWorkingHours=").append(pendingWorkingHours);
        sb.append(", hoursDifferent=").append(hoursDifferent);
        sb.append(", parentUnitId=").append(parentUnitId);
        sb.append(", parentUnitName=").append(parentUnitName);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}