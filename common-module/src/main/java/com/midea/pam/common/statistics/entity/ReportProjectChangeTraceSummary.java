package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目变更跟踪汇总表")
public class ReportProjectChangeTraceSummary extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目经理")
    private Long projectManager;

    @ApiModelProperty(value = "基本信息变更次数")
    private Integer baseInfoCount;

    @ApiModelProperty(value = "基本信息变更类型")
    private String baseInfoType;

    @ApiModelProperty(value = "基本信息最后更新人")
    private Long baseInfoLastUpdater;

    @ApiModelProperty(value = "基本信息最后变更审批通过时间")
    private Date baseInfoLastApprovedTime;

    @ApiModelProperty(value = "里程碑变更次数")
    private Integer milepostCount;

    @ApiModelProperty(value = "里程碑变更类型")
    private String milepostType;

    @ApiModelProperty(value = "里程碑变更最后更新人")
    private Long milepostLastUpdater;

    @ApiModelProperty(value = "里程碑最后变更审批通过时间")
    private Date milepostLastApprovedTime;

    @ApiModelProperty(value = "预算变更次数")
    private Integer budgetCount;

    @ApiModelProperty(value = "预算变更类型")
    private String budgetType;

    @ApiModelProperty(value = "预算变更最后更新人")
    private Long budgetLastUpdater;

    @ApiModelProperty(value = "预算最后变更审批通过时间")
    private Date budgetLastApprovedTime;

    @ApiModelProperty(value = "是否删除")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "项目经理名称")
    private String projectManagerName;

    @ApiModelProperty(value = "基本信息最后更新者名称")
    private String baseInfoLastName;

    @ApiModelProperty(value = "里程碑最后更新者名称")
    private String milepostLastName;

    @ApiModelProperty(value = "预算最后更新者名称")
    private String budgetLastName;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public Long getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(Long projectManager) {
        this.projectManager = projectManager;
    }

    public Integer getBaseInfoCount() {
        return baseInfoCount;
    }

    public void setBaseInfoCount(Integer baseInfoCount) {
        this.baseInfoCount = baseInfoCount;
    }

    public String getBaseInfoType() {
        return baseInfoType;
    }

    public void setBaseInfoType(String baseInfoType) {
        this.baseInfoType = baseInfoType == null ? null : baseInfoType.trim();
    }

    public Long getBaseInfoLastUpdater() {
        return baseInfoLastUpdater;
    }

    public void setBaseInfoLastUpdater(Long baseInfoLastUpdater) {
        this.baseInfoLastUpdater = baseInfoLastUpdater;
    }

    public Date getBaseInfoLastApprovedTime() {
        return baseInfoLastApprovedTime;
    }

    public void setBaseInfoLastApprovedTime(Date baseInfoLastApprovedTime) {
        this.baseInfoLastApprovedTime = baseInfoLastApprovedTime;
    }

    public Integer getMilepostCount() {
        return milepostCount;
    }

    public void setMilepostCount(Integer milepostCount) {
        this.milepostCount = milepostCount;
    }

    public String getMilepostType() {
        return milepostType;
    }

    public void setMilepostType(String milepostType) {
        this.milepostType = milepostType == null ? null : milepostType.trim();
    }

    public Long getMilepostLastUpdater() {
        return milepostLastUpdater;
    }

    public void setMilepostLastUpdater(Long milepostLastUpdater) {
        this.milepostLastUpdater = milepostLastUpdater;
    }

    public Date getMilepostLastApprovedTime() {
        return milepostLastApprovedTime;
    }

    public void setMilepostLastApprovedTime(Date milepostLastApprovedTime) {
        this.milepostLastApprovedTime = milepostLastApprovedTime;
    }

    public Integer getBudgetCount() {
        return budgetCount;
    }

    public void setBudgetCount(Integer budgetCount) {
        this.budgetCount = budgetCount;
    }

    public String getBudgetType() {
        return budgetType;
    }

    public void setBudgetType(String budgetType) {
        this.budgetType = budgetType == null ? null : budgetType.trim();
    }

    public Long getBudgetLastUpdater() {
        return budgetLastUpdater;
    }

    public void setBudgetLastUpdater(Long budgetLastUpdater) {
        this.budgetLastUpdater = budgetLastUpdater;
    }

    public Date getBudgetLastApprovedTime() {
        return budgetLastApprovedTime;
    }

    public void setBudgetLastApprovedTime(Date budgetLastApprovedTime) {
        this.budgetLastApprovedTime = budgetLastApprovedTime;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public String getProjectManagerName() {
        return projectManagerName;
    }

    public void setProjectManagerName(String projectManagerName) {
        this.projectManagerName = projectManagerName == null ? null : projectManagerName.trim();
    }

    public String getBaseInfoLastName() {
        return baseInfoLastName;
    }

    public void setBaseInfoLastName(String baseInfoLastName) {
        this.baseInfoLastName = baseInfoLastName == null ? null : baseInfoLastName.trim();
    }

    public String getMilepostLastName() {
        return milepostLastName;
    }

    public void setMilepostLastName(String milepostLastName) {
        this.milepostLastName = milepostLastName == null ? null : milepostLastName.trim();
    }

    public String getBudgetLastName() {
        return budgetLastName;
    }

    public void setBudgetLastName(String budgetLastName) {
        this.budgetLastName = budgetLastName == null ? null : budgetLastName.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", baseInfoCount=").append(baseInfoCount);
        sb.append(", baseInfoType=").append(baseInfoType);
        sb.append(", baseInfoLastUpdater=").append(baseInfoLastUpdater);
        sb.append(", baseInfoLastApprovedTime=").append(baseInfoLastApprovedTime);
        sb.append(", milepostCount=").append(milepostCount);
        sb.append(", milepostType=").append(milepostType);
        sb.append(", milepostLastUpdater=").append(milepostLastUpdater);
        sb.append(", milepostLastApprovedTime=").append(milepostLastApprovedTime);
        sb.append(", budgetCount=").append(budgetCount);
        sb.append(", budgetType=").append(budgetType);
        sb.append(", budgetLastUpdater=").append(budgetLastUpdater);
        sb.append(", budgetLastApprovedTime=").append(budgetLastApprovedTime);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", projectManagerName=").append(projectManagerName);
        sb.append(", baseInfoLastName=").append(baseInfoLastName);
        sb.append(", milepostLastName=").append(milepostLastName);
        sb.append(", budgetLastName=").append(budgetLastName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}