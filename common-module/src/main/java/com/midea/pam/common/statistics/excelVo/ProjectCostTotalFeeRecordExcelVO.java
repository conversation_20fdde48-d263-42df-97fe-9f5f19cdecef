package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

public class ProjectCostTotalFeeRecordExcelVO {

    @Excel(name = "序号",width = 10)
    private Integer num;

    @Excel(name = "项目",width = 20)
    private String projectCode;

    @Excel(name = "项目名称",width = 20)
    private String projectName;

    @Excel(name = "类型",width = 20)
    private String feeItemName;

    @Excel(name = "预算",width = 20)
    private BigDecimal budget;

    @Excel(name = "已处理成本",width = 20)
    private BigDecimal incurredCost;

    @Excel(name = "待处理成本",width = 20)
    private BigDecimal pendingCost;

    @Excel(name = "EA可用金额",width = 20)
    private BigDecimal eaAvailableAmount;

    @Excel(name = "成本合计",width = 20)
    private BigDecimal totalCost;

    @Excel(name = "剩余预算",width = 20)
    private BigDecimal remainderBudget;

    @Excel(name = "已发生成本比例",width = 20,suffix = "%")
    private BigDecimal incurredRatio;

    @Excel(name = "年度预提汇总",width = 20)
    private BigDecimal annualAccrual;

    @Excel(name = "月度预提汇总",width = 20)
    private BigDecimal monthlyAccrual;

    @Excel(name = "更新时间",width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date createAt;

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getFeeItemName() {
        return feeItemName;
    }

    public void setFeeItemName(String feeItemName) {
        this.feeItemName = feeItemName;
    }

    public BigDecimal getBudget() {
        return budget;
    }

    public void setBudget(BigDecimal budget) {
        this.budget = budget;
    }

    public BigDecimal getIncurredCost() {
        return incurredCost;
    }

    public void setIncurredCost(BigDecimal incurredCost) {
        this.incurredCost = incurredCost;
    }

    public BigDecimal getPendingCost() {
        return pendingCost;
    }

    public void setPendingCost(BigDecimal pendingCost) {
        this.pendingCost = pendingCost;
    }

    public BigDecimal getEaAvailableAmount() {
        return eaAvailableAmount;
    }

    public void setEaAvailableAmount(BigDecimal eaAvailableAmount) {
        this.eaAvailableAmount = eaAvailableAmount;
    }

    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    public BigDecimal getRemainderBudget() {
        return remainderBudget;
    }

    public void setRemainderBudget(BigDecimal remainderBudget) {
        this.remainderBudget = remainderBudget;
    }

    public BigDecimal getIncurredRatio() {
        return incurredRatio;
    }

    public void setIncurredRatio(BigDecimal incurredRatio) {
        this.incurredRatio = incurredRatio;
    }

    public BigDecimal getAnnualAccrual() {
        return annualAccrual;
    }

    public void setAnnualAccrual(BigDecimal annualAccrual) {
        this.annualAccrual = annualAccrual;
    }

    public BigDecimal getMonthlyAccrual() {
        return monthlyAccrual;
    }

    public void setMonthlyAccrual(BigDecimal monthlyAccrual) {
        this.monthlyAccrual = monthlyAccrual;
    }


    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }
}
