package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 **/
public class ProjectBudgetManagerDetailExcelVO {
    @Excel(name = "序号",width = 10)
    private Integer num;

    @Excel(name = "项目号",width = 20)
    private String projectCode;

    @Excel(name = "项目名称",width = 30)
    private String projectName;

    @Excel(name = "项目经理",width = 30)
    private String projectManager;

    @Excel(name = "变更对象",width = 20)
    private String changeObject;

    @Excel(name = "币种",width = 20)
    private String currency;

    @Excel(name = "变更前预算",width = 30)
    private String beforeAmount;

    @Excel(name = "变更后预算",width = 30)
    private String afterAmount;

    @Excel(name = "调整差异额",width = 30)
    private String changeAmount;

    @Excel(name = "变更原因",width = 30)
    private String reasonType;

    @Excel(name = "变更说明",width = 30)
    private String reason;

    @Excel(name = "申请人",width = 20)
    private String applyName;

    @Excel(name = "变更时间",width = 15, format = "yyyy-MM-dd HH:mm:ss")
    private Date changeTime;

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager;
    }

    public String getChangeObject() {
        return changeObject;
    }

    public void setChangeObject(String changeObject) {
        this.changeObject = changeObject;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getBeforeAmount() {
        return beforeAmount;
    }

    public void setBeforeAmount(String beforeAmount) {
        this.beforeAmount = beforeAmount;
    }

    public String getAfterAmount() {
        return afterAmount;
    }

    public void setAfterAmount(String afterAmount) {
        this.afterAmount = afterAmount;
    }

    public String getChangeAmount() {
        return changeAmount;
    }

    public void setChangeAmount(String changeAmount) {
        this.changeAmount = changeAmount;
    }

    public String getReasonType() {
        return reasonType;
    }

    public void setReasonType(String reasonType) {
        this.reasonType = reasonType;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getApplyName() {
        return applyName;
    }

    public void setApplyName(String applyName) {
        this.applyName = applyName;
    }

    public Date getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(Date changeTime) {
        this.changeTime = changeTime;
    }
}
