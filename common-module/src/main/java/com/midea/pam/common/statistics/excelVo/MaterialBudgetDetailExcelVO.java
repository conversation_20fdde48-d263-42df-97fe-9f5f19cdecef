package com.midea.pam.common.statistics.excelVo;

import com.midea.pam.common.util.BigDecimalUtils;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-07-09
 * 物料预算明细报表ExcelVO
 */
@Getter
@Setter
public class MaterialBudgetDetailExcelVO {

    @Excel(name = "序号" , width = 15)
    private String num;

    @Excel(name = "BOOM层级" , width = 15)
    private Integer boomLevel;

    @Excel(name = "集成外包" , width = 10)
    private String ext;

    @Excel(name = "物料类型" , width = 20)
    private String materielType;

    @Excel(name = "物料描述" , width = 40)
    private String materielDescr;

    @Excel(name = "PAM物料编码" , width = 20)
    private String pamCode;

    @Excel(name = "ERP物料编码" , width = 20)
    private String erpCode;

    @Excel(name = "名称" , width = 40)
    private String name;

    @Excel(name = "单位" , width = 10)
    private String unit;

    @Excel(name = "单套数量" , width = 20)
    private BigDecimal number;

    @Excel(name = "采购需求总数" , width = 20)
    private BigDecimal totalNum;

    @Excel(name = "已发布需求数量" , width = 20)
    private BigDecimal requirementNum;

    @Excel(name = "已下采购订单" , width = 20)
    private BigDecimal purchaseOrderPlaced;

    @Excel(name = "库存" , width = 20)
    private BigDecimal inventoryQuantity;

    @Excel(name = "已领料" , width = 20)
    private BigDecimal materialReceived;

    @Excel(name = "未下单" , width = 20)
    private BigDecimal notOrdered;

    @Excel(name = "一揽子价格/估价单价" , width = 20)
    private BigDecimal packagePriceEstimateUnitPrice;

    @Excel(name = "采购单价" , width = 20)
    private BigDecimal purchaseUnitPrice;

    @Excel(name = "领料平均单位成本" , width = 20)
    private BigDecimal averageUnitCostMaterial;

    @Excel(name = "预计待发生成本" , width = 20)
    private BigDecimal expectedPendingCost;

    @Excel(name = "采购总成本" , width = 20)
    private BigDecimal totalPurchaseCost;

    @Excel(name = "领料总成本" , width = 20)
    private BigDecimal totalMaterialCost;

    @Excel(name = "备注" , width = 20)
    private String remark;

    @Excel(name = "物料到货日期" , width = 20, format = "yyyy-MM-dd")
    private Date deliveryTime;

    @Excel(name = "型号/规格/图号" , width = 20)
    private String model;

    @Excel(name = "品牌" , width = 20)
    private String brand;


    public BigDecimal getNumber() {
        return BigDecimalUtils.stripTrailingZeros(number);
    }

    public BigDecimal getTotalNum() {
        return BigDecimalUtils.stripTrailingZeros(totalNum);
    }

    public BigDecimal getRequirementNum() {
        return BigDecimalUtils.stripTrailingZeros(requirementNum);
    }

    public BigDecimal getPurchaseOrderPlaced() {
        return BigDecimalUtils.stripTrailingZeros(purchaseOrderPlaced);
    }

    public BigDecimal getInventoryQuantity() {
        return BigDecimalUtils.stripTrailingZeros(inventoryQuantity);
    }

    public BigDecimal getMaterialReceived() {
        return BigDecimalUtils.stripTrailingZeros(materialReceived);
    }

    public BigDecimal getNotOrdered() {
        return BigDecimalUtils.stripTrailingZeros(notOrdered);
    }

    public BigDecimal getPackagePriceEstimateUnitPrice() {
        return BigDecimalUtils.stripTrailingZeros(packagePriceEstimateUnitPrice);
    }

    public BigDecimal getPurchaseUnitPrice() {
        return BigDecimalUtils.stripTrailingZeros(purchaseUnitPrice);
    }

    public BigDecimal getAverageUnitCostMaterial() {
        return BigDecimalUtils.stripTrailingZeros(averageUnitCostMaterial);
    }

    public BigDecimal getExpectedPendingCost() {
        return BigDecimalUtils.stripTrailingZeros(expectedPendingCost);
    }

    public BigDecimal getTotalPurchaseCost() {
        return BigDecimalUtils.stripTrailingZeros(totalPurchaseCost);
    }

    public BigDecimal getTotalMaterialCost() {
        return BigDecimalUtils.stripTrailingZeros(totalMaterialCost);
    }
}