package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "应收到期日期明细报表")
public class ReportDueDateReceivableDetail extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "子合同编号")
    private String code;

    @ApiModelProperty(value = "子合同名称")
    private String name;

    @ApiModelProperty(value = "主合同编号")
    private String parentCode;

    @ApiModelProperty(value = "主合同名称")
    private String parentName;

    @ApiModelProperty(value = "业务实体")
    private String ouName;

    @ApiModelProperty(value = "销售部门")
    private String unitName;

    @ApiModelProperty(value = "业务模式")
    private String projectType;

    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "是否内部客户:1-内部客户;0-外部客户")
    private Integer customerType;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    @ApiModelProperty(value = "销售人员")
    private String salesManager;

    @ApiModelProperty(value = "币种编码")
    private String currency;

    @ApiModelProperty(value = "子合同含税金额（原币）")
    private BigDecimal amount;

    @ApiModelProperty(value = "子合同不含税金额（原币）")
    private BigDecimal excludingTaxAmount;

    @ApiModelProperty(value = "子合同汇率")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "子合同含税金额（本位币）")
    private BigDecimal standardAmount;

    @ApiModelProperty(value = "子合同不含税金额（本位币）")
    private BigDecimal standardExcludingTaxAmount;

    @ApiModelProperty(value = "子合同已开票金额（原币）")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "子合同已收款金额（原币）")
    private BigDecimal receiptAmount;

    @ApiModelProperty(value = "开票计划")
    private String invoicePlanDetailCode;

    @ApiModelProperty(value = "开票申请")
    private String invoiceApplyCode;

    @ApiModelProperty(value = "开票申请人")
    private String invoiceApplyCreateUserName;

    @ApiModelProperty(value = "应收金额（原币）")
    private BigDecimal receivableAmount;

    @ApiModelProperty(value = "已回款金额（原币）")
    private BigDecimal claimMount;

    @ApiModelProperty(value = "应收余额（原币）")
    private BigDecimal overageAmount;

    @ApiModelProperty(value = "应收到期日")
    private Date expireDate;

    @ApiModelProperty(value = "逾期账龄")
    private String overdueAges;

    @ApiModelProperty(value = "是否删除")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "开票申请审批通过时间")
    private Date invoiceApplyApprovedAt;

    @ApiModelProperty(value = "应收发票号")
    private String invoiceCode;

    @ApiModelProperty(value = "开票条件")
    private String requirement;

    @ApiModelProperty(value = "应收汇率")
    private BigDecimal invoiceConversionRate;

    @ApiModelProperty(value = "应收金额（本位币）")
    private BigDecimal standardReceivableAmount;

    @ApiModelProperty(value = "已回款金额（本位币）")
    private BigDecimal standardClaimMount;

    @ApiModelProperty(value = "应收余额（本位币）")
    private BigDecimal standardOverageAmount;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode == null ? null : parentCode.trim();
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName == null ? null : parentName.trim();
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType == null ? null : projectType.trim();
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode == null ? null : customerCode.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public String getSalesManager() {
        return salesManager;
    }

    public void setSalesManager(String salesManager) {
        this.salesManager = salesManager == null ? null : salesManager.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getExcludingTaxAmount() {
        return excludingTaxAmount;
    }

    public void setExcludingTaxAmount(BigDecimal excludingTaxAmount) {
        this.excludingTaxAmount = excludingTaxAmount;
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public BigDecimal getStandardAmount() {
        return standardAmount;
    }

    public void setStandardAmount(BigDecimal standardAmount) {
        this.standardAmount = standardAmount;
    }

    public BigDecimal getStandardExcludingTaxAmount() {
        return standardExcludingTaxAmount;
    }

    public void setStandardExcludingTaxAmount(BigDecimal standardExcludingTaxAmount) {
        this.standardExcludingTaxAmount = standardExcludingTaxAmount;
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public BigDecimal getReceiptAmount() {
        return receiptAmount;
    }

    public void setReceiptAmount(BigDecimal receiptAmount) {
        this.receiptAmount = receiptAmount;
    }

    public String getInvoicePlanDetailCode() {
        return invoicePlanDetailCode;
    }

    public void setInvoicePlanDetailCode(String invoicePlanDetailCode) {
        this.invoicePlanDetailCode = invoicePlanDetailCode == null ? null : invoicePlanDetailCode.trim();
    }

    public String getInvoiceApplyCode() {
        return invoiceApplyCode;
    }

    public void setInvoiceApplyCode(String invoiceApplyCode) {
        this.invoiceApplyCode = invoiceApplyCode == null ? null : invoiceApplyCode.trim();
    }

    public String getInvoiceApplyCreateUserName() {
        return invoiceApplyCreateUserName;
    }

    public void setInvoiceApplyCreateUserName(String invoiceApplyCreateUserName) {
        this.invoiceApplyCreateUserName = invoiceApplyCreateUserName == null ? null : invoiceApplyCreateUserName.trim();
    }

    public BigDecimal getReceivableAmount() {
        return receivableAmount;
    }

    public void setReceivableAmount(BigDecimal receivableAmount) {
        this.receivableAmount = receivableAmount;
    }

    public BigDecimal getClaimMount() {
        return claimMount;
    }

    public void setClaimMount(BigDecimal claimMount) {
        this.claimMount = claimMount;
    }

    public BigDecimal getOverageAmount() {
        return overageAmount;
    }

    public void setOverageAmount(BigDecimal overageAmount) {
        this.overageAmount = overageAmount;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public String getOverdueAges() {
        return overdueAges;
    }

    public void setOverdueAges(String overdueAges) {
        this.overdueAges = overdueAges == null ? null : overdueAges.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Date getInvoiceApplyApprovedAt() {
        return invoiceApplyApprovedAt;
    }

    public void setInvoiceApplyApprovedAt(Date invoiceApplyApprovedAt) {
        this.invoiceApplyApprovedAt = invoiceApplyApprovedAt;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode == null ? null : invoiceCode.trim();
    }

    public String getRequirement() {
        return requirement;
    }

    public void setRequirement(String requirement) {
        this.requirement = requirement == null ? null : requirement.trim();
    }

    public BigDecimal getInvoiceConversionRate() {
        return invoiceConversionRate;
    }

    public void setInvoiceConversionRate(BigDecimal invoiceConversionRate) {
        this.invoiceConversionRate = invoiceConversionRate;
    }

    public BigDecimal getStandardReceivableAmount() {
        return standardReceivableAmount;
    }

    public void setStandardReceivableAmount(BigDecimal standardReceivableAmount) {
        this.standardReceivableAmount = standardReceivableAmount;
    }

    public BigDecimal getStandardClaimMount() {
        return standardClaimMount;
    }

    public void setStandardClaimMount(BigDecimal standardClaimMount) {
        this.standardClaimMount = standardClaimMount;
    }

    public BigDecimal getStandardOverageAmount() {
        return standardOverageAmount;
    }

    public void setStandardOverageAmount(BigDecimal standardOverageAmount) {
        this.standardOverageAmount = standardOverageAmount;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", code=").append(code);
        sb.append(", name=").append(name);
        sb.append(", parentCode=").append(parentCode);
        sb.append(", parentName=").append(parentName);
        sb.append(", ouName=").append(ouName);
        sb.append(", unitName=").append(unitName);
        sb.append(", projectType=").append(projectType);
        sb.append(", customerCode=").append(customerCode);
        sb.append(", customerName=").append(customerName);
        sb.append(", customerType=").append(customerType);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", salesManager=").append(salesManager);
        sb.append(", currency=").append(currency);
        sb.append(", amount=").append(amount);
        sb.append(", excludingTaxAmount=").append(excludingTaxAmount);
        sb.append(", conversionRate=").append(conversionRate);
        sb.append(", standardAmount=").append(standardAmount);
        sb.append(", standardExcludingTaxAmount=").append(standardExcludingTaxAmount);
        sb.append(", invoiceAmount=").append(invoiceAmount);
        sb.append(", receiptAmount=").append(receiptAmount);
        sb.append(", invoicePlanDetailCode=").append(invoicePlanDetailCode);
        sb.append(", invoiceApplyCode=").append(invoiceApplyCode);
        sb.append(", invoiceApplyCreateUserName=").append(invoiceApplyCreateUserName);
        sb.append(", receivableAmount=").append(receivableAmount);
        sb.append(", claimMount=").append(claimMount);
        sb.append(", overageAmount=").append(overageAmount);
        sb.append(", expireDate=").append(expireDate);
        sb.append(", overdueAges=").append(overdueAges);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", invoiceApplyApprovedAt=").append(invoiceApplyApprovedAt);
        sb.append(", invoiceCode=").append(invoiceCode);
        sb.append(", requirement=").append(requirement);
        sb.append(", invoiceConversionRate=").append(invoiceConversionRate);
        sb.append(", standardReceivableAmount=").append(standardReceivableAmount);
        sb.append(", standardClaimMount=").append(standardClaimMount);
        sb.append(", standardOverageAmount=").append(standardOverageAmount);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}