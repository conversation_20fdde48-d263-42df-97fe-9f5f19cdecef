package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目利润表")
public class ReportProjectMargin extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "数据截止期间")
    private String deadlinePeriod;

    @ApiModelProperty(value = "表样父级代码")
    private String parentCode;

    @ApiModelProperty(value = "表样代码")
    private String code;

    @ApiModelProperty(value = "表样名称")
    private String name;

    @ApiModelProperty(value = "Curent Month（本月）")
    private BigDecimal currentMonthCost;

    @ApiModelProperty(value = "YTD（本年）")
    private BigDecimal currentYearCost;

    @ApiModelProperty(value = "以前年度累计")
    private BigDecimal previousYearCost;

    @ApiModelProperty(value = "YTD Project（项目总资金）")
    private BigDecimal totalCost;

    @ApiModelProperty(value = "基线预算")
    private BigDecimal baselineCost;

    @ApiModelProperty(value = "当前预算")
    private BigDecimal budgetCost;

    @ApiModelProperty(value = "活动事项编码，多个逗号隔开")
    private String projectActivityCode;

    @ApiModelProperty(value = "数据最后更新时间")
    private Date lastUpdateTime;

    @ApiModelProperty(value = "业务实体")
    private Long ouId;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getDeadlinePeriod() {
        return deadlinePeriod;
    }

    public void setDeadlinePeriod(String deadlinePeriod) {
        this.deadlinePeriod = deadlinePeriod == null ? null : deadlinePeriod.trim();
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode == null ? null : parentCode.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public BigDecimal getCurrentMonthCost() {
        return currentMonthCost;
    }

    public void setCurrentMonthCost(BigDecimal currentMonthCost) {
        this.currentMonthCost = currentMonthCost;
    }

    public BigDecimal getCurrentYearCost() {
        return currentYearCost;
    }

    public void setCurrentYearCost(BigDecimal currentYearCost) {
        this.currentYearCost = currentYearCost;
    }

    public BigDecimal getPreviousYearCost() {
        return previousYearCost;
    }

    public void setPreviousYearCost(BigDecimal previousYearCost) {
        this.previousYearCost = previousYearCost;
    }

    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    public BigDecimal getBaselineCost() {
        return baselineCost;
    }

    public void setBaselineCost(BigDecimal baselineCost) {
        this.baselineCost = baselineCost;
    }

    public BigDecimal getBudgetCost() {
        return budgetCost;
    }

    public void setBudgetCost(BigDecimal budgetCost) {
        this.budgetCost = budgetCost;
    }

    public String getProjectActivityCode() {
        return projectActivityCode;
    }

    public void setProjectActivityCode(String projectActivityCode) {
        this.projectActivityCode = projectActivityCode == null ? null : projectActivityCode.trim();
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", deadlinePeriod=").append(deadlinePeriod);
        sb.append(", parentCode=").append(parentCode);
        sb.append(", code=").append(code);
        sb.append(", name=").append(name);
        sb.append(", currentMonthCost=").append(currentMonthCost);
        sb.append(", currentYearCost=").append(currentYearCost);
        sb.append(", previousYearCost=").append(previousYearCost);
        sb.append(", totalCost=").append(totalCost);
        sb.append(", baselineCost=").append(baselineCost);
        sb.append(", budgetCost=").append(budgetCost);
        sb.append(", projectActivityCode=").append(projectActivityCode);
        sb.append(", lastUpdateTime=").append(lastUpdateTime);
        sb.append(", ouId=").append(ouId);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}