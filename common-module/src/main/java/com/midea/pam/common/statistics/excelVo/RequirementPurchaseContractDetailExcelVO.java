package com.midea.pam.common.statistics.excelVo;


import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022-7-25
 * @description 需求单据中的物料已下采购合同金额导出 ---暂未使用
 */
@Getter
@Setter
public class RequirementPurchaseContractDetailExcelVO {

    @Excel(name="序号",width = 5)
    private Integer number;

    @Excel(name = "项目id", width = 15)
    private Long projectId;

    @Excel(name = "项目编码", width = 15)
    private String projectCode;

    @Excel(name = "详细设计单据id", width = 15)
    private Long projectWbsReceiptsId;

    @Excel(name = "需求发布单据编号", width = 15)
    private String requirementCode;

    @Excel(name = "采购合同编号", width = 15)
    private String purchaseContractCode;

    @Excel(name = "总金额", width = 15)
    private BigDecimal totalAmount;

    @Excel(name = "wbs编码", width = 15)
    private String wbsSummaryCode;

    @Excel(name = "活动事项编码", width = 15)
    private String activityCode;

}
