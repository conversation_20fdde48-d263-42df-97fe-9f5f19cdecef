package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(value = "点工需求跟踪报表")
public class HroRequirementReport extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    @ApiModelProperty(value = "采购合同编号")
    private String contractCode;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "PAM物料编码")
    private String materialCode;

    @ApiModelProperty(value = "人力点工需求单据编号，多个逗号分隔")
    private String requirementCode;

    @ApiModelProperty(value = "总需求量")
    private BigDecimal totalNum;

    @ApiModelProperty(value = "已采购量")
    private BigDecimal purchasedNum;

    @ApiModelProperty(value = "已填报工时")
    private BigDecimal fillNum;

    @ApiModelProperty(value = "审批通过工时")
    private BigDecimal approveNum;

    @ApiModelProperty(value = "已对账工时")
    private BigDecimal billNum;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode == null ? null : contractCode.trim();
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName == null ? null : roleName.trim();
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode == null ? null : materialCode.trim();
    }

    public String getRequirementCode() {
        return requirementCode;
    }

    public void setRequirementCode(String requirementCode) {
        this.requirementCode = requirementCode == null ? null : requirementCode.trim();
    }

    public BigDecimal getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(BigDecimal totalNum) {
        this.totalNum = totalNum;
    }

    public BigDecimal getPurchasedNum() {
        return purchasedNum;
    }

    public void setPurchasedNum(BigDecimal purchasedNum) {
        this.purchasedNum = purchasedNum;
    }

    public BigDecimal getFillNum() {
        return fillNum;
    }

    public void setFillNum(BigDecimal fillNum) {
        this.fillNum = fillNum;
    }

    public BigDecimal getApproveNum() {
        return approveNum;
    }

    public void setApproveNum(BigDecimal approveNum) {
        this.approveNum = approveNum;
    }

    public BigDecimal getBillNum() {
        return billNum;
    }

    public void setBillNum(BigDecimal billNum) {
        this.billNum = billNum;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", contractCode=").append(contractCode);
        sb.append(", roleName=").append(roleName);
        sb.append(", materialCode=").append(materialCode);
        sb.append(", requirementCode=").append(requirementCode);
        sb.append(", totalNum=").append(totalNum);
        sb.append(", purchasedNum=").append(purchasedNum);
        sb.append(", fillNum=").append(fillNum);
        sb.append(", approveNum=").append(approveNum);
        sb.append(", billNum=").append(billNum);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}