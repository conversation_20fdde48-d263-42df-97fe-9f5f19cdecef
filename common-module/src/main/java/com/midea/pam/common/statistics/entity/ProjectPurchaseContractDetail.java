package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目采购合同明细表")
public class ProjectPurchaseContractDetail extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "详细设计单据id")
    private Long projectWbsReceiptsId;

    @ApiModelProperty(value = "采购合同编号")
    private String purchaseContractCode;

    @ApiModelProperty(value = "采购合同名称")
    private String purchaseContractName;

    @ApiModelProperty(value = "PAM编码")
    private String pamCode;

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "累计进度执行金额")
    private BigDecimal budgetExecuteAmountTotal;

    @ApiModelProperty(value = "分配进度执行金额")
    private BigDecimal allocationExecuteAmount;

    @ApiModelProperty(value = "wbs编码")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "类型 1-在途、2-已完成")
    private Integer budgetType;

    @ApiModelProperty(value = "业务生成时间")
    private Date dataTime;

    @ApiModelProperty(value = "合同创建时间")
    private Date contractCreateTime;

    @ApiModelProperty(value = "进度执行单号")
    private String progressCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "物料描述")
    private String materielDescr;

    private static final long serialVersionUID = 1L;

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public Long getProjectWbsReceiptsId() {
        return projectWbsReceiptsId;
    }

    public void setProjectWbsReceiptsId(Long projectWbsReceiptsId) {
        this.projectWbsReceiptsId = projectWbsReceiptsId;
    }

    public String getPurchaseContractCode() {
        return purchaseContractCode;
    }

    public void setPurchaseContractCode(String purchaseContractCode) {
        this.purchaseContractCode = purchaseContractCode == null ? null : purchaseContractCode.trim();
    }

    public String getPurchaseContractName() {
        return purchaseContractName;
    }

    public void setPurchaseContractName(String purchaseContractName) {
        this.purchaseContractName = purchaseContractName == null ? null : purchaseContractName.trim();
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode == null ? null : pamCode.trim();
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getBudgetExecuteAmountTotal() {
        return budgetExecuteAmountTotal;
    }

    public void setBudgetExecuteAmountTotal(BigDecimal budgetExecuteAmountTotal) {
        this.budgetExecuteAmountTotal = budgetExecuteAmountTotal;
    }

    public BigDecimal getAllocationExecuteAmount() {
        return allocationExecuteAmount;
    }

    public void setAllocationExecuteAmount(BigDecimal allocationExecuteAmount) {
        this.allocationExecuteAmount = allocationExecuteAmount;
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Integer getBudgetType() {
        return budgetType;
    }

    public void setBudgetType(Integer budgetType) {
        this.budgetType = budgetType;
    }

    public Date getDataTime() {
        return dataTime;
    }

    public void setDataTime(Date dataTime) {
        this.dataTime = dataTime;
    }

    public Date getContractCreateTime() {
        return contractCreateTime;
    }

    public void setContractCreateTime(Date contractCreateTime) {
        this.contractCreateTime = contractCreateTime;
    }

    public String getProgressCode() {
        return progressCode;
    }

    public void setProgressCode(String progressCode) {
        this.progressCode = progressCode == null ? null : progressCode.trim();
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName == null ? null : vendorName.trim();
    }

    public String getMaterielDescr() {
        return materielDescr;
    }

    public void setMaterielDescr(String materielDescr) {
        this.materielDescr = materielDescr == null ? null : materielDescr.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectWbsReceiptsId=").append(projectWbsReceiptsId);
        sb.append(", purchaseContractCode=").append(purchaseContractCode);
        sb.append(", purchaseContractName=").append(purchaseContractName);
        sb.append(", pamCode=").append(pamCode);
        sb.append(", totalPrice=").append(totalPrice);
        sb.append(", budgetExecuteAmountTotal=").append(budgetExecuteAmountTotal);
        sb.append(", allocationExecuteAmount=").append(allocationExecuteAmount);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", budgetType=").append(budgetType);
        sb.append(", dataTime=").append(dataTime);
        sb.append(", contractCreateTime=").append(contractCreateTime);
        sb.append(", progressCode=").append(progressCode);
        sb.append(", vendorName=").append(vendorName);
        sb.append(", materielDescr=").append(materielDescr);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}