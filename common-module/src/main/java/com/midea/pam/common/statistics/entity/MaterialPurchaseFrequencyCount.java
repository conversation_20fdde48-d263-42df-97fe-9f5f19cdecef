package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "物料采购频率统计报表")
public class MaterialPurchaseFrequencyCount extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "采购次数")
    private Long purchaseNum;

    @ApiModelProperty(value = "PAM编码")
    private String pamCode;

    @ApiModelProperty(value = "erp物料编码")
    private String erpCode;

    @ApiModelProperty(value = "物料描述")
    private String itemInfo;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "物料状态")
    private String itemStatus;

    @ApiModelProperty(value = "物料状态名称")
    private String itemStatusName;

    @ApiModelProperty(value = "是否待退市物料(1是/0否)")
    private Byte delistFlag;

    @ApiModelProperty(value = "是否待退市物料(1是/0否)")
    private String delistFlagName;

    @ApiModelProperty(value = "物料大类")
    private String materialClassification;

    @ApiModelProperty(value = "物料中类")
    private String codingMiddleclass;

    @ApiModelProperty(value = "物料小类")
    private String materialType;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "型号/规格")
    private String model;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "“加工件分类”类型有围栏、底座，机加件、焊接钣金件四类")
    private String machiningPartType;

    @ApiModelProperty(value = "材质")
    private String material;

    @ApiModelProperty(value = "单位重量(Kg)")
    private BigDecimal unitWeight;

    @ApiModelProperty(value = "材质处理")
    private String materialProcessing;

    @ApiModelProperty(value = "最小订货量")
    private Long minimumOrderQuantity;

    @ApiModelProperty(value = "接受子库存")
    private String recevingSubinventory;

    @ApiModelProperty(value = "货架")
    private String shelves;

    @ApiModelProperty(value = "配套人员")
    private String sourcer;

    @ApiModelProperty(value = "安全库存")
    private Long safetyStockQuantity;

    @ApiModelProperty(value = "用户物料类型")
    private String itemType;

    @ApiModelProperty(value = "用户物料类型")
    private String itemTypeName;

    @ApiModelProperty(value = "采购员")
    private String buyerNumber;

    @ApiModelProperty(value = "erp采购员ID")
    private String buyerId;

    @ApiModelProperty(value = "采购周期")
    private String buyerRound;

    @ApiModelProperty(value = "采购分类")
    private String purType;

    @ApiModelProperty(value = "库存分类")
    private String inventoryType;

    @ApiModelProperty(value = "库存组织ID")
    private Long organizationId;

    @ApiModelProperty(value = "库存组织名称")
    private String organizationName;

    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    @ApiModelProperty(value = "更新人姓名")
    private String updaterName;

    @ApiModelProperty(value = "图号")
    private String figureNumber;

    @ApiModelProperty(value = "图纸版本号")
    private String chartVersion;

    @ApiModelProperty(value = "品牌商物料编码")
    private String brandMaterialCode;

    @ApiModelProperty(value = "业务实体id")
    private Long ouId;

    @ApiModelProperty(value = "采购订单创建日期)")
    private Date purchaseOrderCreatedDate;

    @ApiModelProperty(value = "采购订单创建日期开始时间)")
    private Date purchaseOrderStartDate;

    @ApiModelProperty(value = "采购订单创建日期结束时间)")
    private Date purchaseOrderEndDate;

    @ApiModelProperty(value = "物料创建日期)")
    private Date materialCreatedDate;

    @ApiModelProperty(value = "物料创建日期开始时间)")
    private Date materialCreatedStartDate;

    @ApiModelProperty(value = "物料创建日期结束时间)")
    private Date materialCreatedEndDate;

    @ApiModelProperty(value = "是否删除")
    private Byte deleteFlag;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getPurchaseNum() {
        return purchaseNum;
    }

    public void setPurchaseNum(Long purchaseNum) {
        this.purchaseNum = purchaseNum;
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode == null ? null : pamCode.trim();
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode == null ? null : erpCode.trim();
    }

    public String getItemInfo() {
        return itemInfo;
    }

    public void setItemInfo(String itemInfo) {
        this.itemInfo = itemInfo == null ? null : itemInfo.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public String getItemStatus() {
        return itemStatus;
    }

    public void setItemStatus(String itemStatus) {
        this.itemStatus = itemStatus == null ? null : itemStatus.trim();
    }

    public String getItemStatusName() {
        return itemStatusName;
    }

    public void setItemStatusName(String itemStatusName) {
        this.itemStatusName = itemStatusName == null ? null : itemStatusName.trim();
    }

    public Byte getDelistFlag() {
        return delistFlag;
    }

    public void setDelistFlag(Byte delistFlag) {
        this.delistFlag = delistFlag;
    }

    public String getDelistFlagName() {
        return delistFlagName;
    }

    public void setDelistFlagName(String delistFlagName) {
        this.delistFlagName = delistFlagName;
    }

    public String getMaterialClassification() {
        return materialClassification;
    }

    public void setMaterialClassification(String materialClassification) {
        this.materialClassification = materialClassification == null ? null : materialClassification.trim();
    }

    public String getCodingMiddleclass() {
        return codingMiddleclass;
    }

    public void setCodingMiddleclass(String codingMiddleclass) {
        this.codingMiddleclass = codingMiddleclass == null ? null : codingMiddleclass.trim();
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType == null ? null : materialType.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public String getMachiningPartType() {
        return machiningPartType;
    }

    public void setMachiningPartType(String machiningPartType) {
        this.machiningPartType = machiningPartType == null ? null : machiningPartType.trim();
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material == null ? null : material.trim();
    }

    public BigDecimal getUnitWeight() {
        return unitWeight;
    }

    public void setUnitWeight(BigDecimal unitWeight) {
        this.unitWeight = unitWeight;
    }

    public String getMaterialProcessing() {
        return materialProcessing;
    }

    public void setMaterialProcessing(String materialProcessing) {
        this.materialProcessing = materialProcessing == null ? null : materialProcessing.trim();
    }

    public Long getMinimumOrderQuantity() {
        return minimumOrderQuantity;
    }

    public void setMinimumOrderQuantity(Long minimumOrderQuantity) {
        this.minimumOrderQuantity = minimumOrderQuantity;
    }

    public String getRecevingSubinventory() {
        return recevingSubinventory;
    }

    public void setRecevingSubinventory(String recevingSubinventory) {
        this.recevingSubinventory = recevingSubinventory == null ? null : recevingSubinventory.trim();
    }

    public String getShelves() {
        return shelves;
    }

    public void setShelves(String shelves) {
        this.shelves = shelves == null ? null : shelves.trim();
    }

    public String getSourcer() {
        return sourcer;
    }

    public void setSourcer(String sourcer) {
        this.sourcer = sourcer == null ? null : sourcer.trim();
    }

    public Long getSafetyStockQuantity() {
        return safetyStockQuantity;
    }

    public void setSafetyStockQuantity(Long safetyStockQuantity) {
        this.safetyStockQuantity = safetyStockQuantity;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType == null ? null : itemType.trim();
    }

    public String getItemTypeName() {
        return itemTypeName;
    }

    public void setItemTypeName(String itemTypeName) {
        this.itemTypeName = itemTypeName == null ? null : itemTypeName.trim();
    }

    public String getBuyerNumber() {
        return buyerNumber;
    }

    public void setBuyerNumber(String buyerNumber) {
        this.buyerNumber = buyerNumber == null ? null : buyerNumber.trim();
    }

    public String getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(String buyerId) {
        this.buyerId = buyerId == null ? null : buyerId.trim();
    }

    public String getBuyerRound() {
        return buyerRound;
    }

    public void setBuyerRound(String buyerRound) {
        this.buyerRound = buyerRound == null ? null : buyerRound.trim();
    }

    public String getPurType() {
        return purType;
    }

    public void setPurType(String purType) {
        this.purType = purType == null ? null : purType.trim();
    }

    public String getInventoryType() {
        return inventoryType;
    }

    public void setInventoryType(String inventoryType) {
        this.inventoryType = inventoryType == null ? null : inventoryType.trim();
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName == null ? null : organizationName.trim();
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName == null ? null : creatorName.trim();
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName == null ? null : updaterName.trim();
    }

    public String getFigureNumber() {
        return figureNumber;
    }

    public void setFigureNumber(String figureNumber) {
        this.figureNumber = figureNumber == null ? null : figureNumber.trim();
    }

    public String getChartVersion() {
        return chartVersion;
    }

    public void setChartVersion(String chartVersion) {
        this.chartVersion = chartVersion == null ? null : chartVersion.trim();
    }

    public String getBrandMaterialCode() {
        return brandMaterialCode;
    }

    public void setBrandMaterialCode(String brandMaterialCode) {
        this.brandMaterialCode = brandMaterialCode == null ? null : brandMaterialCode.trim();
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public Date getPurchaseOrderCreatedDate() {
        return purchaseOrderCreatedDate;
    }

    public void setPurchaseOrderCreatedDate(Date purchaseOrderCreatedDate) {
        this.purchaseOrderCreatedDate = purchaseOrderCreatedDate;
    }

    public Date getPurchaseOrderStartDate() {
        return purchaseOrderStartDate;
    }

    public void setPurchaseOrderStartDate(Date purchaseOrderStartDate) {
        this.purchaseOrderStartDate = purchaseOrderStartDate;
    }

    public Date getPurchaseOrderEndDate() {
        return purchaseOrderEndDate;
    }

    public void setPurchaseOrderEndDate(Date purchaseOrderEndDate) {
        this.purchaseOrderEndDate = purchaseOrderEndDate;
    }

    public Date getMaterialCreatedDate() {
        return materialCreatedDate;
    }

    public void setMaterialCreatedDate(Date materialCreatedDate) {
        this.materialCreatedDate = materialCreatedDate;
    }

    public Date getMaterialCreatedStartDate() {
        return materialCreatedStartDate;
    }

    public void setMaterialCreatedStartDate(Date materialCreatedStartDate) {
        this.materialCreatedStartDate = materialCreatedStartDate;
    }

    public Date getMaterialCreatedEndDate() {
        return materialCreatedEndDate;
    }

    public void setMaterialCreatedEndDate(Date materialCreatedEndDate) {
        this.materialCreatedEndDate = materialCreatedEndDate;
    }

    public Byte getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Byte deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", purchaseNum=").append(purchaseNum);
        sb.append(", pamCode=").append(pamCode);
        sb.append(", erpCode=").append(erpCode);
        sb.append(", itemInfo=").append(itemInfo);
        sb.append(", unit=").append(unit);
        sb.append(", itemStatus=").append(itemStatus);
        sb.append(", itemStatusName=").append(itemStatusName);
        sb.append(", delistFlag=").append(delistFlag);
        sb.append(", materialClassification=").append(materialClassification);
        sb.append(", codingMiddleclass=").append(codingMiddleclass);
        sb.append(", materialType=").append(materialType);
        sb.append(", name=").append(name);
        sb.append(", model=").append(model);
        sb.append(", brand=").append(brand);
        sb.append(", machiningPartType=").append(machiningPartType);
        sb.append(", material=").append(material);
        sb.append(", unitWeight=").append(unitWeight);
        sb.append(", materialProcessing=").append(materialProcessing);
        sb.append(", minimumOrderQuantity=").append(minimumOrderQuantity);
        sb.append(", recevingSubinventory=").append(recevingSubinventory);
        sb.append(", shelves=").append(shelves);
        sb.append(", sourcer=").append(sourcer);
        sb.append(", safetyStockQuantity=").append(safetyStockQuantity);
        sb.append(", itemType=").append(itemType);
        sb.append(", itemTypeName=").append(itemTypeName);
        sb.append(", buyerNumber=").append(buyerNumber);
        sb.append(", buyerId=").append(buyerId);
        sb.append(", buyerRound=").append(buyerRound);
        sb.append(", purType=").append(purType);
        sb.append(", inventoryType=").append(inventoryType);
        sb.append(", organizationId=").append(organizationId);
        sb.append(", organizationName=").append(organizationName);
        sb.append(", creatorName=").append(creatorName);
        sb.append(", updaterName=").append(updaterName);
        sb.append(", figureNumber=").append(figureNumber);
        sb.append(", chartVersion=").append(chartVersion);
        sb.append(", brandMaterialCode=").append(brandMaterialCode);
        sb.append(", ouId=").append(ouId);
        sb.append(", purchaseOrderCreatedDate=").append(purchaseOrderCreatedDate);
        sb.append(", purchaseOrderStartDate=").append(purchaseOrderStartDate);
        sb.append(", purchaseOrderEndDate=").append(purchaseOrderEndDate);
        sb.append(", materialCreatedDate=").append(materialCreatedDate);
        sb.append(", materialCreatedStartDate=").append(materialCreatedStartDate);
        sb.append(", materialCreatedEndDate=").append(materialCreatedEndDate);
        sb.append(", deleteFlag=").append(deleteFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}