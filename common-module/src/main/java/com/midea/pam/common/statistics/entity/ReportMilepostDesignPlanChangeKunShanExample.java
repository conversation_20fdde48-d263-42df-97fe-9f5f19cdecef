package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportMilepostDesignPlanChangeKunShanExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportMilepostDesignPlanChangeKunShanExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdIsNull() {
            addCriterion("project_type_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdIsNotNull() {
            addCriterion("project_type_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdEqualTo(Long value) {
            addCriterion("project_type_id =", value, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdNotEqualTo(Long value) {
            addCriterion("project_type_id <>", value, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdGreaterThan(Long value) {
            addCriterion("project_type_id >", value, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_type_id >=", value, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdLessThan(Long value) {
            addCriterion("project_type_id <", value, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdLessThanOrEqualTo(Long value) {
            addCriterion("project_type_id <=", value, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdIn(List<Long> values) {
            addCriterion("project_type_id in", values, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdNotIn(List<Long> values) {
            addCriterion("project_type_id not in", values, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdBetween(Long value1, Long value2) {
            addCriterion("project_type_id between", value1, value2, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdNotBetween(Long value1, Long value2) {
            addCriterion("project_type_id not between", value1, value2, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameIsNull() {
            addCriterion("project_type_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameIsNotNull() {
            addCriterion("project_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameEqualTo(String value) {
            addCriterion("project_type_name =", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotEqualTo(String value) {
            addCriterion("project_type_name <>", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameGreaterThan(String value) {
            addCriterion("project_type_name >", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_type_name >=", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameLessThan(String value) {
            addCriterion("project_type_name <", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameLessThanOrEqualTo(String value) {
            addCriterion("project_type_name <=", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameLike(String value) {
            addCriterion("project_type_name like", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotLike(String value) {
            addCriterion("project_type_name not like", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameIn(List<String> values) {
            addCriterion("project_type_name in", values, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotIn(List<String> values) {
            addCriterion("project_type_name not in", values, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameBetween(String value1, String value2) {
            addCriterion("project_type_name between", value1, value2, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotBetween(String value1, String value2) {
            addCriterion("project_type_name not between", value1, value2, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Long value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Long value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Long value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Long value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Long> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Long> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Long value1, Long value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNull() {
            addCriterion("unit_name is null");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNotNull() {
            addCriterion("unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andUnitNameEqualTo(String value) {
            addCriterion("unit_name =", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotEqualTo(String value) {
            addCriterion("unit_name <>", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThan(String value) {
            addCriterion("unit_name >", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("unit_name >=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThan(String value) {
            addCriterion("unit_name <", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThanOrEqualTo(String value) {
            addCriterion("unit_name <=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLike(String value) {
            addCriterion("unit_name like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotLike(String value) {
            addCriterion("unit_name not like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIn(List<String> values) {
            addCriterion("unit_name in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotIn(List<String> values) {
            addCriterion("unit_name not in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameBetween(String value1, String value2) {
            addCriterion("unit_name between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotBetween(String value1, String value2) {
            addCriterion("unit_name not between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andManagerNameIsNull() {
            addCriterion("manager_name is null");
            return (Criteria) this;
        }

        public Criteria andManagerNameIsNotNull() {
            addCriterion("manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andManagerNameEqualTo(String value) {
            addCriterion("manager_name =", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotEqualTo(String value) {
            addCriterion("manager_name <>", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameGreaterThan(String value) {
            addCriterion("manager_name >", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("manager_name >=", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameLessThan(String value) {
            addCriterion("manager_name <", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameLessThanOrEqualTo(String value) {
            addCriterion("manager_name <=", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameLike(String value) {
            addCriterion("manager_name like", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotLike(String value) {
            addCriterion("manager_name not like", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameIn(List<String> values) {
            addCriterion("manager_name in", values, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotIn(List<String> values) {
            addCriterion("manager_name not in", values, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameBetween(String value1, String value2) {
            addCriterion("manager_name between", value1, value2, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotBetween(String value1, String value2) {
            addCriterion("manager_name not between", value1, value2, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerIdIsNull() {
            addCriterion("manager_id is null");
            return (Criteria) this;
        }

        public Criteria andManagerIdIsNotNull() {
            addCriterion("manager_id is not null");
            return (Criteria) this;
        }

        public Criteria andManagerIdEqualTo(Long value) {
            addCriterion("manager_id =", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdNotEqualTo(Long value) {
            addCriterion("manager_id <>", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdGreaterThan(Long value) {
            addCriterion("manager_id >", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("manager_id >=", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdLessThan(Long value) {
            addCriterion("manager_id <", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdLessThanOrEqualTo(Long value) {
            addCriterion("manager_id <=", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdIn(List<Long> values) {
            addCriterion("manager_id in", values, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdNotIn(List<Long> values) {
            addCriterion("manager_id not in", values, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdBetween(Long value1, Long value2) {
            addCriterion("manager_id between", value1, value2, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdNotBetween(Long value1, Long value2) {
            addCriterion("manager_id not between", value1, value2, "managerId");
            return (Criteria) this;
        }

        public Criteria andModelNameIsNull() {
            addCriterion("model_name is null");
            return (Criteria) this;
        }

        public Criteria andModelNameIsNotNull() {
            addCriterion("model_name is not null");
            return (Criteria) this;
        }

        public Criteria andModelNameEqualTo(String value) {
            addCriterion("model_name =", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameNotEqualTo(String value) {
            addCriterion("model_name <>", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameGreaterThan(String value) {
            addCriterion("model_name >", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameGreaterThanOrEqualTo(String value) {
            addCriterion("model_name >=", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameLessThan(String value) {
            addCriterion("model_name <", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameLessThanOrEqualTo(String value) {
            addCriterion("model_name <=", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameLike(String value) {
            addCriterion("model_name like", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameNotLike(String value) {
            addCriterion("model_name not like", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameIn(List<String> values) {
            addCriterion("model_name in", values, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameNotIn(List<String> values) {
            addCriterion("model_name not in", values, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameBetween(String value1, String value2) {
            addCriterion("model_name between", value1, value2, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameNotBetween(String value1, String value2) {
            addCriterion("model_name not between", value1, value2, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelIdIsNull() {
            addCriterion("model_id is null");
            return (Criteria) this;
        }

        public Criteria andModelIdIsNotNull() {
            addCriterion("model_id is not null");
            return (Criteria) this;
        }

        public Criteria andModelIdEqualTo(Long value) {
            addCriterion("model_id =", value, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdNotEqualTo(Long value) {
            addCriterion("model_id <>", value, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdGreaterThan(Long value) {
            addCriterion("model_id >", value, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdGreaterThanOrEqualTo(Long value) {
            addCriterion("model_id >=", value, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdLessThan(Long value) {
            addCriterion("model_id <", value, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdLessThanOrEqualTo(Long value) {
            addCriterion("model_id <=", value, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdIn(List<Long> values) {
            addCriterion("model_id in", values, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdNotIn(List<Long> values) {
            addCriterion("model_id not in", values, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdBetween(Long value1, Long value2) {
            addCriterion("model_id between", value1, value2, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelIdNotBetween(Long value1, Long value2) {
            addCriterion("model_id not between", value1, value2, "modelId");
            return (Criteria) this;
        }

        public Criteria andModelPamCodeIsNull() {
            addCriterion("model_pam_code is null");
            return (Criteria) this;
        }

        public Criteria andModelPamCodeIsNotNull() {
            addCriterion("model_pam_code is not null");
            return (Criteria) this;
        }

        public Criteria andModelPamCodeEqualTo(String value) {
            addCriterion("model_pam_code =", value, "modelPamCode");
            return (Criteria) this;
        }

        public Criteria andModelPamCodeNotEqualTo(String value) {
            addCriterion("model_pam_code <>", value, "modelPamCode");
            return (Criteria) this;
        }

        public Criteria andModelPamCodeGreaterThan(String value) {
            addCriterion("model_pam_code >", value, "modelPamCode");
            return (Criteria) this;
        }

        public Criteria andModelPamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("model_pam_code >=", value, "modelPamCode");
            return (Criteria) this;
        }

        public Criteria andModelPamCodeLessThan(String value) {
            addCriterion("model_pam_code <", value, "modelPamCode");
            return (Criteria) this;
        }

        public Criteria andModelPamCodeLessThanOrEqualTo(String value) {
            addCriterion("model_pam_code <=", value, "modelPamCode");
            return (Criteria) this;
        }

        public Criteria andModelPamCodeLike(String value) {
            addCriterion("model_pam_code like", value, "modelPamCode");
            return (Criteria) this;
        }

        public Criteria andModelPamCodeNotLike(String value) {
            addCriterion("model_pam_code not like", value, "modelPamCode");
            return (Criteria) this;
        }

        public Criteria andModelPamCodeIn(List<String> values) {
            addCriterion("model_pam_code in", values, "modelPamCode");
            return (Criteria) this;
        }

        public Criteria andModelPamCodeNotIn(List<String> values) {
            addCriterion("model_pam_code not in", values, "modelPamCode");
            return (Criteria) this;
        }

        public Criteria andModelPamCodeBetween(String value1, String value2) {
            addCriterion("model_pam_code between", value1, value2, "modelPamCode");
            return (Criteria) this;
        }

        public Criteria andModelPamCodeNotBetween(String value1, String value2) {
            addCriterion("model_pam_code not between", value1, value2, "modelPamCode");
            return (Criteria) this;
        }

        public Criteria andMaterialNameIsNull() {
            addCriterion("material_name is null");
            return (Criteria) this;
        }

        public Criteria andMaterialNameIsNotNull() {
            addCriterion("material_name is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialNameEqualTo(String value) {
            addCriterion("material_name =", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotEqualTo(String value) {
            addCriterion("material_name <>", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameGreaterThan(String value) {
            addCriterion("material_name >", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameGreaterThanOrEqualTo(String value) {
            addCriterion("material_name >=", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameLessThan(String value) {
            addCriterion("material_name <", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameLessThanOrEqualTo(String value) {
            addCriterion("material_name <=", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameLike(String value) {
            addCriterion("material_name like", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotLike(String value) {
            addCriterion("material_name not like", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameIn(List<String> values) {
            addCriterion("material_name in", values, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotIn(List<String> values) {
            addCriterion("material_name not in", values, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameBetween(String value1, String value2) {
            addCriterion("material_name between", value1, value2, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotBetween(String value1, String value2) {
            addCriterion("material_name not between", value1, value2, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIsNull() {
            addCriterion("material_id is null");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIsNotNull() {
            addCriterion("material_id is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialIdEqualTo(Long value) {
            addCriterion("material_id =", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotEqualTo(Long value) {
            addCriterion("material_id <>", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdGreaterThan(Long value) {
            addCriterion("material_id >", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdGreaterThanOrEqualTo(Long value) {
            addCriterion("material_id >=", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdLessThan(Long value) {
            addCriterion("material_id <", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdLessThanOrEqualTo(Long value) {
            addCriterion("material_id <=", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIn(List<Long> values) {
            addCriterion("material_id in", values, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotIn(List<Long> values) {
            addCriterion("material_id not in", values, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdBetween(Long value1, Long value2) {
            addCriterion("material_id between", value1, value2, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotBetween(Long value1, Long value2) {
            addCriterion("material_id not between", value1, value2, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialPamCodeIsNull() {
            addCriterion("material_pam_code is null");
            return (Criteria) this;
        }

        public Criteria andMaterialPamCodeIsNotNull() {
            addCriterion("material_pam_code is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialPamCodeEqualTo(String value) {
            addCriterion("material_pam_code =", value, "materialPamCode");
            return (Criteria) this;
        }

        public Criteria andMaterialPamCodeNotEqualTo(String value) {
            addCriterion("material_pam_code <>", value, "materialPamCode");
            return (Criteria) this;
        }

        public Criteria andMaterialPamCodeGreaterThan(String value) {
            addCriterion("material_pam_code >", value, "materialPamCode");
            return (Criteria) this;
        }

        public Criteria andMaterialPamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("material_pam_code >=", value, "materialPamCode");
            return (Criteria) this;
        }

        public Criteria andMaterialPamCodeLessThan(String value) {
            addCriterion("material_pam_code <", value, "materialPamCode");
            return (Criteria) this;
        }

        public Criteria andMaterialPamCodeLessThanOrEqualTo(String value) {
            addCriterion("material_pam_code <=", value, "materialPamCode");
            return (Criteria) this;
        }

        public Criteria andMaterialPamCodeLike(String value) {
            addCriterion("material_pam_code like", value, "materialPamCode");
            return (Criteria) this;
        }

        public Criteria andMaterialPamCodeNotLike(String value) {
            addCriterion("material_pam_code not like", value, "materialPamCode");
            return (Criteria) this;
        }

        public Criteria andMaterialPamCodeIn(List<String> values) {
            addCriterion("material_pam_code in", values, "materialPamCode");
            return (Criteria) this;
        }

        public Criteria andMaterialPamCodeNotIn(List<String> values) {
            addCriterion("material_pam_code not in", values, "materialPamCode");
            return (Criteria) this;
        }

        public Criteria andMaterialPamCodeBetween(String value1, String value2) {
            addCriterion("material_pam_code between", value1, value2, "materialPamCode");
            return (Criteria) this;
        }

        public Criteria andMaterialPamCodeNotBetween(String value1, String value2) {
            addCriterion("material_pam_code not between", value1, value2, "materialPamCode");
            return (Criteria) this;
        }

        public Criteria andMaterialErpCodeIsNull() {
            addCriterion("material_erp_code is null");
            return (Criteria) this;
        }

        public Criteria andMaterialErpCodeIsNotNull() {
            addCriterion("material_erp_code is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialErpCodeEqualTo(String value) {
            addCriterion("material_erp_code =", value, "materialErpCode");
            return (Criteria) this;
        }

        public Criteria andMaterialErpCodeNotEqualTo(String value) {
            addCriterion("material_erp_code <>", value, "materialErpCode");
            return (Criteria) this;
        }

        public Criteria andMaterialErpCodeGreaterThan(String value) {
            addCriterion("material_erp_code >", value, "materialErpCode");
            return (Criteria) this;
        }

        public Criteria andMaterialErpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("material_erp_code >=", value, "materialErpCode");
            return (Criteria) this;
        }

        public Criteria andMaterialErpCodeLessThan(String value) {
            addCriterion("material_erp_code <", value, "materialErpCode");
            return (Criteria) this;
        }

        public Criteria andMaterialErpCodeLessThanOrEqualTo(String value) {
            addCriterion("material_erp_code <=", value, "materialErpCode");
            return (Criteria) this;
        }

        public Criteria andMaterialErpCodeLike(String value) {
            addCriterion("material_erp_code like", value, "materialErpCode");
            return (Criteria) this;
        }

        public Criteria andMaterialErpCodeNotLike(String value) {
            addCriterion("material_erp_code not like", value, "materialErpCode");
            return (Criteria) this;
        }

        public Criteria andMaterialErpCodeIn(List<String> values) {
            addCriterion("material_erp_code in", values, "materialErpCode");
            return (Criteria) this;
        }

        public Criteria andMaterialErpCodeNotIn(List<String> values) {
            addCriterion("material_erp_code not in", values, "materialErpCode");
            return (Criteria) this;
        }

        public Criteria andMaterialErpCodeBetween(String value1, String value2) {
            addCriterion("material_erp_code between", value1, value2, "materialErpCode");
            return (Criteria) this;
        }

        public Criteria andMaterialErpCodeNotBetween(String value1, String value2) {
            addCriterion("material_erp_code not between", value1, value2, "materialErpCode");
            return (Criteria) this;
        }

        public Criteria andMaterialDescIsNull() {
            addCriterion("material_desc is null");
            return (Criteria) this;
        }

        public Criteria andMaterialDescIsNotNull() {
            addCriterion("material_desc is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialDescEqualTo(String value) {
            addCriterion("material_desc =", value, "materialDesc");
            return (Criteria) this;
        }

        public Criteria andMaterialDescNotEqualTo(String value) {
            addCriterion("material_desc <>", value, "materialDesc");
            return (Criteria) this;
        }

        public Criteria andMaterialDescGreaterThan(String value) {
            addCriterion("material_desc >", value, "materialDesc");
            return (Criteria) this;
        }

        public Criteria andMaterialDescGreaterThanOrEqualTo(String value) {
            addCriterion("material_desc >=", value, "materialDesc");
            return (Criteria) this;
        }

        public Criteria andMaterialDescLessThan(String value) {
            addCriterion("material_desc <", value, "materialDesc");
            return (Criteria) this;
        }

        public Criteria andMaterialDescLessThanOrEqualTo(String value) {
            addCriterion("material_desc <=", value, "materialDesc");
            return (Criteria) this;
        }

        public Criteria andMaterialDescLike(String value) {
            addCriterion("material_desc like", value, "materialDesc");
            return (Criteria) this;
        }

        public Criteria andMaterialDescNotLike(String value) {
            addCriterion("material_desc not like", value, "materialDesc");
            return (Criteria) this;
        }

        public Criteria andMaterialDescIn(List<String> values) {
            addCriterion("material_desc in", values, "materialDesc");
            return (Criteria) this;
        }

        public Criteria andMaterialDescNotIn(List<String> values) {
            addCriterion("material_desc not in", values, "materialDesc");
            return (Criteria) this;
        }

        public Criteria andMaterialDescBetween(String value1, String value2) {
            addCriterion("material_desc between", value1, value2, "materialDesc");
            return (Criteria) this;
        }

        public Criteria andMaterialDescNotBetween(String value1, String value2) {
            addCriterion("material_desc not between", value1, value2, "materialDesc");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitIsNull() {
            addCriterion("material_unit is null");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitIsNotNull() {
            addCriterion("material_unit is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitEqualTo(String value) {
            addCriterion("material_unit =", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotEqualTo(String value) {
            addCriterion("material_unit <>", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitGreaterThan(String value) {
            addCriterion("material_unit >", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitGreaterThanOrEqualTo(String value) {
            addCriterion("material_unit >=", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLessThan(String value) {
            addCriterion("material_unit <", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLessThanOrEqualTo(String value) {
            addCriterion("material_unit <=", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLike(String value) {
            addCriterion("material_unit like", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotLike(String value) {
            addCriterion("material_unit not like", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitIn(List<String> values) {
            addCriterion("material_unit in", values, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotIn(List<String> values) {
            addCriterion("material_unit not in", values, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitBetween(String value1, String value2) {
            addCriterion("material_unit between", value1, value2, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotBetween(String value1, String value2) {
            addCriterion("material_unit not between", value1, value2, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitCodeIsNull() {
            addCriterion("material_unit_code is null");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitCodeIsNotNull() {
            addCriterion("material_unit_code is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitCodeEqualTo(String value) {
            addCriterion("material_unit_code =", value, "materialUnitCode");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitCodeNotEqualTo(String value) {
            addCriterion("material_unit_code <>", value, "materialUnitCode");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitCodeGreaterThan(String value) {
            addCriterion("material_unit_code >", value, "materialUnitCode");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitCodeGreaterThanOrEqualTo(String value) {
            addCriterion("material_unit_code >=", value, "materialUnitCode");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitCodeLessThan(String value) {
            addCriterion("material_unit_code <", value, "materialUnitCode");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitCodeLessThanOrEqualTo(String value) {
            addCriterion("material_unit_code <=", value, "materialUnitCode");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitCodeLike(String value) {
            addCriterion("material_unit_code like", value, "materialUnitCode");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitCodeNotLike(String value) {
            addCriterion("material_unit_code not like", value, "materialUnitCode");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitCodeIn(List<String> values) {
            addCriterion("material_unit_code in", values, "materialUnitCode");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitCodeNotIn(List<String> values) {
            addCriterion("material_unit_code not in", values, "materialUnitCode");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitCodeBetween(String value1, String value2) {
            addCriterion("material_unit_code between", value1, value2, "materialUnitCode");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitCodeNotBetween(String value1, String value2) {
            addCriterion("material_unit_code not between", value1, value2, "materialUnitCode");
            return (Criteria) this;
        }

        public Criteria andModelNumIsNull() {
            addCriterion("model_num is null");
            return (Criteria) this;
        }

        public Criteria andModelNumIsNotNull() {
            addCriterion("model_num is not null");
            return (Criteria) this;
        }

        public Criteria andModelNumEqualTo(BigDecimal value) {
            addCriterion("model_num =", value, "modelNum");
            return (Criteria) this;
        }

        public Criteria andModelNumNotEqualTo(BigDecimal value) {
            addCriterion("model_num <>", value, "modelNum");
            return (Criteria) this;
        }

        public Criteria andModelNumGreaterThan(BigDecimal value) {
            addCriterion("model_num >", value, "modelNum");
            return (Criteria) this;
        }

        public Criteria andModelNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("model_num >=", value, "modelNum");
            return (Criteria) this;
        }

        public Criteria andModelNumLessThan(BigDecimal value) {
            addCriterion("model_num <", value, "modelNum");
            return (Criteria) this;
        }

        public Criteria andModelNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("model_num <=", value, "modelNum");
            return (Criteria) this;
        }

        public Criteria andModelNumIn(List<BigDecimal> values) {
            addCriterion("model_num in", values, "modelNum");
            return (Criteria) this;
        }

        public Criteria andModelNumNotIn(List<BigDecimal> values) {
            addCriterion("model_num not in", values, "modelNum");
            return (Criteria) this;
        }

        public Criteria andModelNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("model_num between", value1, value2, "modelNum");
            return (Criteria) this;
        }

        public Criteria andModelNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("model_num not between", value1, value2, "modelNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeNumIsNull() {
            addCriterion("material_change_num is null");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeNumIsNotNull() {
            addCriterion("material_change_num is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeNumEqualTo(BigDecimal value) {
            addCriterion("material_change_num =", value, "materialChangeNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeNumNotEqualTo(BigDecimal value) {
            addCriterion("material_change_num <>", value, "materialChangeNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeNumGreaterThan(BigDecimal value) {
            addCriterion("material_change_num >", value, "materialChangeNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_change_num >=", value, "materialChangeNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeNumLessThan(BigDecimal value) {
            addCriterion("material_change_num <", value, "materialChangeNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_change_num <=", value, "materialChangeNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeNumIn(List<BigDecimal> values) {
            addCriterion("material_change_num in", values, "materialChangeNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeNumNotIn(List<BigDecimal> values) {
            addCriterion("material_change_num not in", values, "materialChangeNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_change_num between", value1, value2, "materialChangeNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_change_num not between", value1, value2, "materialChangeNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalNumIsNull() {
            addCriterion("material_change_total_num is null");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalNumIsNotNull() {
            addCriterion("material_change_total_num is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalNumEqualTo(BigDecimal value) {
            addCriterion("material_change_total_num =", value, "materialChangeTotalNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalNumNotEqualTo(BigDecimal value) {
            addCriterion("material_change_total_num <>", value, "materialChangeTotalNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalNumGreaterThan(BigDecimal value) {
            addCriterion("material_change_total_num >", value, "materialChangeTotalNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_change_total_num >=", value, "materialChangeTotalNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalNumLessThan(BigDecimal value) {
            addCriterion("material_change_total_num <", value, "materialChangeTotalNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_change_total_num <=", value, "materialChangeTotalNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalNumIn(List<BigDecimal> values) {
            addCriterion("material_change_total_num in", values, "materialChangeTotalNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalNumNotIn(List<BigDecimal> values) {
            addCriterion("material_change_total_num not in", values, "materialChangeTotalNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_change_total_num between", value1, value2, "materialChangeTotalNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_change_total_num not between", value1, value2, "materialChangeTotalNum");
            return (Criteria) this;
        }

        public Criteria andMaterialChangePriceIsNull() {
            addCriterion("material_change_price is null");
            return (Criteria) this;
        }

        public Criteria andMaterialChangePriceIsNotNull() {
            addCriterion("material_change_price is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialChangePriceEqualTo(BigDecimal value) {
            addCriterion("material_change_price =", value, "materialChangePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialChangePriceNotEqualTo(BigDecimal value) {
            addCriterion("material_change_price <>", value, "materialChangePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialChangePriceGreaterThan(BigDecimal value) {
            addCriterion("material_change_price >", value, "materialChangePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialChangePriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_change_price >=", value, "materialChangePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialChangePriceLessThan(BigDecimal value) {
            addCriterion("material_change_price <", value, "materialChangePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialChangePriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_change_price <=", value, "materialChangePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialChangePriceIn(List<BigDecimal> values) {
            addCriterion("material_change_price in", values, "materialChangePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialChangePriceNotIn(List<BigDecimal> values) {
            addCriterion("material_change_price not in", values, "materialChangePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialChangePriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_change_price between", value1, value2, "materialChangePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialChangePriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_change_price not between", value1, value2, "materialChangePrice");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalAmountIsNull() {
            addCriterion("material_change_total_amount is null");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalAmountIsNotNull() {
            addCriterion("material_change_total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalAmountEqualTo(BigDecimal value) {
            addCriterion("material_change_total_amount =", value, "materialChangeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("material_change_total_amount <>", value, "materialChangeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("material_change_total_amount >", value, "materialChangeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_change_total_amount >=", value, "materialChangeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalAmountLessThan(BigDecimal value) {
            addCriterion("material_change_total_amount <", value, "materialChangeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_change_total_amount <=", value, "materialChangeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalAmountIn(List<BigDecimal> values) {
            addCriterion("material_change_total_amount in", values, "materialChangeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("material_change_total_amount not in", values, "materialChangeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_change_total_amount between", value1, value2, "materialChangeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialChangeTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_change_total_amount not between", value1, value2, "materialChangeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialCostTypeIsNull() {
            addCriterion("material_cost_type is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCostTypeIsNotNull() {
            addCriterion("material_cost_type is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCostTypeEqualTo(Integer value) {
            addCriterion("material_cost_type =", value, "materialCostType");
            return (Criteria) this;
        }

        public Criteria andMaterialCostTypeNotEqualTo(Integer value) {
            addCriterion("material_cost_type <>", value, "materialCostType");
            return (Criteria) this;
        }

        public Criteria andMaterialCostTypeGreaterThan(Integer value) {
            addCriterion("material_cost_type >", value, "materialCostType");
            return (Criteria) this;
        }

        public Criteria andMaterialCostTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("material_cost_type >=", value, "materialCostType");
            return (Criteria) this;
        }

        public Criteria andMaterialCostTypeLessThan(Integer value) {
            addCriterion("material_cost_type <", value, "materialCostType");
            return (Criteria) this;
        }

        public Criteria andMaterialCostTypeLessThanOrEqualTo(Integer value) {
            addCriterion("material_cost_type <=", value, "materialCostType");
            return (Criteria) this;
        }

        public Criteria andMaterialCostTypeIn(List<Integer> values) {
            addCriterion("material_cost_type in", values, "materialCostType");
            return (Criteria) this;
        }

        public Criteria andMaterialCostTypeNotIn(List<Integer> values) {
            addCriterion("material_cost_type not in", values, "materialCostType");
            return (Criteria) this;
        }

        public Criteria andMaterialCostTypeBetween(Integer value1, Integer value2) {
            addCriterion("material_cost_type between", value1, value2, "materialCostType");
            return (Criteria) this;
        }

        public Criteria andMaterialCostTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("material_cost_type not between", value1, value2, "materialCostType");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdIsNull() {
            addCriterion("change_record_id is null");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdIsNotNull() {
            addCriterion("change_record_id is not null");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdEqualTo(Long value) {
            addCriterion("change_record_id =", value, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdNotEqualTo(Long value) {
            addCriterion("change_record_id <>", value, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdGreaterThan(Long value) {
            addCriterion("change_record_id >", value, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdGreaterThanOrEqualTo(Long value) {
            addCriterion("change_record_id >=", value, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdLessThan(Long value) {
            addCriterion("change_record_id <", value, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdLessThanOrEqualTo(Long value) {
            addCriterion("change_record_id <=", value, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdIn(List<Long> values) {
            addCriterion("change_record_id in", values, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdNotIn(List<Long> values) {
            addCriterion("change_record_id not in", values, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdBetween(Long value1, Long value2) {
            addCriterion("change_record_id between", value1, value2, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdNotBetween(Long value1, Long value2) {
            addCriterion("change_record_id not between", value1, value2, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeTimeIsNull() {
            addCriterion("change_time is null");
            return (Criteria) this;
        }

        public Criteria andChangeTimeIsNotNull() {
            addCriterion("change_time is not null");
            return (Criteria) this;
        }

        public Criteria andChangeTimeEqualTo(Date value) {
            addCriterion("change_time =", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeNotEqualTo(Date value) {
            addCriterion("change_time <>", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeGreaterThan(Date value) {
            addCriterion("change_time >", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("change_time >=", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeLessThan(Date value) {
            addCriterion("change_time <", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeLessThanOrEqualTo(Date value) {
            addCriterion("change_time <=", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeIn(List<Date> values) {
            addCriterion("change_time in", values, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeNotIn(List<Date> values) {
            addCriterion("change_time not in", values, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeBetween(Date value1, Date value2) {
            addCriterion("change_time between", value1, value2, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeNotBetween(Date value1, Date value2) {
            addCriterion("change_time not between", value1, value2, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeUserIdIsNull() {
            addCriterion("change_user_id is null");
            return (Criteria) this;
        }

        public Criteria andChangeUserIdIsNotNull() {
            addCriterion("change_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andChangeUserIdEqualTo(Long value) {
            addCriterion("change_user_id =", value, "changeUserId");
            return (Criteria) this;
        }

        public Criteria andChangeUserIdNotEqualTo(Long value) {
            addCriterion("change_user_id <>", value, "changeUserId");
            return (Criteria) this;
        }

        public Criteria andChangeUserIdGreaterThan(Long value) {
            addCriterion("change_user_id >", value, "changeUserId");
            return (Criteria) this;
        }

        public Criteria andChangeUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("change_user_id >=", value, "changeUserId");
            return (Criteria) this;
        }

        public Criteria andChangeUserIdLessThan(Long value) {
            addCriterion("change_user_id <", value, "changeUserId");
            return (Criteria) this;
        }

        public Criteria andChangeUserIdLessThanOrEqualTo(Long value) {
            addCriterion("change_user_id <=", value, "changeUserId");
            return (Criteria) this;
        }

        public Criteria andChangeUserIdIn(List<Long> values) {
            addCriterion("change_user_id in", values, "changeUserId");
            return (Criteria) this;
        }

        public Criteria andChangeUserIdNotIn(List<Long> values) {
            addCriterion("change_user_id not in", values, "changeUserId");
            return (Criteria) this;
        }

        public Criteria andChangeUserIdBetween(Long value1, Long value2) {
            addCriterion("change_user_id between", value1, value2, "changeUserId");
            return (Criteria) this;
        }

        public Criteria andChangeUserIdNotBetween(Long value1, Long value2) {
            addCriterion("change_user_id not between", value1, value2, "changeUserId");
            return (Criteria) this;
        }

        public Criteria andChangeUserNameIsNull() {
            addCriterion("change_user_name is null");
            return (Criteria) this;
        }

        public Criteria andChangeUserNameIsNotNull() {
            addCriterion("change_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andChangeUserNameEqualTo(String value) {
            addCriterion("change_user_name =", value, "changeUserName");
            return (Criteria) this;
        }

        public Criteria andChangeUserNameNotEqualTo(String value) {
            addCriterion("change_user_name <>", value, "changeUserName");
            return (Criteria) this;
        }

        public Criteria andChangeUserNameGreaterThan(String value) {
            addCriterion("change_user_name >", value, "changeUserName");
            return (Criteria) this;
        }

        public Criteria andChangeUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("change_user_name >=", value, "changeUserName");
            return (Criteria) this;
        }

        public Criteria andChangeUserNameLessThan(String value) {
            addCriterion("change_user_name <", value, "changeUserName");
            return (Criteria) this;
        }

        public Criteria andChangeUserNameLessThanOrEqualTo(String value) {
            addCriterion("change_user_name <=", value, "changeUserName");
            return (Criteria) this;
        }

        public Criteria andChangeUserNameLike(String value) {
            addCriterion("change_user_name like", value, "changeUserName");
            return (Criteria) this;
        }

        public Criteria andChangeUserNameNotLike(String value) {
            addCriterion("change_user_name not like", value, "changeUserName");
            return (Criteria) this;
        }

        public Criteria andChangeUserNameIn(List<String> values) {
            addCriterion("change_user_name in", values, "changeUserName");
            return (Criteria) this;
        }

        public Criteria andChangeUserNameNotIn(List<String> values) {
            addCriterion("change_user_name not in", values, "changeUserName");
            return (Criteria) this;
        }

        public Criteria andChangeUserNameBetween(String value1, String value2) {
            addCriterion("change_user_name between", value1, value2, "changeUserName");
            return (Criteria) this;
        }

        public Criteria andChangeUserNameNotBetween(String value1, String value2) {
            addCriterion("change_user_name not between", value1, value2, "changeUserName");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIsNull() {
            addCriterion("change_reason is null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIsNotNull() {
            addCriterion("change_reason is not null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonEqualTo(String value) {
            addCriterion("change_reason =", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotEqualTo(String value) {
            addCriterion("change_reason <>", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonGreaterThan(String value) {
            addCriterion("change_reason >", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonGreaterThanOrEqualTo(String value) {
            addCriterion("change_reason >=", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLessThan(String value) {
            addCriterion("change_reason <", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLessThanOrEqualTo(String value) {
            addCriterion("change_reason <=", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLike(String value) {
            addCriterion("change_reason like", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotLike(String value) {
            addCriterion("change_reason not like", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIn(List<String> values) {
            addCriterion("change_reason in", values, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotIn(List<String> values) {
            addCriterion("change_reason not in", values, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonBetween(String value1, String value2) {
            addCriterion("change_reason between", value1, value2, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotBetween(String value1, String value2) {
            addCriterion("change_reason not between", value1, value2, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeRemarkIsNull() {
            addCriterion("change_remark is null");
            return (Criteria) this;
        }

        public Criteria andChangeRemarkIsNotNull() {
            addCriterion("change_remark is not null");
            return (Criteria) this;
        }

        public Criteria andChangeRemarkEqualTo(String value) {
            addCriterion("change_remark =", value, "changeRemark");
            return (Criteria) this;
        }

        public Criteria andChangeRemarkNotEqualTo(String value) {
            addCriterion("change_remark <>", value, "changeRemark");
            return (Criteria) this;
        }

        public Criteria andChangeRemarkGreaterThan(String value) {
            addCriterion("change_remark >", value, "changeRemark");
            return (Criteria) this;
        }

        public Criteria andChangeRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("change_remark >=", value, "changeRemark");
            return (Criteria) this;
        }

        public Criteria andChangeRemarkLessThan(String value) {
            addCriterion("change_remark <", value, "changeRemark");
            return (Criteria) this;
        }

        public Criteria andChangeRemarkLessThanOrEqualTo(String value) {
            addCriterion("change_remark <=", value, "changeRemark");
            return (Criteria) this;
        }

        public Criteria andChangeRemarkLike(String value) {
            addCriterion("change_remark like", value, "changeRemark");
            return (Criteria) this;
        }

        public Criteria andChangeRemarkNotLike(String value) {
            addCriterion("change_remark not like", value, "changeRemark");
            return (Criteria) this;
        }

        public Criteria andChangeRemarkIn(List<String> values) {
            addCriterion("change_remark in", values, "changeRemark");
            return (Criteria) this;
        }

        public Criteria andChangeRemarkNotIn(List<String> values) {
            addCriterion("change_remark not in", values, "changeRemark");
            return (Criteria) this;
        }

        public Criteria andChangeRemarkBetween(String value1, String value2) {
            addCriterion("change_remark between", value1, value2, "changeRemark");
            return (Criteria) this;
        }

        public Criteria andChangeRemarkNotBetween(String value1, String value2) {
            addCriterion("change_remark not between", value1, value2, "changeRemark");
            return (Criteria) this;
        }

        public Criteria andChangeTypeIsNull() {
            addCriterion("change_type is null");
            return (Criteria) this;
        }

        public Criteria andChangeTypeIsNotNull() {
            addCriterion("change_type is not null");
            return (Criteria) this;
        }

        public Criteria andChangeTypeEqualTo(Integer value) {
            addCriterion("change_type =", value, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeNotEqualTo(Integer value) {
            addCriterion("change_type <>", value, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeGreaterThan(Integer value) {
            addCriterion("change_type >", value, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("change_type >=", value, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeLessThan(Integer value) {
            addCriterion("change_type <", value, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeLessThanOrEqualTo(Integer value) {
            addCriterion("change_type <=", value, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeIn(List<Integer> values) {
            addCriterion("change_type in", values, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeNotIn(List<Integer> values) {
            addCriterion("change_type not in", values, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeBetween(Integer value1, Integer value2) {
            addCriterion("change_type between", value1, value2, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("change_type not between", value1, value2, "changeType");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeIsNull() {
            addCriterion("ticket_task_code is null");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeIsNotNull() {
            addCriterion("ticket_task_code is not null");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeEqualTo(String value) {
            addCriterion("ticket_task_code =", value, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeNotEqualTo(String value) {
            addCriterion("ticket_task_code <>", value, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeGreaterThan(String value) {
            addCriterion("ticket_task_code >", value, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ticket_task_code >=", value, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeLessThan(String value) {
            addCriterion("ticket_task_code <", value, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeLessThanOrEqualTo(String value) {
            addCriterion("ticket_task_code <=", value, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeLike(String value) {
            addCriterion("ticket_task_code like", value, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeNotLike(String value) {
            addCriterion("ticket_task_code not like", value, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeIn(List<String> values) {
            addCriterion("ticket_task_code in", values, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeNotIn(List<String> values) {
            addCriterion("ticket_task_code not in", values, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeBetween(String value1, String value2) {
            addCriterion("ticket_task_code between", value1, value2, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andTicketTaskCodeNotBetween(String value1, String value2) {
            addCriterion("ticket_task_code not between", value1, value2, "ticketTaskCode");
            return (Criteria) this;
        }

        public Criteria andActualAmountIsNull() {
            addCriterion("actual_amount is null");
            return (Criteria) this;
        }

        public Criteria andActualAmountIsNotNull() {
            addCriterion("actual_amount is not null");
            return (Criteria) this;
        }

        public Criteria andActualAmountEqualTo(BigDecimal value) {
            addCriterion("actual_amount =", value, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountNotEqualTo(BigDecimal value) {
            addCriterion("actual_amount <>", value, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountGreaterThan(BigDecimal value) {
            addCriterion("actual_amount >", value, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_amount >=", value, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountLessThan(BigDecimal value) {
            addCriterion("actual_amount <", value, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_amount <=", value, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountIn(List<BigDecimal> values) {
            addCriterion("actual_amount in", values, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountNotIn(List<BigDecimal> values) {
            addCriterion("actual_amount not in", values, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_amount between", value1, value2, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_amount not between", value1, value2, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andChangeCreateAtIsNull() {
            addCriterion("change_create_at is null");
            return (Criteria) this;
        }

        public Criteria andChangeCreateAtIsNotNull() {
            addCriterion("change_create_at is not null");
            return (Criteria) this;
        }

        public Criteria andChangeCreateAtEqualTo(Date value) {
            addCriterion("change_create_at =", value, "changeCreateAt");
            return (Criteria) this;
        }

        public Criteria andChangeCreateAtNotEqualTo(Date value) {
            addCriterion("change_create_at <>", value, "changeCreateAt");
            return (Criteria) this;
        }

        public Criteria andChangeCreateAtGreaterThan(Date value) {
            addCriterion("change_create_at >", value, "changeCreateAt");
            return (Criteria) this;
        }

        public Criteria andChangeCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("change_create_at >=", value, "changeCreateAt");
            return (Criteria) this;
        }

        public Criteria andChangeCreateAtLessThan(Date value) {
            addCriterion("change_create_at <", value, "changeCreateAt");
            return (Criteria) this;
        }

        public Criteria andChangeCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("change_create_at <=", value, "changeCreateAt");
            return (Criteria) this;
        }

        public Criteria andChangeCreateAtIn(List<Date> values) {
            addCriterion("change_create_at in", values, "changeCreateAt");
            return (Criteria) this;
        }

        public Criteria andChangeCreateAtNotIn(List<Date> values) {
            addCriterion("change_create_at not in", values, "changeCreateAt");
            return (Criteria) this;
        }

        public Criteria andChangeCreateAtBetween(Date value1, Date value2) {
            addCriterion("change_create_at between", value1, value2, "changeCreateAt");
            return (Criteria) this;
        }

        public Criteria andChangeCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("change_create_at not between", value1, value2, "changeCreateAt");
            return (Criteria) this;
        }

        public Criteria andChangeUpdateAtIsNull() {
            addCriterion("change_update_at is null");
            return (Criteria) this;
        }

        public Criteria andChangeUpdateAtIsNotNull() {
            addCriterion("change_update_at is not null");
            return (Criteria) this;
        }

        public Criteria andChangeUpdateAtEqualTo(Date value) {
            addCriterion("change_update_at =", value, "changeUpdateAt");
            return (Criteria) this;
        }

        public Criteria andChangeUpdateAtNotEqualTo(Date value) {
            addCriterion("change_update_at <>", value, "changeUpdateAt");
            return (Criteria) this;
        }

        public Criteria andChangeUpdateAtGreaterThan(Date value) {
            addCriterion("change_update_at >", value, "changeUpdateAt");
            return (Criteria) this;
        }

        public Criteria andChangeUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("change_update_at >=", value, "changeUpdateAt");
            return (Criteria) this;
        }

        public Criteria andChangeUpdateAtLessThan(Date value) {
            addCriterion("change_update_at <", value, "changeUpdateAt");
            return (Criteria) this;
        }

        public Criteria andChangeUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("change_update_at <=", value, "changeUpdateAt");
            return (Criteria) this;
        }

        public Criteria andChangeUpdateAtIn(List<Date> values) {
            addCriterion("change_update_at in", values, "changeUpdateAt");
            return (Criteria) this;
        }

        public Criteria andChangeUpdateAtNotIn(List<Date> values) {
            addCriterion("change_update_at not in", values, "changeUpdateAt");
            return (Criteria) this;
        }

        public Criteria andChangeUpdateAtBetween(Date value1, Date value2) {
            addCriterion("change_update_at between", value1, value2, "changeUpdateAt");
            return (Criteria) this;
        }

        public Criteria andChangeUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("change_update_at not between", value1, value2, "changeUpdateAt");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryIsNull() {
            addCriterion("material_category is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryIsNotNull() {
            addCriterion("material_category is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryEqualTo(String value) {
            addCriterion("material_category =", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryNotEqualTo(String value) {
            addCriterion("material_category <>", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryGreaterThan(String value) {
            addCriterion("material_category >", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("material_category >=", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryLessThan(String value) {
            addCriterion("material_category <", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryLessThanOrEqualTo(String value) {
            addCriterion("material_category <=", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryLike(String value) {
            addCriterion("material_category like", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryNotLike(String value) {
            addCriterion("material_category not like", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryIn(List<String> values) {
            addCriterion("material_category in", values, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryNotIn(List<String> values) {
            addCriterion("material_category not in", values, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryBetween(String value1, String value2) {
            addCriterion("material_category between", value1, value2, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryNotBetween(String value1, String value2) {
            addCriterion("material_category not between", value1, value2, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andShowRequirementConfirmIsNull() {
            addCriterion("show_requirement_confirm is null");
            return (Criteria) this;
        }

        public Criteria andShowRequirementConfirmIsNotNull() {
            addCriterion("show_requirement_confirm is not null");
            return (Criteria) this;
        }

        public Criteria andShowRequirementConfirmEqualTo(String value) {
            addCriterion("show_requirement_confirm =", value, "showRequirementConfirm");
            return (Criteria) this;
        }

        public Criteria andShowRequirementConfirmNotEqualTo(String value) {
            addCriterion("show_requirement_confirm <>", value, "showRequirementConfirm");
            return (Criteria) this;
        }

        public Criteria andShowRequirementConfirmGreaterThan(String value) {
            addCriterion("show_requirement_confirm >", value, "showRequirementConfirm");
            return (Criteria) this;
        }

        public Criteria andShowRequirementConfirmGreaterThanOrEqualTo(String value) {
            addCriterion("show_requirement_confirm >=", value, "showRequirementConfirm");
            return (Criteria) this;
        }

        public Criteria andShowRequirementConfirmLessThan(String value) {
            addCriterion("show_requirement_confirm <", value, "showRequirementConfirm");
            return (Criteria) this;
        }

        public Criteria andShowRequirementConfirmLessThanOrEqualTo(String value) {
            addCriterion("show_requirement_confirm <=", value, "showRequirementConfirm");
            return (Criteria) this;
        }

        public Criteria andShowRequirementConfirmLike(String value) {
            addCriterion("show_requirement_confirm like", value, "showRequirementConfirm");
            return (Criteria) this;
        }

        public Criteria andShowRequirementConfirmNotLike(String value) {
            addCriterion("show_requirement_confirm not like", value, "showRequirementConfirm");
            return (Criteria) this;
        }

        public Criteria andShowRequirementConfirmIn(List<String> values) {
            addCriterion("show_requirement_confirm in", values, "showRequirementConfirm");
            return (Criteria) this;
        }

        public Criteria andShowRequirementConfirmNotIn(List<String> values) {
            addCriterion("show_requirement_confirm not in", values, "showRequirementConfirm");
            return (Criteria) this;
        }

        public Criteria andShowRequirementConfirmBetween(String value1, String value2) {
            addCriterion("show_requirement_confirm between", value1, value2, "showRequirementConfirm");
            return (Criteria) this;
        }

        public Criteria andShowRequirementConfirmNotBetween(String value1, String value2) {
            addCriterion("show_requirement_confirm not between", value1, value2, "showRequirementConfirm");
            return (Criteria) this;
        }

        public Criteria andChangeParentIdIsNull() {
            addCriterion("change_parent_id is null");
            return (Criteria) this;
        }

        public Criteria andChangeParentIdIsNotNull() {
            addCriterion("change_parent_id is not null");
            return (Criteria) this;
        }

        public Criteria andChangeParentIdEqualTo(Long value) {
            addCriterion("change_parent_id =", value, "changeParentId");
            return (Criteria) this;
        }

        public Criteria andChangeParentIdNotEqualTo(Long value) {
            addCriterion("change_parent_id <>", value, "changeParentId");
            return (Criteria) this;
        }

        public Criteria andChangeParentIdGreaterThan(Long value) {
            addCriterion("change_parent_id >", value, "changeParentId");
            return (Criteria) this;
        }

        public Criteria andChangeParentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("change_parent_id >=", value, "changeParentId");
            return (Criteria) this;
        }

        public Criteria andChangeParentIdLessThan(Long value) {
            addCriterion("change_parent_id <", value, "changeParentId");
            return (Criteria) this;
        }

        public Criteria andChangeParentIdLessThanOrEqualTo(Long value) {
            addCriterion("change_parent_id <=", value, "changeParentId");
            return (Criteria) this;
        }

        public Criteria andChangeParentIdIn(List<Long> values) {
            addCriterion("change_parent_id in", values, "changeParentId");
            return (Criteria) this;
        }

        public Criteria andChangeParentIdNotIn(List<Long> values) {
            addCriterion("change_parent_id not in", values, "changeParentId");
            return (Criteria) this;
        }

        public Criteria andChangeParentIdBetween(Long value1, Long value2) {
            addCriterion("change_parent_id between", value1, value2, "changeParentId");
            return (Criteria) this;
        }

        public Criteria andChangeParentIdNotBetween(Long value1, Long value2) {
            addCriterion("change_parent_id not between", value1, value2, "changeParentId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}