package com.midea.pam.common.ctc.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class InnerSwapApplyExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public InnerSwapApplyExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andApplyCodeIsNull() {
            addCriterion("apply_code is null");
            return (Criteria) this;
        }

        public Criteria andApplyCodeIsNotNull() {
            addCriterion("apply_code is not null");
            return (Criteria) this;
        }

        public Criteria andApplyCodeEqualTo(String value) {
            addCriterion("apply_code =", value, "applyCode");
            return (Criteria) this;
        }

        public Criteria andApplyCodeNotEqualTo(String value) {
            addCriterion("apply_code <>", value, "applyCode");
            return (Criteria) this;
        }

        public Criteria andApplyCodeGreaterThan(String value) {
            addCriterion("apply_code >", value, "applyCode");
            return (Criteria) this;
        }

        public Criteria andApplyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("apply_code >=", value, "applyCode");
            return (Criteria) this;
        }

        public Criteria andApplyCodeLessThan(String value) {
            addCriterion("apply_code <", value, "applyCode");
            return (Criteria) this;
        }

        public Criteria andApplyCodeLessThanOrEqualTo(String value) {
            addCriterion("apply_code <=", value, "applyCode");
            return (Criteria) this;
        }

        public Criteria andApplyCodeLike(String value) {
            addCriterion("apply_code like", value, "applyCode");
            return (Criteria) this;
        }

        public Criteria andApplyCodeNotLike(String value) {
            addCriterion("apply_code not like", value, "applyCode");
            return (Criteria) this;
        }

        public Criteria andApplyCodeIn(List<String> values) {
            addCriterion("apply_code in", values, "applyCode");
            return (Criteria) this;
        }

        public Criteria andApplyCodeNotIn(List<String> values) {
            addCriterion("apply_code not in", values, "applyCode");
            return (Criteria) this;
        }

        public Criteria andApplyCodeBetween(String value1, String value2) {
            addCriterion("apply_code between", value1, value2, "applyCode");
            return (Criteria) this;
        }

        public Criteria andApplyCodeNotBetween(String value1, String value2) {
            addCriterion("apply_code not between", value1, value2, "applyCode");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgIdIsNull() {
            addCriterion("provide_product_org_id is null");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgIdIsNotNull() {
            addCriterion("provide_product_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgIdEqualTo(String value) {
            addCriterion("provide_product_org_id =", value, "provideProductOrgId");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgIdNotEqualTo(String value) {
            addCriterion("provide_product_org_id <>", value, "provideProductOrgId");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgIdGreaterThan(String value) {
            addCriterion("provide_product_org_id >", value, "provideProductOrgId");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgIdGreaterThanOrEqualTo(String value) {
            addCriterion("provide_product_org_id >=", value, "provideProductOrgId");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgIdLessThan(String value) {
            addCriterion("provide_product_org_id <", value, "provideProductOrgId");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgIdLessThanOrEqualTo(String value) {
            addCriterion("provide_product_org_id <=", value, "provideProductOrgId");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgIdLike(String value) {
            addCriterion("provide_product_org_id like", value, "provideProductOrgId");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgIdNotLike(String value) {
            addCriterion("provide_product_org_id not like", value, "provideProductOrgId");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgIdIn(List<String> values) {
            addCriterion("provide_product_org_id in", values, "provideProductOrgId");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgIdNotIn(List<String> values) {
            addCriterion("provide_product_org_id not in", values, "provideProductOrgId");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgIdBetween(String value1, String value2) {
            addCriterion("provide_product_org_id between", value1, value2, "provideProductOrgId");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgIdNotBetween(String value1, String value2) {
            addCriterion("provide_product_org_id not between", value1, value2, "provideProductOrgId");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgNameIsNull() {
            addCriterion("provide_product_org_name is null");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgNameIsNotNull() {
            addCriterion("provide_product_org_name is not null");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgNameEqualTo(String value) {
            addCriterion("provide_product_org_name =", value, "provideProductOrgName");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgNameNotEqualTo(String value) {
            addCriterion("provide_product_org_name <>", value, "provideProductOrgName");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgNameGreaterThan(String value) {
            addCriterion("provide_product_org_name >", value, "provideProductOrgName");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("provide_product_org_name >=", value, "provideProductOrgName");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgNameLessThan(String value) {
            addCriterion("provide_product_org_name <", value, "provideProductOrgName");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgNameLessThanOrEqualTo(String value) {
            addCriterion("provide_product_org_name <=", value, "provideProductOrgName");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgNameLike(String value) {
            addCriterion("provide_product_org_name like", value, "provideProductOrgName");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgNameNotLike(String value) {
            addCriterion("provide_product_org_name not like", value, "provideProductOrgName");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgNameIn(List<String> values) {
            addCriterion("provide_product_org_name in", values, "provideProductOrgName");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgNameNotIn(List<String> values) {
            addCriterion("provide_product_org_name not in", values, "provideProductOrgName");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgNameBetween(String value1, String value2) {
            addCriterion("provide_product_org_name between", value1, value2, "provideProductOrgName");
            return (Criteria) this;
        }

        public Criteria andProvideProductOrgNameNotBetween(String value1, String value2) {
            addCriterion("provide_product_org_name not between", value1, value2, "provideProductOrgName");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgIdIsNull() {
            addCriterion("require_product_org_id is null");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgIdIsNotNull() {
            addCriterion("require_product_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgIdEqualTo(String value) {
            addCriterion("require_product_org_id =", value, "requireProductOrgId");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgIdNotEqualTo(String value) {
            addCriterion("require_product_org_id <>", value, "requireProductOrgId");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgIdGreaterThan(String value) {
            addCriterion("require_product_org_id >", value, "requireProductOrgId");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgIdGreaterThanOrEqualTo(String value) {
            addCriterion("require_product_org_id >=", value, "requireProductOrgId");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgIdLessThan(String value) {
            addCriterion("require_product_org_id <", value, "requireProductOrgId");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgIdLessThanOrEqualTo(String value) {
            addCriterion("require_product_org_id <=", value, "requireProductOrgId");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgIdLike(String value) {
            addCriterion("require_product_org_id like", value, "requireProductOrgId");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgIdNotLike(String value) {
            addCriterion("require_product_org_id not like", value, "requireProductOrgId");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgIdIn(List<String> values) {
            addCriterion("require_product_org_id in", values, "requireProductOrgId");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgIdNotIn(List<String> values) {
            addCriterion("require_product_org_id not in", values, "requireProductOrgId");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgIdBetween(String value1, String value2) {
            addCriterion("require_product_org_id between", value1, value2, "requireProductOrgId");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgIdNotBetween(String value1, String value2) {
            addCriterion("require_product_org_id not between", value1, value2, "requireProductOrgId");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgNameIsNull() {
            addCriterion("require_product_org_name is null");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgNameIsNotNull() {
            addCriterion("require_product_org_name is not null");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgNameEqualTo(String value) {
            addCriterion("require_product_org_name =", value, "requireProductOrgName");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgNameNotEqualTo(String value) {
            addCriterion("require_product_org_name <>", value, "requireProductOrgName");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgNameGreaterThan(String value) {
            addCriterion("require_product_org_name >", value, "requireProductOrgName");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("require_product_org_name >=", value, "requireProductOrgName");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgNameLessThan(String value) {
            addCriterion("require_product_org_name <", value, "requireProductOrgName");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgNameLessThanOrEqualTo(String value) {
            addCriterion("require_product_org_name <=", value, "requireProductOrgName");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgNameLike(String value) {
            addCriterion("require_product_org_name like", value, "requireProductOrgName");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgNameNotLike(String value) {
            addCriterion("require_product_org_name not like", value, "requireProductOrgName");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgNameIn(List<String> values) {
            addCriterion("require_product_org_name in", values, "requireProductOrgName");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgNameNotIn(List<String> values) {
            addCriterion("require_product_org_name not in", values, "requireProductOrgName");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgNameBetween(String value1, String value2) {
            addCriterion("require_product_org_name between", value1, value2, "requireProductOrgName");
            return (Criteria) this;
        }

        public Criteria andRequireProductOrgNameNotBetween(String value1, String value2) {
            addCriterion("require_product_org_name not between", value1, value2, "requireProductOrgName");
            return (Criteria) this;
        }

        public Criteria andProvideUserIdIsNull() {
            addCriterion("provide_user_id is null");
            return (Criteria) this;
        }

        public Criteria andProvideUserIdIsNotNull() {
            addCriterion("provide_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andProvideUserIdEqualTo(Long value) {
            addCriterion("provide_user_id =", value, "provideUserId");
            return (Criteria) this;
        }

        public Criteria andProvideUserIdNotEqualTo(Long value) {
            addCriterion("provide_user_id <>", value, "provideUserId");
            return (Criteria) this;
        }

        public Criteria andProvideUserIdGreaterThan(Long value) {
            addCriterion("provide_user_id >", value, "provideUserId");
            return (Criteria) this;
        }

        public Criteria andProvideUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("provide_user_id >=", value, "provideUserId");
            return (Criteria) this;
        }

        public Criteria andProvideUserIdLessThan(Long value) {
            addCriterion("provide_user_id <", value, "provideUserId");
            return (Criteria) this;
        }

        public Criteria andProvideUserIdLessThanOrEqualTo(Long value) {
            addCriterion("provide_user_id <=", value, "provideUserId");
            return (Criteria) this;
        }

        public Criteria andProvideUserIdIn(List<Long> values) {
            addCriterion("provide_user_id in", values, "provideUserId");
            return (Criteria) this;
        }

        public Criteria andProvideUserIdNotIn(List<Long> values) {
            addCriterion("provide_user_id not in", values, "provideUserId");
            return (Criteria) this;
        }

        public Criteria andProvideUserIdBetween(Long value1, Long value2) {
            addCriterion("provide_user_id between", value1, value2, "provideUserId");
            return (Criteria) this;
        }

        public Criteria andProvideUserIdNotBetween(Long value1, Long value2) {
            addCriterion("provide_user_id not between", value1, value2, "provideUserId");
            return (Criteria) this;
        }

        public Criteria andProvideUserNameIsNull() {
            addCriterion("provide_user_name is null");
            return (Criteria) this;
        }

        public Criteria andProvideUserNameIsNotNull() {
            addCriterion("provide_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andProvideUserNameEqualTo(String value) {
            addCriterion("provide_user_name =", value, "provideUserName");
            return (Criteria) this;
        }

        public Criteria andProvideUserNameNotEqualTo(String value) {
            addCriterion("provide_user_name <>", value, "provideUserName");
            return (Criteria) this;
        }

        public Criteria andProvideUserNameGreaterThan(String value) {
            addCriterion("provide_user_name >", value, "provideUserName");
            return (Criteria) this;
        }

        public Criteria andProvideUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("provide_user_name >=", value, "provideUserName");
            return (Criteria) this;
        }

        public Criteria andProvideUserNameLessThan(String value) {
            addCriterion("provide_user_name <", value, "provideUserName");
            return (Criteria) this;
        }

        public Criteria andProvideUserNameLessThanOrEqualTo(String value) {
            addCriterion("provide_user_name <=", value, "provideUserName");
            return (Criteria) this;
        }

        public Criteria andProvideUserNameLike(String value) {
            addCriterion("provide_user_name like", value, "provideUserName");
            return (Criteria) this;
        }

        public Criteria andProvideUserNameNotLike(String value) {
            addCriterion("provide_user_name not like", value, "provideUserName");
            return (Criteria) this;
        }

        public Criteria andProvideUserNameIn(List<String> values) {
            addCriterion("provide_user_name in", values, "provideUserName");
            return (Criteria) this;
        }

        public Criteria andProvideUserNameNotIn(List<String> values) {
            addCriterion("provide_user_name not in", values, "provideUserName");
            return (Criteria) this;
        }

        public Criteria andProvideUserNameBetween(String value1, String value2) {
            addCriterion("provide_user_name between", value1, value2, "provideUserName");
            return (Criteria) this;
        }

        public Criteria andProvideUserNameNotBetween(String value1, String value2) {
            addCriterion("provide_user_name not between", value1, value2, "provideUserName");
            return (Criteria) this;
        }

        public Criteria andRequireUserIdIsNull() {
            addCriterion("require_user_id is null");
            return (Criteria) this;
        }

        public Criteria andRequireUserIdIsNotNull() {
            addCriterion("require_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andRequireUserIdEqualTo(Long value) {
            addCriterion("require_user_id =", value, "requireUserId");
            return (Criteria) this;
        }

        public Criteria andRequireUserIdNotEqualTo(Long value) {
            addCriterion("require_user_id <>", value, "requireUserId");
            return (Criteria) this;
        }

        public Criteria andRequireUserIdGreaterThan(Long value) {
            addCriterion("require_user_id >", value, "requireUserId");
            return (Criteria) this;
        }

        public Criteria andRequireUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("require_user_id >=", value, "requireUserId");
            return (Criteria) this;
        }

        public Criteria andRequireUserIdLessThan(Long value) {
            addCriterion("require_user_id <", value, "requireUserId");
            return (Criteria) this;
        }

        public Criteria andRequireUserIdLessThanOrEqualTo(Long value) {
            addCriterion("require_user_id <=", value, "requireUserId");
            return (Criteria) this;
        }

        public Criteria andRequireUserIdIn(List<Long> values) {
            addCriterion("require_user_id in", values, "requireUserId");
            return (Criteria) this;
        }

        public Criteria andRequireUserIdNotIn(List<Long> values) {
            addCriterion("require_user_id not in", values, "requireUserId");
            return (Criteria) this;
        }

        public Criteria andRequireUserIdBetween(Long value1, Long value2) {
            addCriterion("require_user_id between", value1, value2, "requireUserId");
            return (Criteria) this;
        }

        public Criteria andRequireUserIdNotBetween(Long value1, Long value2) {
            addCriterion("require_user_id not between", value1, value2, "requireUserId");
            return (Criteria) this;
        }

        public Criteria andRequireUserNameIsNull() {
            addCriterion("require_user_name is null");
            return (Criteria) this;
        }

        public Criteria andRequireUserNameIsNotNull() {
            addCriterion("require_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andRequireUserNameEqualTo(String value) {
            addCriterion("require_user_name =", value, "requireUserName");
            return (Criteria) this;
        }

        public Criteria andRequireUserNameNotEqualTo(String value) {
            addCriterion("require_user_name <>", value, "requireUserName");
            return (Criteria) this;
        }

        public Criteria andRequireUserNameGreaterThan(String value) {
            addCriterion("require_user_name >", value, "requireUserName");
            return (Criteria) this;
        }

        public Criteria andRequireUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("require_user_name >=", value, "requireUserName");
            return (Criteria) this;
        }

        public Criteria andRequireUserNameLessThan(String value) {
            addCriterion("require_user_name <", value, "requireUserName");
            return (Criteria) this;
        }

        public Criteria andRequireUserNameLessThanOrEqualTo(String value) {
            addCriterion("require_user_name <=", value, "requireUserName");
            return (Criteria) this;
        }

        public Criteria andRequireUserNameLike(String value) {
            addCriterion("require_user_name like", value, "requireUserName");
            return (Criteria) this;
        }

        public Criteria andRequireUserNameNotLike(String value) {
            addCriterion("require_user_name not like", value, "requireUserName");
            return (Criteria) this;
        }

        public Criteria andRequireUserNameIn(List<String> values) {
            addCriterion("require_user_name in", values, "requireUserName");
            return (Criteria) this;
        }

        public Criteria andRequireUserNameNotIn(List<String> values) {
            addCriterion("require_user_name not in", values, "requireUserName");
            return (Criteria) this;
        }

        public Criteria andRequireUserNameBetween(String value1, String value2) {
            addCriterion("require_user_name between", value1, value2, "requireUserName");
            return (Criteria) this;
        }

        public Criteria andRequireUserNameNotBetween(String value1, String value2) {
            addCriterion("require_user_name not between", value1, value2, "requireUserName");
            return (Criteria) this;
        }

        public Criteria andApplyYearIsNull() {
            addCriterion("apply_year is null");
            return (Criteria) this;
        }

        public Criteria andApplyYearIsNotNull() {
            addCriterion("apply_year is not null");
            return (Criteria) this;
        }

        public Criteria andApplyYearEqualTo(String value) {
            addCriterion("apply_year =", value, "applyYear");
            return (Criteria) this;
        }

        public Criteria andApplyYearNotEqualTo(String value) {
            addCriterion("apply_year <>", value, "applyYear");
            return (Criteria) this;
        }

        public Criteria andApplyYearGreaterThan(String value) {
            addCriterion("apply_year >", value, "applyYear");
            return (Criteria) this;
        }

        public Criteria andApplyYearGreaterThanOrEqualTo(String value) {
            addCriterion("apply_year >=", value, "applyYear");
            return (Criteria) this;
        }

        public Criteria andApplyYearLessThan(String value) {
            addCriterion("apply_year <", value, "applyYear");
            return (Criteria) this;
        }

        public Criteria andApplyYearLessThanOrEqualTo(String value) {
            addCriterion("apply_year <=", value, "applyYear");
            return (Criteria) this;
        }

        public Criteria andApplyYearLike(String value) {
            addCriterion("apply_year like", value, "applyYear");
            return (Criteria) this;
        }

        public Criteria andApplyYearNotLike(String value) {
            addCriterion("apply_year not like", value, "applyYear");
            return (Criteria) this;
        }

        public Criteria andApplyYearIn(List<String> values) {
            addCriterion("apply_year in", values, "applyYear");
            return (Criteria) this;
        }

        public Criteria andApplyYearNotIn(List<String> values) {
            addCriterion("apply_year not in", values, "applyYear");
            return (Criteria) this;
        }

        public Criteria andApplyYearBetween(String value1, String value2) {
            addCriterion("apply_year between", value1, value2, "applyYear");
            return (Criteria) this;
        }

        public Criteria andApplyYearNotBetween(String value1, String value2) {
            addCriterion("apply_year not between", value1, value2, "applyYear");
            return (Criteria) this;
        }

        public Criteria andApplyTotalIncomeIsNull() {
            addCriterion("apply_total_income is null");
            return (Criteria) this;
        }

        public Criteria andApplyTotalIncomeIsNotNull() {
            addCriterion("apply_total_income is not null");
            return (Criteria) this;
        }

        public Criteria andApplyTotalIncomeEqualTo(BigDecimal value) {
            addCriterion("apply_total_income =", value, "applyTotalIncome");
            return (Criteria) this;
        }

        public Criteria andApplyTotalIncomeNotEqualTo(BigDecimal value) {
            addCriterion("apply_total_income <>", value, "applyTotalIncome");
            return (Criteria) this;
        }

        public Criteria andApplyTotalIncomeGreaterThan(BigDecimal value) {
            addCriterion("apply_total_income >", value, "applyTotalIncome");
            return (Criteria) this;
        }

        public Criteria andApplyTotalIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_total_income >=", value, "applyTotalIncome");
            return (Criteria) this;
        }

        public Criteria andApplyTotalIncomeLessThan(BigDecimal value) {
            addCriterion("apply_total_income <", value, "applyTotalIncome");
            return (Criteria) this;
        }

        public Criteria andApplyTotalIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_total_income <=", value, "applyTotalIncome");
            return (Criteria) this;
        }

        public Criteria andApplyTotalIncomeIn(List<BigDecimal> values) {
            addCriterion("apply_total_income in", values, "applyTotalIncome");
            return (Criteria) this;
        }

        public Criteria andApplyTotalIncomeNotIn(List<BigDecimal> values) {
            addCriterion("apply_total_income not in", values, "applyTotalIncome");
            return (Criteria) this;
        }

        public Criteria andApplyTotalIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_total_income between", value1, value2, "applyTotalIncome");
            return (Criteria) this;
        }

        public Criteria andApplyTotalIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_total_income not between", value1, value2, "applyTotalIncome");
            return (Criteria) this;
        }

        public Criteria andTotalMembersIsNull() {
            addCriterion("total_members is null");
            return (Criteria) this;
        }

        public Criteria andTotalMembersIsNotNull() {
            addCriterion("total_members is not null");
            return (Criteria) this;
        }

        public Criteria andTotalMembersEqualTo(Integer value) {
            addCriterion("total_members =", value, "totalMembers");
            return (Criteria) this;
        }

        public Criteria andTotalMembersNotEqualTo(Integer value) {
            addCriterion("total_members <>", value, "totalMembers");
            return (Criteria) this;
        }

        public Criteria andTotalMembersGreaterThan(Integer value) {
            addCriterion("total_members >", value, "totalMembers");
            return (Criteria) this;
        }

        public Criteria andTotalMembersGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_members >=", value, "totalMembers");
            return (Criteria) this;
        }

        public Criteria andTotalMembersLessThan(Integer value) {
            addCriterion("total_members <", value, "totalMembers");
            return (Criteria) this;
        }

        public Criteria andTotalMembersLessThanOrEqualTo(Integer value) {
            addCriterion("total_members <=", value, "totalMembers");
            return (Criteria) this;
        }

        public Criteria andTotalMembersIn(List<Integer> values) {
            addCriterion("total_members in", values, "totalMembers");
            return (Criteria) this;
        }

        public Criteria andTotalMembersNotIn(List<Integer> values) {
            addCriterion("total_members not in", values, "totalMembers");
            return (Criteria) this;
        }

        public Criteria andTotalMembersBetween(Integer value1, Integer value2) {
            addCriterion("total_members between", value1, value2, "totalMembers");
            return (Criteria) this;
        }

        public Criteria andTotalMembersNotBetween(Integer value1, Integer value2) {
            addCriterion("total_members not between", value1, value2, "totalMembers");
            return (Criteria) this;
        }

        public Criteria andTotalMemberDaysIsNull() {
            addCriterion("total_member_days is null");
            return (Criteria) this;
        }

        public Criteria andTotalMemberDaysIsNotNull() {
            addCriterion("total_member_days is not null");
            return (Criteria) this;
        }

        public Criteria andTotalMemberDaysEqualTo(String value) {
            addCriterion("total_member_days =", value, "totalMemberDays");
            return (Criteria) this;
        }

        public Criteria andTotalMemberDaysNotEqualTo(String value) {
            addCriterion("total_member_days <>", value, "totalMemberDays");
            return (Criteria) this;
        }

        public Criteria andTotalMemberDaysGreaterThan(String value) {
            addCriterion("total_member_days >", value, "totalMemberDays");
            return (Criteria) this;
        }

        public Criteria andTotalMemberDaysGreaterThanOrEqualTo(String value) {
            addCriterion("total_member_days >=", value, "totalMemberDays");
            return (Criteria) this;
        }

        public Criteria andTotalMemberDaysLessThan(String value) {
            addCriterion("total_member_days <", value, "totalMemberDays");
            return (Criteria) this;
        }

        public Criteria andTotalMemberDaysLessThanOrEqualTo(String value) {
            addCriterion("total_member_days <=", value, "totalMemberDays");
            return (Criteria) this;
        }

        public Criteria andTotalMemberDaysLike(String value) {
            addCriterion("total_member_days like", value, "totalMemberDays");
            return (Criteria) this;
        }

        public Criteria andTotalMemberDaysNotLike(String value) {
            addCriterion("total_member_days not like", value, "totalMemberDays");
            return (Criteria) this;
        }

        public Criteria andTotalMemberDaysIn(List<String> values) {
            addCriterion("total_member_days in", values, "totalMemberDays");
            return (Criteria) this;
        }

        public Criteria andTotalMemberDaysNotIn(List<String> values) {
            addCriterion("total_member_days not in", values, "totalMemberDays");
            return (Criteria) this;
        }

        public Criteria andTotalMemberDaysBetween(String value1, String value2) {
            addCriterion("total_member_days between", value1, value2, "totalMemberDays");
            return (Criteria) this;
        }

        public Criteria andTotalMemberDaysNotBetween(String value1, String value2) {
            addCriterion("total_member_days not between", value1, value2, "totalMemberDays");
            return (Criteria) this;
        }

        public Criteria andTotalMemeberMoneyIsNull() {
            addCriterion("total_memeber_money is null");
            return (Criteria) this;
        }

        public Criteria andTotalMemeberMoneyIsNotNull() {
            addCriterion("total_memeber_money is not null");
            return (Criteria) this;
        }

        public Criteria andTotalMemeberMoneyEqualTo(BigDecimal value) {
            addCriterion("total_memeber_money =", value, "totalMemeberMoney");
            return (Criteria) this;
        }

        public Criteria andTotalMemeberMoneyNotEqualTo(BigDecimal value) {
            addCriterion("total_memeber_money <>", value, "totalMemeberMoney");
            return (Criteria) this;
        }

        public Criteria andTotalMemeberMoneyGreaterThan(BigDecimal value) {
            addCriterion("total_memeber_money >", value, "totalMemeberMoney");
            return (Criteria) this;
        }

        public Criteria andTotalMemeberMoneyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_memeber_money >=", value, "totalMemeberMoney");
            return (Criteria) this;
        }

        public Criteria andTotalMemeberMoneyLessThan(BigDecimal value) {
            addCriterion("total_memeber_money <", value, "totalMemeberMoney");
            return (Criteria) this;
        }

        public Criteria andTotalMemeberMoneyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_memeber_money <=", value, "totalMemeberMoney");
            return (Criteria) this;
        }

        public Criteria andTotalMemeberMoneyIn(List<BigDecimal> values) {
            addCriterion("total_memeber_money in", values, "totalMemeberMoney");
            return (Criteria) this;
        }

        public Criteria andTotalMemeberMoneyNotIn(List<BigDecimal> values) {
            addCriterion("total_memeber_money not in", values, "totalMemeberMoney");
            return (Criteria) this;
        }

        public Criteria andTotalMemeberMoneyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_memeber_money between", value1, value2, "totalMemeberMoney");
            return (Criteria) this;
        }

        public Criteria andTotalMemeberMoneyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_memeber_money not between", value1, value2, "totalMemeberMoney");
            return (Criteria) this;
        }

        public Criteria andTotalProductMoneyIsNull() {
            addCriterion("total_product_money is null");
            return (Criteria) this;
        }

        public Criteria andTotalProductMoneyIsNotNull() {
            addCriterion("total_product_money is not null");
            return (Criteria) this;
        }

        public Criteria andTotalProductMoneyEqualTo(BigDecimal value) {
            addCriterion("total_product_money =", value, "totalProductMoney");
            return (Criteria) this;
        }

        public Criteria andTotalProductMoneyNotEqualTo(BigDecimal value) {
            addCriterion("total_product_money <>", value, "totalProductMoney");
            return (Criteria) this;
        }

        public Criteria andTotalProductMoneyGreaterThan(BigDecimal value) {
            addCriterion("total_product_money >", value, "totalProductMoney");
            return (Criteria) this;
        }

        public Criteria andTotalProductMoneyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_product_money >=", value, "totalProductMoney");
            return (Criteria) this;
        }

        public Criteria andTotalProductMoneyLessThan(BigDecimal value) {
            addCriterion("total_product_money <", value, "totalProductMoney");
            return (Criteria) this;
        }

        public Criteria andTotalProductMoneyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_product_money <=", value, "totalProductMoney");
            return (Criteria) this;
        }

        public Criteria andTotalProductMoneyIn(List<BigDecimal> values) {
            addCriterion("total_product_money in", values, "totalProductMoney");
            return (Criteria) this;
        }

        public Criteria andTotalProductMoneyNotIn(List<BigDecimal> values) {
            addCriterion("total_product_money not in", values, "totalProductMoney");
            return (Criteria) this;
        }

        public Criteria andTotalProductMoneyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_product_money between", value1, value2, "totalProductMoney");
            return (Criteria) this;
        }

        public Criteria andTotalProductMoneyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_product_money not between", value1, value2, "totalProductMoney");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andApprovedDateIsNull() {
            addCriterion("approved_date is null");
            return (Criteria) this;
        }

        public Criteria andApprovedDateIsNotNull() {
            addCriterion("approved_date is not null");
            return (Criteria) this;
        }

        public Criteria andApprovedDateEqualTo(Date value) {
            addCriterion("approved_date =", value, "approvedDate");
            return (Criteria) this;
        }

        public Criteria andApprovedDateNotEqualTo(Date value) {
            addCriterion("approved_date <>", value, "approvedDate");
            return (Criteria) this;
        }

        public Criteria andApprovedDateGreaterThan(Date value) {
            addCriterion("approved_date >", value, "approvedDate");
            return (Criteria) this;
        }

        public Criteria andApprovedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("approved_date >=", value, "approvedDate");
            return (Criteria) this;
        }

        public Criteria andApprovedDateLessThan(Date value) {
            addCriterion("approved_date <", value, "approvedDate");
            return (Criteria) this;
        }

        public Criteria andApprovedDateLessThanOrEqualTo(Date value) {
            addCriterion("approved_date <=", value, "approvedDate");
            return (Criteria) this;
        }

        public Criteria andApprovedDateIn(List<Date> values) {
            addCriterion("approved_date in", values, "approvedDate");
            return (Criteria) this;
        }

        public Criteria andApprovedDateNotIn(List<Date> values) {
            addCriterion("approved_date not in", values, "approvedDate");
            return (Criteria) this;
        }

        public Criteria andApprovedDateBetween(Date value1, Date value2) {
            addCriterion("approved_date between", value1, value2, "approvedDate");
            return (Criteria) this;
        }

        public Criteria andApprovedDateNotBetween(Date value1, Date value2) {
            addCriterion("approved_date not between", value1, value2, "approvedDate");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andPriceTypeIsNull() {
            addCriterion("price_type is null");
            return (Criteria) this;
        }

        public Criteria andPriceTypeIsNotNull() {
            addCriterion("price_type is not null");
            return (Criteria) this;
        }

        public Criteria andPriceTypeEqualTo(String value) {
            addCriterion("price_type =", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeNotEqualTo(String value) {
            addCriterion("price_type <>", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeGreaterThan(String value) {
            addCriterion("price_type >", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeGreaterThanOrEqualTo(String value) {
            addCriterion("price_type >=", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeLessThan(String value) {
            addCriterion("price_type <", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeLessThanOrEqualTo(String value) {
            addCriterion("price_type <=", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeLike(String value) {
            addCriterion("price_type like", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeNotLike(String value) {
            addCriterion("price_type not like", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeIn(List<String> values) {
            addCriterion("price_type in", values, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeNotIn(List<String> values) {
            addCriterion("price_type not in", values, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeBetween(String value1, String value2) {
            addCriterion("price_type between", value1, value2, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeNotBetween(String value1, String value2) {
            addCriterion("price_type not between", value1, value2, "priceType");
            return (Criteria) this;
        }

        public Criteria andAttribute1IsNull() {
            addCriterion("attribute1 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute1IsNotNull() {
            addCriterion("attribute1 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute1EqualTo(String value) {
            addCriterion("attribute1 =", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotEqualTo(String value) {
            addCriterion("attribute1 <>", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1GreaterThan(String value) {
            addCriterion("attribute1 >", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1GreaterThanOrEqualTo(String value) {
            addCriterion("attribute1 >=", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1LessThan(String value) {
            addCriterion("attribute1 <", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1LessThanOrEqualTo(String value) {
            addCriterion("attribute1 <=", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1Like(String value) {
            addCriterion("attribute1 like", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotLike(String value) {
            addCriterion("attribute1 not like", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1In(List<String> values) {
            addCriterion("attribute1 in", values, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotIn(List<String> values) {
            addCriterion("attribute1 not in", values, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1Between(String value1, String value2) {
            addCriterion("attribute1 between", value1, value2, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotBetween(String value1, String value2) {
            addCriterion("attribute1 not between", value1, value2, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute2IsNull() {
            addCriterion("attribute2 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute2IsNotNull() {
            addCriterion("attribute2 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute2EqualTo(String value) {
            addCriterion("attribute2 =", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotEqualTo(String value) {
            addCriterion("attribute2 <>", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2GreaterThan(String value) {
            addCriterion("attribute2 >", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2GreaterThanOrEqualTo(String value) {
            addCriterion("attribute2 >=", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2LessThan(String value) {
            addCriterion("attribute2 <", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2LessThanOrEqualTo(String value) {
            addCriterion("attribute2 <=", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2Like(String value) {
            addCriterion("attribute2 like", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotLike(String value) {
            addCriterion("attribute2 not like", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2In(List<String> values) {
            addCriterion("attribute2 in", values, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotIn(List<String> values) {
            addCriterion("attribute2 not in", values, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2Between(String value1, String value2) {
            addCriterion("attribute2 between", value1, value2, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotBetween(String value1, String value2) {
            addCriterion("attribute2 not between", value1, value2, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute3IsNull() {
            addCriterion("attribute3 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute3IsNotNull() {
            addCriterion("attribute3 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute3EqualTo(String value) {
            addCriterion("attribute3 =", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotEqualTo(String value) {
            addCriterion("attribute3 <>", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3GreaterThan(String value) {
            addCriterion("attribute3 >", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3GreaterThanOrEqualTo(String value) {
            addCriterion("attribute3 >=", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3LessThan(String value) {
            addCriterion("attribute3 <", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3LessThanOrEqualTo(String value) {
            addCriterion("attribute3 <=", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3Like(String value) {
            addCriterion("attribute3 like", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotLike(String value) {
            addCriterion("attribute3 not like", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3In(List<String> values) {
            addCriterion("attribute3 in", values, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotIn(List<String> values) {
            addCriterion("attribute3 not in", values, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3Between(String value1, String value2) {
            addCriterion("attribute3 between", value1, value2, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotBetween(String value1, String value2) {
            addCriterion("attribute3 not between", value1, value2, "attribute3");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Integer value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Integer value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Integer value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Integer value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Integer value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Integer> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Integer> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Integer value1, Integer value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}