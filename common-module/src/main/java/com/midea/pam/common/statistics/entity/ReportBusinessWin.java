package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ReportBusinessWin extends LongIdEntity implements Serializable {
    private Long id;

    private String businessCode;

    private String ownerName;

    private Date checkAt;

    private String salesdepartmentname;

    private String industry;

    private String department;

    private String regionname;

    private String currencyCode;

    private BigDecimal quoteWithTax;

    private BigDecimal quoteWithoutTax;

    private BigDecimal quoteCostHardwareWithoutTax;

    private BigDecimal quoteCostLabourWithoutTax;

    private BigDecimal quoteCostTravelWithoutTax;

    private BigDecimal quoteCostOtherWithoutTax;

    private BigDecimal quoteCostTotal;

    private BigDecimal quoteProfitAmountWithoutTax;

    private BigDecimal quoteProfitPercentWithoutTax;

    private String customerPamCode;

    private String customerName;

    private String contractCode;

    private String contractName;

    private BigDecimal contractAmount;

    private BigDecimal contractExcludingTaxAmount;

    private Date contractStartTime;

    private BigDecimal contractTotalProjectIncome;

    private BigDecimal contractTotalProjectCost;

    private BigDecimal contractTotalProjectProfitAmount;

    private Long reportId;

    private Long executeId;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode == null ? null : businessCode.trim();
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName == null ? null : ownerName.trim();
    }

    public Date getCheckAt() {
        return checkAt;
    }

    public void setCheckAt(Date checkAt) {
        this.checkAt = checkAt;
    }

    public String getSalesdepartmentname() {
        return salesdepartmentname;
    }

    public void setSalesdepartmentname(String salesdepartmentname) {
        this.salesdepartmentname = salesdepartmentname == null ? null : salesdepartmentname.trim();
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry == null ? null : industry.trim();
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department == null ? null : department.trim();
    }

    public String getRegionname() {
        return regionname;
    }

    public void setRegionname(String regionname) {
        this.regionname = regionname == null ? null : regionname.trim();
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode == null ? null : currencyCode.trim();
    }

    public BigDecimal getQuoteWithTax() {
        return quoteWithTax;
    }

    public void setQuoteWithTax(BigDecimal quoteWithTax) {
        this.quoteWithTax = quoteWithTax;
    }

    public BigDecimal getQuoteWithoutTax() {
        return quoteWithoutTax;
    }

    public void setQuoteWithoutTax(BigDecimal quoteWithoutTax) {
        this.quoteWithoutTax = quoteWithoutTax;
    }

    public BigDecimal getQuoteCostHardwareWithoutTax() {
        return quoteCostHardwareWithoutTax;
    }

    public void setQuoteCostHardwareWithoutTax(BigDecimal quoteCostHardwareWithoutTax) {
        this.quoteCostHardwareWithoutTax = quoteCostHardwareWithoutTax;
    }

    public BigDecimal getQuoteCostLabourWithoutTax() {
        return quoteCostLabourWithoutTax;
    }

    public void setQuoteCostLabourWithoutTax(BigDecimal quoteCostLabourWithoutTax) {
        this.quoteCostLabourWithoutTax = quoteCostLabourWithoutTax;
    }

    public BigDecimal getQuoteCostTravelWithoutTax() {
        return quoteCostTravelWithoutTax;
    }

    public void setQuoteCostTravelWithoutTax(BigDecimal quoteCostTravelWithoutTax) {
        this.quoteCostTravelWithoutTax = quoteCostTravelWithoutTax;
    }

    public BigDecimal getQuoteCostOtherWithoutTax() {
        return quoteCostOtherWithoutTax;
    }

    public void setQuoteCostOtherWithoutTax(BigDecimal quoteCostOtherWithoutTax) {
        this.quoteCostOtherWithoutTax = quoteCostOtherWithoutTax;
    }

    public BigDecimal getQuoteCostTotal() {
        return quoteCostTotal;
    }

    public void setQuoteCostTotal(BigDecimal quoteCostTotal) {
        this.quoteCostTotal = quoteCostTotal;
    }

    public BigDecimal getQuoteProfitAmountWithoutTax() {
        return quoteProfitAmountWithoutTax;
    }

    public void setQuoteProfitAmountWithoutTax(BigDecimal quoteProfitAmountWithoutTax) {
        this.quoteProfitAmountWithoutTax = quoteProfitAmountWithoutTax;
    }

    public BigDecimal getQuoteProfitPercentWithoutTax() {
        return quoteProfitPercentWithoutTax;
    }

    public void setQuoteProfitPercentWithoutTax(BigDecimal quoteProfitPercentWithoutTax) {
        this.quoteProfitPercentWithoutTax = quoteProfitPercentWithoutTax;
    }

    public String getCustomerPamCode() {
        return customerPamCode;
    }

    public void setCustomerPamCode(String customerPamCode) {
        this.customerPamCode = customerPamCode == null ? null : customerPamCode.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode == null ? null : contractCode.trim();
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName == null ? null : contractName.trim();
    }

    public BigDecimal getContractAmount() {
        return contractAmount;
    }

    public void setContractAmount(BigDecimal contractAmount) {
        this.contractAmount = contractAmount;
    }

    public BigDecimal getContractExcludingTaxAmount() {
        return contractExcludingTaxAmount;
    }

    public void setContractExcludingTaxAmount(BigDecimal contractExcludingTaxAmount) {
        this.contractExcludingTaxAmount = contractExcludingTaxAmount;
    }

    public Date getContractStartTime() {
        return contractStartTime;
    }

    public void setContractStartTime(Date contractStartTime) {
        this.contractStartTime = contractStartTime;
    }

    public BigDecimal getContractTotalProjectIncome() {
        return contractTotalProjectIncome;
    }

    public void setContractTotalProjectIncome(BigDecimal contractTotalProjectIncome) {
        this.contractTotalProjectIncome = contractTotalProjectIncome;
    }

    public BigDecimal getContractTotalProjectCost() {
        return contractTotalProjectCost;
    }

    public void setContractTotalProjectCost(BigDecimal contractTotalProjectCost) {
        this.contractTotalProjectCost = contractTotalProjectCost;
    }

    public BigDecimal getContractTotalProjectProfitAmount() {
        return contractTotalProjectProfitAmount;
    }

    public void setContractTotalProjectProfitAmount(BigDecimal contractTotalProjectProfitAmount) {
        this.contractTotalProjectProfitAmount = contractTotalProjectProfitAmount;
    }

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", businessCode=").append(businessCode);
        sb.append(", ownerName=").append(ownerName);
        sb.append(", checkAt=").append(checkAt);
        sb.append(", salesdepartmentname=").append(salesdepartmentname);
        sb.append(", industry=").append(industry);
        sb.append(", department=").append(department);
        sb.append(", regionname=").append(regionname);
        sb.append(", currencyCode=").append(currencyCode);
        sb.append(", quoteWithTax=").append(quoteWithTax);
        sb.append(", quoteWithoutTax=").append(quoteWithoutTax);
        sb.append(", quoteCostHardwareWithoutTax=").append(quoteCostHardwareWithoutTax);
        sb.append(", quoteCostLabourWithoutTax=").append(quoteCostLabourWithoutTax);
        sb.append(", quoteCostTravelWithoutTax=").append(quoteCostTravelWithoutTax);
        sb.append(", quoteCostOtherWithoutTax=").append(quoteCostOtherWithoutTax);
        sb.append(", quoteCostTotal=").append(quoteCostTotal);
        sb.append(", quoteProfitAmountWithoutTax=").append(quoteProfitAmountWithoutTax);
        sb.append(", quoteProfitPercentWithoutTax=").append(quoteProfitPercentWithoutTax);
        sb.append(", customerPamCode=").append(customerPamCode);
        sb.append(", customerName=").append(customerName);
        sb.append(", contractCode=").append(contractCode);
        sb.append(", contractName=").append(contractName);
        sb.append(", contractAmount=").append(contractAmount);
        sb.append(", contractExcludingTaxAmount=").append(contractExcludingTaxAmount);
        sb.append(", contractStartTime=").append(contractStartTime);
        sb.append(", contractTotalProjectIncome=").append(contractTotalProjectIncome);
        sb.append(", contractTotalProjectCost=").append(contractTotalProjectCost);
        sb.append(", contractTotalProjectProfitAmount=").append(contractTotalProjectProfitAmount);
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}