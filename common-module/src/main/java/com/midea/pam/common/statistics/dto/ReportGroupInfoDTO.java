package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.ReportGroupInfo;
import com.midea.pam.common.statistics.entity.ReportInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/7
 * @description
 */
public class ReportGroupInfoDTO extends ReportGroupInfo {

    /**
     * 报表个数
     */
    private Integer reportNum;

    /**
     * 分配用户数
     */
    private Integer userNum;

    /**
     * 关联报表
     */
    private List<ReportInfo> reportInfos;

    /**
     * 控制权限，多个用,隔开
     */
    private String permissionStr;

    /**
     * 控制权限
     */
    //4A
    private String unitNameList;

    private List<String> permissionList;

    private Long reportId;

    private String createName;

    private String updateName;

    private Long userId;

    private Long reportGroupId;

    public Integer getReportNum() {
        return reportNum;
    }

    public void setReportNum(Integer reportNum) {
        this.reportNum = reportNum;
    }

    public Integer getUserNum() {
        return userNum;
    }

    public void setUserNum(Integer userNum) {
        this.userNum = userNum;
    }

    public List<ReportInfo> getReportInfos() {
        return reportInfos;
    }

    public void setReportInfos(List<ReportInfo> reportInfos) {
        this.reportInfos = reportInfos;
    }

    public String getPermissionStr() {
        return permissionStr;
    }

    public void setPermissionStr(String permissionStr) {
        this.permissionStr = permissionStr;
    }

    public List<String> getPermissionList() {
        return permissionList;
    }

    public void setPermissionList(List<String> permissionList) {
        this.permissionList = permissionList;
    }

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getReportGroupId() {
        return reportGroupId;
    }

    public void setReportGroupId(Long reportGroupId) {
        this.reportGroupId = reportGroupId;
    }

    public String getUnitNameList() {
        return unitNameList;
    }

    public void setUnitNameList(String unitNameList) {
        this.unitNameList = unitNameList;
    }
}
