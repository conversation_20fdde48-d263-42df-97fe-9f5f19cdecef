package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class ReportReceivableReceiptClaimExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportReceivableReceiptClaimExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNull() {
            addCriterion("project_manager is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNotNull() {
            addCriterion("project_manager is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerEqualTo(String value) {
            addCriterion("project_manager =", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotEqualTo(String value) {
            addCriterion("project_manager <>", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThan(String value) {
            addCriterion("project_manager >", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThanOrEqualTo(String value) {
            addCriterion("project_manager >=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThan(String value) {
            addCriterion("project_manager <", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThanOrEqualTo(String value) {
            addCriterion("project_manager <=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLike(String value) {
            addCriterion("project_manager like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotLike(String value) {
            addCriterion("project_manager not like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIn(List<String> values) {
            addCriterion("project_manager in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotIn(List<String> values) {
            addCriterion("project_manager not in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerBetween(String value1, String value2) {
            addCriterion("project_manager between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotBetween(String value1, String value2) {
            addCriterion("project_manager not between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerIsNull() {
            addCriterion("sales_manager is null");
            return (Criteria) this;
        }

        public Criteria andSalesManagerIsNotNull() {
            addCriterion("sales_manager is not null");
            return (Criteria) this;
        }

        public Criteria andSalesManagerEqualTo(String value) {
            addCriterion("sales_manager =", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNotEqualTo(String value) {
            addCriterion("sales_manager <>", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerGreaterThan(String value) {
            addCriterion("sales_manager >", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerGreaterThanOrEqualTo(String value) {
            addCriterion("sales_manager >=", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerLessThan(String value) {
            addCriterion("sales_manager <", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerLessThanOrEqualTo(String value) {
            addCriterion("sales_manager <=", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerLike(String value) {
            addCriterion("sales_manager like", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNotLike(String value) {
            addCriterion("sales_manager not like", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerIn(List<String> values) {
            addCriterion("sales_manager in", values, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNotIn(List<String> values) {
            addCriterion("sales_manager not in", values, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerBetween(String value1, String value2) {
            addCriterion("sales_manager between", value1, value2, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNotBetween(String value1, String value2) {
            addCriterion("sales_manager not between", value1, value2, "salesManager");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andParentCodeIsNull() {
            addCriterion("parent_code is null");
            return (Criteria) this;
        }

        public Criteria andParentCodeIsNotNull() {
            addCriterion("parent_code is not null");
            return (Criteria) this;
        }

        public Criteria andParentCodeEqualTo(String value) {
            addCriterion("parent_code =", value, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeNotEqualTo(String value) {
            addCriterion("parent_code <>", value, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeGreaterThan(String value) {
            addCriterion("parent_code >", value, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeGreaterThanOrEqualTo(String value) {
            addCriterion("parent_code >=", value, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeLessThan(String value) {
            addCriterion("parent_code <", value, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeLessThanOrEqualTo(String value) {
            addCriterion("parent_code <=", value, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeLike(String value) {
            addCriterion("parent_code like", value, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeNotLike(String value) {
            addCriterion("parent_code not like", value, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeIn(List<String> values) {
            addCriterion("parent_code in", values, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeNotIn(List<String> values) {
            addCriterion("parent_code not in", values, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeBetween(String value1, String value2) {
            addCriterion("parent_code between", value1, value2, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentCodeNotBetween(String value1, String value2) {
            addCriterion("parent_code not between", value1, value2, "parentCode");
            return (Criteria) this;
        }

        public Criteria andParentNameIsNull() {
            addCriterion("parent_name is null");
            return (Criteria) this;
        }

        public Criteria andParentNameIsNotNull() {
            addCriterion("parent_name is not null");
            return (Criteria) this;
        }

        public Criteria andParentNameEqualTo(String value) {
            addCriterion("parent_name =", value, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameNotEqualTo(String value) {
            addCriterion("parent_name <>", value, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameGreaterThan(String value) {
            addCriterion("parent_name >", value, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameGreaterThanOrEqualTo(String value) {
            addCriterion("parent_name >=", value, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameLessThan(String value) {
            addCriterion("parent_name <", value, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameLessThanOrEqualTo(String value) {
            addCriterion("parent_name <=", value, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameLike(String value) {
            addCriterion("parent_name like", value, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameNotLike(String value) {
            addCriterion("parent_name not like", value, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameIn(List<String> values) {
            addCriterion("parent_name in", values, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameNotIn(List<String> values) {
            addCriterion("parent_name not in", values, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameBetween(String value1, String value2) {
            addCriterion("parent_name between", value1, value2, "parentName");
            return (Criteria) this;
        }

        public Criteria andParentNameNotBetween(String value1, String value2) {
            addCriterion("parent_name not between", value1, value2, "parentName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNull() {
            addCriterion("unit_name is null");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNotNull() {
            addCriterion("unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andUnitNameEqualTo(String value) {
            addCriterion("unit_name =", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotEqualTo(String value) {
            addCriterion("unit_name <>", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThan(String value) {
            addCriterion("unit_name >", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("unit_name >=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThan(String value) {
            addCriterion("unit_name <", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThanOrEqualTo(String value) {
            addCriterion("unit_name <=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLike(String value) {
            addCriterion("unit_name like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotLike(String value) {
            addCriterion("unit_name not like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIn(List<String> values) {
            addCriterion("unit_name in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotIn(List<String> values) {
            addCriterion("unit_name not in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameBetween(String value1, String value2) {
            addCriterion("unit_name between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotBetween(String value1, String value2) {
            addCriterion("unit_name not between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNull() {
            addCriterion("project_type is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNotNull() {
            addCriterion("project_type is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeEqualTo(String value) {
            addCriterion("project_type =", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotEqualTo(String value) {
            addCriterion("project_type <>", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThan(String value) {
            addCriterion("project_type >", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThanOrEqualTo(String value) {
            addCriterion("project_type >=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThan(String value) {
            addCriterion("project_type <", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThanOrEqualTo(String value) {
            addCriterion("project_type <=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLike(String value) {
            addCriterion("project_type like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotLike(String value) {
            addCriterion("project_type not like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIn(List<String> values) {
            addCriterion("project_type in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotIn(List<String> values) {
            addCriterion("project_type not in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeBetween(String value1, String value2) {
            addCriterion("project_type between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotBetween(String value1, String value2) {
            addCriterion("project_type not between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNull() {
            addCriterion("customer_code is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNotNull() {
            addCriterion("customer_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeEqualTo(String value) {
            addCriterion("customer_code =", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotEqualTo(String value) {
            addCriterion("customer_code <>", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThan(String value) {
            addCriterion("customer_code >", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("customer_code >=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThan(String value) {
            addCriterion("customer_code <", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThanOrEqualTo(String value) {
            addCriterion("customer_code <=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLike(String value) {
            addCriterion("customer_code like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotLike(String value) {
            addCriterion("customer_code not like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIn(List<String> values) {
            addCriterion("customer_code in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotIn(List<String> values) {
            addCriterion("customer_code not in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBetween(String value1, String value2) {
            addCriterion("customer_code between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotBetween(String value1, String value2) {
            addCriterion("customer_code not between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeIsNull() {
            addCriterion("customer_type is null");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeIsNotNull() {
            addCriterion("customer_type is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeEqualTo(Integer value) {
            addCriterion("customer_type =", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeNotEqualTo(Integer value) {
            addCriterion("customer_type <>", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeGreaterThan(Integer value) {
            addCriterion("customer_type >", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_type >=", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeLessThan(Integer value) {
            addCriterion("customer_type <", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeLessThanOrEqualTo(Integer value) {
            addCriterion("customer_type <=", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeIn(List<Integer> values) {
            addCriterion("customer_type in", values, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeNotIn(List<Integer> values) {
            addCriterion("customer_type not in", values, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeBetween(Integer value1, Integer value2) {
            addCriterion("customer_type between", value1, value2, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_type not between", value1, value2, "customerType");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountIsNull() {
            addCriterion("invoice_amount is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountIsNotNull() {
            addCriterion("invoice_amount is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountEqualTo(BigDecimal value) {
            addCriterion("invoice_amount =", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountNotEqualTo(BigDecimal value) {
            addCriterion("invoice_amount <>", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountGreaterThan(BigDecimal value) {
            addCriterion("invoice_amount >", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("invoice_amount >=", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountLessThan(BigDecimal value) {
            addCriterion("invoice_amount <", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("invoice_amount <=", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountIn(List<BigDecimal> values) {
            addCriterion("invoice_amount in", values, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountNotIn(List<BigDecimal> values) {
            addCriterion("invoice_amount not in", values, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invoice_amount between", value1, value2, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invoice_amount not between", value1, value2, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailCodeIsNull() {
            addCriterion("receipt_plan_detail_code is null");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailCodeIsNotNull() {
            addCriterion("receipt_plan_detail_code is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailCodeEqualTo(String value) {
            addCriterion("receipt_plan_detail_code =", value, "receiptPlanDetailCode");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailCodeNotEqualTo(String value) {
            addCriterion("receipt_plan_detail_code <>", value, "receiptPlanDetailCode");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailCodeGreaterThan(String value) {
            addCriterion("receipt_plan_detail_code >", value, "receiptPlanDetailCode");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailCodeGreaterThanOrEqualTo(String value) {
            addCriterion("receipt_plan_detail_code >=", value, "receiptPlanDetailCode");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailCodeLessThan(String value) {
            addCriterion("receipt_plan_detail_code <", value, "receiptPlanDetailCode");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailCodeLessThanOrEqualTo(String value) {
            addCriterion("receipt_plan_detail_code <=", value, "receiptPlanDetailCode");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailCodeLike(String value) {
            addCriterion("receipt_plan_detail_code like", value, "receiptPlanDetailCode");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailCodeNotLike(String value) {
            addCriterion("receipt_plan_detail_code not like", value, "receiptPlanDetailCode");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailCodeIn(List<String> values) {
            addCriterion("receipt_plan_detail_code in", values, "receiptPlanDetailCode");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailCodeNotIn(List<String> values) {
            addCriterion("receipt_plan_detail_code not in", values, "receiptPlanDetailCode");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailCodeBetween(String value1, String value2) {
            addCriterion("receipt_plan_detail_code between", value1, value2, "receiptPlanDetailCode");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailCodeNotBetween(String value1, String value2) {
            addCriterion("receipt_plan_detail_code not between", value1, value2, "receiptPlanDetailCode");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailNumIsNull() {
            addCriterion("receipt_plan_detail_num is null");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailNumIsNotNull() {
            addCriterion("receipt_plan_detail_num is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailNumEqualTo(Integer value) {
            addCriterion("receipt_plan_detail_num =", value, "receiptPlanDetailNum");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailNumNotEqualTo(Integer value) {
            addCriterion("receipt_plan_detail_num <>", value, "receiptPlanDetailNum");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailNumGreaterThan(Integer value) {
            addCriterion("receipt_plan_detail_num >", value, "receiptPlanDetailNum");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("receipt_plan_detail_num >=", value, "receiptPlanDetailNum");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailNumLessThan(Integer value) {
            addCriterion("receipt_plan_detail_num <", value, "receiptPlanDetailNum");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailNumLessThanOrEqualTo(Integer value) {
            addCriterion("receipt_plan_detail_num <=", value, "receiptPlanDetailNum");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailNumIn(List<Integer> values) {
            addCriterion("receipt_plan_detail_num in", values, "receiptPlanDetailNum");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailNumNotIn(List<Integer> values) {
            addCriterion("receipt_plan_detail_num not in", values, "receiptPlanDetailNum");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailNumBetween(Integer value1, Integer value2) {
            addCriterion("receipt_plan_detail_num between", value1, value2, "receiptPlanDetailNum");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailNumNotBetween(Integer value1, Integer value2) {
            addCriterion("receipt_plan_detail_num not between", value1, value2, "receiptPlanDetailNum");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailDateIsNull() {
            addCriterion("receipt_plan_detail_date is null");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailDateIsNotNull() {
            addCriterion("receipt_plan_detail_date is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailDateEqualTo(Date value) {
            addCriterionForJDBCDate("receipt_plan_detail_date =", value, "receiptPlanDetailDate");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("receipt_plan_detail_date <>", value, "receiptPlanDetailDate");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailDateGreaterThan(Date value) {
            addCriterionForJDBCDate("receipt_plan_detail_date >", value, "receiptPlanDetailDate");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("receipt_plan_detail_date >=", value, "receiptPlanDetailDate");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailDateLessThan(Date value) {
            addCriterionForJDBCDate("receipt_plan_detail_date <", value, "receiptPlanDetailDate");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("receipt_plan_detail_date <=", value, "receiptPlanDetailDate");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailDateIn(List<Date> values) {
            addCriterionForJDBCDate("receipt_plan_detail_date in", values, "receiptPlanDetailDate");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("receipt_plan_detail_date not in", values, "receiptPlanDetailDate");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("receipt_plan_detail_date between", value1, value2, "receiptPlanDetailDate");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("receipt_plan_detail_date not between", value1, value2, "receiptPlanDetailDate");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailAmountIsNull() {
            addCriterion("receipt_plan_detail_amount is null");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailAmountIsNotNull() {
            addCriterion("receipt_plan_detail_amount is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailAmountEqualTo(BigDecimal value) {
            addCriterion("receipt_plan_detail_amount =", value, "receiptPlanDetailAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailAmountNotEqualTo(BigDecimal value) {
            addCriterion("receipt_plan_detail_amount <>", value, "receiptPlanDetailAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailAmountGreaterThan(BigDecimal value) {
            addCriterion("receipt_plan_detail_amount >", value, "receiptPlanDetailAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("receipt_plan_detail_amount >=", value, "receiptPlanDetailAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailAmountLessThan(BigDecimal value) {
            addCriterion("receipt_plan_detail_amount <", value, "receiptPlanDetailAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("receipt_plan_detail_amount <=", value, "receiptPlanDetailAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailAmountIn(List<BigDecimal> values) {
            addCriterion("receipt_plan_detail_amount in", values, "receiptPlanDetailAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailAmountNotIn(List<BigDecimal> values) {
            addCriterion("receipt_plan_detail_amount not in", values, "receiptPlanDetailAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("receipt_plan_detail_amount between", value1, value2, "receiptPlanDetailAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptPlanDetailAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("receipt_plan_detail_amount not between", value1, value2, "receiptPlanDetailAmount");
            return (Criteria) this;
        }

        public Criteria andRequirementIsNull() {
            addCriterion("requirement is null");
            return (Criteria) this;
        }

        public Criteria andRequirementIsNotNull() {
            addCriterion("requirement is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementEqualTo(String value) {
            addCriterion("requirement =", value, "requirement");
            return (Criteria) this;
        }

        public Criteria andRequirementNotEqualTo(String value) {
            addCriterion("requirement <>", value, "requirement");
            return (Criteria) this;
        }

        public Criteria andRequirementGreaterThan(String value) {
            addCriterion("requirement >", value, "requirement");
            return (Criteria) this;
        }

        public Criteria andRequirementGreaterThanOrEqualTo(String value) {
            addCriterion("requirement >=", value, "requirement");
            return (Criteria) this;
        }

        public Criteria andRequirementLessThan(String value) {
            addCriterion("requirement <", value, "requirement");
            return (Criteria) this;
        }

        public Criteria andRequirementLessThanOrEqualTo(String value) {
            addCriterion("requirement <=", value, "requirement");
            return (Criteria) this;
        }

        public Criteria andRequirementLike(String value) {
            addCriterion("requirement like", value, "requirement");
            return (Criteria) this;
        }

        public Criteria andRequirementNotLike(String value) {
            addCriterion("requirement not like", value, "requirement");
            return (Criteria) this;
        }

        public Criteria andRequirementIn(List<String> values) {
            addCriterion("requirement in", values, "requirement");
            return (Criteria) this;
        }

        public Criteria andRequirementNotIn(List<String> values) {
            addCriterion("requirement not in", values, "requirement");
            return (Criteria) this;
        }

        public Criteria andRequirementBetween(String value1, String value2) {
            addCriterion("requirement between", value1, value2, "requirement");
            return (Criteria) this;
        }

        public Criteria andRequirementNotBetween(String value1, String value2) {
            addCriterion("requirement not between", value1, value2, "requirement");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyDetailsCodeIsNull() {
            addCriterion("invoice_apply_details_code is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyDetailsCodeIsNotNull() {
            addCriterion("invoice_apply_details_code is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyDetailsCodeEqualTo(String value) {
            addCriterion("invoice_apply_details_code =", value, "invoiceApplyDetailsCode");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyDetailsCodeNotEqualTo(String value) {
            addCriterion("invoice_apply_details_code <>", value, "invoiceApplyDetailsCode");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyDetailsCodeGreaterThan(String value) {
            addCriterion("invoice_apply_details_code >", value, "invoiceApplyDetailsCode");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyDetailsCodeGreaterThanOrEqualTo(String value) {
            addCriterion("invoice_apply_details_code >=", value, "invoiceApplyDetailsCode");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyDetailsCodeLessThan(String value) {
            addCriterion("invoice_apply_details_code <", value, "invoiceApplyDetailsCode");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyDetailsCodeLessThanOrEqualTo(String value) {
            addCriterion("invoice_apply_details_code <=", value, "invoiceApplyDetailsCode");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyDetailsCodeLike(String value) {
            addCriterion("invoice_apply_details_code like", value, "invoiceApplyDetailsCode");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyDetailsCodeNotLike(String value) {
            addCriterion("invoice_apply_details_code not like", value, "invoiceApplyDetailsCode");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyDetailsCodeIn(List<String> values) {
            addCriterion("invoice_apply_details_code in", values, "invoiceApplyDetailsCode");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyDetailsCodeNotIn(List<String> values) {
            addCriterion("invoice_apply_details_code not in", values, "invoiceApplyDetailsCode");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyDetailsCodeBetween(String value1, String value2) {
            addCriterion("invoice_apply_details_code between", value1, value2, "invoiceApplyDetailsCode");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyDetailsCodeNotBetween(String value1, String value2) {
            addCriterion("invoice_apply_details_code not between", value1, value2, "invoiceApplyDetailsCode");
            return (Criteria) this;
        }

        public Criteria andMilestoneNameIsNull() {
            addCriterion("milestone_name is null");
            return (Criteria) this;
        }

        public Criteria andMilestoneNameIsNotNull() {
            addCriterion("milestone_name is not null");
            return (Criteria) this;
        }

        public Criteria andMilestoneNameEqualTo(String value) {
            addCriterion("milestone_name =", value, "milestoneName");
            return (Criteria) this;
        }

        public Criteria andMilestoneNameNotEqualTo(String value) {
            addCriterion("milestone_name <>", value, "milestoneName");
            return (Criteria) this;
        }

        public Criteria andMilestoneNameGreaterThan(String value) {
            addCriterion("milestone_name >", value, "milestoneName");
            return (Criteria) this;
        }

        public Criteria andMilestoneNameGreaterThanOrEqualTo(String value) {
            addCriterion("milestone_name >=", value, "milestoneName");
            return (Criteria) this;
        }

        public Criteria andMilestoneNameLessThan(String value) {
            addCriterion("milestone_name <", value, "milestoneName");
            return (Criteria) this;
        }

        public Criteria andMilestoneNameLessThanOrEqualTo(String value) {
            addCriterion("milestone_name <=", value, "milestoneName");
            return (Criteria) this;
        }

        public Criteria andMilestoneNameLike(String value) {
            addCriterion("milestone_name like", value, "milestoneName");
            return (Criteria) this;
        }

        public Criteria andMilestoneNameNotLike(String value) {
            addCriterion("milestone_name not like", value, "milestoneName");
            return (Criteria) this;
        }

        public Criteria andMilestoneNameIn(List<String> values) {
            addCriterion("milestone_name in", values, "milestoneName");
            return (Criteria) this;
        }

        public Criteria andMilestoneNameNotIn(List<String> values) {
            addCriterion("milestone_name not in", values, "milestoneName");
            return (Criteria) this;
        }

        public Criteria andMilestoneNameBetween(String value1, String value2) {
            addCriterion("milestone_name between", value1, value2, "milestoneName");
            return (Criteria) this;
        }

        public Criteria andMilestoneNameNotBetween(String value1, String value2) {
            addCriterion("milestone_name not between", value1, value2, "milestoneName");
            return (Criteria) this;
        }

        public Criteria andActualAmountIsNull() {
            addCriterion("actual_amount is null");
            return (Criteria) this;
        }

        public Criteria andActualAmountIsNotNull() {
            addCriterion("actual_amount is not null");
            return (Criteria) this;
        }

        public Criteria andActualAmountEqualTo(BigDecimal value) {
            addCriterion("actual_amount =", value, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountNotEqualTo(BigDecimal value) {
            addCriterion("actual_amount <>", value, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountGreaterThan(BigDecimal value) {
            addCriterion("actual_amount >", value, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_amount >=", value, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountLessThan(BigDecimal value) {
            addCriterion("actual_amount <", value, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_amount <=", value, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountIn(List<BigDecimal> values) {
            addCriterion("actual_amount in", values, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountNotIn(List<BigDecimal> values) {
            addCriterion("actual_amount not in", values, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_amount between", value1, value2, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andActualAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_amount not between", value1, value2, "actualAmount");
            return (Criteria) this;
        }

        public Criteria andExpireAmountIsNull() {
            addCriterion("expire_amount is null");
            return (Criteria) this;
        }

        public Criteria andExpireAmountIsNotNull() {
            addCriterion("expire_amount is not null");
            return (Criteria) this;
        }

        public Criteria andExpireAmountEqualTo(BigDecimal value) {
            addCriterion("expire_amount =", value, "expireAmount");
            return (Criteria) this;
        }

        public Criteria andExpireAmountNotEqualTo(BigDecimal value) {
            addCriterion("expire_amount <>", value, "expireAmount");
            return (Criteria) this;
        }

        public Criteria andExpireAmountGreaterThan(BigDecimal value) {
            addCriterion("expire_amount >", value, "expireAmount");
            return (Criteria) this;
        }

        public Criteria andExpireAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("expire_amount >=", value, "expireAmount");
            return (Criteria) this;
        }

        public Criteria andExpireAmountLessThan(BigDecimal value) {
            addCriterion("expire_amount <", value, "expireAmount");
            return (Criteria) this;
        }

        public Criteria andExpireAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("expire_amount <=", value, "expireAmount");
            return (Criteria) this;
        }

        public Criteria andExpireAmountIn(List<BigDecimal> values) {
            addCriterion("expire_amount in", values, "expireAmount");
            return (Criteria) this;
        }

        public Criteria andExpireAmountNotIn(List<BigDecimal> values) {
            addCriterion("expire_amount not in", values, "expireAmount");
            return (Criteria) this;
        }

        public Criteria andExpireAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("expire_amount between", value1, value2, "expireAmount");
            return (Criteria) this;
        }

        public Criteria andExpireAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("expire_amount not between", value1, value2, "expireAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationTypeIsNull() {
            addCriterion("receipt_configuration_type is null");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationTypeIsNotNull() {
            addCriterion("receipt_configuration_type is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationTypeEqualTo(String value) {
            addCriterion("receipt_configuration_type =", value, "receiptConfigurationType");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationTypeNotEqualTo(String value) {
            addCriterion("receipt_configuration_type <>", value, "receiptConfigurationType");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationTypeGreaterThan(String value) {
            addCriterion("receipt_configuration_type >", value, "receiptConfigurationType");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationTypeGreaterThanOrEqualTo(String value) {
            addCriterion("receipt_configuration_type >=", value, "receiptConfigurationType");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationTypeLessThan(String value) {
            addCriterion("receipt_configuration_type <", value, "receiptConfigurationType");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationTypeLessThanOrEqualTo(String value) {
            addCriterion("receipt_configuration_type <=", value, "receiptConfigurationType");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationTypeLike(String value) {
            addCriterion("receipt_configuration_type like", value, "receiptConfigurationType");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationTypeNotLike(String value) {
            addCriterion("receipt_configuration_type not like", value, "receiptConfigurationType");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationTypeIn(List<String> values) {
            addCriterion("receipt_configuration_type in", values, "receiptConfigurationType");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationTypeNotIn(List<String> values) {
            addCriterion("receipt_configuration_type not in", values, "receiptConfigurationType");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationTypeBetween(String value1, String value2) {
            addCriterion("receipt_configuration_type between", value1, value2, "receiptConfigurationType");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationTypeNotBetween(String value1, String value2) {
            addCriterion("receipt_configuration_type not between", value1, value2, "receiptConfigurationType");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationRuleIsNull() {
            addCriterion("receipt_configuration_rule is null");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationRuleIsNotNull() {
            addCriterion("receipt_configuration_rule is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationRuleEqualTo(String value) {
            addCriterion("receipt_configuration_rule =", value, "receiptConfigurationRule");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationRuleNotEqualTo(String value) {
            addCriterion("receipt_configuration_rule <>", value, "receiptConfigurationRule");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationRuleGreaterThan(String value) {
            addCriterion("receipt_configuration_rule >", value, "receiptConfigurationRule");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationRuleGreaterThanOrEqualTo(String value) {
            addCriterion("receipt_configuration_rule >=", value, "receiptConfigurationRule");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationRuleLessThan(String value) {
            addCriterion("receipt_configuration_rule <", value, "receiptConfigurationRule");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationRuleLessThanOrEqualTo(String value) {
            addCriterion("receipt_configuration_rule <=", value, "receiptConfigurationRule");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationRuleLike(String value) {
            addCriterion("receipt_configuration_rule like", value, "receiptConfigurationRule");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationRuleNotLike(String value) {
            addCriterion("receipt_configuration_rule not like", value, "receiptConfigurationRule");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationRuleIn(List<String> values) {
            addCriterion("receipt_configuration_rule in", values, "receiptConfigurationRule");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationRuleNotIn(List<String> values) {
            addCriterion("receipt_configuration_rule not in", values, "receiptConfigurationRule");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationRuleBetween(String value1, String value2) {
            addCriterion("receipt_configuration_rule between", value1, value2, "receiptConfigurationRule");
            return (Criteria) this;
        }

        public Criteria andReceiptConfigurationRuleNotBetween(String value1, String value2) {
            addCriterion("receipt_configuration_rule not between", value1, value2, "receiptConfigurationRule");
            return (Criteria) this;
        }

        public Criteria andReceiptDateStartIsNull() {
            addCriterion("receipt_date_start is null");
            return (Criteria) this;
        }

        public Criteria andReceiptDateStartIsNotNull() {
            addCriterion("receipt_date_start is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptDateStartEqualTo(Date value) {
            addCriterionForJDBCDate("receipt_date_start =", value, "receiptDateStart");
            return (Criteria) this;
        }

        public Criteria andReceiptDateStartNotEqualTo(Date value) {
            addCriterionForJDBCDate("receipt_date_start <>", value, "receiptDateStart");
            return (Criteria) this;
        }

        public Criteria andReceiptDateStartGreaterThan(Date value) {
            addCriterionForJDBCDate("receipt_date_start >", value, "receiptDateStart");
            return (Criteria) this;
        }

        public Criteria andReceiptDateStartGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("receipt_date_start >=", value, "receiptDateStart");
            return (Criteria) this;
        }

        public Criteria andReceiptDateStartLessThan(Date value) {
            addCriterionForJDBCDate("receipt_date_start <", value, "receiptDateStart");
            return (Criteria) this;
        }

        public Criteria andReceiptDateStartLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("receipt_date_start <=", value, "receiptDateStart");
            return (Criteria) this;
        }

        public Criteria andReceiptDateStartIn(List<Date> values) {
            addCriterionForJDBCDate("receipt_date_start in", values, "receiptDateStart");
            return (Criteria) this;
        }

        public Criteria andReceiptDateStartNotIn(List<Date> values) {
            addCriterionForJDBCDate("receipt_date_start not in", values, "receiptDateStart");
            return (Criteria) this;
        }

        public Criteria andReceiptDateStartBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("receipt_date_start between", value1, value2, "receiptDateStart");
            return (Criteria) this;
        }

        public Criteria andReceiptDateStartNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("receipt_date_start not between", value1, value2, "receiptDateStart");
            return (Criteria) this;
        }

        public Criteria andDaysIsNull() {
            addCriterion("days is null");
            return (Criteria) this;
        }

        public Criteria andDaysIsNotNull() {
            addCriterion("days is not null");
            return (Criteria) this;
        }

        public Criteria andDaysEqualTo(BigDecimal value) {
            addCriterion("days =", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysNotEqualTo(BigDecimal value) {
            addCriterion("days <>", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysGreaterThan(BigDecimal value) {
            addCriterion("days >", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("days >=", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysLessThan(BigDecimal value) {
            addCriterion("days <", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysLessThanOrEqualTo(BigDecimal value) {
            addCriterion("days <=", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysIn(List<BigDecimal> values) {
            addCriterion("days in", values, "days");
            return (Criteria) this;
        }

        public Criteria andDaysNotIn(List<BigDecimal> values) {
            addCriterion("days not in", values, "days");
            return (Criteria) this;
        }

        public Criteria andDaysBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("days between", value1, value2, "days");
            return (Criteria) this;
        }

        public Criteria andDaysNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("days not between", value1, value2, "days");
            return (Criteria) this;
        }

        public Criteria andReceiptDateEndIsNull() {
            addCriterion("receipt_date_end is null");
            return (Criteria) this;
        }

        public Criteria andReceiptDateEndIsNotNull() {
            addCriterion("receipt_date_end is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptDateEndEqualTo(Date value) {
            addCriterionForJDBCDate("receipt_date_end =", value, "receiptDateEnd");
            return (Criteria) this;
        }

        public Criteria andReceiptDateEndNotEqualTo(Date value) {
            addCriterionForJDBCDate("receipt_date_end <>", value, "receiptDateEnd");
            return (Criteria) this;
        }

        public Criteria andReceiptDateEndGreaterThan(Date value) {
            addCriterionForJDBCDate("receipt_date_end >", value, "receiptDateEnd");
            return (Criteria) this;
        }

        public Criteria andReceiptDateEndGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("receipt_date_end >=", value, "receiptDateEnd");
            return (Criteria) this;
        }

        public Criteria andReceiptDateEndLessThan(Date value) {
            addCriterionForJDBCDate("receipt_date_end <", value, "receiptDateEnd");
            return (Criteria) this;
        }

        public Criteria andReceiptDateEndLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("receipt_date_end <=", value, "receiptDateEnd");
            return (Criteria) this;
        }

        public Criteria andReceiptDateEndIn(List<Date> values) {
            addCriterionForJDBCDate("receipt_date_end in", values, "receiptDateEnd");
            return (Criteria) this;
        }

        public Criteria andReceiptDateEndNotIn(List<Date> values) {
            addCriterionForJDBCDate("receipt_date_end not in", values, "receiptDateEnd");
            return (Criteria) this;
        }

        public Criteria andReceiptDateEndBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("receipt_date_end between", value1, value2, "receiptDateEnd");
            return (Criteria) this;
        }

        public Criteria andReceiptDateEndNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("receipt_date_end not between", value1, value2, "receiptDateEnd");
            return (Criteria) this;
        }

        public Criteria andAgeAmount0IsNull() {
            addCriterion("age_amount_0 is null");
            return (Criteria) this;
        }

        public Criteria andAgeAmount0IsNotNull() {
            addCriterion("age_amount_0 is not null");
            return (Criteria) this;
        }

        public Criteria andAgeAmount0EqualTo(BigDecimal value) {
            addCriterion("age_amount_0 =", value, "ageAmount0");
            return (Criteria) this;
        }

        public Criteria andAgeAmount0NotEqualTo(BigDecimal value) {
            addCriterion("age_amount_0 <>", value, "ageAmount0");
            return (Criteria) this;
        }

        public Criteria andAgeAmount0GreaterThan(BigDecimal value) {
            addCriterion("age_amount_0 >", value, "ageAmount0");
            return (Criteria) this;
        }

        public Criteria andAgeAmount0GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("age_amount_0 >=", value, "ageAmount0");
            return (Criteria) this;
        }

        public Criteria andAgeAmount0LessThan(BigDecimal value) {
            addCriterion("age_amount_0 <", value, "ageAmount0");
            return (Criteria) this;
        }

        public Criteria andAgeAmount0LessThanOrEqualTo(BigDecimal value) {
            addCriterion("age_amount_0 <=", value, "ageAmount0");
            return (Criteria) this;
        }

        public Criteria andAgeAmount0In(List<BigDecimal> values) {
            addCriterion("age_amount_0 in", values, "ageAmount0");
            return (Criteria) this;
        }

        public Criteria andAgeAmount0NotIn(List<BigDecimal> values) {
            addCriterion("age_amount_0 not in", values, "ageAmount0");
            return (Criteria) this;
        }

        public Criteria andAgeAmount0Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("age_amount_0 between", value1, value2, "ageAmount0");
            return (Criteria) this;
        }

        public Criteria andAgeAmount0NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("age_amount_0 not between", value1, value2, "ageAmount0");
            return (Criteria) this;
        }

        public Criteria andAgeAmount1IsNull() {
            addCriterion("age_amount_1 is null");
            return (Criteria) this;
        }

        public Criteria andAgeAmount1IsNotNull() {
            addCriterion("age_amount_1 is not null");
            return (Criteria) this;
        }

        public Criteria andAgeAmount1EqualTo(BigDecimal value) {
            addCriterion("age_amount_1 =", value, "ageAmount1");
            return (Criteria) this;
        }

        public Criteria andAgeAmount1NotEqualTo(BigDecimal value) {
            addCriterion("age_amount_1 <>", value, "ageAmount1");
            return (Criteria) this;
        }

        public Criteria andAgeAmount1GreaterThan(BigDecimal value) {
            addCriterion("age_amount_1 >", value, "ageAmount1");
            return (Criteria) this;
        }

        public Criteria andAgeAmount1GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("age_amount_1 >=", value, "ageAmount1");
            return (Criteria) this;
        }

        public Criteria andAgeAmount1LessThan(BigDecimal value) {
            addCriterion("age_amount_1 <", value, "ageAmount1");
            return (Criteria) this;
        }

        public Criteria andAgeAmount1LessThanOrEqualTo(BigDecimal value) {
            addCriterion("age_amount_1 <=", value, "ageAmount1");
            return (Criteria) this;
        }

        public Criteria andAgeAmount1In(List<BigDecimal> values) {
            addCriterion("age_amount_1 in", values, "ageAmount1");
            return (Criteria) this;
        }

        public Criteria andAgeAmount1NotIn(List<BigDecimal> values) {
            addCriterion("age_amount_1 not in", values, "ageAmount1");
            return (Criteria) this;
        }

        public Criteria andAgeAmount1Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("age_amount_1 between", value1, value2, "ageAmount1");
            return (Criteria) this;
        }

        public Criteria andAgeAmount1NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("age_amount_1 not between", value1, value2, "ageAmount1");
            return (Criteria) this;
        }

        public Criteria andAgeAmount2IsNull() {
            addCriterion("age_amount_2 is null");
            return (Criteria) this;
        }

        public Criteria andAgeAmount2IsNotNull() {
            addCriterion("age_amount_2 is not null");
            return (Criteria) this;
        }

        public Criteria andAgeAmount2EqualTo(BigDecimal value) {
            addCriterion("age_amount_2 =", value, "ageAmount2");
            return (Criteria) this;
        }

        public Criteria andAgeAmount2NotEqualTo(BigDecimal value) {
            addCriterion("age_amount_2 <>", value, "ageAmount2");
            return (Criteria) this;
        }

        public Criteria andAgeAmount2GreaterThan(BigDecimal value) {
            addCriterion("age_amount_2 >", value, "ageAmount2");
            return (Criteria) this;
        }

        public Criteria andAgeAmount2GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("age_amount_2 >=", value, "ageAmount2");
            return (Criteria) this;
        }

        public Criteria andAgeAmount2LessThan(BigDecimal value) {
            addCriterion("age_amount_2 <", value, "ageAmount2");
            return (Criteria) this;
        }

        public Criteria andAgeAmount2LessThanOrEqualTo(BigDecimal value) {
            addCriterion("age_amount_2 <=", value, "ageAmount2");
            return (Criteria) this;
        }

        public Criteria andAgeAmount2In(List<BigDecimal> values) {
            addCriterion("age_amount_2 in", values, "ageAmount2");
            return (Criteria) this;
        }

        public Criteria andAgeAmount2NotIn(List<BigDecimal> values) {
            addCriterion("age_amount_2 not in", values, "ageAmount2");
            return (Criteria) this;
        }

        public Criteria andAgeAmount2Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("age_amount_2 between", value1, value2, "ageAmount2");
            return (Criteria) this;
        }

        public Criteria andAgeAmount2NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("age_amount_2 not between", value1, value2, "ageAmount2");
            return (Criteria) this;
        }

        public Criteria andAgeAmount3IsNull() {
            addCriterion("age_amount_3 is null");
            return (Criteria) this;
        }

        public Criteria andAgeAmount3IsNotNull() {
            addCriterion("age_amount_3 is not null");
            return (Criteria) this;
        }

        public Criteria andAgeAmount3EqualTo(BigDecimal value) {
            addCriterion("age_amount_3 =", value, "ageAmount3");
            return (Criteria) this;
        }

        public Criteria andAgeAmount3NotEqualTo(BigDecimal value) {
            addCriterion("age_amount_3 <>", value, "ageAmount3");
            return (Criteria) this;
        }

        public Criteria andAgeAmount3GreaterThan(BigDecimal value) {
            addCriterion("age_amount_3 >", value, "ageAmount3");
            return (Criteria) this;
        }

        public Criteria andAgeAmount3GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("age_amount_3 >=", value, "ageAmount3");
            return (Criteria) this;
        }

        public Criteria andAgeAmount3LessThan(BigDecimal value) {
            addCriterion("age_amount_3 <", value, "ageAmount3");
            return (Criteria) this;
        }

        public Criteria andAgeAmount3LessThanOrEqualTo(BigDecimal value) {
            addCriterion("age_amount_3 <=", value, "ageAmount3");
            return (Criteria) this;
        }

        public Criteria andAgeAmount3In(List<BigDecimal> values) {
            addCriterion("age_amount_3 in", values, "ageAmount3");
            return (Criteria) this;
        }

        public Criteria andAgeAmount3NotIn(List<BigDecimal> values) {
            addCriterion("age_amount_3 not in", values, "ageAmount3");
            return (Criteria) this;
        }

        public Criteria andAgeAmount3Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("age_amount_3 between", value1, value2, "ageAmount3");
            return (Criteria) this;
        }

        public Criteria andAgeAmount3NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("age_amount_3 not between", value1, value2, "ageAmount3");
            return (Criteria) this;
        }

        public Criteria andAgeAmount4IsNull() {
            addCriterion("age_amount_4 is null");
            return (Criteria) this;
        }

        public Criteria andAgeAmount4IsNotNull() {
            addCriterion("age_amount_4 is not null");
            return (Criteria) this;
        }

        public Criteria andAgeAmount4EqualTo(BigDecimal value) {
            addCriterion("age_amount_4 =", value, "ageAmount4");
            return (Criteria) this;
        }

        public Criteria andAgeAmount4NotEqualTo(BigDecimal value) {
            addCriterion("age_amount_4 <>", value, "ageAmount4");
            return (Criteria) this;
        }

        public Criteria andAgeAmount4GreaterThan(BigDecimal value) {
            addCriterion("age_amount_4 >", value, "ageAmount4");
            return (Criteria) this;
        }

        public Criteria andAgeAmount4GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("age_amount_4 >=", value, "ageAmount4");
            return (Criteria) this;
        }

        public Criteria andAgeAmount4LessThan(BigDecimal value) {
            addCriterion("age_amount_4 <", value, "ageAmount4");
            return (Criteria) this;
        }

        public Criteria andAgeAmount4LessThanOrEqualTo(BigDecimal value) {
            addCriterion("age_amount_4 <=", value, "ageAmount4");
            return (Criteria) this;
        }

        public Criteria andAgeAmount4In(List<BigDecimal> values) {
            addCriterion("age_amount_4 in", values, "ageAmount4");
            return (Criteria) this;
        }

        public Criteria andAgeAmount4NotIn(List<BigDecimal> values) {
            addCriterion("age_amount_4 not in", values, "ageAmount4");
            return (Criteria) this;
        }

        public Criteria andAgeAmount4Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("age_amount_4 between", value1, value2, "ageAmount4");
            return (Criteria) this;
        }

        public Criteria andAgeAmount4NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("age_amount_4 not between", value1, value2, "ageAmount4");
            return (Criteria) this;
        }

        public Criteria andAgeAmount5IsNull() {
            addCriterion("age_amount_5 is null");
            return (Criteria) this;
        }

        public Criteria andAgeAmount5IsNotNull() {
            addCriterion("age_amount_5 is not null");
            return (Criteria) this;
        }

        public Criteria andAgeAmount5EqualTo(BigDecimal value) {
            addCriterion("age_amount_5 =", value, "ageAmount5");
            return (Criteria) this;
        }

        public Criteria andAgeAmount5NotEqualTo(BigDecimal value) {
            addCriterion("age_amount_5 <>", value, "ageAmount5");
            return (Criteria) this;
        }

        public Criteria andAgeAmount5GreaterThan(BigDecimal value) {
            addCriterion("age_amount_5 >", value, "ageAmount5");
            return (Criteria) this;
        }

        public Criteria andAgeAmount5GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("age_amount_5 >=", value, "ageAmount5");
            return (Criteria) this;
        }

        public Criteria andAgeAmount5LessThan(BigDecimal value) {
            addCriterion("age_amount_5 <", value, "ageAmount5");
            return (Criteria) this;
        }

        public Criteria andAgeAmount5LessThanOrEqualTo(BigDecimal value) {
            addCriterion("age_amount_5 <=", value, "ageAmount5");
            return (Criteria) this;
        }

        public Criteria andAgeAmount5In(List<BigDecimal> values) {
            addCriterion("age_amount_5 in", values, "ageAmount5");
            return (Criteria) this;
        }

        public Criteria andAgeAmount5NotIn(List<BigDecimal> values) {
            addCriterion("age_amount_5 not in", values, "ageAmount5");
            return (Criteria) this;
        }

        public Criteria andAgeAmount5Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("age_amount_5 between", value1, value2, "ageAmount5");
            return (Criteria) this;
        }

        public Criteria andAgeAmount5NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("age_amount_5 not between", value1, value2, "ageAmount5");
            return (Criteria) this;
        }

        public Criteria andAgeAmount6IsNull() {
            addCriterion("age_amount_6 is null");
            return (Criteria) this;
        }

        public Criteria andAgeAmount6IsNotNull() {
            addCriterion("age_amount_6 is not null");
            return (Criteria) this;
        }

        public Criteria andAgeAmount6EqualTo(BigDecimal value) {
            addCriterion("age_amount_6 =", value, "ageAmount6");
            return (Criteria) this;
        }

        public Criteria andAgeAmount6NotEqualTo(BigDecimal value) {
            addCriterion("age_amount_6 <>", value, "ageAmount6");
            return (Criteria) this;
        }

        public Criteria andAgeAmount6GreaterThan(BigDecimal value) {
            addCriterion("age_amount_6 >", value, "ageAmount6");
            return (Criteria) this;
        }

        public Criteria andAgeAmount6GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("age_amount_6 >=", value, "ageAmount6");
            return (Criteria) this;
        }

        public Criteria andAgeAmount6LessThan(BigDecimal value) {
            addCriterion("age_amount_6 <", value, "ageAmount6");
            return (Criteria) this;
        }

        public Criteria andAgeAmount6LessThanOrEqualTo(BigDecimal value) {
            addCriterion("age_amount_6 <=", value, "ageAmount6");
            return (Criteria) this;
        }

        public Criteria andAgeAmount6In(List<BigDecimal> values) {
            addCriterion("age_amount_6 in", values, "ageAmount6");
            return (Criteria) this;
        }

        public Criteria andAgeAmount6NotIn(List<BigDecimal> values) {
            addCriterion("age_amount_6 not in", values, "ageAmount6");
            return (Criteria) this;
        }

        public Criteria andAgeAmount6Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("age_amount_6 between", value1, value2, "ageAmount6");
            return (Criteria) this;
        }

        public Criteria andAgeAmount6NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("age_amount_6 not between", value1, value2, "ageAmount6");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}