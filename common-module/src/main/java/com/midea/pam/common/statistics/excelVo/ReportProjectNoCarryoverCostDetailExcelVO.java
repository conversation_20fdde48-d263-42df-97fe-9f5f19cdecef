package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020-09-09 10:48
 * 项目未结转成本明细报表ExcelVO
 */
public class ReportProjectNoCarryoverCostDetailExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer number;

    @Excel(name = "项目号", width = 10)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "项目经理", width = 10)
    private String projectManager;

    @Excel(name = "项目状态", width = 15, replace = {"预立项审批驳回_-4", "预立项转正驳回_-3", "审批驳回_-2",
            "财务驳回_-1", "草稿_0", "审批中_3",
            "项目进行中_4", "项目变更中_9", "结项_10",
            "审批撤回_11", "预立项审批撤回_13", "作废_12",
            "预立项转正作废_-13", "预立项转正删除_23", "预立项转正审批中_7",
            "项目终止审批中_15", "项目终止_16", "项目流程删除_17","_null"})
    private Integer projectStatus;

    @Excel(name = "项目类型", width = 20)
    private String projectType;

    @Excel(name = "客户编号", width = 15)
    private String customerCode;

    @Excel(name = "客户名称", width = 30)
    private String customerName;

    @Excel(name = "业务分类", width = 20)
    private String unitName;

    @Excel(name = "子合同编号", width = 15)
    private String code;

    @Excel(name = "子合同名称", width = 30)
    private String name;

    private BigDecimal costNoCarryover;

    @Excel(name = "未结转金额", width = 20)
    private String costNoCarryover_dt;

    @Excel(name = "成本类型", width = 15)
    private String costType;

    @Excel(name = "关联单据号", width = 20)
    private String relationCode;

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager;
    }

    public Integer getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(Integer projectStatus) {
        this.projectStatus = projectStatus;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getCostNoCarryover() {
        return costNoCarryover;
    }

    public void setCostNoCarryover(BigDecimal costNoCarryover) {
        this.costNoCarryover = costNoCarryover;
    }

    public String getCostNoCarryover_dt() {
        if (Objects.nonNull(this.getCostNoCarryover())) {
            return this.costNoCarryover.stripTrailingZeros().toPlainString();
        } else {
            return this.costNoCarryover_dt;
        }
    }

    public void setCostNoCarryover_dt(String costNoCarryover_dt) {
        this.costNoCarryover_dt = costNoCarryover_dt;
    }

    public String getCostType() {
        return costType;
    }

    public void setCostType(String costType) {
        this.costType = costType;
    }

    public String getRelationCode() {
        return relationCode;
    }

    public void setRelationCode(String relationCode) {
        this.relationCode = relationCode;
    }

}