package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;

public class ReportProjectProfit extends LongIdEntity implements Serializable {
    private Long id;

    private Long unitId;

    private String unitName;

    private String divisionCode;

    private String divisionName;

    private Long projectId;

    private String projectCode;

    private String priceType;

    private String projectName;

    private String managerName;

    private Long managerId;

    private Byte status;

    private Byte previewFlag;

    private Long type;

    private String typeName;

    private BigDecimal amount;

    private BigDecimal quoteCostTotal;

    private Integer budgetChangeNum;

    private BigDecimal budgetCost;

    private BigDecimal incurredCost;

    private BigDecimal confirmedCostTotalAmount;

    private BigDecimal confirmedIncomeTotalAmount;

    private BigDecimal confirmedIncomeRatio;

    private BigDecimal confirmedGrossProfitAmount;

    private BigDecimal confirmedGrossProfitRatio;

    private String childContractCode;

    private String childContractName;

    private Byte childContractStatus;

    private BigDecimal childContractAmount;

    private BigDecimal childContractActualInvoiceAmountWithtax;

    private BigDecimal childContractRemainInvoiceAmountWithtax;

    private BigDecimal childContractActualInvoiceRatio;

    private BigDecimal childContractActualReceiptAmountWithtax;

    private BigDecimal childContractRemainReceiptAmountWithtax;

    private BigDecimal childContractActualReceiptRatio;

    private BigDecimal childContractInvoiceSubReceiptAmountWithtax;

    private String parentContractCode;

    private Byte parentContractStatus;

    private BigDecimal parentContractAmount;

    private BigDecimal parentContractActualReceiptAmountWithtax;

    private String customerName;

    private Boolean customerIsInner;

    private BigDecimal quoteCostHardwareWithoutTax;

    private BigDecimal quoteCostLabourWithoutTax;

    private BigDecimal quoteCostTravelWithoutTax;

    private BigDecimal quoteCostOtherWithoutTax;

    private BigDecimal projectBudgetHardwareWithoutTax;

    private BigDecimal projectBudgetLabourWithoutTax;

    private BigDecimal projectBudgetTravelWithoutTax;

    private BigDecimal projectBudgetOtherWithoutTax;

    private BigDecimal incurredCostHardware;

    private BigDecimal incurredCostLabour;

    private BigDecimal incurredCostTravel;

    private BigDecimal incurredCostOther;

    private BigDecimal confirmedCostHardware;

    private BigDecimal confirmedCostLabour;

    private BigDecimal confirmedCostTravel;

    private BigDecimal confirmedCostOther;

    private Byte isObjectiveProject;

    private Byte projectLevel;

    private String ouname;

    private Long reportId;

    private Long executeId;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode == null ? null : divisionCode.trim();
    }

    public String getDivisionName() {
        return divisionName;
    }

    public void setDivisionName(String divisionName) {
        this.divisionName = divisionName == null ? null : divisionName.trim();
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getPriceType() {
        return priceType;
    }

    public void setPriceType(String priceType) {
        this.priceType = priceType == null ? null : priceType.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName == null ? null : managerName.trim();
    }

    public Long getManagerId() {
        return managerId;
    }

    public void setManagerId(Long managerId) {
        this.managerId = managerId;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Byte getPreviewFlag() {
        return previewFlag;
    }

    public void setPreviewFlag(Byte previewFlag) {
        this.previewFlag = previewFlag;
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName == null ? null : typeName.trim();
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getQuoteCostTotal() {
        return quoteCostTotal;
    }

    public void setQuoteCostTotal(BigDecimal quoteCostTotal) {
        this.quoteCostTotal = quoteCostTotal;
    }

    public Integer getBudgetChangeNum() {
        return budgetChangeNum;
    }

    public void setBudgetChangeNum(Integer budgetChangeNum) {
        this.budgetChangeNum = budgetChangeNum;
    }

    public BigDecimal getBudgetCost() {
        return budgetCost;
    }

    public void setBudgetCost(BigDecimal budgetCost) {
        this.budgetCost = budgetCost;
    }

    public BigDecimal getIncurredCost() {
        return incurredCost;
    }

    public void setIncurredCost(BigDecimal incurredCost) {
        this.incurredCost = incurredCost;
    }

    public BigDecimal getConfirmedCostTotalAmount() {
        return confirmedCostTotalAmount;
    }

    public void setConfirmedCostTotalAmount(BigDecimal confirmedCostTotalAmount) {
        this.confirmedCostTotalAmount = confirmedCostTotalAmount;
    }

    public BigDecimal getConfirmedIncomeTotalAmount() {
        return confirmedIncomeTotalAmount;
    }

    public void setConfirmedIncomeTotalAmount(BigDecimal confirmedIncomeTotalAmount) {
        this.confirmedIncomeTotalAmount = confirmedIncomeTotalAmount;
    }

    public BigDecimal getConfirmedIncomeRatio() {
        return confirmedIncomeRatio;
    }

    public void setConfirmedIncomeRatio(BigDecimal confirmedIncomeRatio) {
        this.confirmedIncomeRatio = confirmedIncomeRatio;
    }

    public BigDecimal getConfirmedGrossProfitAmount() {
        return confirmedGrossProfitAmount;
    }

    public void setConfirmedGrossProfitAmount(BigDecimal confirmedGrossProfitAmount) {
        this.confirmedGrossProfitAmount = confirmedGrossProfitAmount;
    }

    public BigDecimal getConfirmedGrossProfitRatio() {
        return confirmedGrossProfitRatio;
    }

    public void setConfirmedGrossProfitRatio(BigDecimal confirmedGrossProfitRatio) {
        this.confirmedGrossProfitRatio = confirmedGrossProfitRatio;
    }

    public String getChildContractCode() {
        return childContractCode;
    }

    public void setChildContractCode(String childContractCode) {
        this.childContractCode = childContractCode == null ? null : childContractCode.trim();
    }

    public String getChildContractName() {
        return childContractName;
    }

    public void setChildContractName(String childContractName) {
        this.childContractName = childContractName == null ? null : childContractName.trim();
    }

    public Byte getChildContractStatus() {
        return childContractStatus;
    }

    public void setChildContractStatus(Byte childContractStatus) {
        this.childContractStatus = childContractStatus;
    }

    public BigDecimal getChildContractAmount() {
        return childContractAmount;
    }

    public void setChildContractAmount(BigDecimal childContractAmount) {
        this.childContractAmount = childContractAmount;
    }

    public BigDecimal getChildContractActualInvoiceAmountWithtax() {
        return childContractActualInvoiceAmountWithtax;
    }

    public void setChildContractActualInvoiceAmountWithtax(BigDecimal childContractActualInvoiceAmountWithtax) {
        this.childContractActualInvoiceAmountWithtax = childContractActualInvoiceAmountWithtax;
    }

    public BigDecimal getChildContractRemainInvoiceAmountWithtax() {
        return childContractRemainInvoiceAmountWithtax;
    }

    public void setChildContractRemainInvoiceAmountWithtax(BigDecimal childContractRemainInvoiceAmountWithtax) {
        this.childContractRemainInvoiceAmountWithtax = childContractRemainInvoiceAmountWithtax;
    }

    public BigDecimal getChildContractActualInvoiceRatio() {
        return childContractActualInvoiceRatio;
    }

    public void setChildContractActualInvoiceRatio(BigDecimal childContractActualInvoiceRatio) {
        this.childContractActualInvoiceRatio = childContractActualInvoiceRatio;
    }

    public BigDecimal getChildContractActualReceiptAmountWithtax() {
        return childContractActualReceiptAmountWithtax;
    }

    public void setChildContractActualReceiptAmountWithtax(BigDecimal childContractActualReceiptAmountWithtax) {
        this.childContractActualReceiptAmountWithtax = childContractActualReceiptAmountWithtax;
    }

    public BigDecimal getChildContractRemainReceiptAmountWithtax() {
        return childContractRemainReceiptAmountWithtax;
    }

    public void setChildContractRemainReceiptAmountWithtax(BigDecimal childContractRemainReceiptAmountWithtax) {
        this.childContractRemainReceiptAmountWithtax = childContractRemainReceiptAmountWithtax;
    }

    public BigDecimal getChildContractActualReceiptRatio() {
        return childContractActualReceiptRatio;
    }

    public void setChildContractActualReceiptRatio(BigDecimal childContractActualReceiptRatio) {
        this.childContractActualReceiptRatio = childContractActualReceiptRatio;
    }

    public BigDecimal getChildContractInvoiceSubReceiptAmountWithtax() {
        return childContractInvoiceSubReceiptAmountWithtax;
    }

    public void setChildContractInvoiceSubReceiptAmountWithtax(BigDecimal childContractInvoiceSubReceiptAmountWithtax) {
        this.childContractInvoiceSubReceiptAmountWithtax = childContractInvoiceSubReceiptAmountWithtax;
    }

    public String getParentContractCode() {
        return parentContractCode;
    }

    public void setParentContractCode(String parentContractCode) {
        this.parentContractCode = parentContractCode == null ? null : parentContractCode.trim();
    }

    public Byte getParentContractStatus() {
        return parentContractStatus;
    }

    public void setParentContractStatus(Byte parentContractStatus) {
        this.parentContractStatus = parentContractStatus;
    }

    public BigDecimal getParentContractAmount() {
        return parentContractAmount;
    }

    public void setParentContractAmount(BigDecimal parentContractAmount) {
        this.parentContractAmount = parentContractAmount;
    }

    public BigDecimal getParentContractActualReceiptAmountWithtax() {
        return parentContractActualReceiptAmountWithtax;
    }

    public void setParentContractActualReceiptAmountWithtax(BigDecimal parentContractActualReceiptAmountWithtax) {
        this.parentContractActualReceiptAmountWithtax = parentContractActualReceiptAmountWithtax;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public Boolean getCustomerIsInner() {
        return customerIsInner;
    }

    public void setCustomerIsInner(Boolean customerIsInner) {
        this.customerIsInner = customerIsInner;
    }

    public BigDecimal getQuoteCostHardwareWithoutTax() {
        return quoteCostHardwareWithoutTax;
    }

    public void setQuoteCostHardwareWithoutTax(BigDecimal quoteCostHardwareWithoutTax) {
        this.quoteCostHardwareWithoutTax = quoteCostHardwareWithoutTax;
    }

    public BigDecimal getQuoteCostLabourWithoutTax() {
        return quoteCostLabourWithoutTax;
    }

    public void setQuoteCostLabourWithoutTax(BigDecimal quoteCostLabourWithoutTax) {
        this.quoteCostLabourWithoutTax = quoteCostLabourWithoutTax;
    }

    public BigDecimal getQuoteCostTravelWithoutTax() {
        return quoteCostTravelWithoutTax;
    }

    public void setQuoteCostTravelWithoutTax(BigDecimal quoteCostTravelWithoutTax) {
        this.quoteCostTravelWithoutTax = quoteCostTravelWithoutTax;
    }

    public BigDecimal getQuoteCostOtherWithoutTax() {
        return quoteCostOtherWithoutTax;
    }

    public void setQuoteCostOtherWithoutTax(BigDecimal quoteCostOtherWithoutTax) {
        this.quoteCostOtherWithoutTax = quoteCostOtherWithoutTax;
    }

    public BigDecimal getProjectBudgetHardwareWithoutTax() {
        return projectBudgetHardwareWithoutTax;
    }

    public void setProjectBudgetHardwareWithoutTax(BigDecimal projectBudgetHardwareWithoutTax) {
        this.projectBudgetHardwareWithoutTax = projectBudgetHardwareWithoutTax;
    }

    public BigDecimal getProjectBudgetLabourWithoutTax() {
        return projectBudgetLabourWithoutTax;
    }

    public void setProjectBudgetLabourWithoutTax(BigDecimal projectBudgetLabourWithoutTax) {
        this.projectBudgetLabourWithoutTax = projectBudgetLabourWithoutTax;
    }

    public BigDecimal getProjectBudgetTravelWithoutTax() {
        return projectBudgetTravelWithoutTax;
    }

    public void setProjectBudgetTravelWithoutTax(BigDecimal projectBudgetTravelWithoutTax) {
        this.projectBudgetTravelWithoutTax = projectBudgetTravelWithoutTax;
    }

    public BigDecimal getProjectBudgetOtherWithoutTax() {
        return projectBudgetOtherWithoutTax;
    }

    public void setProjectBudgetOtherWithoutTax(BigDecimal projectBudgetOtherWithoutTax) {
        this.projectBudgetOtherWithoutTax = projectBudgetOtherWithoutTax;
    }

    public BigDecimal getIncurredCostHardware() {
        return incurredCostHardware;
    }

    public void setIncurredCostHardware(BigDecimal incurredCostHardware) {
        this.incurredCostHardware = incurredCostHardware;
    }

    public BigDecimal getIncurredCostLabour() {
        return incurredCostLabour;
    }

    public void setIncurredCostLabour(BigDecimal incurredCostLabour) {
        this.incurredCostLabour = incurredCostLabour;
    }

    public BigDecimal getIncurredCostTravel() {
        return incurredCostTravel;
    }

    public void setIncurredCostTravel(BigDecimal incurredCostTravel) {
        this.incurredCostTravel = incurredCostTravel;
    }

    public BigDecimal getIncurredCostOther() {
        return incurredCostOther;
    }

    public void setIncurredCostOther(BigDecimal incurredCostOther) {
        this.incurredCostOther = incurredCostOther;
    }

    public BigDecimal getConfirmedCostHardware() {
        return confirmedCostHardware;
    }

    public void setConfirmedCostHardware(BigDecimal confirmedCostHardware) {
        this.confirmedCostHardware = confirmedCostHardware;
    }

    public BigDecimal getConfirmedCostLabour() {
        return confirmedCostLabour;
    }

    public void setConfirmedCostLabour(BigDecimal confirmedCostLabour) {
        this.confirmedCostLabour = confirmedCostLabour;
    }

    public BigDecimal getConfirmedCostTravel() {
        return confirmedCostTravel;
    }

    public void setConfirmedCostTravel(BigDecimal confirmedCostTravel) {
        this.confirmedCostTravel = confirmedCostTravel;
    }

    public BigDecimal getConfirmedCostOther() {
        return confirmedCostOther;
    }

    public void setConfirmedCostOther(BigDecimal confirmedCostOther) {
        this.confirmedCostOther = confirmedCostOther;
    }

    public Byte getIsObjectiveProject() {
        return isObjectiveProject;
    }

    public void setIsObjectiveProject(Byte isObjectiveProject) {
        this.isObjectiveProject = isObjectiveProject;
    }

    public Byte getProjectLevel() {
        return projectLevel;
    }

    public void setProjectLevel(Byte projectLevel) {
        this.projectLevel = projectLevel;
    }

    public String getOuname() {
        return ouname;
    }

    public void setOuname(String ouname) {
        this.ouname = ouname == null ? null : ouname.trim();
    }

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", unitId=").append(unitId);
        sb.append(", unitName=").append(unitName);
        sb.append(", divisionCode=").append(divisionCode);
        sb.append(", divisionName=").append(divisionName);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", priceType=").append(priceType);
        sb.append(", projectName=").append(projectName);
        sb.append(", managerName=").append(managerName);
        sb.append(", managerId=").append(managerId);
        sb.append(", status=").append(status);
        sb.append(", previewFlag=").append(previewFlag);
        sb.append(", type=").append(type);
        sb.append(", typeName=").append(typeName);
        sb.append(", amount=").append(amount);
        sb.append(", quoteCostTotal=").append(quoteCostTotal);
        sb.append(", budgetChangeNum=").append(budgetChangeNum);
        sb.append(", budgetCost=").append(budgetCost);
        sb.append(", incurredCost=").append(incurredCost);
        sb.append(", confirmedCostTotalAmount=").append(confirmedCostTotalAmount);
        sb.append(", confirmedIncomeTotalAmount=").append(confirmedIncomeTotalAmount);
        sb.append(", confirmedIncomeRatio=").append(confirmedIncomeRatio);
        sb.append(", confirmedGrossProfitAmount=").append(confirmedGrossProfitAmount);
        sb.append(", confirmedGrossProfitRatio=").append(confirmedGrossProfitRatio);
        sb.append(", childContractCode=").append(childContractCode);
        sb.append(", childContractName=").append(childContractName);
        sb.append(", childContractStatus=").append(childContractStatus);
        sb.append(", childContractAmount=").append(childContractAmount);
        sb.append(", childContractActualInvoiceAmountWithtax=").append(childContractActualInvoiceAmountWithtax);
        sb.append(", childContractRemainInvoiceAmountWithtax=").append(childContractRemainInvoiceAmountWithtax);
        sb.append(", childContractActualInvoiceRatio=").append(childContractActualInvoiceRatio);
        sb.append(", childContractActualReceiptAmountWithtax=").append(childContractActualReceiptAmountWithtax);
        sb.append(", childContractRemainReceiptAmountWithtax=").append(childContractRemainReceiptAmountWithtax);
        sb.append(", childContractActualReceiptRatio=").append(childContractActualReceiptRatio);
        sb.append(", childContractInvoiceSubReceiptAmountWithtax=").append(childContractInvoiceSubReceiptAmountWithtax);
        sb.append(", parentContractCode=").append(parentContractCode);
        sb.append(", parentContractStatus=").append(parentContractStatus);
        sb.append(", parentContractAmount=").append(parentContractAmount);
        sb.append(", parentContractActualReceiptAmountWithtax=").append(parentContractActualReceiptAmountWithtax);
        sb.append(", customerName=").append(customerName);
        sb.append(", customerIsInner=").append(customerIsInner);
        sb.append(", quoteCostHardwareWithoutTax=").append(quoteCostHardwareWithoutTax);
        sb.append(", quoteCostLabourWithoutTax=").append(quoteCostLabourWithoutTax);
        sb.append(", quoteCostTravelWithoutTax=").append(quoteCostTravelWithoutTax);
        sb.append(", quoteCostOtherWithoutTax=").append(quoteCostOtherWithoutTax);
        sb.append(", projectBudgetHardwareWithoutTax=").append(projectBudgetHardwareWithoutTax);
        sb.append(", projectBudgetLabourWithoutTax=").append(projectBudgetLabourWithoutTax);
        sb.append(", projectBudgetTravelWithoutTax=").append(projectBudgetTravelWithoutTax);
        sb.append(", projectBudgetOtherWithoutTax=").append(projectBudgetOtherWithoutTax);
        sb.append(", incurredCostHardware=").append(incurredCostHardware);
        sb.append(", incurredCostLabour=").append(incurredCostLabour);
        sb.append(", incurredCostTravel=").append(incurredCostTravel);
        sb.append(", incurredCostOther=").append(incurredCostOther);
        sb.append(", confirmedCostHardware=").append(confirmedCostHardware);
        sb.append(", confirmedCostLabour=").append(confirmedCostLabour);
        sb.append(", confirmedCostTravel=").append(confirmedCostTravel);
        sb.append(", confirmedCostOther=").append(confirmedCostOther);
        sb.append(", isObjectiveProject=").append(isObjectiveProject);
        sb.append(", projectLevel=").append(projectLevel);
        sb.append(", ouname=").append(ouname);
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}