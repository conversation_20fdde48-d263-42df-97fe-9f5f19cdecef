package com.midea.pam.common.statistics.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel(value = "ProjectIncomingCostDto", description = "项目收入成本分析")
public class ProjectIncomingCostDto {

    @ApiModelProperty(value = "事业部或产品的名称")
    String unitName;

    @ApiModelProperty(value = " 内部已确认收入")
    private BigDecimal innerIncoming;

    @ApiModelProperty(value = "内部利润")
    private BigDecimal innerProfit;

    @ApiModelProperty(value = "内部收入达成率")
    private BigDecimal innerIncomingAchievementRate;

    @ApiModelProperty(value = "内部利润达成率")
    private BigDecimal innerProfitAchievementRate;

    @ApiModelProperty(value = " 外部已确认收入")
    private BigDecimal outerIncoming;

    @ApiModelProperty(value = "外部利润")
    private BigDecimal outerProfit;

    @ApiModelProperty(value = "外部收入达成率")
    private BigDecimal outerIncomingAchievementRate;

    @ApiModelProperty(value = "外部利润达成率")
    private BigDecimal outerProfitAchievementRate;

    @ApiModelProperty(value = " 全部已确认收入")
    private BigDecimal totalIncoming;

    @ApiModelProperty(value = "全部利润")
    private BigDecimal totalProfit;

    @ApiModelProperty(value = "全部收入达成率")
    private BigDecimal totalIncomingAchievementRate;

    @ApiModelProperty(value = "全部利润达成率")
    private BigDecimal totalProfitAchievementRate;

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getInnerIncoming() {
        return innerIncoming;
    }

    public void setInnerIncoming(BigDecimal innerIncoming) {
        this.innerIncoming = innerIncoming;
    }

    public BigDecimal getInnerProfit() {
        return innerProfit;
    }

    public void setInnerProfit(BigDecimal innerProfit) {
        this.innerProfit = innerProfit;
    }

    public BigDecimal getInnerIncomingAchievementRate() {
        return innerIncomingAchievementRate;
    }

    public void setInnerIncomingAchievementRate(BigDecimal innerIncomingAchievementRate) {
        this.innerIncomingAchievementRate = innerIncomingAchievementRate;
    }

    public BigDecimal getInnerProfitAchievementRate() {
        return innerProfitAchievementRate;
    }

    public void setInnerProfitAchievementRate(BigDecimal innerProfitAchievementRate) {
        this.innerProfitAchievementRate = innerProfitAchievementRate;
    }

    public BigDecimal getOuterIncoming() {
        return outerIncoming;
    }

    public void setOuterIncoming(BigDecimal outerIncoming) {
        this.outerIncoming = outerIncoming;
    }

    public BigDecimal getOuterProfit() {
        return outerProfit;
    }

    public void setOuterProfit(BigDecimal outerProfit) {
        this.outerProfit = outerProfit;
    }

    public BigDecimal getOuterIncomingAchievementRate() {
        return outerIncomingAchievementRate;
    }

    public void setOuterIncomingAchievementRate(BigDecimal outerIncomingAchievementRate) {
        this.outerIncomingAchievementRate = outerIncomingAchievementRate;
    }

    public BigDecimal getOuterProfitAchievementRate() {
        return outerProfitAchievementRate;
    }

    public void setOuterProfitAchievementRate(BigDecimal outerProfitAchievementRate) {
        this.outerProfitAchievementRate = outerProfitAchievementRate;
    }

    public BigDecimal getTotalIncoming() {
        return totalIncoming;
    }

    public void setTotalIncoming(BigDecimal totalIncoming) {
        this.totalIncoming = totalIncoming;
    }

    public BigDecimal getTotalProfit() {
        return totalProfit;
    }

    public void setTotalProfit(BigDecimal totalProfit) {
        this.totalProfit = totalProfit;
    }

    public BigDecimal getTotalIncomingAchievementRate() {
        return totalIncomingAchievementRate;
    }

    public void setTotalIncomingAchievementRate(BigDecimal totalIncomingAchievementRate) {
        this.totalIncomingAchievementRate = totalIncomingAchievementRate;
    }

    public BigDecimal getTotalProfitAchievementRate() {
        return totalProfitAchievementRate;
    }

    public void setTotalProfitAchievementRate(BigDecimal totalProfitAchievementRate) {
        this.totalProfitAchievementRate = totalProfitAchievementRate;
    }
}