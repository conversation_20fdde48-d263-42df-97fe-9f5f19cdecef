package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.Objects;

/**
 *
 **/
public class ProjectBudgetManagerExcelVO {
    @Excel(name = "序号",width = 10)
    private Integer num;

    @Excel(name = "项目编号",width = 20)
    private String projectCode;

    @Excel(name = "项目名称",width = 30)
    private String projectName;

    @Excel(name = "项目类型",width = 15)
    private String projectType;

    @Excel(name = "销售人员",width = 15)
    private String ownerName;

    @Excel(name = "项目经理",width = 30)
    private String projectManager;

    @Excel(name = "项目状态",width = 20,replace = {"审批驳回_-2", "财务驳回_-1", "草稿_0", "审批中_3", "项目进行中_4", "项目变更中_9", "结项_10",
            "预立项转正驳回_-3", "预立项审批驳回_-4", "审批撤回_11", "预立项审批撤回_13", "作废_12", "预立项转正审批中_7", "预立项转正审批撤回_14", "终止_16"})
    private String projectStatus;

    @Excel(name = "当前里程碑",width = 15)
    private String projectMilepostName;

    @Excel(name = "项目金额",width = 20)
    private BigDecimal amount;

    private BigDecimal materialQuotation;
    @Excel(name = "报价物料成本",width = 20)
    private String materialQuotationStr;

    private BigDecimal humanQuotation;
    @Excel(name = "报价人力成本",width = 20)
    private String humanQuotationStr;

    private BigDecimal travelQuotation;
    @Excel(name = "报价差旅成本",width = 20)
    private String travelQuotationStr;

    private BigDecimal noTravelQuotation;
    @Excel(name = "非差旅费用成本",width = 20)
    private String noTravelQuotationStr;

    private BigDecimal quotationAmount;
    @Excel(name = "报价总成本",width = 20)
    private String quotationAmountStr;

    private BigDecimal winRate;
    @Excel(name = "赢单毛利率",width = 20)
    private String winRateStr;

    @Excel(name = "物料预算",width = 20)
    private BigDecimal materialBudget;

    @Excel(name = "人力预算",width = 20)
    private BigDecimal humanBudget;

    @Excel(name = "差旅预算",width = 20)
    private BigDecimal travelBudget;

    @Excel(name = "非差旅预算",width = 20)
    private BigDecimal noTravelBudget;

    @Excel(name = "预算总成本",width = 20)
    private BigDecimal budgetAmount;

    @Excel(name = "预算毛利率",width = 20,suffix = "%")
    private BigDecimal budgetProfitRate;

    @Excel(name = "预算总成本与报价总成本差额",width = 20)
    private BigDecimal quotationBudget;

    @Excel(name = "已发生物料成本",width = 20)
    private BigDecimal materialCostActual;

    @Excel(name = "已发生人力成本",width = 20)
    private BigDecimal humanCostActual;

    @Excel(name = "已发生差旅成本",width = 20)
    private BigDecimal travelCostActual;

    @Excel(name = "已发生非差旅成本",width = 20)
    private BigDecimal noTravelCostActual;

    @Excel(name = "已发生总成本",width = 20)
    private BigDecimal projectCostAmount;

    private BigDecimal performProfitRate;
    @Excel(name = "执行毛利率",width = 20)
    private String performProfitRateStr;

    @Excel(name = "预算执行进度",width = 20,suffix = "%")
    private BigDecimal budgetExecutionSchedule;

    @Excel(name = "执行总成本与报价总成本差额",width = 20)
    private BigDecimal quotationActual;

    @Excel(name = "业务实体",width = 30)
    private String ouName;

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager;
    }

    public String getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(String projectStatus) {
        this.projectStatus = projectStatus;
    }

    public String getProjectMilepostName() {
        return projectMilepostName;
    }

    public void setProjectMilepostName(String projectMilepostName) {
        this.projectMilepostName = projectMilepostName;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getMaterialQuotation() {
        return materialQuotation;
    }

    public void setMaterialQuotation(BigDecimal materialQuotation) {
        this.materialQuotation = materialQuotation;
    }

    public BigDecimal getHumanQuotation() {
        return humanQuotation;
    }

    public void setHumanQuotation(BigDecimal humanQuotation) {
        this.humanQuotation = humanQuotation;
    }

    public BigDecimal getTravelQuotation() {
        return travelQuotation;
    }

    public void setTravelQuotation(BigDecimal travelQuotation) {
        this.travelQuotation = travelQuotation;
    }

    public BigDecimal getNoTravelQuotation() {
        return noTravelQuotation;
    }

    public void setNoTravelQuotation(BigDecimal noTravelQuotation) {
        this.noTravelQuotation = noTravelQuotation;
    }

    public BigDecimal getQuotationAmount() {
        return quotationAmount;
    }

    public void setQuotationAmount(BigDecimal quotationAmount) {
        this.quotationAmount = quotationAmount;
    }

    public BigDecimal getWinRate() {
        return winRate;
    }

    public void setWinRate(BigDecimal winRate) {
        this.winRate = winRate;
    }

    public BigDecimal getMaterialBudget() {
        return materialBudget;
    }

    public void setMaterialBudget(BigDecimal materialBudget) {
        this.materialBudget = materialBudget;
    }

    public BigDecimal getHumanBudget() {
        return humanBudget;
    }

    public void setHumanBudget(BigDecimal humanBudget) {
        this.humanBudget = humanBudget;
    }

    public BigDecimal getTravelBudget() {
        return travelBudget;
    }

    public void setTravelBudget(BigDecimal travelBudget) {
        this.travelBudget = travelBudget;
    }

    public BigDecimal getNoTravelBudget() {
        return noTravelBudget;
    }

    public void setNoTravelBudget(BigDecimal noTravelBudget) {
        this.noTravelBudget = noTravelBudget;
    }

    public BigDecimal getBudgetAmount() {
        return budgetAmount;
    }

    public void setBudgetAmount(BigDecimal budgetAmount) {
        this.budgetAmount = budgetAmount;
    }

    public BigDecimal getBudgetProfitRate() {
        return budgetProfitRate;
    }

    public void setBudgetProfitRate(BigDecimal budgetProfitRate) {
        this.budgetProfitRate = budgetProfitRate;
    }

    public BigDecimal getQuotationBudget() {
        return quotationBudget;
    }

    public void setQuotationBudget(BigDecimal quotationBudget) {
        this.quotationBudget = quotationBudget;
    }

    public BigDecimal getMaterialCostActual() {
        return materialCostActual;
    }

    public void setMaterialCostActual(BigDecimal materialCostActual) {
        this.materialCostActual = materialCostActual;
    }

    public BigDecimal getHumanCostActual() {
        return humanCostActual;
    }

    public void setHumanCostActual(BigDecimal humanCostActual) {
        this.humanCostActual = humanCostActual;
    }

    public BigDecimal getTravelCostActual() {
        return travelCostActual;
    }

    public void setTravelCostActual(BigDecimal travelCostActual) {
        this.travelCostActual = travelCostActual;
    }

    public BigDecimal getNoTravelCostActual() {
        return noTravelCostActual;
    }

    public void setNoTravelCostActual(BigDecimal noTravelCostActual) {
        this.noTravelCostActual = noTravelCostActual;
    }

    public BigDecimal getProjectCostAmount() {
        return projectCostAmount;
    }

    public void setProjectCostAmount(BigDecimal projectCostAmount) {
        this.projectCostAmount = projectCostAmount;
    }

    public BigDecimal getPerformProfitRate() {
        return performProfitRate;
    }

    public void setPerformProfitRate(BigDecimal performProfitRate) {
        this.performProfitRate = performProfitRate;
    }

    public BigDecimal getBudgetExecutionSchedule() {
        return budgetExecutionSchedule;
    }

    public void setBudgetExecutionSchedule(BigDecimal budgetExecutionSchedule) {
        this.budgetExecutionSchedule = budgetExecutionSchedule;
    }

    public BigDecimal getQuotationActual() {
        return quotationActual;
    }

    public void setQuotationActual(BigDecimal quotationActual) {
        this.quotationActual = quotationActual;
    }

    public String getMaterialQuotationStr() {
        if (Objects.nonNull(this.materialQuotation)) {
            return this.materialQuotation.toString();
        } else {
            return "-";
        }
    }

    public void setMaterialQuotationStr(String materialQuotationStr) {
        this.materialQuotationStr = materialQuotationStr;
    }

    public String getHumanQuotationStr() {
        if (Objects.nonNull(this.humanQuotation)) {
            return this.humanQuotation.toString();
        } else {
            return "-";
        }
    }

    public void setHumanQuotationStr(String humanQuotationStr) {
        this.humanQuotationStr = humanQuotationStr;
    }

    public String getTravelQuotationStr() {
        if (Objects.nonNull(this.travelQuotation)) {
            return this.travelQuotation.toString();
        } else {
            return "-";
        }
    }

    public void setTravelQuotationStr(String travelQuotationStr) {
        this.travelQuotationStr = travelQuotationStr;
    }

    public String getNoTravelQuotationStr() {
        if (Objects.nonNull(this.noTravelQuotation)) {
            return this.noTravelQuotation.toString();
        } else {
            return "-";
        }
    }

    public void setNoTravelQuotationStr(String noTravelQuotationStr) {
        this.noTravelQuotationStr = noTravelQuotationStr;
    }

    public String getQuotationAmountStr() {
        if (Objects.nonNull(this.quotationAmount)) {
            return this.quotationAmount.toString();
        } else {
            return "-";
        }
    }

    public void setQuotationAmountStr(String quotationAmountStr) {
        this.quotationAmountStr = quotationAmountStr;
    }

    public String getWinRateStr() {
        if (Objects.nonNull(this.winRate)) {
            return this.winRate.toString()+"%";
        } else {
            return "-";
        }
    }

    public void setWinRateStr(String winRateStr) {
        this.winRateStr = winRateStr;
    }

    public String getPerformProfitRateStr() {
        if (Objects.nonNull(this.performProfitRate)) {
            return this.performProfitRate.toString()+"%";
        } else {
            return "-";
        }
    }

    public void setPerformProfitRateStr(String performProfitRateStr) {
        this.performProfitRateStr = performProfitRateStr;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName;
    }
}
