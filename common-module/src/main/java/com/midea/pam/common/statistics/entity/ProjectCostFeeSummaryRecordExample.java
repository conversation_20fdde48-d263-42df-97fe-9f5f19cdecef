package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectCostFeeSummaryRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectCostFeeSummaryRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andCostIsNull() {
            addCriterion("cost is null");
            return (Criteria) this;
        }

        public Criteria andCostIsNotNull() {
            addCriterion("cost is not null");
            return (Criteria) this;
        }

        public Criteria andCostEqualTo(BigDecimal value) {
            addCriterion("cost =", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostNotEqualTo(BigDecimal value) {
            addCriterion("cost <>", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostGreaterThan(BigDecimal value) {
            addCriterion("cost >", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cost >=", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostLessThan(BigDecimal value) {
            addCriterion("cost <", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cost <=", value, "cost");
            return (Criteria) this;
        }

        public Criteria andCostIn(List<BigDecimal> values) {
            addCriterion("cost in", values, "cost");
            return (Criteria) this;
        }

        public Criteria andCostNotIn(List<BigDecimal> values) {
            addCriterion("cost not in", values, "cost");
            return (Criteria) this;
        }

        public Criteria andCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost between", value1, value2, "cost");
            return (Criteria) this;
        }

        public Criteria andCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost not between", value1, value2, "cost");
            return (Criteria) this;
        }

        public Criteria andBudgetIsNull() {
            addCriterion("budget is null");
            return (Criteria) this;
        }

        public Criteria andBudgetIsNotNull() {
            addCriterion("budget is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetEqualTo(BigDecimal value) {
            addCriterion("budget =", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetNotEqualTo(BigDecimal value) {
            addCriterion("budget <>", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetGreaterThan(BigDecimal value) {
            addCriterion("budget >", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget >=", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetLessThan(BigDecimal value) {
            addCriterion("budget <", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget <=", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetIn(List<BigDecimal> values) {
            addCriterion("budget in", values, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetNotIn(List<BigDecimal> values) {
            addCriterion("budget not in", values, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget between", value1, value2, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget not between", value1, value2, "budget");
            return (Criteria) this;
        }

        public Criteria andIncurredCostIsNull() {
            addCriterion("incurred_cost is null");
            return (Criteria) this;
        }

        public Criteria andIncurredCostIsNotNull() {
            addCriterion("incurred_cost is not null");
            return (Criteria) this;
        }

        public Criteria andIncurredCostEqualTo(BigDecimal value) {
            addCriterion("incurred_cost =", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostNotEqualTo(BigDecimal value) {
            addCriterion("incurred_cost <>", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostGreaterThan(BigDecimal value) {
            addCriterion("incurred_cost >", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_cost >=", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLessThan(BigDecimal value) {
            addCriterion("incurred_cost <", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_cost <=", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostIn(List<BigDecimal> values) {
            addCriterion("incurred_cost in", values, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostNotIn(List<BigDecimal> values) {
            addCriterion("incurred_cost not in", values, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_cost between", value1, value2, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_cost not between", value1, value2, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetIsNull() {
            addCriterion("remainder_budget is null");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetIsNotNull() {
            addCriterion("remainder_budget is not null");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetEqualTo(BigDecimal value) {
            addCriterion("remainder_budget =", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetNotEqualTo(BigDecimal value) {
            addCriterion("remainder_budget <>", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetGreaterThan(BigDecimal value) {
            addCriterion("remainder_budget >", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("remainder_budget >=", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetLessThan(BigDecimal value) {
            addCriterion("remainder_budget <", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("remainder_budget <=", value, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetIn(List<BigDecimal> values) {
            addCriterion("remainder_budget in", values, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetNotIn(List<BigDecimal> values) {
            addCriterion("remainder_budget not in", values, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("remainder_budget between", value1, value2, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andRemainderBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("remainder_budget not between", value1, value2, "remainderBudget");
            return (Criteria) this;
        }

        public Criteria andOtherCurrentTargetCostIsNull() {
            addCriterion("other_current_target_cost is null");
            return (Criteria) this;
        }

        public Criteria andOtherCurrentTargetCostIsNotNull() {
            addCriterion("other_current_target_cost is not null");
            return (Criteria) this;
        }

        public Criteria andOtherCurrentTargetCostEqualTo(BigDecimal value) {
            addCriterion("other_current_target_cost =", value, "otherCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andOtherCurrentTargetCostNotEqualTo(BigDecimal value) {
            addCriterion("other_current_target_cost <>", value, "otherCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andOtherCurrentTargetCostGreaterThan(BigDecimal value) {
            addCriterion("other_current_target_cost >", value, "otherCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andOtherCurrentTargetCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("other_current_target_cost >=", value, "otherCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andOtherCurrentTargetCostLessThan(BigDecimal value) {
            addCriterion("other_current_target_cost <", value, "otherCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andOtherCurrentTargetCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("other_current_target_cost <=", value, "otherCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andOtherCurrentTargetCostIn(List<BigDecimal> values) {
            addCriterion("other_current_target_cost in", values, "otherCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andOtherCurrentTargetCostNotIn(List<BigDecimal> values) {
            addCriterion("other_current_target_cost not in", values, "otherCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andOtherCurrentTargetCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_current_target_cost between", value1, value2, "otherCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andOtherCurrentTargetCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_current_target_cost not between", value1, value2, "otherCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andTravelCurrentTargetCostIsNull() {
            addCriterion("travel_current_target_cost is null");
            return (Criteria) this;
        }

        public Criteria andTravelCurrentTargetCostIsNotNull() {
            addCriterion("travel_current_target_cost is not null");
            return (Criteria) this;
        }

        public Criteria andTravelCurrentTargetCostEqualTo(BigDecimal value) {
            addCriterion("travel_current_target_cost =", value, "travelCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andTravelCurrentTargetCostNotEqualTo(BigDecimal value) {
            addCriterion("travel_current_target_cost <>", value, "travelCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andTravelCurrentTargetCostGreaterThan(BigDecimal value) {
            addCriterion("travel_current_target_cost >", value, "travelCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andTravelCurrentTargetCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_current_target_cost >=", value, "travelCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andTravelCurrentTargetCostLessThan(BigDecimal value) {
            addCriterion("travel_current_target_cost <", value, "travelCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andTravelCurrentTargetCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_current_target_cost <=", value, "travelCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andTravelCurrentTargetCostIn(List<BigDecimal> values) {
            addCriterion("travel_current_target_cost in", values, "travelCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andTravelCurrentTargetCostNotIn(List<BigDecimal> values) {
            addCriterion("travel_current_target_cost not in", values, "travelCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andTravelCurrentTargetCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_current_target_cost between", value1, value2, "travelCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andTravelCurrentTargetCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_current_target_cost not between", value1, value2, "travelCurrentTargetCost");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioIsNull() {
            addCriterion("incurred_ratio is null");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioIsNotNull() {
            addCriterion("incurred_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioEqualTo(BigDecimal value) {
            addCriterion("incurred_ratio =", value, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioNotEqualTo(BigDecimal value) {
            addCriterion("incurred_ratio <>", value, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioGreaterThan(BigDecimal value) {
            addCriterion("incurred_ratio >", value, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_ratio >=", value, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioLessThan(BigDecimal value) {
            addCriterion("incurred_ratio <", value, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_ratio <=", value, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioIn(List<BigDecimal> values) {
            addCriterion("incurred_ratio in", values, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioNotIn(List<BigDecimal> values) {
            addCriterion("incurred_ratio not in", values, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_ratio between", value1, value2, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andIncurredRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_ratio not between", value1, value2, "incurredRatio");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}