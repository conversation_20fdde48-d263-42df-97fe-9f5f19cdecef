package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/02
 */
@Getter
@Setter
@ApiModel(value = "InvoiceApplyReceivableAgesQuery", description = "财务口径应收报表")
public class InvoiceApplyReceivableAgesQuery {

    private Long id;

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    @ApiModelProperty(value = "报表ID")
    private Long reportId;

    @ApiModelProperty(value = "销售部门id列表")
    private List<Long> unitIdList;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "客户名称或CRM编码")
    private String customerName;

    @ApiModelProperty(value = "业务实体id列表")
    private List<Long> ouIdList;

    @ApiModelProperty(value = "业务模式id列表")
    private List<Long> projectTypeList;

    @ApiModelProperty(value = "开票申请是否启用账期：0否 1是")
    private String termEnable;

    @ApiModelProperty(value = "应收发票余额为零时是否显示：true/false")
    private Boolean displayZeroOrNot;

    @ApiModelProperty(value = "账龄规则id")
    private Long agingSegmentHeaderId;

    @ApiModelProperty(value = "规则范围拼接")
    private String ruleScope;

    @ApiModelProperty(value = "规则名称拼接")
    private String ruleName;

    private Date expireDate;

    private Long personal;

    private Long companyId;

    private Long createBy;

}
