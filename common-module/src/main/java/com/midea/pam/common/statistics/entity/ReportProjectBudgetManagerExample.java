package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ReportProjectBudgetManagerExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportProjectBudgetManagerExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNull() {
            addCriterion("project_type is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNotNull() {
            addCriterion("project_type is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeEqualTo(String value) {
            addCriterion("project_type =", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotEqualTo(String value) {
            addCriterion("project_type <>", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThan(String value) {
            addCriterion("project_type >", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThanOrEqualTo(String value) {
            addCriterion("project_type >=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThan(String value) {
            addCriterion("project_type <", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThanOrEqualTo(String value) {
            addCriterion("project_type <=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLike(String value) {
            addCriterion("project_type like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotLike(String value) {
            addCriterion("project_type not like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIn(List<String> values) {
            addCriterion("project_type in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotIn(List<String> values) {
            addCriterion("project_type not in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeBetween(String value1, String value2) {
            addCriterion("project_type between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotBetween(String value1, String value2) {
            addCriterion("project_type not between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdIsNull() {
            addCriterion("owner_user_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdIsNotNull() {
            addCriterion("owner_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdEqualTo(Long value) {
            addCriterion("owner_user_id =", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdNotEqualTo(Long value) {
            addCriterion("owner_user_id <>", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdGreaterThan(Long value) {
            addCriterion("owner_user_id >", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("owner_user_id >=", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdLessThan(Long value) {
            addCriterion("owner_user_id <", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdLessThanOrEqualTo(Long value) {
            addCriterion("owner_user_id <=", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdIn(List<Long> values) {
            addCriterion("owner_user_id in", values, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdNotIn(List<Long> values) {
            addCriterion("owner_user_id not in", values, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdBetween(Long value1, Long value2) {
            addCriterion("owner_user_id between", value1, value2, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdNotBetween(Long value1, Long value2) {
            addCriterion("owner_user_id not between", value1, value2, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerNameIsNull() {
            addCriterion("owner_name is null");
            return (Criteria) this;
        }

        public Criteria andOwnerNameIsNotNull() {
            addCriterion("owner_name is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerNameEqualTo(String value) {
            addCriterion("owner_name =", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotEqualTo(String value) {
            addCriterion("owner_name <>", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameGreaterThan(String value) {
            addCriterion("owner_name >", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameGreaterThanOrEqualTo(String value) {
            addCriterion("owner_name >=", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameLessThan(String value) {
            addCriterion("owner_name <", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameLessThanOrEqualTo(String value) {
            addCriterion("owner_name <=", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameLike(String value) {
            addCriterion("owner_name like", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotLike(String value) {
            addCriterion("owner_name not like", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameIn(List<String> values) {
            addCriterion("owner_name in", values, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotIn(List<String> values) {
            addCriterion("owner_name not in", values, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameBetween(String value1, String value2) {
            addCriterion("owner_name between", value1, value2, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotBetween(String value1, String value2) {
            addCriterion("owner_name not between", value1, value2, "ownerName");
            return (Criteria) this;
        }

        public Criteria andManagerIdIsNull() {
            addCriterion("manager_id is null");
            return (Criteria) this;
        }

        public Criteria andManagerIdIsNotNull() {
            addCriterion("manager_id is not null");
            return (Criteria) this;
        }

        public Criteria andManagerIdEqualTo(Long value) {
            addCriterion("manager_id =", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdNotEqualTo(Long value) {
            addCriterion("manager_id <>", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdGreaterThan(Long value) {
            addCriterion("manager_id >", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("manager_id >=", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdLessThan(Long value) {
            addCriterion("manager_id <", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdLessThanOrEqualTo(Long value) {
            addCriterion("manager_id <=", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdIn(List<Long> values) {
            addCriterion("manager_id in", values, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdNotIn(List<Long> values) {
            addCriterion("manager_id not in", values, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdBetween(Long value1, Long value2) {
            addCriterion("manager_id between", value1, value2, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdNotBetween(Long value1, Long value2) {
            addCriterion("manager_id not between", value1, value2, "managerId");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNull() {
            addCriterion("project_manager is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNotNull() {
            addCriterion("project_manager is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerEqualTo(String value) {
            addCriterion("project_manager =", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotEqualTo(String value) {
            addCriterion("project_manager <>", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThan(String value) {
            addCriterion("project_manager >", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThanOrEqualTo(String value) {
            addCriterion("project_manager >=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThan(String value) {
            addCriterion("project_manager <", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThanOrEqualTo(String value) {
            addCriterion("project_manager <=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLike(String value) {
            addCriterion("project_manager like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotLike(String value) {
            addCriterion("project_manager not like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIn(List<String> values) {
            addCriterion("project_manager in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotIn(List<String> values) {
            addCriterion("project_manager not in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerBetween(String value1, String value2) {
            addCriterion("project_manager between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotBetween(String value1, String value2) {
            addCriterion("project_manager not between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNull() {
            addCriterion("project_status is null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNotNull() {
            addCriterion("project_status is not null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusEqualTo(Integer value) {
            addCriterion("project_status =", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotEqualTo(Integer value) {
            addCriterion("project_status <>", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThan(Integer value) {
            addCriterion("project_status >", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("project_status >=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThan(Integer value) {
            addCriterion("project_status <", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThanOrEqualTo(Integer value) {
            addCriterion("project_status <=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIn(List<Integer> values) {
            addCriterion("project_status in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotIn(List<Integer> values) {
            addCriterion("project_status not in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusBetween(Integer value1, Integer value2) {
            addCriterion("project_status between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("project_status not between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameIsNull() {
            addCriterion("project_milepost_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameIsNotNull() {
            addCriterion("project_milepost_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameEqualTo(String value) {
            addCriterion("project_milepost_name =", value, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameNotEqualTo(String value) {
            addCriterion("project_milepost_name <>", value, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameGreaterThan(String value) {
            addCriterion("project_milepost_name >", value, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_milepost_name >=", value, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameLessThan(String value) {
            addCriterion("project_milepost_name <", value, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameLessThanOrEqualTo(String value) {
            addCriterion("project_milepost_name <=", value, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameLike(String value) {
            addCriterion("project_milepost_name like", value, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameNotLike(String value) {
            addCriterion("project_milepost_name not like", value, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameIn(List<String> values) {
            addCriterion("project_milepost_name in", values, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameNotIn(List<String> values) {
            addCriterion("project_milepost_name not in", values, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameBetween(String value1, String value2) {
            addCriterion("project_milepost_name between", value1, value2, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andProjectMilepostNameNotBetween(String value1, String value2) {
            addCriterion("project_milepost_name not between", value1, value2, "projectMilepostName");
            return (Criteria) this;
        }

        public Criteria andAmountIsNull() {
            addCriterion("amount is null");
            return (Criteria) this;
        }

        public Criteria andAmountIsNotNull() {
            addCriterion("amount is not null");
            return (Criteria) this;
        }

        public Criteria andAmountEqualTo(BigDecimal value) {
            addCriterion("amount =", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotEqualTo(BigDecimal value) {
            addCriterion("amount <>", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThan(BigDecimal value) {
            addCriterion("amount >", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("amount >=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThan(BigDecimal value) {
            addCriterion("amount <", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("amount <=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountIn(List<BigDecimal> values) {
            addCriterion("amount in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotIn(List<BigDecimal> values) {
            addCriterion("amount not in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount not between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotationIsNull() {
            addCriterion("material_quotation is null");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotationIsNotNull() {
            addCriterion("material_quotation is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotationEqualTo(BigDecimal value) {
            addCriterion("material_quotation =", value, "materialQuotation");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotationNotEqualTo(BigDecimal value) {
            addCriterion("material_quotation <>", value, "materialQuotation");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotationGreaterThan(BigDecimal value) {
            addCriterion("material_quotation >", value, "materialQuotation");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotationGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_quotation >=", value, "materialQuotation");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotationLessThan(BigDecimal value) {
            addCriterion("material_quotation <", value, "materialQuotation");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotationLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_quotation <=", value, "materialQuotation");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotationIn(List<BigDecimal> values) {
            addCriterion("material_quotation in", values, "materialQuotation");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotationNotIn(List<BigDecimal> values) {
            addCriterion("material_quotation not in", values, "materialQuotation");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotationBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_quotation between", value1, value2, "materialQuotation");
            return (Criteria) this;
        }

        public Criteria andMaterialQuotationNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_quotation not between", value1, value2, "materialQuotation");
            return (Criteria) this;
        }

        public Criteria andHumanQuotationIsNull() {
            addCriterion("human_quotation is null");
            return (Criteria) this;
        }

        public Criteria andHumanQuotationIsNotNull() {
            addCriterion("human_quotation is not null");
            return (Criteria) this;
        }

        public Criteria andHumanQuotationEqualTo(BigDecimal value) {
            addCriterion("human_quotation =", value, "humanQuotation");
            return (Criteria) this;
        }

        public Criteria andHumanQuotationNotEqualTo(BigDecimal value) {
            addCriterion("human_quotation <>", value, "humanQuotation");
            return (Criteria) this;
        }

        public Criteria andHumanQuotationGreaterThan(BigDecimal value) {
            addCriterion("human_quotation >", value, "humanQuotation");
            return (Criteria) this;
        }

        public Criteria andHumanQuotationGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("human_quotation >=", value, "humanQuotation");
            return (Criteria) this;
        }

        public Criteria andHumanQuotationLessThan(BigDecimal value) {
            addCriterion("human_quotation <", value, "humanQuotation");
            return (Criteria) this;
        }

        public Criteria andHumanQuotationLessThanOrEqualTo(BigDecimal value) {
            addCriterion("human_quotation <=", value, "humanQuotation");
            return (Criteria) this;
        }

        public Criteria andHumanQuotationIn(List<BigDecimal> values) {
            addCriterion("human_quotation in", values, "humanQuotation");
            return (Criteria) this;
        }

        public Criteria andHumanQuotationNotIn(List<BigDecimal> values) {
            addCriterion("human_quotation not in", values, "humanQuotation");
            return (Criteria) this;
        }

        public Criteria andHumanQuotationBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("human_quotation between", value1, value2, "humanQuotation");
            return (Criteria) this;
        }

        public Criteria andHumanQuotationNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("human_quotation not between", value1, value2, "humanQuotation");
            return (Criteria) this;
        }

        public Criteria andTravelQuotationIsNull() {
            addCriterion("travel_quotation is null");
            return (Criteria) this;
        }

        public Criteria andTravelQuotationIsNotNull() {
            addCriterion("travel_quotation is not null");
            return (Criteria) this;
        }

        public Criteria andTravelQuotationEqualTo(BigDecimal value) {
            addCriterion("travel_quotation =", value, "travelQuotation");
            return (Criteria) this;
        }

        public Criteria andTravelQuotationNotEqualTo(BigDecimal value) {
            addCriterion("travel_quotation <>", value, "travelQuotation");
            return (Criteria) this;
        }

        public Criteria andTravelQuotationGreaterThan(BigDecimal value) {
            addCriterion("travel_quotation >", value, "travelQuotation");
            return (Criteria) this;
        }

        public Criteria andTravelQuotationGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_quotation >=", value, "travelQuotation");
            return (Criteria) this;
        }

        public Criteria andTravelQuotationLessThan(BigDecimal value) {
            addCriterion("travel_quotation <", value, "travelQuotation");
            return (Criteria) this;
        }

        public Criteria andTravelQuotationLessThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_quotation <=", value, "travelQuotation");
            return (Criteria) this;
        }

        public Criteria andTravelQuotationIn(List<BigDecimal> values) {
            addCriterion("travel_quotation in", values, "travelQuotation");
            return (Criteria) this;
        }

        public Criteria andTravelQuotationNotIn(List<BigDecimal> values) {
            addCriterion("travel_quotation not in", values, "travelQuotation");
            return (Criteria) this;
        }

        public Criteria andTravelQuotationBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_quotation between", value1, value2, "travelQuotation");
            return (Criteria) this;
        }

        public Criteria andTravelQuotationNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_quotation not between", value1, value2, "travelQuotation");
            return (Criteria) this;
        }

        public Criteria andNoTravelQuotationIsNull() {
            addCriterion("no_travel_quotation is null");
            return (Criteria) this;
        }

        public Criteria andNoTravelQuotationIsNotNull() {
            addCriterion("no_travel_quotation is not null");
            return (Criteria) this;
        }

        public Criteria andNoTravelQuotationEqualTo(BigDecimal value) {
            addCriterion("no_travel_quotation =", value, "noTravelQuotation");
            return (Criteria) this;
        }

        public Criteria andNoTravelQuotationNotEqualTo(BigDecimal value) {
            addCriterion("no_travel_quotation <>", value, "noTravelQuotation");
            return (Criteria) this;
        }

        public Criteria andNoTravelQuotationGreaterThan(BigDecimal value) {
            addCriterion("no_travel_quotation >", value, "noTravelQuotation");
            return (Criteria) this;
        }

        public Criteria andNoTravelQuotationGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("no_travel_quotation >=", value, "noTravelQuotation");
            return (Criteria) this;
        }

        public Criteria andNoTravelQuotationLessThan(BigDecimal value) {
            addCriterion("no_travel_quotation <", value, "noTravelQuotation");
            return (Criteria) this;
        }

        public Criteria andNoTravelQuotationLessThanOrEqualTo(BigDecimal value) {
            addCriterion("no_travel_quotation <=", value, "noTravelQuotation");
            return (Criteria) this;
        }

        public Criteria andNoTravelQuotationIn(List<BigDecimal> values) {
            addCriterion("no_travel_quotation in", values, "noTravelQuotation");
            return (Criteria) this;
        }

        public Criteria andNoTravelQuotationNotIn(List<BigDecimal> values) {
            addCriterion("no_travel_quotation not in", values, "noTravelQuotation");
            return (Criteria) this;
        }

        public Criteria andNoTravelQuotationBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_travel_quotation between", value1, value2, "noTravelQuotation");
            return (Criteria) this;
        }

        public Criteria andNoTravelQuotationNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_travel_quotation not between", value1, value2, "noTravelQuotation");
            return (Criteria) this;
        }

        public Criteria andQuotationAmountIsNull() {
            addCriterion("quotation_amount is null");
            return (Criteria) this;
        }

        public Criteria andQuotationAmountIsNotNull() {
            addCriterion("quotation_amount is not null");
            return (Criteria) this;
        }

        public Criteria andQuotationAmountEqualTo(BigDecimal value) {
            addCriterion("quotation_amount =", value, "quotationAmount");
            return (Criteria) this;
        }

        public Criteria andQuotationAmountNotEqualTo(BigDecimal value) {
            addCriterion("quotation_amount <>", value, "quotationAmount");
            return (Criteria) this;
        }

        public Criteria andQuotationAmountGreaterThan(BigDecimal value) {
            addCriterion("quotation_amount >", value, "quotationAmount");
            return (Criteria) this;
        }

        public Criteria andQuotationAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quotation_amount >=", value, "quotationAmount");
            return (Criteria) this;
        }

        public Criteria andQuotationAmountLessThan(BigDecimal value) {
            addCriterion("quotation_amount <", value, "quotationAmount");
            return (Criteria) this;
        }

        public Criteria andQuotationAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quotation_amount <=", value, "quotationAmount");
            return (Criteria) this;
        }

        public Criteria andQuotationAmountIn(List<BigDecimal> values) {
            addCriterion("quotation_amount in", values, "quotationAmount");
            return (Criteria) this;
        }

        public Criteria andQuotationAmountNotIn(List<BigDecimal> values) {
            addCriterion("quotation_amount not in", values, "quotationAmount");
            return (Criteria) this;
        }

        public Criteria andQuotationAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quotation_amount between", value1, value2, "quotationAmount");
            return (Criteria) this;
        }

        public Criteria andQuotationAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quotation_amount not between", value1, value2, "quotationAmount");
            return (Criteria) this;
        }

        public Criteria andWinRateIsNull() {
            addCriterion("win_rate is null");
            return (Criteria) this;
        }

        public Criteria andWinRateIsNotNull() {
            addCriterion("win_rate is not null");
            return (Criteria) this;
        }

        public Criteria andWinRateEqualTo(BigDecimal value) {
            addCriterion("win_rate =", value, "winRate");
            return (Criteria) this;
        }

        public Criteria andWinRateNotEqualTo(BigDecimal value) {
            addCriterion("win_rate <>", value, "winRate");
            return (Criteria) this;
        }

        public Criteria andWinRateGreaterThan(BigDecimal value) {
            addCriterion("win_rate >", value, "winRate");
            return (Criteria) this;
        }

        public Criteria andWinRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("win_rate >=", value, "winRate");
            return (Criteria) this;
        }

        public Criteria andWinRateLessThan(BigDecimal value) {
            addCriterion("win_rate <", value, "winRate");
            return (Criteria) this;
        }

        public Criteria andWinRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("win_rate <=", value, "winRate");
            return (Criteria) this;
        }

        public Criteria andWinRateIn(List<BigDecimal> values) {
            addCriterion("win_rate in", values, "winRate");
            return (Criteria) this;
        }

        public Criteria andWinRateNotIn(List<BigDecimal> values) {
            addCriterion("win_rate not in", values, "winRate");
            return (Criteria) this;
        }

        public Criteria andWinRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("win_rate between", value1, value2, "winRate");
            return (Criteria) this;
        }

        public Criteria andWinRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("win_rate not between", value1, value2, "winRate");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetIsNull() {
            addCriterion("material_budget is null");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetIsNotNull() {
            addCriterion("material_budget is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetEqualTo(BigDecimal value) {
            addCriterion("material_budget =", value, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetNotEqualTo(BigDecimal value) {
            addCriterion("material_budget <>", value, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetGreaterThan(BigDecimal value) {
            addCriterion("material_budget >", value, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_budget >=", value, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetLessThan(BigDecimal value) {
            addCriterion("material_budget <", value, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_budget <=", value, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetIn(List<BigDecimal> values) {
            addCriterion("material_budget in", values, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetNotIn(List<BigDecimal> values) {
            addCriterion("material_budget not in", values, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_budget between", value1, value2, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_budget not between", value1, value2, "materialBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetIsNull() {
            addCriterion("human_budget is null");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetIsNotNull() {
            addCriterion("human_budget is not null");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetEqualTo(BigDecimal value) {
            addCriterion("human_budget =", value, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetNotEqualTo(BigDecimal value) {
            addCriterion("human_budget <>", value, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetGreaterThan(BigDecimal value) {
            addCriterion("human_budget >", value, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("human_budget >=", value, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetLessThan(BigDecimal value) {
            addCriterion("human_budget <", value, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("human_budget <=", value, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetIn(List<BigDecimal> values) {
            addCriterion("human_budget in", values, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetNotIn(List<BigDecimal> values) {
            addCriterion("human_budget not in", values, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("human_budget between", value1, value2, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andHumanBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("human_budget not between", value1, value2, "humanBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetIsNull() {
            addCriterion("travel_budget is null");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetIsNotNull() {
            addCriterion("travel_budget is not null");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetEqualTo(BigDecimal value) {
            addCriterion("travel_budget =", value, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetNotEqualTo(BigDecimal value) {
            addCriterion("travel_budget <>", value, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetGreaterThan(BigDecimal value) {
            addCriterion("travel_budget >", value, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_budget >=", value, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetLessThan(BigDecimal value) {
            addCriterion("travel_budget <", value, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_budget <=", value, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetIn(List<BigDecimal> values) {
            addCriterion("travel_budget in", values, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetNotIn(List<BigDecimal> values) {
            addCriterion("travel_budget not in", values, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_budget between", value1, value2, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andTravelBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_budget not between", value1, value2, "travelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetIsNull() {
            addCriterion("no_travel_budget is null");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetIsNotNull() {
            addCriterion("no_travel_budget is not null");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetEqualTo(BigDecimal value) {
            addCriterion("no_travel_budget =", value, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetNotEqualTo(BigDecimal value) {
            addCriterion("no_travel_budget <>", value, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetGreaterThan(BigDecimal value) {
            addCriterion("no_travel_budget >", value, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("no_travel_budget >=", value, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetLessThan(BigDecimal value) {
            addCriterion("no_travel_budget <", value, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("no_travel_budget <=", value, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetIn(List<BigDecimal> values) {
            addCriterion("no_travel_budget in", values, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetNotIn(List<BigDecimal> values) {
            addCriterion("no_travel_budget not in", values, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_travel_budget between", value1, value2, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andNoTravelBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_travel_budget not between", value1, value2, "noTravelBudget");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountIsNull() {
            addCriterion("budget_amount is null");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountIsNotNull() {
            addCriterion("budget_amount is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountEqualTo(BigDecimal value) {
            addCriterion("budget_amount =", value, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountNotEqualTo(BigDecimal value) {
            addCriterion("budget_amount <>", value, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountGreaterThan(BigDecimal value) {
            addCriterion("budget_amount >", value, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_amount >=", value, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountLessThan(BigDecimal value) {
            addCriterion("budget_amount <", value, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_amount <=", value, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountIn(List<BigDecimal> values) {
            addCriterion("budget_amount in", values, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountNotIn(List<BigDecimal> values) {
            addCriterion("budget_amount not in", values, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_amount between", value1, value2, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_amount not between", value1, value2, "budgetAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateIsNull() {
            addCriterion("budget_profit_rate is null");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateIsNotNull() {
            addCriterion("budget_profit_rate is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateEqualTo(BigDecimal value) {
            addCriterion("budget_profit_rate =", value, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateNotEqualTo(BigDecimal value) {
            addCriterion("budget_profit_rate <>", value, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateGreaterThan(BigDecimal value) {
            addCriterion("budget_profit_rate >", value, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_profit_rate >=", value, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateLessThan(BigDecimal value) {
            addCriterion("budget_profit_rate <", value, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_profit_rate <=", value, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateIn(List<BigDecimal> values) {
            addCriterion("budget_profit_rate in", values, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateNotIn(List<BigDecimal> values) {
            addCriterion("budget_profit_rate not in", values, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_profit_rate between", value1, value2, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_profit_rate not between", value1, value2, "budgetProfitRate");
            return (Criteria) this;
        }

        public Criteria andQuotationBudgetIsNull() {
            addCriterion("quotation_budget is null");
            return (Criteria) this;
        }

        public Criteria andQuotationBudgetIsNotNull() {
            addCriterion("quotation_budget is not null");
            return (Criteria) this;
        }

        public Criteria andQuotationBudgetEqualTo(BigDecimal value) {
            addCriterion("quotation_budget =", value, "quotationBudget");
            return (Criteria) this;
        }

        public Criteria andQuotationBudgetNotEqualTo(BigDecimal value) {
            addCriterion("quotation_budget <>", value, "quotationBudget");
            return (Criteria) this;
        }

        public Criteria andQuotationBudgetGreaterThan(BigDecimal value) {
            addCriterion("quotation_budget >", value, "quotationBudget");
            return (Criteria) this;
        }

        public Criteria andQuotationBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quotation_budget >=", value, "quotationBudget");
            return (Criteria) this;
        }

        public Criteria andQuotationBudgetLessThan(BigDecimal value) {
            addCriterion("quotation_budget <", value, "quotationBudget");
            return (Criteria) this;
        }

        public Criteria andQuotationBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quotation_budget <=", value, "quotationBudget");
            return (Criteria) this;
        }

        public Criteria andQuotationBudgetIn(List<BigDecimal> values) {
            addCriterion("quotation_budget in", values, "quotationBudget");
            return (Criteria) this;
        }

        public Criteria andQuotationBudgetNotIn(List<BigDecimal> values) {
            addCriterion("quotation_budget not in", values, "quotationBudget");
            return (Criteria) this;
        }

        public Criteria andQuotationBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quotation_budget between", value1, value2, "quotationBudget");
            return (Criteria) this;
        }

        public Criteria andQuotationBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quotation_budget not between", value1, value2, "quotationBudget");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualIsNull() {
            addCriterion("material_cost_actual is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualIsNotNull() {
            addCriterion("material_cost_actual is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualEqualTo(BigDecimal value) {
            addCriterion("material_cost_actual =", value, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualNotEqualTo(BigDecimal value) {
            addCriterion("material_cost_actual <>", value, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualGreaterThan(BigDecimal value) {
            addCriterion("material_cost_actual >", value, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_cost_actual >=", value, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualLessThan(BigDecimal value) {
            addCriterion("material_cost_actual <", value, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_cost_actual <=", value, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualIn(List<BigDecimal> values) {
            addCriterion("material_cost_actual in", values, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualNotIn(List<BigDecimal> values) {
            addCriterion("material_cost_actual not in", values, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_cost_actual between", value1, value2, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andMaterialCostActualNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_cost_actual not between", value1, value2, "materialCostActual");
            return (Criteria) this;
        }

        public Criteria andHumanCostActualIsNull() {
            addCriterion("human_cost_actual is null");
            return (Criteria) this;
        }

        public Criteria andHumanCostActualIsNotNull() {
            addCriterion("human_cost_actual is not null");
            return (Criteria) this;
        }

        public Criteria andHumanCostActualEqualTo(BigDecimal value) {
            addCriterion("human_cost_actual =", value, "humanCostActual");
            return (Criteria) this;
        }

        public Criteria andHumanCostActualNotEqualTo(BigDecimal value) {
            addCriterion("human_cost_actual <>", value, "humanCostActual");
            return (Criteria) this;
        }

        public Criteria andHumanCostActualGreaterThan(BigDecimal value) {
            addCriterion("human_cost_actual >", value, "humanCostActual");
            return (Criteria) this;
        }

        public Criteria andHumanCostActualGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("human_cost_actual >=", value, "humanCostActual");
            return (Criteria) this;
        }

        public Criteria andHumanCostActualLessThan(BigDecimal value) {
            addCriterion("human_cost_actual <", value, "humanCostActual");
            return (Criteria) this;
        }

        public Criteria andHumanCostActualLessThanOrEqualTo(BigDecimal value) {
            addCriterion("human_cost_actual <=", value, "humanCostActual");
            return (Criteria) this;
        }

        public Criteria andHumanCostActualIn(List<BigDecimal> values) {
            addCriterion("human_cost_actual in", values, "humanCostActual");
            return (Criteria) this;
        }

        public Criteria andHumanCostActualNotIn(List<BigDecimal> values) {
            addCriterion("human_cost_actual not in", values, "humanCostActual");
            return (Criteria) this;
        }

        public Criteria andHumanCostActualBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("human_cost_actual between", value1, value2, "humanCostActual");
            return (Criteria) this;
        }

        public Criteria andHumanCostActualNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("human_cost_actual not between", value1, value2, "humanCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualIsNull() {
            addCriterion("travel_cost_actual is null");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualIsNotNull() {
            addCriterion("travel_cost_actual is not null");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualEqualTo(BigDecimal value) {
            addCriterion("travel_cost_actual =", value, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualNotEqualTo(BigDecimal value) {
            addCriterion("travel_cost_actual <>", value, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualGreaterThan(BigDecimal value) {
            addCriterion("travel_cost_actual >", value, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_cost_actual >=", value, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualLessThan(BigDecimal value) {
            addCriterion("travel_cost_actual <", value, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualLessThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_cost_actual <=", value, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualIn(List<BigDecimal> values) {
            addCriterion("travel_cost_actual in", values, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualNotIn(List<BigDecimal> values) {
            addCriterion("travel_cost_actual not in", values, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_cost_actual between", value1, value2, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andTravelCostActualNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_cost_actual not between", value1, value2, "travelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualIsNull() {
            addCriterion("no_travel_cost_actual is null");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualIsNotNull() {
            addCriterion("no_travel_cost_actual is not null");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualEqualTo(BigDecimal value) {
            addCriterion("no_travel_cost_actual =", value, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualNotEqualTo(BigDecimal value) {
            addCriterion("no_travel_cost_actual <>", value, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualGreaterThan(BigDecimal value) {
            addCriterion("no_travel_cost_actual >", value, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("no_travel_cost_actual >=", value, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualLessThan(BigDecimal value) {
            addCriterion("no_travel_cost_actual <", value, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualLessThanOrEqualTo(BigDecimal value) {
            addCriterion("no_travel_cost_actual <=", value, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualIn(List<BigDecimal> values) {
            addCriterion("no_travel_cost_actual in", values, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualNotIn(List<BigDecimal> values) {
            addCriterion("no_travel_cost_actual not in", values, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_travel_cost_actual between", value1, value2, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andNoTravelCostActualNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_travel_cost_actual not between", value1, value2, "noTravelCostActual");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountIsNull() {
            addCriterion("project_cost_amount is null");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountIsNotNull() {
            addCriterion("project_cost_amount is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountEqualTo(BigDecimal value) {
            addCriterion("project_cost_amount =", value, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountNotEqualTo(BigDecimal value) {
            addCriterion("project_cost_amount <>", value, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountGreaterThan(BigDecimal value) {
            addCriterion("project_cost_amount >", value, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_cost_amount >=", value, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountLessThan(BigDecimal value) {
            addCriterion("project_cost_amount <", value, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_cost_amount <=", value, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountIn(List<BigDecimal> values) {
            addCriterion("project_cost_amount in", values, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountNotIn(List<BigDecimal> values) {
            addCriterion("project_cost_amount not in", values, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_cost_amount between", value1, value2, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCostAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_cost_amount not between", value1, value2, "projectCostAmount");
            return (Criteria) this;
        }

        public Criteria andPerformProfitRateIsNull() {
            addCriterion("perform_profit_rate is null");
            return (Criteria) this;
        }

        public Criteria andPerformProfitRateIsNotNull() {
            addCriterion("perform_profit_rate is not null");
            return (Criteria) this;
        }

        public Criteria andPerformProfitRateEqualTo(BigDecimal value) {
            addCriterion("perform_profit_rate =", value, "performProfitRate");
            return (Criteria) this;
        }

        public Criteria andPerformProfitRateNotEqualTo(BigDecimal value) {
            addCriterion("perform_profit_rate <>", value, "performProfitRate");
            return (Criteria) this;
        }

        public Criteria andPerformProfitRateGreaterThan(BigDecimal value) {
            addCriterion("perform_profit_rate >", value, "performProfitRate");
            return (Criteria) this;
        }

        public Criteria andPerformProfitRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("perform_profit_rate >=", value, "performProfitRate");
            return (Criteria) this;
        }

        public Criteria andPerformProfitRateLessThan(BigDecimal value) {
            addCriterion("perform_profit_rate <", value, "performProfitRate");
            return (Criteria) this;
        }

        public Criteria andPerformProfitRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("perform_profit_rate <=", value, "performProfitRate");
            return (Criteria) this;
        }

        public Criteria andPerformProfitRateIn(List<BigDecimal> values) {
            addCriterion("perform_profit_rate in", values, "performProfitRate");
            return (Criteria) this;
        }

        public Criteria andPerformProfitRateNotIn(List<BigDecimal> values) {
            addCriterion("perform_profit_rate not in", values, "performProfitRate");
            return (Criteria) this;
        }

        public Criteria andPerformProfitRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("perform_profit_rate between", value1, value2, "performProfitRate");
            return (Criteria) this;
        }

        public Criteria andPerformProfitRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("perform_profit_rate not between", value1, value2, "performProfitRate");
            return (Criteria) this;
        }

        public Criteria andBudgetExecutionScheduleIsNull() {
            addCriterion("budget_execution_schedule is null");
            return (Criteria) this;
        }

        public Criteria andBudgetExecutionScheduleIsNotNull() {
            addCriterion("budget_execution_schedule is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetExecutionScheduleEqualTo(BigDecimal value) {
            addCriterion("budget_execution_schedule =", value, "budgetExecutionSchedule");
            return (Criteria) this;
        }

        public Criteria andBudgetExecutionScheduleNotEqualTo(BigDecimal value) {
            addCriterion("budget_execution_schedule <>", value, "budgetExecutionSchedule");
            return (Criteria) this;
        }

        public Criteria andBudgetExecutionScheduleGreaterThan(BigDecimal value) {
            addCriterion("budget_execution_schedule >", value, "budgetExecutionSchedule");
            return (Criteria) this;
        }

        public Criteria andBudgetExecutionScheduleGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_execution_schedule >=", value, "budgetExecutionSchedule");
            return (Criteria) this;
        }

        public Criteria andBudgetExecutionScheduleLessThan(BigDecimal value) {
            addCriterion("budget_execution_schedule <", value, "budgetExecutionSchedule");
            return (Criteria) this;
        }

        public Criteria andBudgetExecutionScheduleLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_execution_schedule <=", value, "budgetExecutionSchedule");
            return (Criteria) this;
        }

        public Criteria andBudgetExecutionScheduleIn(List<BigDecimal> values) {
            addCriterion("budget_execution_schedule in", values, "budgetExecutionSchedule");
            return (Criteria) this;
        }

        public Criteria andBudgetExecutionScheduleNotIn(List<BigDecimal> values) {
            addCriterion("budget_execution_schedule not in", values, "budgetExecutionSchedule");
            return (Criteria) this;
        }

        public Criteria andBudgetExecutionScheduleBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_execution_schedule between", value1, value2, "budgetExecutionSchedule");
            return (Criteria) this;
        }

        public Criteria andBudgetExecutionScheduleNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_execution_schedule not between", value1, value2, "budgetExecutionSchedule");
            return (Criteria) this;
        }

        public Criteria andQuotationActualIsNull() {
            addCriterion("quotation_actual is null");
            return (Criteria) this;
        }

        public Criteria andQuotationActualIsNotNull() {
            addCriterion("quotation_actual is not null");
            return (Criteria) this;
        }

        public Criteria andQuotationActualEqualTo(BigDecimal value) {
            addCriterion("quotation_actual =", value, "quotationActual");
            return (Criteria) this;
        }

        public Criteria andQuotationActualNotEqualTo(BigDecimal value) {
            addCriterion("quotation_actual <>", value, "quotationActual");
            return (Criteria) this;
        }

        public Criteria andQuotationActualGreaterThan(BigDecimal value) {
            addCriterion("quotation_actual >", value, "quotationActual");
            return (Criteria) this;
        }

        public Criteria andQuotationActualGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quotation_actual >=", value, "quotationActual");
            return (Criteria) this;
        }

        public Criteria andQuotationActualLessThan(BigDecimal value) {
            addCriterion("quotation_actual <", value, "quotationActual");
            return (Criteria) this;
        }

        public Criteria andQuotationActualLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quotation_actual <=", value, "quotationActual");
            return (Criteria) this;
        }

        public Criteria andQuotationActualIn(List<BigDecimal> values) {
            addCriterion("quotation_actual in", values, "quotationActual");
            return (Criteria) this;
        }

        public Criteria andQuotationActualNotIn(List<BigDecimal> values) {
            addCriterion("quotation_actual not in", values, "quotationActual");
            return (Criteria) this;
        }

        public Criteria andQuotationActualBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quotation_actual between", value1, value2, "quotationActual");
            return (Criteria) this;
        }

        public Criteria andQuotationActualNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quotation_actual not between", value1, value2, "quotationActual");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNull() {
            addCriterion("ou_name is null");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNotNull() {
            addCriterion("ou_name is not null");
            return (Criteria) this;
        }

        public Criteria andOuNameEqualTo(String value) {
            addCriterion("ou_name =", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotEqualTo(String value) {
            addCriterion("ou_name <>", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThan(String value) {
            addCriterion("ou_name >", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThanOrEqualTo(String value) {
            addCriterion("ou_name >=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThan(String value) {
            addCriterion("ou_name <", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThanOrEqualTo(String value) {
            addCriterion("ou_name <=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLike(String value) {
            addCriterion("ou_name like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotLike(String value) {
            addCriterion("ou_name not like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameIn(List<String> values) {
            addCriterion("ou_name in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotIn(List<String> values) {
            addCriterion("ou_name not in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameBetween(String value1, String value2) {
            addCriterion("ou_name between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotBetween(String value1, String value2) {
            addCriterion("ou_name not between", value1, value2, "ouName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}