package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ReportIncomeProductTaskExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportIncomeProductTaskExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNull() {
            addCriterion("department_id is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNotNull() {
            addCriterion("department_id is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdEqualTo(Long value) {
            addCriterion("department_id =", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotEqualTo(Long value) {
            addCriterion("department_id <>", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThan(Long value) {
            addCriterion("department_id >", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("department_id >=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThan(Long value) {
            addCriterion("department_id <", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThanOrEqualTo(Long value) {
            addCriterion("department_id <=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIn(List<Long> values) {
            addCriterion("department_id in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotIn(List<Long> values) {
            addCriterion("department_id not in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdBetween(Long value1, Long value2) {
            addCriterion("department_id between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotBetween(Long value1, Long value2) {
            addCriterion("department_id not between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIsNull() {
            addCriterion("department_name is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIsNotNull() {
            addCriterion("department_name is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameEqualTo(String value) {
            addCriterion("department_name =", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotEqualTo(String value) {
            addCriterion("department_name <>", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameGreaterThan(String value) {
            addCriterion("department_name >", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameGreaterThanOrEqualTo(String value) {
            addCriterion("department_name >=", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLessThan(String value) {
            addCriterion("department_name <", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLessThanOrEqualTo(String value) {
            addCriterion("department_name <=", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLike(String value) {
            addCriterion("department_name like", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotLike(String value) {
            addCriterion("department_name not like", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIn(List<String> values) {
            addCriterion("department_name in", values, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotIn(List<String> values) {
            addCriterion("department_name not in", values, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameBetween(String value1, String value2) {
            addCriterion("department_name between", value1, value2, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotBetween(String value1, String value2) {
            addCriterion("department_name not between", value1, value2, "departmentName");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdIsNull() {
            addCriterion("product_org_id is null");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdIsNotNull() {
            addCriterion("product_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdEqualTo(Long value) {
            addCriterion("product_org_id =", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdNotEqualTo(Long value) {
            addCriterion("product_org_id <>", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdGreaterThan(Long value) {
            addCriterion("product_org_id >", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("product_org_id >=", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdLessThan(Long value) {
            addCriterion("product_org_id <", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("product_org_id <=", value, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdIn(List<Long> values) {
            addCriterion("product_org_id in", values, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdNotIn(List<Long> values) {
            addCriterion("product_org_id not in", values, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdBetween(Long value1, Long value2) {
            addCriterion("product_org_id between", value1, value2, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("product_org_id not between", value1, value2, "productOrgId");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameIsNull() {
            addCriterion("product_org_name is null");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameIsNotNull() {
            addCriterion("product_org_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameEqualTo(String value) {
            addCriterion("product_org_name =", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameNotEqualTo(String value) {
            addCriterion("product_org_name <>", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameGreaterThan(String value) {
            addCriterion("product_org_name >", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_org_name >=", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameLessThan(String value) {
            addCriterion("product_org_name <", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameLessThanOrEqualTo(String value) {
            addCriterion("product_org_name <=", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameLike(String value) {
            addCriterion("product_org_name like", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameNotLike(String value) {
            addCriterion("product_org_name not like", value, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameIn(List<String> values) {
            addCriterion("product_org_name in", values, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameNotIn(List<String> values) {
            addCriterion("product_org_name not in", values, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameBetween(String value1, String value2) {
            addCriterion("product_org_name between", value1, value2, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andProductOrgNameNotBetween(String value1, String value2) {
            addCriterion("product_org_name not between", value1, value2, "productOrgName");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeIsNull() {
            addCriterion("inner_income is null");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeIsNotNull() {
            addCriterion("inner_income is not null");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeEqualTo(BigDecimal value) {
            addCriterion("inner_income =", value, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeNotEqualTo(BigDecimal value) {
            addCriterion("inner_income <>", value, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeGreaterThan(BigDecimal value) {
            addCriterion("inner_income >", value, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_income >=", value, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeLessThan(BigDecimal value) {
            addCriterion("inner_income <", value, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_income <=", value, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeIn(List<BigDecimal> values) {
            addCriterion("inner_income in", values, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeNotIn(List<BigDecimal> values) {
            addCriterion("inner_income not in", values, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_income between", value1, value2, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andInnerIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_income not between", value1, value2, "innerIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeIsNull() {
            addCriterion("outter_income is null");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeIsNotNull() {
            addCriterion("outter_income is not null");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeEqualTo(BigDecimal value) {
            addCriterion("outter_income =", value, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeNotEqualTo(BigDecimal value) {
            addCriterion("outter_income <>", value, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeGreaterThan(BigDecimal value) {
            addCriterion("outter_income >", value, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("outter_income >=", value, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeLessThan(BigDecimal value) {
            addCriterion("outter_income <", value, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("outter_income <=", value, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeIn(List<BigDecimal> values) {
            addCriterion("outter_income in", values, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeNotIn(List<BigDecimal> values) {
            addCriterion("outter_income not in", values, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outter_income between", value1, value2, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andOutterIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outter_income not between", value1, value2, "outterIncome");
            return (Criteria) this;
        }

        public Criteria andMonthIsNull() {
            addCriterion("month is null");
            return (Criteria) this;
        }

        public Criteria andMonthIsNotNull() {
            addCriterion("month is not null");
            return (Criteria) this;
        }

        public Criteria andMonthEqualTo(String value) {
            addCriterion("month =", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotEqualTo(String value) {
            addCriterion("month <>", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthGreaterThan(String value) {
            addCriterion("month >", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthGreaterThanOrEqualTo(String value) {
            addCriterion("month >=", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthLessThan(String value) {
            addCriterion("month <", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthLessThanOrEqualTo(String value) {
            addCriterion("month <=", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthLike(String value) {
            addCriterion("month like", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotLike(String value) {
            addCriterion("month not like", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthIn(List<String> values) {
            addCriterion("month in", values, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotIn(List<String> values) {
            addCriterion("month not in", values, "month");
            return (Criteria) this;
        }

        public Criteria andMonthBetween(String value1, String value2) {
            addCriterion("month between", value1, value2, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotBetween(String value1, String value2) {
            addCriterion("month not between", value1, value2, "month");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}