package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-12-9
 * @description 里程碑信息
 */
@Getter
@Setter
public class MilepostInfoExcelVO {

    @Excel(name = "里程碑序号", width = 10)
    private Integer orderNum;

    @Excel(name = "里程碑", width = 20)
    private String name;

    @Excel(name = "并行交付线名称", width = 20)
    private String groupName;

    @Excel(name = "并行交付里程碑序号", width = 10)
    private Integer childOrderNum;

    @Excel(name = "并行交付里程碑名称", width = 20)
    private String childName;

    @Excel(name = "里程碑交付状态", width = 15, replace = {"进行中_0","评审中_1","通过_2","驳回_3","废弃_4","未开始_-3"})
    private Integer status;

    @Excel(name = "里程碑计划开始日期", width = 15, format = "yyyy-MM-dd")
    private Date startTime;

    @Excel(name = "里程碑计划结束日期", width = 15, format = "yyyy-MM-dd")
    private Date endTime;

    @Excel(name = "实际开始日期", width = 15, format = "yyyy-MM-dd")
    private Date actualStartTime;

    @Excel(name = "实际结束日期", width = 15, format = "yyyy-MM-dd")
    private Date actualEndTime;

}
