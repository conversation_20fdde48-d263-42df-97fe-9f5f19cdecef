package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WorkingHourExceptionRdmExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WorkingHourExceptionRdmExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCostCollectionWorkingHourIdIsNull() {
            addCriterion("cost_collection_working_hour_id is null");
            return (Criteria) this;
        }

        public Criteria andCostCollectionWorkingHourIdIsNotNull() {
            addCriterion("cost_collection_working_hour_id is not null");
            return (Criteria) this;
        }

        public Criteria andCostCollectionWorkingHourIdEqualTo(Long value) {
            addCriterion("cost_collection_working_hour_id =", value, "costCollectionWorkingHourId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionWorkingHourIdNotEqualTo(Long value) {
            addCriterion("cost_collection_working_hour_id <>", value, "costCollectionWorkingHourId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionWorkingHourIdGreaterThan(Long value) {
            addCriterion("cost_collection_working_hour_id >", value, "costCollectionWorkingHourId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionWorkingHourIdGreaterThanOrEqualTo(Long value) {
            addCriterion("cost_collection_working_hour_id >=", value, "costCollectionWorkingHourId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionWorkingHourIdLessThan(Long value) {
            addCriterion("cost_collection_working_hour_id <", value, "costCollectionWorkingHourId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionWorkingHourIdLessThanOrEqualTo(Long value) {
            addCriterion("cost_collection_working_hour_id <=", value, "costCollectionWorkingHourId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionWorkingHourIdIn(List<Long> values) {
            addCriterion("cost_collection_working_hour_id in", values, "costCollectionWorkingHourId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionWorkingHourIdNotIn(List<Long> values) {
            addCriterion("cost_collection_working_hour_id not in", values, "costCollectionWorkingHourId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionWorkingHourIdBetween(Long value1, Long value2) {
            addCriterion("cost_collection_working_hour_id between", value1, value2, "costCollectionWorkingHourId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionWorkingHourIdNotBetween(Long value1, Long value2) {
            addCriterion("cost_collection_working_hour_id not between", value1, value2, "costCollectionWorkingHourId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdIsNull() {
            addCriterion("parent_unit_id is null");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdIsNotNull() {
            addCriterion("parent_unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdEqualTo(Long value) {
            addCriterion("parent_unit_id =", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdNotEqualTo(Long value) {
            addCriterion("parent_unit_id <>", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdGreaterThan(Long value) {
            addCriterion("parent_unit_id >", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("parent_unit_id >=", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdLessThan(Long value) {
            addCriterion("parent_unit_id <", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("parent_unit_id <=", value, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdIn(List<Long> values) {
            addCriterion("parent_unit_id in", values, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdNotIn(List<Long> values) {
            addCriterion("parent_unit_id not in", values, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdBetween(Long value1, Long value2) {
            addCriterion("parent_unit_id between", value1, value2, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andParentUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("parent_unit_id not between", value1, value2, "parentUnitId");
            return (Criteria) this;
        }

        public Criteria andErpMessagesIsNull() {
            addCriterion("erp_messages is null");
            return (Criteria) this;
        }

        public Criteria andErpMessagesIsNotNull() {
            addCriterion("erp_messages is not null");
            return (Criteria) this;
        }

        public Criteria andErpMessagesEqualTo(String value) {
            addCriterion("erp_messages =", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesNotEqualTo(String value) {
            addCriterion("erp_messages <>", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesGreaterThan(String value) {
            addCriterion("erp_messages >", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesGreaterThanOrEqualTo(String value) {
            addCriterion("erp_messages >=", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesLessThan(String value) {
            addCriterion("erp_messages <", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesLessThanOrEqualTo(String value) {
            addCriterion("erp_messages <=", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesLike(String value) {
            addCriterion("erp_messages like", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesNotLike(String value) {
            addCriterion("erp_messages not like", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesIn(List<String> values) {
            addCriterion("erp_messages in", values, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesNotIn(List<String> values) {
            addCriterion("erp_messages not in", values, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesBetween(String value1, String value2) {
            addCriterion("erp_messages between", value1, value2, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesNotBetween(String value1, String value2) {
            addCriterion("erp_messages not between", value1, value2, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNull() {
            addCriterion("user_name is null");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNotNull() {
            addCriterion("user_name is not null");
            return (Criteria) this;
        }

        public Criteria andUserNameEqualTo(String value) {
            addCriterion("user_name =", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotEqualTo(String value) {
            addCriterion("user_name <>", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThan(String value) {
            addCriterion("user_name >", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("user_name >=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThan(String value) {
            addCriterion("user_name <", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThanOrEqualTo(String value) {
            addCriterion("user_name <=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLike(String value) {
            addCriterion("user_name like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotLike(String value) {
            addCriterion("user_name not like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameIn(List<String> values) {
            addCriterion("user_name in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotIn(List<String> values) {
            addCriterion("user_name not in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameBetween(String value1, String value2) {
            addCriterion("user_name between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotBetween(String value1, String value2) {
            addCriterion("user_name not between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andUserMipIsNull() {
            addCriterion("user_mip is null");
            return (Criteria) this;
        }

        public Criteria andUserMipIsNotNull() {
            addCriterion("user_mip is not null");
            return (Criteria) this;
        }

        public Criteria andUserMipEqualTo(String value) {
            addCriterion("user_mip =", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipNotEqualTo(String value) {
            addCriterion("user_mip <>", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipGreaterThan(String value) {
            addCriterion("user_mip >", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipGreaterThanOrEqualTo(String value) {
            addCriterion("user_mip >=", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipLessThan(String value) {
            addCriterion("user_mip <", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipLessThanOrEqualTo(String value) {
            addCriterion("user_mip <=", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipLike(String value) {
            addCriterion("user_mip like", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipNotLike(String value) {
            addCriterion("user_mip not like", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipIn(List<String> values) {
            addCriterion("user_mip in", values, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipNotIn(List<String> values) {
            addCriterion("user_mip not in", values, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipBetween(String value1, String value2) {
            addCriterion("user_mip between", value1, value2, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipNotBetween(String value1, String value2) {
            addCriterion("user_mip not between", value1, value2, "userMip");
            return (Criteria) this;
        }

        public Criteria andAttHourIsNull() {
            addCriterion("att_hour is null");
            return (Criteria) this;
        }

        public Criteria andAttHourIsNotNull() {
            addCriterion("att_hour is not null");
            return (Criteria) this;
        }

        public Criteria andAttHourEqualTo(BigDecimal value) {
            addCriterion("att_hour =", value, "attHour");
            return (Criteria) this;
        }

        public Criteria andAttHourNotEqualTo(BigDecimal value) {
            addCriterion("att_hour <>", value, "attHour");
            return (Criteria) this;
        }

        public Criteria andAttHourGreaterThan(BigDecimal value) {
            addCriterion("att_hour >", value, "attHour");
            return (Criteria) this;
        }

        public Criteria andAttHourGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("att_hour >=", value, "attHour");
            return (Criteria) this;
        }

        public Criteria andAttHourLessThan(BigDecimal value) {
            addCriterion("att_hour <", value, "attHour");
            return (Criteria) this;
        }

        public Criteria andAttHourLessThanOrEqualTo(BigDecimal value) {
            addCriterion("att_hour <=", value, "attHour");
            return (Criteria) this;
        }

        public Criteria andAttHourIn(List<BigDecimal> values) {
            addCriterion("att_hour in", values, "attHour");
            return (Criteria) this;
        }

        public Criteria andAttHourNotIn(List<BigDecimal> values) {
            addCriterion("att_hour not in", values, "attHour");
            return (Criteria) this;
        }

        public Criteria andAttHourBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("att_hour between", value1, value2, "attHour");
            return (Criteria) this;
        }

        public Criteria andAttHourNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("att_hour not between", value1, value2, "attHour");
            return (Criteria) this;
        }

        public Criteria andAttDateIsNull() {
            addCriterion("att_date is null");
            return (Criteria) this;
        }

        public Criteria andAttDateIsNotNull() {
            addCriterion("att_date is not null");
            return (Criteria) this;
        }

        public Criteria andAttDateEqualTo(Date value) {
            addCriterion("att_date =", value, "attDate");
            return (Criteria) this;
        }

        public Criteria andAttDateNotEqualTo(Date value) {
            addCriterion("att_date <>", value, "attDate");
            return (Criteria) this;
        }

        public Criteria andAttDateGreaterThan(Date value) {
            addCriterion("att_date >", value, "attDate");
            return (Criteria) this;
        }

        public Criteria andAttDateGreaterThanOrEqualTo(Date value) {
            addCriterion("att_date >=", value, "attDate");
            return (Criteria) this;
        }

        public Criteria andAttDateLessThan(Date value) {
            addCriterion("att_date <", value, "attDate");
            return (Criteria) this;
        }

        public Criteria andAttDateLessThanOrEqualTo(Date value) {
            addCriterion("att_date <=", value, "attDate");
            return (Criteria) this;
        }

        public Criteria andAttDateIn(List<Date> values) {
            addCriterion("att_date in", values, "attDate");
            return (Criteria) this;
        }

        public Criteria andAttDateNotIn(List<Date> values) {
            addCriterion("att_date not in", values, "attDate");
            return (Criteria) this;
        }

        public Criteria andAttDateBetween(Date value1, Date value2) {
            addCriterion("att_date between", value1, value2, "attDate");
            return (Criteria) this;
        }

        public Criteria andAttDateNotBetween(Date value1, Date value2) {
            addCriterion("att_date not between", value1, value2, "attDate");
            return (Criteria) this;
        }

        public Criteria andReportHourIsNull() {
            addCriterion("report_hour is null");
            return (Criteria) this;
        }

        public Criteria andReportHourIsNotNull() {
            addCriterion("report_hour is not null");
            return (Criteria) this;
        }

        public Criteria andReportHourEqualTo(BigDecimal value) {
            addCriterion("report_hour =", value, "reportHour");
            return (Criteria) this;
        }

        public Criteria andReportHourNotEqualTo(BigDecimal value) {
            addCriterion("report_hour <>", value, "reportHour");
            return (Criteria) this;
        }

        public Criteria andReportHourGreaterThan(BigDecimal value) {
            addCriterion("report_hour >", value, "reportHour");
            return (Criteria) this;
        }

        public Criteria andReportHourGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("report_hour >=", value, "reportHour");
            return (Criteria) this;
        }

        public Criteria andReportHourLessThan(BigDecimal value) {
            addCriterion("report_hour <", value, "reportHour");
            return (Criteria) this;
        }

        public Criteria andReportHourLessThanOrEqualTo(BigDecimal value) {
            addCriterion("report_hour <=", value, "reportHour");
            return (Criteria) this;
        }

        public Criteria andReportHourIn(List<BigDecimal> values) {
            addCriterion("report_hour in", values, "reportHour");
            return (Criteria) this;
        }

        public Criteria andReportHourNotIn(List<BigDecimal> values) {
            addCriterion("report_hour not in", values, "reportHour");
            return (Criteria) this;
        }

        public Criteria andReportHourBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("report_hour between", value1, value2, "reportHour");
            return (Criteria) this;
        }

        public Criteria andReportHourNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("report_hour not between", value1, value2, "reportHour");
            return (Criteria) this;
        }

        public Criteria andReportDateIsNull() {
            addCriterion("report_date is null");
            return (Criteria) this;
        }

        public Criteria andReportDateIsNotNull() {
            addCriterion("report_date is not null");
            return (Criteria) this;
        }

        public Criteria andReportDateEqualTo(Date value) {
            addCriterion("report_date =", value, "reportDate");
            return (Criteria) this;
        }

        public Criteria andReportDateNotEqualTo(Date value) {
            addCriterion("report_date <>", value, "reportDate");
            return (Criteria) this;
        }

        public Criteria andReportDateGreaterThan(Date value) {
            addCriterion("report_date >", value, "reportDate");
            return (Criteria) this;
        }

        public Criteria andReportDateGreaterThanOrEqualTo(Date value) {
            addCriterion("report_date >=", value, "reportDate");
            return (Criteria) this;
        }

        public Criteria andReportDateLessThan(Date value) {
            addCriterion("report_date <", value, "reportDate");
            return (Criteria) this;
        }

        public Criteria andReportDateLessThanOrEqualTo(Date value) {
            addCriterion("report_date <=", value, "reportDate");
            return (Criteria) this;
        }

        public Criteria andReportDateIn(List<Date> values) {
            addCriterion("report_date in", values, "reportDate");
            return (Criteria) this;
        }

        public Criteria andReportDateNotIn(List<Date> values) {
            addCriterion("report_date not in", values, "reportDate");
            return (Criteria) this;
        }

        public Criteria andReportDateBetween(Date value1, Date value2) {
            addCriterion("report_date between", value1, value2, "reportDate");
            return (Criteria) this;
        }

        public Criteria andReportDateNotBetween(Date value1, Date value2) {
            addCriterion("report_date not between", value1, value2, "reportDate");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdIsNull() {
            addCriterion("working_hour_id is null");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdIsNotNull() {
            addCriterion("working_hour_id is not null");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdEqualTo(Long value) {
            addCriterion("working_hour_id =", value, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdNotEqualTo(Long value) {
            addCriterion("working_hour_id <>", value, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdGreaterThan(Long value) {
            addCriterion("working_hour_id >", value, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdGreaterThanOrEqualTo(Long value) {
            addCriterion("working_hour_id >=", value, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdLessThan(Long value) {
            addCriterion("working_hour_id <", value, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdLessThanOrEqualTo(Long value) {
            addCriterion("working_hour_id <=", value, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdIn(List<Long> values) {
            addCriterion("working_hour_id in", values, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdNotIn(List<Long> values) {
            addCriterion("working_hour_id not in", values, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdBetween(Long value1, Long value2) {
            addCriterion("working_hour_id between", value1, value2, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIdNotBetween(Long value1, Long value2) {
            addCriterion("working_hour_id not between", value1, value2, "workingHourId");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNull() {
            addCriterion("project_manager is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNotNull() {
            addCriterion("project_manager is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerEqualTo(String value) {
            addCriterion("project_manager =", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotEqualTo(String value) {
            addCriterion("project_manager <>", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThan(String value) {
            addCriterion("project_manager >", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThanOrEqualTo(String value) {
            addCriterion("project_manager >=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThan(String value) {
            addCriterion("project_manager <", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThanOrEqualTo(String value) {
            addCriterion("project_manager <=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLike(String value) {
            addCriterion("project_manager like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotLike(String value) {
            addCriterion("project_manager not like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIn(List<String> values) {
            addCriterion("project_manager in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotIn(List<String> values) {
            addCriterion("project_manager not in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerBetween(String value1, String value2) {
            addCriterion("project_manager between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotBetween(String value1, String value2) {
            addCriterion("project_manager not between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andCheckHourIsNull() {
            addCriterion("check_hour is null");
            return (Criteria) this;
        }

        public Criteria andCheckHourIsNotNull() {
            addCriterion("check_hour is not null");
            return (Criteria) this;
        }

        public Criteria andCheckHourEqualTo(BigDecimal value) {
            addCriterion("check_hour =", value, "checkHour");
            return (Criteria) this;
        }

        public Criteria andCheckHourNotEqualTo(BigDecimal value) {
            addCriterion("check_hour <>", value, "checkHour");
            return (Criteria) this;
        }

        public Criteria andCheckHourGreaterThan(BigDecimal value) {
            addCriterion("check_hour >", value, "checkHour");
            return (Criteria) this;
        }

        public Criteria andCheckHourGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("check_hour >=", value, "checkHour");
            return (Criteria) this;
        }

        public Criteria andCheckHourLessThan(BigDecimal value) {
            addCriterion("check_hour <", value, "checkHour");
            return (Criteria) this;
        }

        public Criteria andCheckHourLessThanOrEqualTo(BigDecimal value) {
            addCriterion("check_hour <=", value, "checkHour");
            return (Criteria) this;
        }

        public Criteria andCheckHourIn(List<BigDecimal> values) {
            addCriterion("check_hour in", values, "checkHour");
            return (Criteria) this;
        }

        public Criteria andCheckHourNotIn(List<BigDecimal> values) {
            addCriterion("check_hour not in", values, "checkHour");
            return (Criteria) this;
        }

        public Criteria andCheckHourBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("check_hour between", value1, value2, "checkHour");
            return (Criteria) this;
        }

        public Criteria andCheckHourNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("check_hour not between", value1, value2, "checkHour");
            return (Criteria) this;
        }

        public Criteria andCheckDateIsNull() {
            addCriterion("check_date is null");
            return (Criteria) this;
        }

        public Criteria andCheckDateIsNotNull() {
            addCriterion("check_date is not null");
            return (Criteria) this;
        }

        public Criteria andCheckDateEqualTo(Date value) {
            addCriterion("check_date =", value, "checkDate");
            return (Criteria) this;
        }

        public Criteria andCheckDateNotEqualTo(Date value) {
            addCriterion("check_date <>", value, "checkDate");
            return (Criteria) this;
        }

        public Criteria andCheckDateGreaterThan(Date value) {
            addCriterion("check_date >", value, "checkDate");
            return (Criteria) this;
        }

        public Criteria andCheckDateGreaterThanOrEqualTo(Date value) {
            addCriterion("check_date >=", value, "checkDate");
            return (Criteria) this;
        }

        public Criteria andCheckDateLessThan(Date value) {
            addCriterion("check_date <", value, "checkDate");
            return (Criteria) this;
        }

        public Criteria andCheckDateLessThanOrEqualTo(Date value) {
            addCriterion("check_date <=", value, "checkDate");
            return (Criteria) this;
        }

        public Criteria andCheckDateIn(List<Date> values) {
            addCriterion("check_date in", values, "checkDate");
            return (Criteria) this;
        }

        public Criteria andCheckDateNotIn(List<Date> values) {
            addCriterion("check_date not in", values, "checkDate");
            return (Criteria) this;
        }

        public Criteria andCheckDateBetween(Date value1, Date value2) {
            addCriterion("check_date between", value1, value2, "checkDate");
            return (Criteria) this;
        }

        public Criteria andCheckDateNotBetween(Date value1, Date value2) {
            addCriterion("check_date not between", value1, value2, "checkDate");
            return (Criteria) this;
        }

        public Criteria andConfirmHourIsNull() {
            addCriterion("confirm_hour is null");
            return (Criteria) this;
        }

        public Criteria andConfirmHourIsNotNull() {
            addCriterion("confirm_hour is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmHourEqualTo(BigDecimal value) {
            addCriterion("confirm_hour =", value, "confirmHour");
            return (Criteria) this;
        }

        public Criteria andConfirmHourNotEqualTo(BigDecimal value) {
            addCriterion("confirm_hour <>", value, "confirmHour");
            return (Criteria) this;
        }

        public Criteria andConfirmHourGreaterThan(BigDecimal value) {
            addCriterion("confirm_hour >", value, "confirmHour");
            return (Criteria) this;
        }

        public Criteria andConfirmHourGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirm_hour >=", value, "confirmHour");
            return (Criteria) this;
        }

        public Criteria andConfirmHourLessThan(BigDecimal value) {
            addCriterion("confirm_hour <", value, "confirmHour");
            return (Criteria) this;
        }

        public Criteria andConfirmHourLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirm_hour <=", value, "confirmHour");
            return (Criteria) this;
        }

        public Criteria andConfirmHourIn(List<BigDecimal> values) {
            addCriterion("confirm_hour in", values, "confirmHour");
            return (Criteria) this;
        }

        public Criteria andConfirmHourNotIn(List<BigDecimal> values) {
            addCriterion("confirm_hour not in", values, "confirmHour");
            return (Criteria) this;
        }

        public Criteria andConfirmHourBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirm_hour between", value1, value2, "confirmHour");
            return (Criteria) this;
        }

        public Criteria andConfirmHourNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirm_hour not between", value1, value2, "confirmHour");
            return (Criteria) this;
        }

        public Criteria andConfirmDateIsNull() {
            addCriterion("confirm_date is null");
            return (Criteria) this;
        }

        public Criteria andConfirmDateIsNotNull() {
            addCriterion("confirm_date is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmDateEqualTo(Date value) {
            addCriterion("confirm_date =", value, "confirmDate");
            return (Criteria) this;
        }

        public Criteria andConfirmDateNotEqualTo(Date value) {
            addCriterion("confirm_date <>", value, "confirmDate");
            return (Criteria) this;
        }

        public Criteria andConfirmDateGreaterThan(Date value) {
            addCriterion("confirm_date >", value, "confirmDate");
            return (Criteria) this;
        }

        public Criteria andConfirmDateGreaterThanOrEqualTo(Date value) {
            addCriterion("confirm_date >=", value, "confirmDate");
            return (Criteria) this;
        }

        public Criteria andConfirmDateLessThan(Date value) {
            addCriterion("confirm_date <", value, "confirmDate");
            return (Criteria) this;
        }

        public Criteria andConfirmDateLessThanOrEqualTo(Date value) {
            addCriterion("confirm_date <=", value, "confirmDate");
            return (Criteria) this;
        }

        public Criteria andConfirmDateIn(List<Date> values) {
            addCriterion("confirm_date in", values, "confirmDate");
            return (Criteria) this;
        }

        public Criteria andConfirmDateNotIn(List<Date> values) {
            addCriterion("confirm_date not in", values, "confirmDate");
            return (Criteria) this;
        }

        public Criteria andConfirmDateBetween(Date value1, Date value2) {
            addCriterion("confirm_date between", value1, value2, "confirmDate");
            return (Criteria) this;
        }

        public Criteria andConfirmDateNotBetween(Date value1, Date value2) {
            addCriterion("confirm_date not between", value1, value2, "confirmDate");
            return (Criteria) this;
        }

        public Criteria andDockerIsNull() {
            addCriterion("docker is null");
            return (Criteria) this;
        }

        public Criteria andDockerIsNotNull() {
            addCriterion("docker is not null");
            return (Criteria) this;
        }

        public Criteria andDockerEqualTo(String value) {
            addCriterion("docker =", value, "docker");
            return (Criteria) this;
        }

        public Criteria andDockerNotEqualTo(String value) {
            addCriterion("docker <>", value, "docker");
            return (Criteria) this;
        }

        public Criteria andDockerGreaterThan(String value) {
            addCriterion("docker >", value, "docker");
            return (Criteria) this;
        }

        public Criteria andDockerGreaterThanOrEqualTo(String value) {
            addCriterion("docker >=", value, "docker");
            return (Criteria) this;
        }

        public Criteria andDockerLessThan(String value) {
            addCriterion("docker <", value, "docker");
            return (Criteria) this;
        }

        public Criteria andDockerLessThanOrEqualTo(String value) {
            addCriterion("docker <=", value, "docker");
            return (Criteria) this;
        }

        public Criteria andDockerLike(String value) {
            addCriterion("docker like", value, "docker");
            return (Criteria) this;
        }

        public Criteria andDockerNotLike(String value) {
            addCriterion("docker not like", value, "docker");
            return (Criteria) this;
        }

        public Criteria andDockerIn(List<String> values) {
            addCriterion("docker in", values, "docker");
            return (Criteria) this;
        }

        public Criteria andDockerNotIn(List<String> values) {
            addCriterion("docker not in", values, "docker");
            return (Criteria) this;
        }

        public Criteria andDockerBetween(String value1, String value2) {
            addCriterion("docker between", value1, value2, "docker");
            return (Criteria) this;
        }

        public Criteria andDockerNotBetween(String value1, String value2) {
            addCriterion("docker not between", value1, value2, "docker");
            return (Criteria) this;
        }

        public Criteria andDayPriceIsNull() {
            addCriterion("day_price is null");
            return (Criteria) this;
        }

        public Criteria andDayPriceIsNotNull() {
            addCriterion("day_price is not null");
            return (Criteria) this;
        }

        public Criteria andDayPriceEqualTo(BigDecimal value) {
            addCriterion("day_price =", value, "dayPrice");
            return (Criteria) this;
        }

        public Criteria andDayPriceNotEqualTo(BigDecimal value) {
            addCriterion("day_price <>", value, "dayPrice");
            return (Criteria) this;
        }

        public Criteria andDayPriceGreaterThan(BigDecimal value) {
            addCriterion("day_price >", value, "dayPrice");
            return (Criteria) this;
        }

        public Criteria andDayPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("day_price >=", value, "dayPrice");
            return (Criteria) this;
        }

        public Criteria andDayPriceLessThan(BigDecimal value) {
            addCriterion("day_price <", value, "dayPrice");
            return (Criteria) this;
        }

        public Criteria andDayPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("day_price <=", value, "dayPrice");
            return (Criteria) this;
        }

        public Criteria andDayPriceIn(List<BigDecimal> values) {
            addCriterion("day_price in", values, "dayPrice");
            return (Criteria) this;
        }

        public Criteria andDayPriceNotIn(List<BigDecimal> values) {
            addCriterion("day_price not in", values, "dayPrice");
            return (Criteria) this;
        }

        public Criteria andDayPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("day_price between", value1, value2, "dayPrice");
            return (Criteria) this;
        }

        public Criteria andDayPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("day_price not between", value1, value2, "dayPrice");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andSupplierIsNull() {
            addCriterion("supplier is null");
            return (Criteria) this;
        }

        public Criteria andSupplierIsNotNull() {
            addCriterion("supplier is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierEqualTo(String value) {
            addCriterion("supplier =", value, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierNotEqualTo(String value) {
            addCriterion("supplier <>", value, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierGreaterThan(String value) {
            addCriterion("supplier >", value, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierGreaterThanOrEqualTo(String value) {
            addCriterion("supplier >=", value, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierLessThan(String value) {
            addCriterion("supplier <", value, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierLessThanOrEqualTo(String value) {
            addCriterion("supplier <=", value, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierLike(String value) {
            addCriterion("supplier like", value, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierNotLike(String value) {
            addCriterion("supplier not like", value, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierIn(List<String> values) {
            addCriterion("supplier in", values, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierNotIn(List<String> values) {
            addCriterion("supplier not in", values, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierBetween(String value1, String value2) {
            addCriterion("supplier between", value1, value2, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierNotBetween(String value1, String value2) {
            addCriterion("supplier not between", value1, value2, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeIsNull() {
            addCriterion("supplier_code is null");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeIsNotNull() {
            addCriterion("supplier_code is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeEqualTo(String value) {
            addCriterion("supplier_code =", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotEqualTo(String value) {
            addCriterion("supplier_code <>", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeGreaterThan(String value) {
            addCriterion("supplier_code >", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeGreaterThanOrEqualTo(String value) {
            addCriterion("supplier_code >=", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeLessThan(String value) {
            addCriterion("supplier_code <", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeLessThanOrEqualTo(String value) {
            addCriterion("supplier_code <=", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeLike(String value) {
            addCriterion("supplier_code like", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotLike(String value) {
            addCriterion("supplier_code not like", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeIn(List<String> values) {
            addCriterion("supplier_code in", values, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotIn(List<String> values) {
            addCriterion("supplier_code not in", values, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeBetween(String value1, String value2) {
            addCriterion("supplier_code between", value1, value2, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotBetween(String value1, String value2) {
            addCriterion("supplier_code not between", value1, value2, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andResourceFlagIsNull() {
            addCriterion("resource_flag is null");
            return (Criteria) this;
        }

        public Criteria andResourceFlagIsNotNull() {
            addCriterion("resource_flag is not null");
            return (Criteria) this;
        }

        public Criteria andResourceFlagEqualTo(Integer value) {
            addCriterion("resource_flag =", value, "resourceFlag");
            return (Criteria) this;
        }

        public Criteria andResourceFlagNotEqualTo(Integer value) {
            addCriterion("resource_flag <>", value, "resourceFlag");
            return (Criteria) this;
        }

        public Criteria andResourceFlagGreaterThan(Integer value) {
            addCriterion("resource_flag >", value, "resourceFlag");
            return (Criteria) this;
        }

        public Criteria andResourceFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("resource_flag >=", value, "resourceFlag");
            return (Criteria) this;
        }

        public Criteria andResourceFlagLessThan(Integer value) {
            addCriterion("resource_flag <", value, "resourceFlag");
            return (Criteria) this;
        }

        public Criteria andResourceFlagLessThanOrEqualTo(Integer value) {
            addCriterion("resource_flag <=", value, "resourceFlag");
            return (Criteria) this;
        }

        public Criteria andResourceFlagIn(List<Integer> values) {
            addCriterion("resource_flag in", values, "resourceFlag");
            return (Criteria) this;
        }

        public Criteria andResourceFlagNotIn(List<Integer> values) {
            addCriterion("resource_flag not in", values, "resourceFlag");
            return (Criteria) this;
        }

        public Criteria andResourceFlagBetween(Integer value1, Integer value2) {
            addCriterion("resource_flag between", value1, value2, "resourceFlag");
            return (Criteria) this;
        }

        public Criteria andResourceFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("resource_flag not between", value1, value2, "resourceFlag");
            return (Criteria) this;
        }

        public Criteria andResourceIdIsNull() {
            addCriterion("resource_id is null");
            return (Criteria) this;
        }

        public Criteria andResourceIdIsNotNull() {
            addCriterion("resource_id is not null");
            return (Criteria) this;
        }

        public Criteria andResourceIdEqualTo(Long value) {
            addCriterion("resource_id =", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotEqualTo(Long value) {
            addCriterion("resource_id <>", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdGreaterThan(Long value) {
            addCriterion("resource_id >", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdGreaterThanOrEqualTo(Long value) {
            addCriterion("resource_id >=", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdLessThan(Long value) {
            addCriterion("resource_id <", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdLessThanOrEqualTo(Long value) {
            addCriterion("resource_id <=", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdIn(List<Long> values) {
            addCriterion("resource_id in", values, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotIn(List<Long> values) {
            addCriterion("resource_id not in", values, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdBetween(Long value1, Long value2) {
            addCriterion("resource_id between", value1, value2, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotBetween(Long value1, Long value2) {
            addCriterion("resource_id not between", value1, value2, "resourceId");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}