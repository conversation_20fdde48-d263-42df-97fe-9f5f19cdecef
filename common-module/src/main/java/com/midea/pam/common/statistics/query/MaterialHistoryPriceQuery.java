package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel(value = "MaterialHistoryPriceQuery", description = "物料历史价格表")
public class MaterialHistoryPriceQuery {

    private Long id;

    @ApiModelProperty(value = "采购下单日期-开始时间")
    private String startTime;

    @ApiModelProperty(value = "采购下单日期-结束时间")
    private String endTime;

    @ApiModelProperty(value = "详细设计单据编号，单据类型为：需求发布、详细设计变更")
    private String requirementCode;

    @ApiModelProperty(value = "PAM编码")
    private List<String> pamCodeList = new ArrayList<>();

    @ApiModelProperty(value = "ERP编码")
    private List<String> erpCodeList = new ArrayList<>();

    @ApiModelProperty(value = "业务分类：业务部门ID列表，不为空时，只查业务部门属于此列表中的项目")
    private List<Long> unitIds;

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    @ApiModelProperty(value = "报表ID")
    private Long reportId;

    @ApiModelProperty(value = "业务实体id列表")
    private List<Long> ouIdList;

    private Date expireDate;

    private Long personal;

    private Long companyId;

    private Long createBy;

    @Override
    public String toString() {
        return "MaterialHistoryPriceQuery{" +
                "id=" + id +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", requirementCode='" + requirementCode + '\'' +
                ", pamCodeList=" + pamCodeList +
                ", erpCodeList=" + erpCodeList +
                ", unitIds=" + unitIds +
                ", executeId=" + executeId +
                ", reportId=" + reportId +
                ", ouIdList=" + ouIdList +
                ", expireDate=" + expireDate +
                ", personal=" + personal +
                ", companyId=" + companyId +
                ", createBy=" + createBy +
                '}';
    }

}
