package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "")
public class ProjectWorkingHourDetail extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "人员")
    private String userMip;

    @ApiModelProperty(value = "角色")
    private String level;

    @ApiModelProperty(value = "时间")
    private Date applyDate;

    @ApiModelProperty(value = "工时(审批通过/待审批)")
    private BigDecimal workingHours;

    @ApiModelProperty(value = "标准费率")
    private BigDecimal costMoney;

    @ApiModelProperty(value = "已填报待审批的工时*标准费率/8")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "工时成本明细类型 1-在途成本、2-已发生成本")
    private Integer budgetType;

    @ApiModelProperty(value = "wbs编码")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "业务生成时间")
    private Date dataTime;

    private static final long serialVersionUID = 1L;

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getUserMip() {
        return userMip;
    }

    public void setUserMip(String userMip) {
        this.userMip = userMip == null ? null : userMip.trim();
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level == null ? null : level.trim();
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public BigDecimal getWorkingHours() {
        return workingHours;
    }

    public void setWorkingHours(BigDecimal workingHours) {
        this.workingHours = workingHours;
    }

    public BigDecimal getCostMoney() {
        return costMoney;
    }

    public void setCostMoney(BigDecimal costMoney) {
        this.costMoney = costMoney;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getBudgetType() {
        return budgetType;
    }

    public void setBudgetType(Integer budgetType) {
        this.budgetType = budgetType;
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Date getDataTime() {
        return dataTime;
    }

    public void setDataTime(Date dataTime) {
        this.dataTime = dataTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", userMip=").append(userMip);
        sb.append(", level=").append(level);
        sb.append(", applyDate=").append(applyDate);
        sb.append(", workingHours=").append(workingHours);
        sb.append(", costMoney=").append(costMoney);
        sb.append(", totalAmount=").append(totalAmount);
        sb.append(", budgetType=").append(budgetType);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", dataTime=").append(dataTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}