package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;

public class ReportIncomeProductTask extends LongIdEntity implements Serializable {
    private Long id;

    private Long executeId;

    private Long departmentId;

    private String departmentName;

    private Long productOrgId;

    private String productOrgName;

    private BigDecimal innerIncome;

    private BigDecimal outterIncome;

    private String month;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName == null ? null : departmentName.trim();
    }

    public Long getProductOrgId() {
        return productOrgId;
    }

    public void setProductOrgId(Long productOrgId) {
        this.productOrgId = productOrgId;
    }

    public String getProductOrgName() {
        return productOrgName;
    }

    public void setProductOrgName(String productOrgName) {
        this.productOrgName = productOrgName == null ? null : productOrgName.trim();
    }

    public BigDecimal getInnerIncome() {
        return innerIncome;
    }

    public void setInnerIncome(BigDecimal innerIncome) {
        this.innerIncome = innerIncome;
    }

    public BigDecimal getOutterIncome() {
        return outterIncome;
    }

    public void setOutterIncome(BigDecimal outterIncome) {
        this.outterIncome = outterIncome;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month == null ? null : month.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", executeId=").append(executeId);
        sb.append(", departmentId=").append(departmentId);
        sb.append(", departmentName=").append(departmentName);
        sb.append(", productOrgId=").append(productOrgId);
        sb.append(", productOrgName=").append(productOrgName);
        sb.append(", innerIncome=").append(innerIncome);
        sb.append(", outterIncome=").append(outterIncome);
        sb.append(", month=").append(month);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}