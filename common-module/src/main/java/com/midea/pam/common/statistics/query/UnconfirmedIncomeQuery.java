package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/18
 */
@ApiModel(value = "UnconfirmedIncomeQuery", description = "销售合同剩余待确认收入明细报表")
public class UnconfirmedIncomeQuery {

    private Long id;

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    @ApiModelProperty(value = "销售部门id列表")
    private List<Long> unitIdList;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "客户名称或CRM编码")
    private String customerName;

    @ApiModelProperty(value = "业务实体id列表")
    private List<Long> ouIdList;

    /**
     * 当年年初
     */
    private Date curYear;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public List<Long> getUnitIdList() {
        return unitIdList;
    }

    public void setUnitIdList(List<Long> unitIdList) {
        this.unitIdList = unitIdList;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public List<Long> getOuIdList() {
        return ouIdList;
    }

    public void setOuIdList(List<Long> ouIdList) {
        this.ouIdList = ouIdList;
    }

    public Date getCurYear() {
        return curYear;
    }

    public void setCurYear(Date curYear) {
        this.curYear = curYear;
    }

    @Override
    public String toString() {
        return "UnconfirmedIncomeQuery{" +
                "id=" + id +
                ", executeId=" + executeId +
                ", unitIdList=" + unitIdList +
                ", customerId=" + customerId +
                ", customerName='" + customerName + '\'' +
                ", ouIdList=" + ouIdList +
                ", curYear=" + curYear +
                '}';
    }
}
