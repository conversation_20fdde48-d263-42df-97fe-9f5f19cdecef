package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Description
 * Created by chenchong
 * Date 2022/09/05 16:57
 */
@Setter
@Getter
public class ReportPurchaseProgressExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer number;

    @Excel(name = "需求类型", width = 15, replace = {"非整包需求_1", "整包需求_2"})
    private Integer demandType;

    @Excel(name = "需求发布单号", width = 25)
    private String requirementCode;

    @Excel(name = "需求发布审批通过日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date approvedTime;

    @Excel(name = "采购需求状态", width = 15, replace = {"待下达_0", "已下达_1", "已关闭_2"})
    private Integer demandStatus;

    @Excel(name = "wbs编码", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "物料编码-PAM编码", width = 20)
    private String pamCode;

    @Excel(name = "物料编码-ERP编码", width = 20)
    private String erpCode;

    @Excel(name = "物料描述", width = 30)
    private String materielDescr;

    @Excel(name = "物料品牌", width = 15)
    private String brand;

    @Excel(name = "图号/型号", width = 15)
    private String model;

    @Excel(name = "物料计量单位", width = 15)
    private String unit;

    @Excel(name = "物料需求总量", width = 15)
    private BigDecimal needTotal;

    @Excel(name = "待下达数量", width = 15)
    private BigDecimal unreleasedQuantity;

    @Excel(name = "已下单数量", width = 15)
    private BigDecimal releasedQuantity;

    @Excel(name = "已关闭需求数量", width = 15)
    private BigDecimal closedQuantity;

    @Excel(name = "需求日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;

    @Excel(name = "活动事项编码", width = 15)
    private String activityCode;

    @Excel(name = "详设发布批次号", width = 15)
    private String designReleaseLotNumber;

    @Excel(name = "设计人员", width = 15)
    private String producerName;

    @Excel(name = "物料类别-大类", width = 20)
    private String materialClassification;

    @Excel(name = "物料类别-中类", width = 20)
    private String codingMiddleclass;

    @Excel(name = "物料类别-小类", width = 20)
    private String materialType;

    @Excel(name = "订单号/合同号", width = 30)
    private String codes;

    @Excel(name = "采购员", width = 15)
    private String buyer;

    @Excel(name = "最迟PO承诺交期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date lastPoDelivery;

    @Excel(name = "最新PO接收时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date newestPoReceive;

    @Excel(name = "最新PO入库时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date newestPoWarehousing;

    @Excel(name = "累计接收数量", width = 15)
    private BigDecimal receiveQuantity;

    @Excel(name = "累计入库数量", width = 15)
    private BigDecimal warehousingQuantity;

    @Excel(name = "未送货数量", width = 15)
    private BigDecimal notDeliveryQuantity;

    @Excel(name = "累计开票数量", width = 15)
    private BigDecimal invoicingQuantity;

    @Excel(name = "累计未开票数量", width = 15)
    private BigDecimal notInvoicingQuantity;

    @Excel(name = "项目编号", width = 15)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "项目类型", width = 25)
    private String projectType;

    @Excel(name = "项目业务分类", width = 15)
    private String projectSort;

    @Excel(name = "项目经理", width = 15)
    private String projectManager;

}
