package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: common-module
 * @description: 实际成本归集汇总导出实体
 * @author:zhongpeng
 * @create:2020-03-24 15:34
 **/
@Getter
@Setter
public class CostCollectionStatExcelVO {
    @Excel(name = "序号", width = 10)
    private Integer num;

    @Excel(name = "归集日期", width = 20, format = "yyyy-MM-dd")
    private Date collectionDate;

    @Excel(name = "成本发生日期", width = 20, format = "yyyy-MM-dd")
    private Date costDate;

    @Excel(name = "结转状态", width = 20, replace = {"未结转_0", "已结转_1", "无需入账_2", "未结转_null"})
    private Integer carryStatus;

    @Excel(name = "结转期间", width = 20)
    private String glPeriod;

    @Excel(name = "项目编号", width = 30)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "项目类型", width = 30)
    private String projectType;

    @Excel(name = "物料成本", width = 20)
    private BigDecimal materialActualCost;

    @Excel(name = "物料外包成本", width = 20)
    private BigDecimal materialOutsourceCost;

    @Excel(name = "材料扣罚", width = 20)
    private BigDecimal materialPenaltyCost;

    @Excel(name = "物料差异成本", width = 20)
    private BigDecimal materialDifferenceCost;

    @Excel(name = "人力成本(内部)", width = 20)
    private BigDecimal innerLaborCost;

    @Excel(name = "人力成本(外部)", width = 20)
    private BigDecimal outerLaborCost;

    @Excel(name = "费用成本", width = 20)
    private BigDecimal feeCost;

    @Excel(name = "资产折旧成本", width = 20)
    private BigDecimal assetDeprnCost;

    @Excel(name = "结转单号", width = 20)
    private String billNum;

    @Excel(name = "币种", width = 20)
    private String currency;

    @Excel(name = "业务分类", width = 30)
    private String typeName;

    @Excel(name = "业务实体", width = 30)
    private String ouName;

}
