package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class ProjectCostGetreturnMaterialRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectCostGetreturnMaterialRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdIsNull() {
            addCriterion("summary_id is null");
            return (Criteria) this;
        }

        public Criteria andSummaryIdIsNotNull() {
            addCriterion("summary_id is not null");
            return (Criteria) this;
        }

        public Criteria andSummaryIdEqualTo(Long value) {
            addCriterion("summary_id =", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdNotEqualTo(Long value) {
            addCriterion("summary_id <>", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdGreaterThan(Long value) {
            addCriterion("summary_id >", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("summary_id >=", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdLessThan(Long value) {
            addCriterion("summary_id <", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdLessThanOrEqualTo(Long value) {
            addCriterion("summary_id <=", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdIn(List<Long> values) {
            addCriterion("summary_id in", values, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdNotIn(List<Long> values) {
            addCriterion("summary_id not in", values, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdBetween(Long value1, Long value2) {
            addCriterion("summary_id between", value1, value2, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdNotBetween(Long value1, Long value2) {
            addCriterion("summary_id not between", value1, value2, "summaryId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNull() {
            addCriterion("erp_code is null");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNotNull() {
            addCriterion("erp_code is not null");
            return (Criteria) this;
        }

        public Criteria andErpCodeEqualTo(String value) {
            addCriterion("erp_code =", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotEqualTo(String value) {
            addCriterion("erp_code <>", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThan(String value) {
            addCriterion("erp_code >", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("erp_code >=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThan(String value) {
            addCriterion("erp_code <", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThanOrEqualTo(String value) {
            addCriterion("erp_code <=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLike(String value) {
            addCriterion("erp_code like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotLike(String value) {
            addCriterion("erp_code not like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIn(List<String> values) {
            addCriterion("erp_code in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotIn(List<String> values) {
            addCriterion("erp_code not in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeBetween(String value1, String value2) {
            addCriterion("erp_code between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotBetween(String value1, String value2) {
            addCriterion("erp_code not between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andItemInfoIsNull() {
            addCriterion("item_info is null");
            return (Criteria) this;
        }

        public Criteria andItemInfoIsNotNull() {
            addCriterion("item_info is not null");
            return (Criteria) this;
        }

        public Criteria andItemInfoEqualTo(String value) {
            addCriterion("item_info =", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoNotEqualTo(String value) {
            addCriterion("item_info <>", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoGreaterThan(String value) {
            addCriterion("item_info >", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoGreaterThanOrEqualTo(String value) {
            addCriterion("item_info >=", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoLessThan(String value) {
            addCriterion("item_info <", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoLessThanOrEqualTo(String value) {
            addCriterion("item_info <=", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoLike(String value) {
            addCriterion("item_info like", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoNotLike(String value) {
            addCriterion("item_info not like", value, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoIn(List<String> values) {
            addCriterion("item_info in", values, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoNotIn(List<String> values) {
            addCriterion("item_info not in", values, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoBetween(String value1, String value2) {
            addCriterion("item_info between", value1, value2, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andItemInfoNotBetween(String value1, String value2) {
            addCriterion("item_info not between", value1, value2, "itemInfo");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andTotalApplyAmountIsNull() {
            addCriterion("total_apply_amount is null");
            return (Criteria) this;
        }

        public Criteria andTotalApplyAmountIsNotNull() {
            addCriterion("total_apply_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTotalApplyAmountEqualTo(BigDecimal value) {
            addCriterion("total_apply_amount =", value, "totalApplyAmount");
            return (Criteria) this;
        }

        public Criteria andTotalApplyAmountNotEqualTo(BigDecimal value) {
            addCriterion("total_apply_amount <>", value, "totalApplyAmount");
            return (Criteria) this;
        }

        public Criteria andTotalApplyAmountGreaterThan(BigDecimal value) {
            addCriterion("total_apply_amount >", value, "totalApplyAmount");
            return (Criteria) this;
        }

        public Criteria andTotalApplyAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_apply_amount >=", value, "totalApplyAmount");
            return (Criteria) this;
        }

        public Criteria andTotalApplyAmountLessThan(BigDecimal value) {
            addCriterion("total_apply_amount <", value, "totalApplyAmount");
            return (Criteria) this;
        }

        public Criteria andTotalApplyAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_apply_amount <=", value, "totalApplyAmount");
            return (Criteria) this;
        }

        public Criteria andTotalApplyAmountIn(List<BigDecimal> values) {
            addCriterion("total_apply_amount in", values, "totalApplyAmount");
            return (Criteria) this;
        }

        public Criteria andTotalApplyAmountNotIn(List<BigDecimal> values) {
            addCriterion("total_apply_amount not in", values, "totalApplyAmount");
            return (Criteria) this;
        }

        public Criteria andTotalApplyAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_apply_amount between", value1, value2, "totalApplyAmount");
            return (Criteria) this;
        }

        public Criteria andTotalApplyAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_apply_amount not between", value1, value2, "totalApplyAmount");
            return (Criteria) this;
        }

        public Criteria andTotalActualAmountIsNull() {
            addCriterion("total_actual_amount is null");
            return (Criteria) this;
        }

        public Criteria andTotalActualAmountIsNotNull() {
            addCriterion("total_actual_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTotalActualAmountEqualTo(BigDecimal value) {
            addCriterion("total_actual_amount =", value, "totalActualAmount");
            return (Criteria) this;
        }

        public Criteria andTotalActualAmountNotEqualTo(BigDecimal value) {
            addCriterion("total_actual_amount <>", value, "totalActualAmount");
            return (Criteria) this;
        }

        public Criteria andTotalActualAmountGreaterThan(BigDecimal value) {
            addCriterion("total_actual_amount >", value, "totalActualAmount");
            return (Criteria) this;
        }

        public Criteria andTotalActualAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_actual_amount >=", value, "totalActualAmount");
            return (Criteria) this;
        }

        public Criteria andTotalActualAmountLessThan(BigDecimal value) {
            addCriterion("total_actual_amount <", value, "totalActualAmount");
            return (Criteria) this;
        }

        public Criteria andTotalActualAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_actual_amount <=", value, "totalActualAmount");
            return (Criteria) this;
        }

        public Criteria andTotalActualAmountIn(List<BigDecimal> values) {
            addCriterion("total_actual_amount in", values, "totalActualAmount");
            return (Criteria) this;
        }

        public Criteria andTotalActualAmountNotIn(List<BigDecimal> values) {
            addCriterion("total_actual_amount not in", values, "totalActualAmount");
            return (Criteria) this;
        }

        public Criteria andTotalActualAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_actual_amount between", value1, value2, "totalActualAmount");
            return (Criteria) this;
        }

        public Criteria andTotalActualAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_actual_amount not between", value1, value2, "totalActualAmount");
            return (Criteria) this;
        }

        public Criteria andGetReturnTimeIsNull() {
            addCriterion("get_return_time is null");
            return (Criteria) this;
        }

        public Criteria andGetReturnTimeIsNotNull() {
            addCriterion("get_return_time is not null");
            return (Criteria) this;
        }

        public Criteria andGetReturnTimeEqualTo(Date value) {
            addCriterionForJDBCDate("get_return_time =", value, "getReturnTime");
            return (Criteria) this;
        }

        public Criteria andGetReturnTimeNotEqualTo(Date value) {
            addCriterionForJDBCDate("get_return_time <>", value, "getReturnTime");
            return (Criteria) this;
        }

        public Criteria andGetReturnTimeGreaterThan(Date value) {
            addCriterionForJDBCDate("get_return_time >", value, "getReturnTime");
            return (Criteria) this;
        }

        public Criteria andGetReturnTimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("get_return_time >=", value, "getReturnTime");
            return (Criteria) this;
        }

        public Criteria andGetReturnTimeLessThan(Date value) {
            addCriterionForJDBCDate("get_return_time <", value, "getReturnTime");
            return (Criteria) this;
        }

        public Criteria andGetReturnTimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("get_return_time <=", value, "getReturnTime");
            return (Criteria) this;
        }

        public Criteria andGetReturnTimeIn(List<Date> values) {
            addCriterionForJDBCDate("get_return_time in", values, "getReturnTime");
            return (Criteria) this;
        }

        public Criteria andGetReturnTimeNotIn(List<Date> values) {
            addCriterionForJDBCDate("get_return_time not in", values, "getReturnTime");
            return (Criteria) this;
        }

        public Criteria andGetReturnTimeBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("get_return_time between", value1, value2, "getReturnTime");
            return (Criteria) this;
        }

        public Criteria andGetReturnTimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("get_return_time not between", value1, value2, "getReturnTime");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNull() {
            addCriterion("order_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNotNull() {
            addCriterion("order_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualTo(String value) {
            addCriterion("order_type =", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualTo(String value) {
            addCriterion("order_type <>", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThan(String value) {
            addCriterion("order_type >", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualTo(String value) {
            addCriterion("order_type >=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThan(String value) {
            addCriterion("order_type <", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualTo(String value) {
            addCriterion("order_type <=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLike(String value) {
            addCriterion("order_type like", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotLike(String value) {
            addCriterion("order_type not like", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIn(List<String> values) {
            addCriterion("order_type in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotIn(List<String> values) {
            addCriterion("order_type not in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeBetween(String value1, String value2) {
            addCriterion("order_type between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotBetween(String value1, String value2) {
            addCriterion("order_type not between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andGetReturnCodeIsNull() {
            addCriterion("get_return_code is null");
            return (Criteria) this;
        }

        public Criteria andGetReturnCodeIsNotNull() {
            addCriterion("get_return_code is not null");
            return (Criteria) this;
        }

        public Criteria andGetReturnCodeEqualTo(String value) {
            addCriterion("get_return_code =", value, "getReturnCode");
            return (Criteria) this;
        }

        public Criteria andGetReturnCodeNotEqualTo(String value) {
            addCriterion("get_return_code <>", value, "getReturnCode");
            return (Criteria) this;
        }

        public Criteria andGetReturnCodeGreaterThan(String value) {
            addCriterion("get_return_code >", value, "getReturnCode");
            return (Criteria) this;
        }

        public Criteria andGetReturnCodeGreaterThanOrEqualTo(String value) {
            addCriterion("get_return_code >=", value, "getReturnCode");
            return (Criteria) this;
        }

        public Criteria andGetReturnCodeLessThan(String value) {
            addCriterion("get_return_code <", value, "getReturnCode");
            return (Criteria) this;
        }

        public Criteria andGetReturnCodeLessThanOrEqualTo(String value) {
            addCriterion("get_return_code <=", value, "getReturnCode");
            return (Criteria) this;
        }

        public Criteria andGetReturnCodeLike(String value) {
            addCriterion("get_return_code like", value, "getReturnCode");
            return (Criteria) this;
        }

        public Criteria andGetReturnCodeNotLike(String value) {
            addCriterion("get_return_code not like", value, "getReturnCode");
            return (Criteria) this;
        }

        public Criteria andGetReturnCodeIn(List<String> values) {
            addCriterion("get_return_code in", values, "getReturnCode");
            return (Criteria) this;
        }

        public Criteria andGetReturnCodeNotIn(List<String> values) {
            addCriterion("get_return_code not in", values, "getReturnCode");
            return (Criteria) this;
        }

        public Criteria andGetReturnCodeBetween(String value1, String value2) {
            addCriterion("get_return_code between", value1, value2, "getReturnCode");
            return (Criteria) this;
        }

        public Criteria andGetReturnCodeNotBetween(String value1, String value2) {
            addCriterion("get_return_code not between", value1, value2, "getReturnCode");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andMaterialCostIsNull() {
            addCriterion("material_cost is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCostIsNotNull() {
            addCriterion("material_cost is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCostEqualTo(BigDecimal value) {
            addCriterion("material_cost =", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostNotEqualTo(BigDecimal value) {
            addCriterion("material_cost <>", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostGreaterThan(BigDecimal value) {
            addCriterion("material_cost >", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_cost >=", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostLessThan(BigDecimal value) {
            addCriterion("material_cost <", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_cost <=", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostIn(List<BigDecimal> values) {
            addCriterion("material_cost in", values, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostNotIn(List<BigDecimal> values) {
            addCriterion("material_cost not in", values, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_cost between", value1, value2, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_cost not between", value1, value2, "materialCost");
            return (Criteria) this;
        }

        public Criteria andGetReturnAmountIsNull() {
            addCriterion("get_return_amount is null");
            return (Criteria) this;
        }

        public Criteria andGetReturnAmountIsNotNull() {
            addCriterion("get_return_amount is not null");
            return (Criteria) this;
        }

        public Criteria andGetReturnAmountEqualTo(BigDecimal value) {
            addCriterion("get_return_amount =", value, "getReturnAmount");
            return (Criteria) this;
        }

        public Criteria andGetReturnAmountNotEqualTo(BigDecimal value) {
            addCriterion("get_return_amount <>", value, "getReturnAmount");
            return (Criteria) this;
        }

        public Criteria andGetReturnAmountGreaterThan(BigDecimal value) {
            addCriterion("get_return_amount >", value, "getReturnAmount");
            return (Criteria) this;
        }

        public Criteria andGetReturnAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("get_return_amount >=", value, "getReturnAmount");
            return (Criteria) this;
        }

        public Criteria andGetReturnAmountLessThan(BigDecimal value) {
            addCriterion("get_return_amount <", value, "getReturnAmount");
            return (Criteria) this;
        }

        public Criteria andGetReturnAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("get_return_amount <=", value, "getReturnAmount");
            return (Criteria) this;
        }

        public Criteria andGetReturnAmountIn(List<BigDecimal> values) {
            addCriterion("get_return_amount in", values, "getReturnAmount");
            return (Criteria) this;
        }

        public Criteria andGetReturnAmountNotIn(List<BigDecimal> values) {
            addCriterion("get_return_amount not in", values, "getReturnAmount");
            return (Criteria) this;
        }

        public Criteria andGetReturnAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("get_return_amount between", value1, value2, "getReturnAmount");
            return (Criteria) this;
        }

        public Criteria andGetReturnAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("get_return_amount not between", value1, value2, "getReturnAmount");
            return (Criteria) this;
        }

        public Criteria andActualGetReturnAccountIsNull() {
            addCriterion("actual_get_return_account is null");
            return (Criteria) this;
        }

        public Criteria andActualGetReturnAccountIsNotNull() {
            addCriterion("actual_get_return_account is not null");
            return (Criteria) this;
        }

        public Criteria andActualGetReturnAccountEqualTo(BigDecimal value) {
            addCriterion("actual_get_return_account =", value, "actualGetReturnAccount");
            return (Criteria) this;
        }

        public Criteria andActualGetReturnAccountNotEqualTo(BigDecimal value) {
            addCriterion("actual_get_return_account <>", value, "actualGetReturnAccount");
            return (Criteria) this;
        }

        public Criteria andActualGetReturnAccountGreaterThan(BigDecimal value) {
            addCriterion("actual_get_return_account >", value, "actualGetReturnAccount");
            return (Criteria) this;
        }

        public Criteria andActualGetReturnAccountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_get_return_account >=", value, "actualGetReturnAccount");
            return (Criteria) this;
        }

        public Criteria andActualGetReturnAccountLessThan(BigDecimal value) {
            addCriterion("actual_get_return_account <", value, "actualGetReturnAccount");
            return (Criteria) this;
        }

        public Criteria andActualGetReturnAccountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_get_return_account <=", value, "actualGetReturnAccount");
            return (Criteria) this;
        }

        public Criteria andActualGetReturnAccountIn(List<BigDecimal> values) {
            addCriterion("actual_get_return_account in", values, "actualGetReturnAccount");
            return (Criteria) this;
        }

        public Criteria andActualGetReturnAccountNotIn(List<BigDecimal> values) {
            addCriterion("actual_get_return_account not in", values, "actualGetReturnAccount");
            return (Criteria) this;
        }

        public Criteria andActualGetReturnAccountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_get_return_account between", value1, value2, "actualGetReturnAccount");
            return (Criteria) this;
        }

        public Criteria andActualGetReturnAccountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_get_return_account not between", value1, value2, "actualGetReturnAccount");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullIsNull() {
            addCriterion("item_cost_is_null is null");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullIsNotNull() {
            addCriterion("item_cost_is_null is not null");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullEqualTo(Integer value) {
            addCriterion("item_cost_is_null =", value, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullNotEqualTo(Integer value) {
            addCriterion("item_cost_is_null <>", value, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullGreaterThan(Integer value) {
            addCriterion("item_cost_is_null >", value, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullGreaterThanOrEqualTo(Integer value) {
            addCriterion("item_cost_is_null >=", value, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullLessThan(Integer value) {
            addCriterion("item_cost_is_null <", value, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullLessThanOrEqualTo(Integer value) {
            addCriterion("item_cost_is_null <=", value, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullIn(List<Integer> values) {
            addCriterion("item_cost_is_null in", values, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullNotIn(List<Integer> values) {
            addCriterion("item_cost_is_null not in", values, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullBetween(Integer value1, Integer value2) {
            addCriterion("item_cost_is_null between", value1, value2, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullNotBetween(Integer value1, Integer value2) {
            addCriterion("item_cost_is_null not between", value1, value2, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("source is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("source is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(String value) {
            addCriterion("source =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(String value) {
            addCriterion("source <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(String value) {
            addCriterion("source >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(String value) {
            addCriterion("source >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(String value) {
            addCriterion("source <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(String value) {
            addCriterion("source <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLike(String value) {
            addCriterion("source like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotLike(String value) {
            addCriterion("source not like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<String> values) {
            addCriterion("source in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<String> values) {
            addCriterion("source not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(String value1, String value2) {
            addCriterion("source between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(String value1, String value2) {
            addCriterion("source not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andTransitionIdIsNull() {
            addCriterion("transition_id is null");
            return (Criteria) this;
        }

        public Criteria andTransitionIdIsNotNull() {
            addCriterion("transition_id is not null");
            return (Criteria) this;
        }

        public Criteria andTransitionIdEqualTo(Long value) {
            addCriterion("transition_id =", value, "transitionId");
            return (Criteria) this;
        }

        public Criteria andTransitionIdNotEqualTo(Long value) {
            addCriterion("transition_id <>", value, "transitionId");
            return (Criteria) this;
        }

        public Criteria andTransitionIdGreaterThan(Long value) {
            addCriterion("transition_id >", value, "transitionId");
            return (Criteria) this;
        }

        public Criteria andTransitionIdGreaterThanOrEqualTo(Long value) {
            addCriterion("transition_id >=", value, "transitionId");
            return (Criteria) this;
        }

        public Criteria andTransitionIdLessThan(Long value) {
            addCriterion("transition_id <", value, "transitionId");
            return (Criteria) this;
        }

        public Criteria andTransitionIdLessThanOrEqualTo(Long value) {
            addCriterion("transition_id <=", value, "transitionId");
            return (Criteria) this;
        }

        public Criteria andTransitionIdIn(List<Long> values) {
            addCriterion("transition_id in", values, "transitionId");
            return (Criteria) this;
        }

        public Criteria andTransitionIdNotIn(List<Long> values) {
            addCriterion("transition_id not in", values, "transitionId");
            return (Criteria) this;
        }

        public Criteria andTransitionIdBetween(Long value1, Long value2) {
            addCriterion("transition_id between", value1, value2, "transitionId");
            return (Criteria) this;
        }

        public Criteria andTransitionIdNotBetween(Long value1, Long value2) {
            addCriterion("transition_id not between", value1, value2, "transitionId");
            return (Criteria) this;
        }

        public Criteria andHeadRemarkIsNull() {
            addCriterion("head_remark is null");
            return (Criteria) this;
        }

        public Criteria andHeadRemarkIsNotNull() {
            addCriterion("head_remark is not null");
            return (Criteria) this;
        }

        public Criteria andHeadRemarkEqualTo(String value) {
            addCriterion("head_remark =", value, "headRemark");
            return (Criteria) this;
        }

        public Criteria andHeadRemarkNotEqualTo(String value) {
            addCriterion("head_remark <>", value, "headRemark");
            return (Criteria) this;
        }

        public Criteria andHeadRemarkGreaterThan(String value) {
            addCriterion("head_remark >", value, "headRemark");
            return (Criteria) this;
        }

        public Criteria andHeadRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("head_remark >=", value, "headRemark");
            return (Criteria) this;
        }

        public Criteria andHeadRemarkLessThan(String value) {
            addCriterion("head_remark <", value, "headRemark");
            return (Criteria) this;
        }

        public Criteria andHeadRemarkLessThanOrEqualTo(String value) {
            addCriterion("head_remark <=", value, "headRemark");
            return (Criteria) this;
        }

        public Criteria andHeadRemarkLike(String value) {
            addCriterion("head_remark like", value, "headRemark");
            return (Criteria) this;
        }

        public Criteria andHeadRemarkNotLike(String value) {
            addCriterion("head_remark not like", value, "headRemark");
            return (Criteria) this;
        }

        public Criteria andHeadRemarkIn(List<String> values) {
            addCriterion("head_remark in", values, "headRemark");
            return (Criteria) this;
        }

        public Criteria andHeadRemarkNotIn(List<String> values) {
            addCriterion("head_remark not in", values, "headRemark");
            return (Criteria) this;
        }

        public Criteria andHeadRemarkBetween(String value1, String value2) {
            addCriterion("head_remark between", value1, value2, "headRemark");
            return (Criteria) this;
        }

        public Criteria andHeadRemarkNotBetween(String value1, String value2) {
            addCriterion("head_remark not between", value1, value2, "headRemark");
            return (Criteria) this;
        }

        public Criteria andDetailRemarkIsNull() {
            addCriterion("detail_remark is null");
            return (Criteria) this;
        }

        public Criteria andDetailRemarkIsNotNull() {
            addCriterion("detail_remark is not null");
            return (Criteria) this;
        }

        public Criteria andDetailRemarkEqualTo(String value) {
            addCriterion("detail_remark =", value, "detailRemark");
            return (Criteria) this;
        }

        public Criteria andDetailRemarkNotEqualTo(String value) {
            addCriterion("detail_remark <>", value, "detailRemark");
            return (Criteria) this;
        }

        public Criteria andDetailRemarkGreaterThan(String value) {
            addCriterion("detail_remark >", value, "detailRemark");
            return (Criteria) this;
        }

        public Criteria andDetailRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("detail_remark >=", value, "detailRemark");
            return (Criteria) this;
        }

        public Criteria andDetailRemarkLessThan(String value) {
            addCriterion("detail_remark <", value, "detailRemark");
            return (Criteria) this;
        }

        public Criteria andDetailRemarkLessThanOrEqualTo(String value) {
            addCriterion("detail_remark <=", value, "detailRemark");
            return (Criteria) this;
        }

        public Criteria andDetailRemarkLike(String value) {
            addCriterion("detail_remark like", value, "detailRemark");
            return (Criteria) this;
        }

        public Criteria andDetailRemarkNotLike(String value) {
            addCriterion("detail_remark not like", value, "detailRemark");
            return (Criteria) this;
        }

        public Criteria andDetailRemarkIn(List<String> values) {
            addCriterion("detail_remark in", values, "detailRemark");
            return (Criteria) this;
        }

        public Criteria andDetailRemarkNotIn(List<String> values) {
            addCriterion("detail_remark not in", values, "detailRemark");
            return (Criteria) this;
        }

        public Criteria andDetailRemarkBetween(String value1, String value2) {
            addCriterion("detail_remark between", value1, value2, "detailRemark");
            return (Criteria) this;
        }

        public Criteria andDetailRemarkNotBetween(String value1, String value2) {
            addCriterion("detail_remark not between", value1, value2, "detailRemark");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}