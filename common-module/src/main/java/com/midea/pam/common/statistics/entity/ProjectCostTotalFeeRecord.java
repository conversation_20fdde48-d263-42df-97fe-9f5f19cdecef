package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目费用成本汇总-报表")
public class ProjectCostTotalFeeRecord extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "执行ID")
    private Long executeId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "费用类型ID")
    private Long feeItemId;

    @ApiModelProperty(value = "费用类型")
    private String feeItemName;

    @ApiModelProperty(value = "成本合计")
    private BigDecimal totalCost;

    @ApiModelProperty(value = "已发生成本")
    private BigDecimal incurredCost;

    @ApiModelProperty(value = "待处理费用")
    private BigDecimal pendingCost;

    @ApiModelProperty(value = "EA可用金额，取ems_budget_occupy_detail表中项目对应费用类型的overplus_ea_amount字段金额的汇总值")
    private BigDecimal eaAvailableAmount;

    @ApiModelProperty(value = "预算")
    private BigDecimal budget;

    @ApiModelProperty(value = "剩余预算")
    private BigDecimal remainderBudget;

    @ApiModelProperty(value = "已发生成本比例")
    private BigDecimal incurredRatio;

    @ApiModelProperty(value = "年度预提汇总")
    private BigDecimal annualAccrual;

    @ApiModelProperty(value = "月度预提汇总")
    private BigDecimal monthlyAccrual;

    @ApiModelProperty(value = "类型 1：差旅；2：非差旅")
    private Integer type;

    @ApiModelProperty(value = "删除标示")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public Long getFeeItemId() {
        return feeItemId;
    }

    public void setFeeItemId(Long feeItemId) {
        this.feeItemId = feeItemId;
    }

    public String getFeeItemName() {
        return feeItemName;
    }

    public void setFeeItemName(String feeItemName) {
        this.feeItemName = feeItemName == null ? null : feeItemName.trim();
    }

    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    public BigDecimal getIncurredCost() {
        return incurredCost;
    }

    public void setIncurredCost(BigDecimal incurredCost) {
        this.incurredCost = incurredCost;
    }

    public BigDecimal getPendingCost() {
        return pendingCost;
    }

    public void setPendingCost(BigDecimal pendingCost) {
        this.pendingCost = pendingCost;
    }

    public BigDecimal getEaAvailableAmount() {
        return eaAvailableAmount;
    }

    public void setEaAvailableAmount(BigDecimal eaAvailableAmount) {
        this.eaAvailableAmount = eaAvailableAmount;
    }

    public BigDecimal getBudget() {
        return budget;
    }

    public void setBudget(BigDecimal budget) {
        this.budget = budget;
    }

    public BigDecimal getRemainderBudget() {
        return remainderBudget;
    }

    public void setRemainderBudget(BigDecimal remainderBudget) {
        this.remainderBudget = remainderBudget;
    }

    public BigDecimal getIncurredRatio() {
        return incurredRatio;
    }

    public void setIncurredRatio(BigDecimal incurredRatio) {
        this.incurredRatio = incurredRatio;
    }

    public BigDecimal getAnnualAccrual() {
        return annualAccrual;
    }

    public ProjectCostTotalFeeRecord setAnnualAccrual(BigDecimal annualAccrual) {
        this.annualAccrual = annualAccrual;
        return this;
    }

    public BigDecimal getMonthlyAccrual() {
        return monthlyAccrual;
    }

    public ProjectCostTotalFeeRecord setMonthlyAccrual(BigDecimal monthlyAccrual) {
        this.monthlyAccrual = monthlyAccrual;
        return this;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }



    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", executeId=").append(executeId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", feeItemId=").append(feeItemId);
        sb.append(", feeItemName=").append(feeItemName);
        sb.append(", totalCost=").append(totalCost);
        sb.append(", incurredCost=").append(incurredCost);
        sb.append(", pendingCost=").append(pendingCost);
        sb.append(", eaAvailableAmount=").append(eaAvailableAmount);
        sb.append(", budget=").append(budget);
        sb.append(", remainderBudget=").append(remainderBudget);
        sb.append(", incurredRatio=").append(incurredRatio);
        sb.append(", annualAccrual=").append(annualAccrual);
        sb.append(", monthlyAccrual=").append(monthlyAccrual);
        sb.append(", type=").append(type);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}