package com.midea.pam.common.statistics.excelVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/22
 * @description
 */
public class ReportIncomeCalculateExcelVO {

    private String keyword;

    private Long departmentId;

    private String departmentName;

    private Long productOrgId;

    private String productOrgName;

    private BigDecimal innerIncome;

    private BigDecimal outterIncome;

    private String month;

    private String monthStr;

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Long getProductOrgId() {
        return productOrgId;
    }

    public void setProductOrgId(Long productOrgId) {
        this.productOrgId = productOrgId;
    }

    public String getProductOrgName() {
        return productOrgName;
    }

    public void setProductOrgName(String productOrgName) {
        this.productOrgName = productOrgName;
    }

    public BigDecimal getInnerIncome() {
        return innerIncome;
    }

    public void setInnerIncome(BigDecimal innerIncome) {
        this.innerIncome = innerIncome;
    }

    public BigDecimal getOutterIncome() {
        return outterIncome;
    }

    public void setOutterIncome(BigDecimal outterIncome) {
        this.outterIncome = outterIncome;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getMonthStr() {
        return monthStr;
    }

    public void setMonthStr(String monthStr) {
        this.monthStr = monthStr;
    }
}

