package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ReportUnconfirmedIncomeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportUnconfirmedIncomeExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andContractIdIsNull() {
            addCriterion("contract_id is null");
            return (Criteria) this;
        }

        public Criteria andContractIdIsNotNull() {
            addCriterion("contract_id is not null");
            return (Criteria) this;
        }

        public Criteria andContractIdEqualTo(Long value) {
            addCriterion("contract_id =", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdNotEqualTo(Long value) {
            addCriterion("contract_id <>", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdGreaterThan(Long value) {
            addCriterion("contract_id >", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdGreaterThanOrEqualTo(Long value) {
            addCriterion("contract_id >=", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdLessThan(Long value) {
            addCriterion("contract_id <", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdLessThanOrEqualTo(Long value) {
            addCriterion("contract_id <=", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdIn(List<Long> values) {
            addCriterion("contract_id in", values, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdNotIn(List<Long> values) {
            addCriterion("contract_id not in", values, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdBetween(Long value1, Long value2) {
            addCriterion("contract_id between", value1, value2, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdNotBetween(Long value1, Long value2) {
            addCriterion("contract_id not between", value1, value2, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNull() {
            addCriterion("contract_code is null");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNotNull() {
            addCriterion("contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andContractCodeEqualTo(String value) {
            addCriterion("contract_code =", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotEqualTo(String value) {
            addCriterion("contract_code <>", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThan(String value) {
            addCriterion("contract_code >", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_code >=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThan(String value) {
            addCriterion("contract_code <", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThanOrEqualTo(String value) {
            addCriterion("contract_code <=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLike(String value) {
            addCriterion("contract_code like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotLike(String value) {
            addCriterion("contract_code not like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeIn(List<String> values) {
            addCriterion("contract_code in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotIn(List<String> values) {
            addCriterion("contract_code not in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeBetween(String value1, String value2) {
            addCriterion("contract_code between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotBetween(String value1, String value2) {
            addCriterion("contract_code not between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractNameIsNull() {
            addCriterion("contract_name is null");
            return (Criteria) this;
        }

        public Criteria andContractNameIsNotNull() {
            addCriterion("contract_name is not null");
            return (Criteria) this;
        }

        public Criteria andContractNameEqualTo(String value) {
            addCriterion("contract_name =", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotEqualTo(String value) {
            addCriterion("contract_name <>", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThan(String value) {
            addCriterion("contract_name >", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThanOrEqualTo(String value) {
            addCriterion("contract_name >=", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameLessThan(String value) {
            addCriterion("contract_name <", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameLessThanOrEqualTo(String value) {
            addCriterion("contract_name <=", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameLike(String value) {
            addCriterion("contract_name like", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotLike(String value) {
            addCriterion("contract_name not like", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameIn(List<String> values) {
            addCriterion("contract_name in", values, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotIn(List<String> values) {
            addCriterion("contract_name not in", values, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameBetween(String value1, String value2) {
            addCriterion("contract_name between", value1, value2, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotBetween(String value1, String value2) {
            addCriterion("contract_name not between", value1, value2, "contractName");
            return (Criteria) this;
        }

        public Criteria andOrderYearIsNull() {
            addCriterion("order_year is null");
            return (Criteria) this;
        }

        public Criteria andOrderYearIsNotNull() {
            addCriterion("order_year is not null");
            return (Criteria) this;
        }

        public Criteria andOrderYearEqualTo(String value) {
            addCriterion("order_year =", value, "orderYear");
            return (Criteria) this;
        }

        public Criteria andOrderYearNotEqualTo(String value) {
            addCriterion("order_year <>", value, "orderYear");
            return (Criteria) this;
        }

        public Criteria andOrderYearGreaterThan(String value) {
            addCriterion("order_year >", value, "orderYear");
            return (Criteria) this;
        }

        public Criteria andOrderYearGreaterThanOrEqualTo(String value) {
            addCriterion("order_year >=", value, "orderYear");
            return (Criteria) this;
        }

        public Criteria andOrderYearLessThan(String value) {
            addCriterion("order_year <", value, "orderYear");
            return (Criteria) this;
        }

        public Criteria andOrderYearLessThanOrEqualTo(String value) {
            addCriterion("order_year <=", value, "orderYear");
            return (Criteria) this;
        }

        public Criteria andOrderYearLike(String value) {
            addCriterion("order_year like", value, "orderYear");
            return (Criteria) this;
        }

        public Criteria andOrderYearNotLike(String value) {
            addCriterion("order_year not like", value, "orderYear");
            return (Criteria) this;
        }

        public Criteria andOrderYearIn(List<String> values) {
            addCriterion("order_year in", values, "orderYear");
            return (Criteria) this;
        }

        public Criteria andOrderYearNotIn(List<String> values) {
            addCriterion("order_year not in", values, "orderYear");
            return (Criteria) this;
        }

        public Criteria andOrderYearBetween(String value1, String value2) {
            addCriterion("order_year between", value1, value2, "orderYear");
            return (Criteria) this;
        }

        public Criteria andOrderYearNotBetween(String value1, String value2) {
            addCriterion("order_year not between", value1, value2, "orderYear");
            return (Criteria) this;
        }

        public Criteria andOrderMonthIsNull() {
            addCriterion("order_month is null");
            return (Criteria) this;
        }

        public Criteria andOrderMonthIsNotNull() {
            addCriterion("order_month is not null");
            return (Criteria) this;
        }

        public Criteria andOrderMonthEqualTo(String value) {
            addCriterion("order_month =", value, "orderMonth");
            return (Criteria) this;
        }

        public Criteria andOrderMonthNotEqualTo(String value) {
            addCriterion("order_month <>", value, "orderMonth");
            return (Criteria) this;
        }

        public Criteria andOrderMonthGreaterThan(String value) {
            addCriterion("order_month >", value, "orderMonth");
            return (Criteria) this;
        }

        public Criteria andOrderMonthGreaterThanOrEqualTo(String value) {
            addCriterion("order_month >=", value, "orderMonth");
            return (Criteria) this;
        }

        public Criteria andOrderMonthLessThan(String value) {
            addCriterion("order_month <", value, "orderMonth");
            return (Criteria) this;
        }

        public Criteria andOrderMonthLessThanOrEqualTo(String value) {
            addCriterion("order_month <=", value, "orderMonth");
            return (Criteria) this;
        }

        public Criteria andOrderMonthLike(String value) {
            addCriterion("order_month like", value, "orderMonth");
            return (Criteria) this;
        }

        public Criteria andOrderMonthNotLike(String value) {
            addCriterion("order_month not like", value, "orderMonth");
            return (Criteria) this;
        }

        public Criteria andOrderMonthIn(List<String> values) {
            addCriterion("order_month in", values, "orderMonth");
            return (Criteria) this;
        }

        public Criteria andOrderMonthNotIn(List<String> values) {
            addCriterion("order_month not in", values, "orderMonth");
            return (Criteria) this;
        }

        public Criteria andOrderMonthBetween(String value1, String value2) {
            addCriterion("order_month between", value1, value2, "orderMonth");
            return (Criteria) this;
        }

        public Criteria andOrderMonthNotBetween(String value1, String value2) {
            addCriterion("order_month not between", value1, value2, "orderMonth");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Long value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Long value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Long value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Long value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Long> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Long> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Long value1, Long value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNull() {
            addCriterion("unit_name is null");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNotNull() {
            addCriterion("unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andUnitNameEqualTo(String value) {
            addCriterion("unit_name =", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotEqualTo(String value) {
            addCriterion("unit_name <>", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThan(String value) {
            addCriterion("unit_name >", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("unit_name >=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThan(String value) {
            addCriterion("unit_name <", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThanOrEqualTo(String value) {
            addCriterion("unit_name <=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLike(String value) {
            addCriterion("unit_name like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotLike(String value) {
            addCriterion("unit_name not like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIn(List<String> values) {
            addCriterion("unit_name in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotIn(List<String> values) {
            addCriterion("unit_name not in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameBetween(String value1, String value2) {
            addCriterion("unit_name between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotBetween(String value1, String value2) {
            addCriterion("unit_name not between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(Long value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(Long value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(Long value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(Long value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(Long value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<Long> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<Long> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(Long value1, Long value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(Long value1, Long value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNull() {
            addCriterion("customer_code is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNotNull() {
            addCriterion("customer_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeEqualTo(String value) {
            addCriterion("customer_code =", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotEqualTo(String value) {
            addCriterion("customer_code <>", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThan(String value) {
            addCriterion("customer_code >", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("customer_code >=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThan(String value) {
            addCriterion("customer_code <", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThanOrEqualTo(String value) {
            addCriterion("customer_code <=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLike(String value) {
            addCriterion("customer_code like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotLike(String value) {
            addCriterion("customer_code not like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIn(List<String> values) {
            addCriterion("customer_code in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotIn(List<String> values) {
            addCriterion("customer_code not in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBetween(String value1, String value2) {
            addCriterion("customer_code between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotBetween(String value1, String value2) {
            addCriterion("customer_code not between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeIsNull() {
            addCriterion("customer_type is null");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeIsNotNull() {
            addCriterion("customer_type is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeEqualTo(String value) {
            addCriterion("customer_type =", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeNotEqualTo(String value) {
            addCriterion("customer_type <>", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeGreaterThan(String value) {
            addCriterion("customer_type >", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeGreaterThanOrEqualTo(String value) {
            addCriterion("customer_type >=", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeLessThan(String value) {
            addCriterion("customer_type <", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeLessThanOrEqualTo(String value) {
            addCriterion("customer_type <=", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeLike(String value) {
            addCriterion("customer_type like", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeNotLike(String value) {
            addCriterion("customer_type not like", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeIn(List<String> values) {
            addCriterion("customer_type in", values, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeNotIn(List<String> values) {
            addCriterion("customer_type not in", values, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeBetween(String value1, String value2) {
            addCriterion("customer_type between", value1, value2, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeNotBetween(String value1, String value2) {
            addCriterion("customer_type not between", value1, value2, "customerType");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodOrderAmountIsNull() {
            addCriterion("initial_period_order_amount is null");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodOrderAmountIsNotNull() {
            addCriterion("initial_period_order_amount is not null");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodOrderAmountEqualTo(BigDecimal value) {
            addCriterion("initial_period_order_amount =", value, "initialPeriodOrderAmount");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodOrderAmountNotEqualTo(BigDecimal value) {
            addCriterion("initial_period_order_amount <>", value, "initialPeriodOrderAmount");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodOrderAmountGreaterThan(BigDecimal value) {
            addCriterion("initial_period_order_amount >", value, "initialPeriodOrderAmount");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodOrderAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("initial_period_order_amount >=", value, "initialPeriodOrderAmount");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodOrderAmountLessThan(BigDecimal value) {
            addCriterion("initial_period_order_amount <", value, "initialPeriodOrderAmount");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodOrderAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("initial_period_order_amount <=", value, "initialPeriodOrderAmount");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodOrderAmountIn(List<BigDecimal> values) {
            addCriterion("initial_period_order_amount in", values, "initialPeriodOrderAmount");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodOrderAmountNotIn(List<BigDecimal> values) {
            addCriterion("initial_period_order_amount not in", values, "initialPeriodOrderAmount");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodOrderAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("initial_period_order_amount between", value1, value2, "initialPeriodOrderAmount");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodOrderAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("initial_period_order_amount not between", value1, value2, "initialPeriodOrderAmount");
            return (Criteria) this;
        }

        public Criteria andHisYearConfirmedIncomeIsNull() {
            addCriterion("his_year_confirmed_income is null");
            return (Criteria) this;
        }

        public Criteria andHisYearConfirmedIncomeIsNotNull() {
            addCriterion("his_year_confirmed_income is not null");
            return (Criteria) this;
        }

        public Criteria andHisYearConfirmedIncomeEqualTo(BigDecimal value) {
            addCriterion("his_year_confirmed_income =", value, "hisYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andHisYearConfirmedIncomeNotEqualTo(BigDecimal value) {
            addCriterion("his_year_confirmed_income <>", value, "hisYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andHisYearConfirmedIncomeGreaterThan(BigDecimal value) {
            addCriterion("his_year_confirmed_income >", value, "hisYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andHisYearConfirmedIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("his_year_confirmed_income >=", value, "hisYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andHisYearConfirmedIncomeLessThan(BigDecimal value) {
            addCriterion("his_year_confirmed_income <", value, "hisYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andHisYearConfirmedIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("his_year_confirmed_income <=", value, "hisYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andHisYearConfirmedIncomeIn(List<BigDecimal> values) {
            addCriterion("his_year_confirmed_income in", values, "hisYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andHisYearConfirmedIncomeNotIn(List<BigDecimal> values) {
            addCriterion("his_year_confirmed_income not in", values, "hisYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andHisYearConfirmedIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("his_year_confirmed_income between", value1, value2, "hisYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andHisYearConfirmedIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("his_year_confirmed_income not between", value1, value2, "hisYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodObIsNull() {
            addCriterion("initial_period_ob is null");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodObIsNotNull() {
            addCriterion("initial_period_ob is not null");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodObEqualTo(BigDecimal value) {
            addCriterion("initial_period_ob =", value, "initialPeriodOb");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodObNotEqualTo(BigDecimal value) {
            addCriterion("initial_period_ob <>", value, "initialPeriodOb");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodObGreaterThan(BigDecimal value) {
            addCriterion("initial_period_ob >", value, "initialPeriodOb");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodObGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("initial_period_ob >=", value, "initialPeriodOb");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodObLessThan(BigDecimal value) {
            addCriterion("initial_period_ob <", value, "initialPeriodOb");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodObLessThanOrEqualTo(BigDecimal value) {
            addCriterion("initial_period_ob <=", value, "initialPeriodOb");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodObIn(List<BigDecimal> values) {
            addCriterion("initial_period_ob in", values, "initialPeriodOb");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodObNotIn(List<BigDecimal> values) {
            addCriterion("initial_period_ob not in", values, "initialPeriodOb");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodObBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("initial_period_ob between", value1, value2, "initialPeriodOb");
            return (Criteria) this;
        }

        public Criteria andInitialPeriodObNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("initial_period_ob not between", value1, value2, "initialPeriodOb");
            return (Criteria) this;
        }

        public Criteria andCurYearOrderAmountIsNull() {
            addCriterion("cur_year_order_amount is null");
            return (Criteria) this;
        }

        public Criteria andCurYearOrderAmountIsNotNull() {
            addCriterion("cur_year_order_amount is not null");
            return (Criteria) this;
        }

        public Criteria andCurYearOrderAmountEqualTo(BigDecimal value) {
            addCriterion("cur_year_order_amount =", value, "curYearOrderAmount");
            return (Criteria) this;
        }

        public Criteria andCurYearOrderAmountNotEqualTo(BigDecimal value) {
            addCriterion("cur_year_order_amount <>", value, "curYearOrderAmount");
            return (Criteria) this;
        }

        public Criteria andCurYearOrderAmountGreaterThan(BigDecimal value) {
            addCriterion("cur_year_order_amount >", value, "curYearOrderAmount");
            return (Criteria) this;
        }

        public Criteria andCurYearOrderAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cur_year_order_amount >=", value, "curYearOrderAmount");
            return (Criteria) this;
        }

        public Criteria andCurYearOrderAmountLessThan(BigDecimal value) {
            addCriterion("cur_year_order_amount <", value, "curYearOrderAmount");
            return (Criteria) this;
        }

        public Criteria andCurYearOrderAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cur_year_order_amount <=", value, "curYearOrderAmount");
            return (Criteria) this;
        }

        public Criteria andCurYearOrderAmountIn(List<BigDecimal> values) {
            addCriterion("cur_year_order_amount in", values, "curYearOrderAmount");
            return (Criteria) this;
        }

        public Criteria andCurYearOrderAmountNotIn(List<BigDecimal> values) {
            addCriterion("cur_year_order_amount not in", values, "curYearOrderAmount");
            return (Criteria) this;
        }

        public Criteria andCurYearOrderAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cur_year_order_amount between", value1, value2, "curYearOrderAmount");
            return (Criteria) this;
        }

        public Criteria andCurYearOrderAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cur_year_order_amount not between", value1, value2, "curYearOrderAmount");
            return (Criteria) this;
        }

        public Criteria andCurYearConfirmedIncomeIsNull() {
            addCriterion("cur_year_confirmed_income is null");
            return (Criteria) this;
        }

        public Criteria andCurYearConfirmedIncomeIsNotNull() {
            addCriterion("cur_year_confirmed_income is not null");
            return (Criteria) this;
        }

        public Criteria andCurYearConfirmedIncomeEqualTo(BigDecimal value) {
            addCriterion("cur_year_confirmed_income =", value, "curYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andCurYearConfirmedIncomeNotEqualTo(BigDecimal value) {
            addCriterion("cur_year_confirmed_income <>", value, "curYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andCurYearConfirmedIncomeGreaterThan(BigDecimal value) {
            addCriterion("cur_year_confirmed_income >", value, "curYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andCurYearConfirmedIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cur_year_confirmed_income >=", value, "curYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andCurYearConfirmedIncomeLessThan(BigDecimal value) {
            addCriterion("cur_year_confirmed_income <", value, "curYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andCurYearConfirmedIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cur_year_confirmed_income <=", value, "curYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andCurYearConfirmedIncomeIn(List<BigDecimal> values) {
            addCriterion("cur_year_confirmed_income in", values, "curYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andCurYearConfirmedIncomeNotIn(List<BigDecimal> values) {
            addCriterion("cur_year_confirmed_income not in", values, "curYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andCurYearConfirmedIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cur_year_confirmed_income between", value1, value2, "curYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andCurYearConfirmedIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cur_year_confirmed_income not between", value1, value2, "curYearConfirmedIncome");
            return (Criteria) this;
        }

        public Criteria andRemainingOrderAmountIsNull() {
            addCriterion("remaining_order_amount is null");
            return (Criteria) this;
        }

        public Criteria andRemainingOrderAmountIsNotNull() {
            addCriterion("remaining_order_amount is not null");
            return (Criteria) this;
        }

        public Criteria andRemainingOrderAmountEqualTo(BigDecimal value) {
            addCriterion("remaining_order_amount =", value, "remainingOrderAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingOrderAmountNotEqualTo(BigDecimal value) {
            addCriterion("remaining_order_amount <>", value, "remainingOrderAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingOrderAmountGreaterThan(BigDecimal value) {
            addCriterion("remaining_order_amount >", value, "remainingOrderAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingOrderAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("remaining_order_amount >=", value, "remainingOrderAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingOrderAmountLessThan(BigDecimal value) {
            addCriterion("remaining_order_amount <", value, "remainingOrderAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingOrderAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("remaining_order_amount <=", value, "remainingOrderAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingOrderAmountIn(List<BigDecimal> values) {
            addCriterion("remaining_order_amount in", values, "remainingOrderAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingOrderAmountNotIn(List<BigDecimal> values) {
            addCriterion("remaining_order_amount not in", values, "remainingOrderAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingOrderAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("remaining_order_amount between", value1, value2, "remainingOrderAmount");
            return (Criteria) this;
        }

        public Criteria andRemainingOrderAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("remaining_order_amount not between", value1, value2, "remainingOrderAmount");
            return (Criteria) this;
        }

        public Criteria andContractStatusIsNull() {
            addCriterion("contract_status is null");
            return (Criteria) this;
        }

        public Criteria andContractStatusIsNotNull() {
            addCriterion("contract_status is not null");
            return (Criteria) this;
        }

        public Criteria andContractStatusEqualTo(String value) {
            addCriterion("contract_status =", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusNotEqualTo(String value) {
            addCriterion("contract_status <>", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusGreaterThan(String value) {
            addCriterion("contract_status >", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusGreaterThanOrEqualTo(String value) {
            addCriterion("contract_status >=", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusLessThan(String value) {
            addCriterion("contract_status <", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusLessThanOrEqualTo(String value) {
            addCriterion("contract_status <=", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusLike(String value) {
            addCriterion("contract_status like", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusNotLike(String value) {
            addCriterion("contract_status not like", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusIn(List<String> values) {
            addCriterion("contract_status in", values, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusNotIn(List<String> values) {
            addCriterion("contract_status not in", values, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusBetween(String value1, String value2) {
            addCriterion("contract_status between", value1, value2, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusNotBetween(String value1, String value2) {
            addCriterion("contract_status not between", value1, value2, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameIsNull() {
            addCriterion("project_type_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameIsNotNull() {
            addCriterion("project_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameEqualTo(String value) {
            addCriterion("project_type_name =", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotEqualTo(String value) {
            addCriterion("project_type_name <>", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameGreaterThan(String value) {
            addCriterion("project_type_name >", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_type_name >=", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameLessThan(String value) {
            addCriterion("project_type_name <", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameLessThanOrEqualTo(String value) {
            addCriterion("project_type_name <=", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameLike(String value) {
            addCriterion("project_type_name like", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotLike(String value) {
            addCriterion("project_type_name not like", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameIn(List<String> values) {
            addCriterion("project_type_name in", values, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotIn(List<String> values) {
            addCriterion("project_type_name not in", values, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameBetween(String value1, String value2) {
            addCriterion("project_type_name between", value1, value2, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotBetween(String value1, String value2) {
            addCriterion("project_type_name not between", value1, value2, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNull() {
            addCriterion("project_status is null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNotNull() {
            addCriterion("project_status is not null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusEqualTo(String value) {
            addCriterion("project_status =", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotEqualTo(String value) {
            addCriterion("project_status <>", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThan(String value) {
            addCriterion("project_status >", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThanOrEqualTo(String value) {
            addCriterion("project_status >=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThan(String value) {
            addCriterion("project_status <", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThanOrEqualTo(String value) {
            addCriterion("project_status <=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLike(String value) {
            addCriterion("project_status like", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotLike(String value) {
            addCriterion("project_status not like", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIn(List<String> values) {
            addCriterion("project_status in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotIn(List<String> values) {
            addCriterion("project_status not in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusBetween(String value1, String value2) {
            addCriterion("project_status between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotBetween(String value1, String value2) {
            addCriterion("project_status not between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNull() {
            addCriterion("ou_name is null");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNotNull() {
            addCriterion("ou_name is not null");
            return (Criteria) this;
        }

        public Criteria andOuNameEqualTo(String value) {
            addCriterion("ou_name =", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotEqualTo(String value) {
            addCriterion("ou_name <>", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThan(String value) {
            addCriterion("ou_name >", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThanOrEqualTo(String value) {
            addCriterion("ou_name >=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThan(String value) {
            addCriterion("ou_name <", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThanOrEqualTo(String value) {
            addCriterion("ou_name <=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLike(String value) {
            addCriterion("ou_name like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotLike(String value) {
            addCriterion("ou_name not like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameIn(List<String> values) {
            addCriterion("ou_name in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotIn(List<String> values) {
            addCriterion("ou_name not in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameBetween(String value1, String value2) {
            addCriterion("ou_name between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotBetween(String value1, String value2) {
            addCriterion("ou_name not between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andTotalConfirmedIncomeAmountIsNull() {
            addCriterion("total_confirmed_income_amount is null");
            return (Criteria) this;
        }

        public Criteria andTotalConfirmedIncomeAmountIsNotNull() {
            addCriterion("total_confirmed_income_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTotalConfirmedIncomeAmountEqualTo(BigDecimal value) {
            addCriterion("total_confirmed_income_amount =", value, "totalConfirmedIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andTotalConfirmedIncomeAmountNotEqualTo(BigDecimal value) {
            addCriterion("total_confirmed_income_amount <>", value, "totalConfirmedIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andTotalConfirmedIncomeAmountGreaterThan(BigDecimal value) {
            addCriterion("total_confirmed_income_amount >", value, "totalConfirmedIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andTotalConfirmedIncomeAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_confirmed_income_amount >=", value, "totalConfirmedIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andTotalConfirmedIncomeAmountLessThan(BigDecimal value) {
            addCriterion("total_confirmed_income_amount <", value, "totalConfirmedIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andTotalConfirmedIncomeAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_confirmed_income_amount <=", value, "totalConfirmedIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andTotalConfirmedIncomeAmountIn(List<BigDecimal> values) {
            addCriterion("total_confirmed_income_amount in", values, "totalConfirmedIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andTotalConfirmedIncomeAmountNotIn(List<BigDecimal> values) {
            addCriterion("total_confirmed_income_amount not in", values, "totalConfirmedIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andTotalConfirmedIncomeAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_confirmed_income_amount between", value1, value2, "totalConfirmedIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andTotalConfirmedIncomeAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_confirmed_income_amount not between", value1, value2, "totalConfirmedIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andIncomeAchivementRateIsNull() {
            addCriterion("income_achivement_rate is null");
            return (Criteria) this;
        }

        public Criteria andIncomeAchivementRateIsNotNull() {
            addCriterion("income_achivement_rate is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeAchivementRateEqualTo(BigDecimal value) {
            addCriterion("income_achivement_rate =", value, "incomeAchivementRate");
            return (Criteria) this;
        }

        public Criteria andIncomeAchivementRateNotEqualTo(BigDecimal value) {
            addCriterion("income_achivement_rate <>", value, "incomeAchivementRate");
            return (Criteria) this;
        }

        public Criteria andIncomeAchivementRateGreaterThan(BigDecimal value) {
            addCriterion("income_achivement_rate >", value, "incomeAchivementRate");
            return (Criteria) this;
        }

        public Criteria andIncomeAchivementRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income_achivement_rate >=", value, "incomeAchivementRate");
            return (Criteria) this;
        }

        public Criteria andIncomeAchivementRateLessThan(BigDecimal value) {
            addCriterion("income_achivement_rate <", value, "incomeAchivementRate");
            return (Criteria) this;
        }

        public Criteria andIncomeAchivementRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income_achivement_rate <=", value, "incomeAchivementRate");
            return (Criteria) this;
        }

        public Criteria andIncomeAchivementRateIn(List<BigDecimal> values) {
            addCriterion("income_achivement_rate in", values, "incomeAchivementRate");
            return (Criteria) this;
        }

        public Criteria andIncomeAchivementRateNotIn(List<BigDecimal> values) {
            addCriterion("income_achivement_rate not in", values, "incomeAchivementRate");
            return (Criteria) this;
        }

        public Criteria andIncomeAchivementRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_achivement_rate between", value1, value2, "incomeAchivementRate");
            return (Criteria) this;
        }

        public Criteria andIncomeAchivementRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_achivement_rate not between", value1, value2, "incomeAchivementRate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}