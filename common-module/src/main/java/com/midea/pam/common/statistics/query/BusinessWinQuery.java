package com.midea.pam.common.statistics.query;

import com.midea.pam.common.basedata.dto.TeamUserDto;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.crm.dto.*;
import com.midea.pam.common.statistics.entity.ReportProjectDimension;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 创建时间 2020-5-8
 *
 * <AUTHOR>
 */
@ApiModel(value = "BusinessWinQuery", description = "销售赢单报表查询参数")
public class BusinessWinQuery {

    @ApiModelProperty(value = "赢单开始日期，不可空，只查找赢单日期大于或等于此日期的赢单")
    private Date winDateBegin;

    @ApiModelProperty(value = "赢单结束日期，不可空, 只查找赢单日期小于或等于此日期的赢单")
    private Date winDateEnd;

    @ApiModelProperty(value = "销售部门列表，不为空时，只查销售部门属于此列表中的赢单")
    private List<Long> saleDepartments;

    @ApiModelProperty(value = "事业部列表，不为空时，只查事业部属于此列表中的赢单")
    private List<Long> departments;

    @ApiModelProperty(value = "行业列表，不为空时，只查行业属于此列表中的赢单")
    private List<String> industryCodes;

    @ApiModelProperty(value = "合同生效开始日期，不可空，只查找合同生效日期大于或等于此日期的赢单")
    private Date contractDateBegin;

    @ApiModelProperty(value = "合同生效结束日期，不可空, 只查找合同生效日期小于或等于此日期的赢单")
    private Date contractDateEnd;

    @ApiModelProperty(value = "销售人员列表，不为空时，只查由此列表中的人员销售的赢单")
    private List<Long> salesmen;

    private Long personal;
    private Long companyId;
    private Long reportId;
    private Long executeId;
    private Long createBy;

    public Date getWinDateBegin() {
        return winDateBegin;
    }

    public void setWinDateBegin(Date winDateBegin) {
        this.winDateBegin = winDateBegin;
    }

    public Date getWinDateEnd() {
        return winDateEnd;
    }

    public void setWinDateEnd(Date winDateEnd) {
        this.winDateEnd = winDateEnd;
    }

    public List<Long> getSaleDepartments() {
        return saleDepartments;
    }

    public void setSaleDepartments(List<Long> saleDepartments) {
        this.saleDepartments = saleDepartments;
    }

    public List<Long> getDepartments() {
        return departments;
    }

    public void setDepartments(List<Long> departments) {
        this.departments = departments;
    }

    public List<String> getIndustryCodes() {
        return industryCodes;
    }

    public void setIndustryCodes(List<String> industryCodes) {
        this.industryCodes = industryCodes;
    }

    public Date getContractDateBegin() {
        return contractDateBegin;
    }

    public void setContractDateBegin(Date contractDateBegin) {
        this.contractDateBegin = contractDateBegin;
    }

    public Date getContractDateEnd() {
        return contractDateEnd;
    }

    public void setContractDateEnd(Date contractDateEnd) {
        this.contractDateEnd = contractDateEnd;
    }

    public List<Long> getSalesmen() {
        return salesmen;
    }

    public void setSalesmen(List<Long> salesmen) {
        this.salesmen = salesmen;
    }

    public Long getPersonal() {
        return personal;
    }

    public void setPersonal(Long personal) {
        this.personal = personal;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    @Override
    public String toString() {
        return "BusinessWinQuery{" +
                "winDateBegin=" + winDateBegin +
                ", winDateEnd=" + winDateEnd +
                ", saleDepartments=" + saleDepartments +
                ", departments=" + departments +
                ", industryCodes=" + industryCodes +
                ", contractDateBegin=" + contractDateBegin +
                ", contractDateEnd=" + contractDateEnd +
                ", salesmen=" + salesmen +
                ", personal=" + personal +
                ", companyId=" + companyId +
                ", reportId=" + reportId +
                ", executeId=" + executeId +
                ", createBy=" + createBy +
                '}';
    }
}
