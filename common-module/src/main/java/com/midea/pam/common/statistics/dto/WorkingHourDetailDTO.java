package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.WorkingHourDetail;

import java.util.Date;

public class WorkingHourDetailDTO extends WorkingHourDetail {

    private String attendanceStartDate;
    private String attendanceEndDate;

    private String[] projectStatusList;

    private String[] unitIdList;

    private String[] departmentIdList;

    private String[] ouIdList;

    private String[] statusList;

    private String managerId;

    private String projectCodeOrName;

    private Long personal;

    private Long companyId;

    private Long createById;

    private String vendorName;

    public String getAttendanceStartDate() {
        return attendanceStartDate;
    }

    public void setAttendanceStartDate(String attendanceStartDate) {
        this.attendanceStartDate = attendanceStartDate;
    }

    public String getAttendanceEndDate() {
        return attendanceEndDate;
    }

    public void setAttendanceEndDate(String attendanceEndDate) {
        this.attendanceEndDate = attendanceEndDate;
    }

    public String[] getProjectStatusList() {
        return projectStatusList;
    }

    public void setProjectStatusList(String[] projectStatusList) {
        this.projectStatusList = projectStatusList;
    }

    public String[] getUnitIdList() {
        return unitIdList;
    }

    public void setUnitIdList(String[] unitIdList) {
        this.unitIdList = unitIdList;
    }

    public String[] getDepartmentIdList() {
        return departmentIdList;
    }

    public void setDepartmentIdList(String[] departmentIdList) {
        this.departmentIdList = departmentIdList;
    }

    public String[] getOuIdList() {
        return ouIdList;
    }

    public void setOuIdList(String[] ouIdList) {
        this.ouIdList = ouIdList;
    }

    public String[] getStatusList() {
        return statusList;
    }

    public void setStatusList(String[] statusList) {
        this.statusList = statusList;
    }

    public String getManagerId() {
        return managerId;
    }

    public void setManagerId(String managerId) {
        this.managerId = managerId;
    }

    public String getProjectCodeOrName() {
        return projectCodeOrName;
    }

    public void setProjectCodeOrName(String projectCodeOrName) {
        this.projectCodeOrName = projectCodeOrName;
    }

    public Long getPersonal() {
        return personal;
    }

    public void setPersonal(Long personal) {
        this.personal = personal;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getCreateById() {
        return createById;
    }

    public void setCreateById(Long createById) {
        this.createById = createById;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }
}
