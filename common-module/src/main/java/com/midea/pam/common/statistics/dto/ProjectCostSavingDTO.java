package com.midea.pam.common.statistics.dto;


import com.midea.pam.common.statistics.entity.ProjectCostSaving;
import io.swagger.annotations.ApiModelProperty;


public class ProjectCostSavingDTO extends ProjectCostSaving {

    @ApiModelProperty(value = "超预算说明1")
    private String overBudgetDes1;

    @ApiModelProperty(value = "备注1")
    private String remark1;

    public String getOverBudgetDes1() {
        return overBudgetDes1;
    }

    public void setOverBudgetDes1(String overBudgetDes1) {
        this.overBudgetDes1 = overBudgetDes1;
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1;
    }
}
