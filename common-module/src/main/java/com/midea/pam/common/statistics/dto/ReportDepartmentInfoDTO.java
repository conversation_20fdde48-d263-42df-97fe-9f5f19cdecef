package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.ReportDepartmentInfo;
import com.midea.pam.common.statistics.entity.ReportDepartmentUnitRel;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/7
 * @description 事业部关系设置
 */
public class ReportDepartmentInfoDTO extends ReportDepartmentInfo {

    /**
     * 事业部业务分类关系
     */
    private List<ReportDepartmentUnitRel> departmentUnitRels;

    /**
     * 业务分类，多个用,隔开
     */
    private String unitNames;

    /**
     * 业务分类，多个用,隔开
     */
    private String unitIds;

    private String createName;

    private String updateName;

    public List<ReportDepartmentUnitRel> getDepartmentUnitRels() {
        return departmentUnitRels;
    }

    public void setDepartmentUnitRels(List<ReportDepartmentUnitRel> departmentUnitRels) {
        this.departmentUnitRels = departmentUnitRels;
    }

    public String getUnitNames() {
        return unitNames;
    }

    public void setUnitNames(String unitNames) {
        this.unitNames = unitNames;
    }

    public String getUnitIds() {
        return unitIds;
    }

    public void setUnitIds(String unitIds) {
        this.unitIds = unitIds;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }
}
