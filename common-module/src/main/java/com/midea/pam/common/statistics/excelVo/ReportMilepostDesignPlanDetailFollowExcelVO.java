package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

public class ReportMilepostDesignPlanDetailFollowExcelVO {

    @Excel(name = "序号", width = 10)
    private String serialNumber;

    @Excel(name = "物料描述", width = 20)
    private String materielDescr;

    @Excel(name = "PAM编码", width = 20)
    private String pamCode;

    @Excel(name = "ERP物料编码", width = 20)
    private String erpCode;

    @Excel(name = "单位", width = 20)
    private String unit;

    private BigDecimal totalNum;

    @Excel(name = "采购需求总数", width = 20)
    private String totalNum_dt;

    private BigDecimal number;

    @Excel(name = "单套数量", width = 20)
    private String number_dt;

    private BigDecimal requirementNum;

    @Excel(name = "已生成采购需求数量", width = 20)
    private String requirementNum_dt;

    @Excel(name = "需求日期", width = 20, format = "yyyy-MM-dd")
    private Date deliveryTime;

    private BigDecimal milepostDesignPlanDetailTotalNum;

    @Excel(name = "详设发布数量（项目+物料+日期汇总）", width = 20)
    private String milepostDesignPlanDetailTotalNum_dt;

    private BigDecimal needTotal;

    @Excel(name = "总需求数量（项目+物料+日期汇总）", width = 20)
    private String needTotal_dt;

    private BigDecimal unreleasedAmount;

    @Excel(name = "待下达数量（项目+物料+日期汇总）", width = 20)
    private String unreleasedAmount_dt;

    private BigDecimal releasedQuantity;

    @Excel(name = "已下达数量（项目+物料+日期汇总）", width = 20)
    private String releasedQuantity_dt;

    private BigDecimal closedQuantity;

    @Excel(name = "已关闭需求数量（项目+物料+日期汇总）", width = 20)
    private String closedQuantity_dt;

    @Excel(name = "订单号", width = 20)
    private String purchaseOrderNum;

    private BigDecimal purchaseOrderCancelNumber;

    @Excel(name = "订单取消数量", width = 20)
    private String purchaseOrderCancelNumber_dt;

    private BigDecimal purchaseOrderTransactionQuantity;

    @Excel(name = "入库数量", width = 20)
    private String purchaseOrderTransactionQuantity_dt;

    @Excel(name = "名称", width = 20)
    private String name;

    @Excel(name = "品牌", width = 20)
    private String brand;

    @Excel(name = "规格/型号", width = 20)
    private String model;

    @Excel(name = "图号", width = 20)
    private String figureNumber;

    @Excel(name = "物料类别-大类", width = 20)
    private String materialClassification;

    @Excel(name = "物料类别-中类", width = 20)
    private String codingMiddleClass;

    @Excel(name = "物料类别-小类", width = 20)
    private String materielType;

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getMaterielDescr() {
        return materielDescr;
    }

    public void setMaterielDescr(String materielDescr) {
        this.materielDescr = materielDescr;
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode;
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(BigDecimal totalNum) {
        this.totalNum = totalNum;
    }

    public String getTotalNum_dt() {
        if (Objects.nonNull(this.totalNum)) {
            return this.totalNum.stripTrailingZeros().toPlainString();
        } else {
            return this.totalNum_dt;
        }
    }

    public void setTotalNum_dt(String totalNum_dt) {
        this.totalNum_dt = totalNum_dt;
    }

    public BigDecimal getNumber() {
        return number;
    }

    public void setNumber(BigDecimal number) {
        this.number = number;
    }

    public String getNumber_dt() {
        if (Objects.nonNull(this.number)) {
            return this.number.stripTrailingZeros().toPlainString();
        } else {
            return this.number_dt;
        }
    }

    public void setNumber_dt(String number_dt) {
        this.number_dt = number_dt;
    }

    public BigDecimal getRequirementNum() {
        return requirementNum;
    }

    public void setRequirementNum(BigDecimal requirementNum) {
        this.requirementNum = requirementNum;
    }

    public String getRequirementNum_dt() {
        if (Objects.nonNull(this.requirementNum)) {
            return this.requirementNum.stripTrailingZeros().toPlainString();
        } else {
            return this.requirementNum_dt;
        }
    }

    public void setRequirementNum_dt(String requirementNum_dt) {
        this.requirementNum_dt = requirementNum_dt;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public BigDecimal getMilepostDesignPlanDetailTotalNum() {
        return milepostDesignPlanDetailTotalNum;
    }

    public void setMilepostDesignPlanDetailTotalNum(BigDecimal milepostDesignPlanDetailTotalNum) {
        this.milepostDesignPlanDetailTotalNum = milepostDesignPlanDetailTotalNum;
    }

    public String getMilepostDesignPlanDetailTotalNum_dt() {
        if (Objects.nonNull(this.milepostDesignPlanDetailTotalNum)) {
            return this.milepostDesignPlanDetailTotalNum.stripTrailingZeros().toPlainString();
        } else {
            return this.milepostDesignPlanDetailTotalNum_dt;
        }
    }

    public void setMilepostDesignPlanDetailTotalNum_dt(String milepostDesignPlanDetailTotalNum_dt) {
        this.milepostDesignPlanDetailTotalNum_dt = milepostDesignPlanDetailTotalNum_dt;
    }

    public BigDecimal getNeedTotal() {
        return needTotal;
    }

    public void setNeedTotal(BigDecimal needTotal) {
        this.needTotal = needTotal;
    }

    public String getNeedTotal_dt() {
        if (Objects.nonNull(this.needTotal)) {
            return this.needTotal.stripTrailingZeros().toPlainString();
        } else {
            return this.needTotal_dt;
        }
    }

    public void setNeedTotal_dt(String needTotal_dt) {
        this.needTotal_dt = needTotal_dt;
    }

    public BigDecimal getUnreleasedAmount() {
        return unreleasedAmount;
    }

    public void setUnreleasedAmount(BigDecimal unreleasedAmount) {
        this.unreleasedAmount = unreleasedAmount;
    }

    public String getUnreleasedAmount_dt() {
        if (Objects.nonNull(this.unreleasedAmount)) {
            return this.unreleasedAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.unreleasedAmount_dt;
        }
    }

    public void setUnreleasedAmount_dt(String unreleasedAmount_dt) {
        this.unreleasedAmount_dt = unreleasedAmount_dt;
    }

    public BigDecimal getReleasedQuantity() {
        return releasedQuantity;
    }

    public void setReleasedQuantity(BigDecimal releasedQuantity) {
        this.releasedQuantity = releasedQuantity;
    }

    public String getReleasedQuantity_dt() {
        if (Objects.nonNull(this.releasedQuantity)) {
            return this.releasedQuantity.stripTrailingZeros().toPlainString();
        } else {
            return this.releasedQuantity_dt;
        }
    }

    public void setReleasedQuantity_dt(String releasedQuantity_dt) {
        this.releasedQuantity_dt = releasedQuantity_dt;
    }

    public BigDecimal getClosedQuantity() {
        return closedQuantity;
    }

    public void setClosedQuantity(BigDecimal closedQuantity) {
        this.closedQuantity = closedQuantity;
    }

    public String getClosedQuantity_dt() {
        if (Objects.nonNull(this.closedQuantity)) {
            return this.closedQuantity.stripTrailingZeros().toPlainString();
        } else {
            return this.closedQuantity_dt;
        }
    }

    public void setClosedQuantity_dt(String closedQuantity_dt) {
        this.closedQuantity_dt = closedQuantity_dt;
    }

    public String getPurchaseOrderNum() {
        return purchaseOrderNum;
    }

    public void setPurchaseOrderNum(String purchaseOrderNum) {
        this.purchaseOrderNum = purchaseOrderNum;
    }

    public BigDecimal getPurchaseOrderCancelNumber() {
        return purchaseOrderCancelNumber;
    }

    public void setPurchaseOrderCancelNumber(BigDecimal purchaseOrderCancelNumber) {
        this.purchaseOrderCancelNumber = purchaseOrderCancelNumber;
    }

    public String getPurchaseOrderCancelNumber_dt() {
        if (Objects.nonNull(this.purchaseOrderCancelNumber)) {
            return this.purchaseOrderCancelNumber.stripTrailingZeros().toPlainString();
        } else {
            return this.purchaseOrderCancelNumber_dt;
        }
    }

    public void setPurchaseOrderCancelNumber_dt(String purchaseOrderCancelNumber_dt) {
        this.purchaseOrderCancelNumber_dt = purchaseOrderCancelNumber_dt;
    }

    public BigDecimal getPurchaseOrderTransactionQuantity() {
        return purchaseOrderTransactionQuantity;
    }

    public void setPurchaseOrderTransactionQuantity(BigDecimal purchaseOrderTransactionQuantity) {
        this.purchaseOrderTransactionQuantity = purchaseOrderTransactionQuantity;
    }

    public String getPurchaseOrderTransactionQuantity_dt() {
        if (Objects.nonNull(this.purchaseOrderTransactionQuantity)) {
            return this.purchaseOrderTransactionQuantity.stripTrailingZeros().toPlainString();
        } else {
            return this.purchaseOrderTransactionQuantity_dt;
        }
    }

    public void setPurchaseOrderTransactionQuantity_dt(String purchaseOrderTransactionQuantity_dt) {
        this.purchaseOrderTransactionQuantity_dt = purchaseOrderTransactionQuantity_dt;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getFigureNumber() {
        return figureNumber;
    }

    public void setFigureNumber(String figureNumber) {
        this.figureNumber = figureNumber;
    }

    public String getMaterialClassification() {
        return materialClassification;
    }

    public void setMaterialClassification(String materialClassification) {
        this.materialClassification = materialClassification;
    }

    public String getCodingMiddleClass() {
        return codingMiddleClass;
    }

    public void setCodingMiddleClass(String codingMiddleClass) {
        this.codingMiddleClass = codingMiddleClass;
    }

    public String getMaterielType() {
        return materielType;
    }

    public void setMaterielType(String materielType) {
        this.materielType = materielType;
    }
}
