package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.ProjectCostFeeItemRecord;

import java.math.BigDecimal;

public class ProjectCostFeeItemRecordDto extends ProjectCostFeeItemRecord {

    private BigDecimal annual_accrual;

    private BigDecimal monthly_accrual;

    public BigDecimal getAnnual_accrual() {
        return annual_accrual;
    }

    public ProjectCostFeeItemRecordDto setAnnual_accrual(BigDecimal annual_accrual) {
        this.annual_accrual = annual_accrual;
        return this;
    }

    public BigDecimal getMonthly_accrual() {
        return monthly_accrual;
    }

    public ProjectCostFeeItemRecordDto setMonthly_accrual(BigDecimal monthly_accrual) {
        this.monthly_accrual = monthly_accrual;
        return this;
    }
}
