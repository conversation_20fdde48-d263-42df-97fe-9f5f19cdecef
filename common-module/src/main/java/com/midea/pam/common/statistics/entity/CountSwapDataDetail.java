package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class CountSwapDataDetail extends LongIdEntity implements Serializable {
    private Long id;

    private Long executeId;

    private String division;

    private String produectDepartment;

    private BigDecimal contractAmount;

    private BigDecimal accruedContractAmount;

    private BigDecimal leftSwapReceiptAmount;

    private BigDecimal accruedLeftSwapReceiptAmount;

    private BigDecimal swapExecuteTotalAmount;

    private BigDecimal accruedSwapExecuteTotalAmount;

    private BigDecimal memberSwapCost;

    private BigDecimal accruedMemberSwapCost;

    private BigDecimal feeTotalCost;

    private BigDecimal accruedFeeTotalCost;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getDivision() {
        return division;
    }

    public void setDivision(String division) {
        this.division = division == null ? null : division.trim();
    }

    public String getProduectDepartment() {
        return produectDepartment;
    }

    public void setProduectDepartment(String produectDepartment) {
        this.produectDepartment = produectDepartment == null ? null : produectDepartment.trim();
    }

    public BigDecimal getContractAmount() {
        return contractAmount;
    }

    public void setContractAmount(BigDecimal contractAmount) {
        this.contractAmount = contractAmount;
    }

    public BigDecimal getAccruedContractAmount() {
        return accruedContractAmount;
    }

    public void setAccruedContractAmount(BigDecimal accruedContractAmount) {
        this.accruedContractAmount = accruedContractAmount;
    }

    public BigDecimal getLeftSwapReceiptAmount() {
        return leftSwapReceiptAmount;
    }

    public void setLeftSwapReceiptAmount(BigDecimal leftSwapReceiptAmount) {
        this.leftSwapReceiptAmount = leftSwapReceiptAmount;
    }

    public BigDecimal getAccruedLeftSwapReceiptAmount() {
        return accruedLeftSwapReceiptAmount;
    }

    public void setAccruedLeftSwapReceiptAmount(BigDecimal accruedLeftSwapReceiptAmount) {
        this.accruedLeftSwapReceiptAmount = accruedLeftSwapReceiptAmount;
    }

    public BigDecimal getSwapExecuteTotalAmount() {
        return swapExecuteTotalAmount;
    }

    public void setSwapExecuteTotalAmount(BigDecimal swapExecuteTotalAmount) {
        this.swapExecuteTotalAmount = swapExecuteTotalAmount;
    }

    public BigDecimal getAccruedSwapExecuteTotalAmount() {
        return accruedSwapExecuteTotalAmount;
    }

    public void setAccruedSwapExecuteTotalAmount(BigDecimal accruedSwapExecuteTotalAmount) {
        this.accruedSwapExecuteTotalAmount = accruedSwapExecuteTotalAmount;
    }

    public BigDecimal getMemberSwapCost() {
        return memberSwapCost;
    }

    public void setMemberSwapCost(BigDecimal memberSwapCost) {
        this.memberSwapCost = memberSwapCost;
    }

    public BigDecimal getAccruedMemberSwapCost() {
        return accruedMemberSwapCost;
    }

    public void setAccruedMemberSwapCost(BigDecimal accruedMemberSwapCost) {
        this.accruedMemberSwapCost = accruedMemberSwapCost;
    }

    public BigDecimal getFeeTotalCost() {
        return feeTotalCost;
    }

    public void setFeeTotalCost(BigDecimal feeTotalCost) {
        this.feeTotalCost = feeTotalCost;
    }

    public BigDecimal getAccruedFeeTotalCost() {
        return accruedFeeTotalCost;
    }

    public void setAccruedFeeTotalCost(BigDecimal accruedFeeTotalCost) {
        this.accruedFeeTotalCost = accruedFeeTotalCost;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", executeId=").append(executeId);
        sb.append(", division=").append(division);
        sb.append(", produectDepartment=").append(produectDepartment);
        sb.append(", contractAmount=").append(contractAmount);
        sb.append(", accruedContractAmount=").append(accruedContractAmount);
        sb.append(", leftSwapReceiptAmount=").append(leftSwapReceiptAmount);
        sb.append(", accruedLeftSwapReceiptAmount=").append(accruedLeftSwapReceiptAmount);
        sb.append(", swapExecuteTotalAmount=").append(swapExecuteTotalAmount);
        sb.append(", accruedSwapExecuteTotalAmount=").append(accruedSwapExecuteTotalAmount);
        sb.append(", memberSwapCost=").append(memberSwapCost);
        sb.append(", accruedMemberSwapCost=").append(accruedMemberSwapCost);
        sb.append(", feeTotalCost=").append(feeTotalCost);
        sb.append(", accruedFeeTotalCost=").append(accruedFeeTotalCost);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}