package com.midea.pam.common.statistics.dto;



import com.midea.pam.common.statistics.entity.ReportMilepostDesignPlanChangeKunShan;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/9
 * @description
 */
public class ReportMilepostDesignPlanChangeKunShanDTO extends ReportMilepostDesignPlanChangeKunShan {

    /**
     * 变更开始日期
     */
    private String changeStartTime;

    /**
     * 变更结束日期
     */
    private String changeEndTime;

    /**
     * 项目类型ID
     */
    private List<Long> projectTypeIds;

    private List<Long> unitIds;

    private List<String> changeReasonList;

    /**
     * 创建用户ID
     */
    private Long currentUserId;

    /**
     * 使用单位
     */
    private Long companyId;

    /**
     * 数据权限类型
     */
    private String permission;

    /**
     * 模组编码
     */
    private String moduleCode;

    /**
     * 是否已确认需求
     */
    private String showRequirementConfirm;

    /**
     * 是否已确认需求标识（0，否；1，是）
     */
    private String requirementCode;

    /**
     * 库存组织
     */
    private String orgId;

    private String  projectName;

    private Long ouId;

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getChangeStartTime() {
        return changeStartTime;
    }

    public void setChangeStartTime(String changeStartTime) {
        this.changeStartTime = changeStartTime;
    }

    public String getChangeEndTime() {
        return changeEndTime;
    }

    public void setChangeEndTime(String changeEndTime) {
        this.changeEndTime = changeEndTime;
    }

    public List<Long> getProjectTypeIds() {
        return projectTypeIds;
    }

    public void setProjectTypeIds(List<Long> projectTypeIds) {
        this.projectTypeIds = projectTypeIds;
    }

    public List<Long> getUnitIds() {
        return unitIds;
    }

    public void setUnitIds(List<Long> unitIds) {
        this.unitIds = unitIds;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getCurrentUserId() {
        return currentUserId;
    }

    public void setCurrentUserId(Long currentUserId) {
        this.currentUserId = currentUserId;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public List<String> getChangeReasonList() {
        return changeReasonList;
    }

    public void setChangeReasonList(List<String> changeReasonList) {
        this.changeReasonList = changeReasonList;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    @Override
    public String getShowRequirementConfirm() {
        return showRequirementConfirm;
    }

    @Override
    public void setShowRequirementConfirm(String showRequirementConfirm) {
        this.showRequirementConfirm = showRequirementConfirm;
    }

    public String getRequirementCode() {
        return requirementCode;
    }

    public void setRequirementCode(String requirementCode) {
        this.requirementCode = requirementCode;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    @Override
    public String getProjectName() {
        return projectName;
    }

    @Override
    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
}
