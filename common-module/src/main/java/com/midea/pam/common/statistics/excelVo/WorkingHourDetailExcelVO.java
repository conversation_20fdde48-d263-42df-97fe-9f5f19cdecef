package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

public class WorkingHourDetailExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer num;

    @Excel(name = "项目编号", width = 20)
    private String projectCode;

    @Excel(name = "项目名称", width = 20)
    private String projectName;

    @Excel(name = "项目状态", width = 20, replace = {"审批驳回_-2", "财务驳回_-1", "草稿_0", "审批中_3", "项目进行中_4", "项目变更中_9", "结项_10", "预立项转正驳回_-3", "预立项审批驳回_-4", "审批撤回_11", "预立项审批撤回_13", "作废_12", "预立项转正审批中_7", "终止_16"})
    private Integer projectStatus;

    @Excel(name = "事业部", width = 20)
    private String departmentName;

    @Excel(name = "项目经理", width = 20)
    private String projectManager;

    @Excel(name = "填报人姓名", width = 20)
    private String fillByName;

    @Excel(name = "填报人MIP", width = 20)
    private String fillByMip;

    @Excel(name = "填报人部门", width = 20)
    private String applyOrg;

    @Excel(name = "出勤日期", width = 20, format = "yyyy-MM-dd")
    private Date attendanceDate;

    @Excel(name = "填报日期", width = 20, format = "yyyy-MM-dd")
    private Date fillDate;

    @Excel(name = "填报工时(H)", width = 20)
    private BigDecimal fillWorkingHours;

    @Excel(name = "填报WBS", width = 20)
    private String wbsBudgetCode;

    @Excel(name = "填报角色", width = 20)
    private String laborWbsCostName;

    @Excel(name = "WBS工时汇总（按出勤日期）", width = 30)
    private BigDecimal wbsWorkHourSum;

    @Excel(name = "考勤工时(H)", width = 20)
    private BigDecimal ihrAttendHours;

    @Excel(name = "审批人姓名", width = 20)
    private String approveByName;

    @Excel(name = "审批人MIP", width = 20)
    private String approveByMip;

    @Excel(name = "审批日期", width = 20, format = "yyyy-MM-dd")
    private Date approveDate;

    @Excel(name = "审批工时", width = 20)
    private BigDecimal approveWorkingHours;

    @Excel(name = "工时状态", width = 10, replace = {"未提交_1", "审核中_2", "被驳回_3", "通过_4", "变更中_5", "变更驳回_6"})
    private Byte status;

    @Excel(name = "RDM确认工时(H)", width = 20)
    private BigDecimal rdmWorkingHours;

    @Excel(name = "项目类型", width = 20)
    private String projectType;

    @Excel(name = "业务分类", width = 20)
    private String unitName;

    @Excel(name = "业务实体", width = 20)
    private String ouName;

    @Excel(name = "是否开票", width = 10, replace = {"否_false", "是_true", "_null"})
    private Boolean invoiceApplyFlag;

    @Excel(name = "是否结转", width = 10, replace = {"否_false", "是_true", "_null"})
    private Boolean costCollectionFlag;

    @Excel(name = "是否跨单位工时", width = 20, replace = {"否_false", "是_true", "_null"})
    private Boolean crossUnitLaborCost;

    @Excel(name = "角色", width = 15, replace = {"_null"})
    private String roleName;

    @Excel(name = "供应商编号", width = 15, replace = {"_null"})
    private String vendorCode;

    @Excel(name = "供应商名称", width = 15, replace = {"_null"})
    private String vendorName;

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Integer getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(Integer projectStatus) {
        this.projectStatus = projectStatus;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager;
    }

    public String getFillByName() {
        return fillByName;
    }

    public void setFillByName(String fillByName) {
        this.fillByName = fillByName;
    }

    public String getFillByMip() {
        return fillByMip;
    }

    public void setFillByMip(String fillByMip) {
        this.fillByMip = fillByMip;
    }

    public String getApplyOrg() {
        return applyOrg;
    }

    public void setApplyOrg(String applyOrg) {
        this.applyOrg = applyOrg;
    }

    public Date getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(Date attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public Date getFillDate() {
        return fillDate;
    }

    public void setFillDate(Date fillDate) {
        this.fillDate = fillDate;
    }

    public BigDecimal getFillWorkingHours() {
        return fillWorkingHours;
    }

    public void setFillWorkingHours(BigDecimal fillWorkingHours) {
        this.fillWorkingHours = fillWorkingHours;
    }

    public Date getApproveDate() {
        return approveDate;
    }

    public void setApproveDate(Date approveDate) {
        this.approveDate = approveDate;
    }

    public BigDecimal getApproveWorkingHours() {
        return approveWorkingHours;
    }

    public void setApproveWorkingHours(BigDecimal approveWorkingHours) {
        this.approveWorkingHours = approveWorkingHours;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public BigDecimal getRdmWorkingHours() {
        return rdmWorkingHours;
    }

    public void setRdmWorkingHours(BigDecimal rdmWorkingHours) {
        this.rdmWorkingHours = rdmWorkingHours;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName;
    }

    public BigDecimal getIhrAttendHours() {
        return ihrAttendHours;
    }

    public void setIhrAttendHours(BigDecimal ihrAttendHours) {
        this.ihrAttendHours = ihrAttendHours;
    }

    public String getApproveByName() {
        return approveByName;
    }

    public void setApproveByName(String approveByName) {
        this.approveByName = approveByName;
    }

    public String getApproveByMip() {
        return approveByMip;
    }

    public void setApproveByMip(String approveByMip) {
        this.approveByMip = approveByMip;
    }

    public Boolean getInvoiceApplyFlag() {
        return invoiceApplyFlag;
    }

    public void setInvoiceApplyFlag(Boolean invoiceApplyFlag) {
        this.invoiceApplyFlag = invoiceApplyFlag;
    }

    public Boolean getCostCollectionFlag() {
        return costCollectionFlag;
    }

    public void setCostCollectionFlag(Boolean costCollectionFlag) {
        this.costCollectionFlag = costCollectionFlag;
    }

    public Boolean getCrossUnitLaborCost() {
        return crossUnitLaborCost;
    }

    public void setCrossUnitLaborCost(Boolean crossUnitLaborCost) {
        this.crossUnitLaborCost = crossUnitLaborCost;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getWbsBudgetCode() {
        return wbsBudgetCode;
    }

    public void setWbsBudgetCode(String wbsBudgetCode) {
        this.wbsBudgetCode = wbsBudgetCode;
    }

    public String getLaborWbsCostName() {
        return laborWbsCostName;
    }

    public void setLaborWbsCostName(String laborWbsCostName) {
        this.laborWbsCostName = laborWbsCostName;
    }

    public BigDecimal getWbsWorkHourSum() {
        return wbsWorkHourSum;
    }

    public void setWbsWorkHourSum(BigDecimal wbsWorkHourSum) {
        this.wbsWorkHourSum = wbsWorkHourSum;
    }
}
