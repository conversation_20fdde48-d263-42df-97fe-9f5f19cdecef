package com.midea.pam.common.statistics.dto;

import java.util.Date;
import java.util.List;

public class WorkingHourCollectionErrorTemporaryDto {

    private Long id;
    //归集日期--开始日期
    private Date collectionDateStart;
    //归集日期--结束日期
    private Date collectionDateEnd;


    //成本发生日期--开始日期
    private Date costDateStart;
    //成本发生日期--结束日期
    private Date costDateEnd;

    //结转状态
    private Integer carryStatus;
    //结转期间
    private String carryGlPeriod;
    //项目编号
    private String projectCode;
    //项目名称
    private String projectName;
    //项目类型
    private String projectTypeStr;
    private List<Long> projectTypeList;
    //币种
    private String currency;
    private List<String> currencyList;
    //业务实体
    private String ouIdStr;
    private List<String> ouIdList;
    //使用单位id(一级unit.id)
    private Long bizUnitId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBizUnitId() {
        return bizUnitId;
    }

    public void setBizUnitId(Long bizUnitId) {
        this.bizUnitId = bizUnitId;
    }

    public Date getCollectionDateStart() {
        return collectionDateStart;
    }

    public void setCollectionDateStart(Date collectionDateStart) {
        this.collectionDateStart = collectionDateStart;
    }

    public Date getCollectionDateEnd() {
        return collectionDateEnd;
    }

    public void setCollectionDateEnd(Date collectionDateEnd) {
        this.collectionDateEnd = collectionDateEnd;
    }

    public Date getCostDateStart() {
        return costDateStart;
    }

    public void setCostDateStart(Date costDateStart) {
        this.costDateStart = costDateStart;
    }

    public Date getCostDateEnd() {
        return costDateEnd;
    }

    public void setCostDateEnd(Date costDateEnd) {
        this.costDateEnd = costDateEnd;
    }

    public Integer getCarryStatus() {
        return carryStatus;
    }

    public void setCarryStatus(Integer carryStatus) {
        this.carryStatus = carryStatus;
    }

    public String getCarryGlPeriod() {
        return carryGlPeriod;
    }

    public void setCarryGlPeriod(String carryGlPeriod) {
        this.carryGlPeriod = carryGlPeriod;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectTypeStr() {
        return projectTypeStr;
    }

    public void setProjectTypeStr(String projectTypeStr) {
        this.projectTypeStr = projectTypeStr;
    }

    public List<Long> getProjectTypeList() {
        return projectTypeList;
    }

    public void setProjectTypeList(List<Long> projectTypeList) {
        this.projectTypeList = projectTypeList;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public List<String> getCurrencyList() {
        return currencyList;
    }

    public void setCurrencyList(List<String> currencyList) {
        this.currencyList = currencyList;
    }

    public String getOuIdStr() {
        return ouIdStr;
    }

    public void setOuIdStr(String ouIdStr) {
        this.ouIdStr = ouIdStr;
    }

    public List<String> getOuIdList() {
        return ouIdList;
    }

    public void setOuIdList(List<String> ouIdList) {
        this.ouIdList = ouIdList;
    }
}
