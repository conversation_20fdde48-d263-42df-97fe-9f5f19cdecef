package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目成本-供应商罚扣成本汇总表")
public class VendorPenaltyProjectCostSummary extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "罚扣详情id")
    private Long penaltyDetailId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "wbs号")
    private String wbsCode;

    @ApiModelProperty(value = "活动事项编号")
    private String activityCode;

    @ApiModelProperty(value = "罚扣编号")
    private String penaltyCode;

    @ApiModelProperty(value = "供应商编号")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "汇率")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "罚扣金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "项目成本")
    private BigDecimal projectCost;

    @ApiModelProperty(value = "已发生成本")
    private BigDecimal occurCost;

    @ApiModelProperty(value = "业务生成时间")
    private Date dataTime;

    @ApiModelProperty(value = "删除标志")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getPenaltyDetailId() {
        return penaltyDetailId;
    }

    public void setPenaltyDetailId(Long penaltyDetailId) {
        this.penaltyDetailId = penaltyDetailId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getWbsCode() {
        return wbsCode;
    }

    public void setWbsCode(String wbsCode) {
        this.wbsCode = wbsCode == null ? null : wbsCode.trim();
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public String getPenaltyCode() {
        return penaltyCode;
    }

    public void setPenaltyCode(String penaltyCode) {
        this.penaltyCode = penaltyCode == null ? null : penaltyCode.trim();
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode == null ? null : vendorCode.trim();
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName == null ? null : vendorName.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getProjectCost() {
        return projectCost;
    }

    public void setProjectCost(BigDecimal projectCost) {
        this.projectCost = projectCost;
    }

    public BigDecimal getOccurCost() {
        return occurCost;
    }

    public void setOccurCost(BigDecimal occurCost) {
        this.occurCost = occurCost;
    }

    public Date getDataTime() {
        return dataTime;
    }

    public void setDataTime(Date dataTime) {
        this.dataTime = dataTime;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", executeId=").append(executeId);
        sb.append(", penaltyDetailId=").append(penaltyDetailId);
        sb.append(", projectId=").append(projectId);
        sb.append(", wbsCode=").append(wbsCode);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", penaltyCode=").append(penaltyCode);
        sb.append(", vendorCode=").append(vendorCode);
        sb.append(", vendorName=").append(vendorName);
        sb.append(", currency=").append(currency);
        sb.append(", conversionRate=").append(conversionRate);
        sb.append(", amount=").append(amount);
        sb.append(", projectCost=").append(projectCost);
        sb.append(", occurCost=").append(occurCost);
        sb.append(", dataTime=").append(dataTime);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}