package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RequirementPoDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public RequirementPoDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderIdIsNull() {
            addCriterion("purchase_order_id is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderIdIsNotNull() {
            addCriterion("purchase_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderIdEqualTo(Long value) {
            addCriterion("purchase_order_id =", value, "purchaseOrderId");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderIdNotEqualTo(Long value) {
            addCriterion("purchase_order_id <>", value, "purchaseOrderId");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderIdGreaterThan(Long value) {
            addCriterion("purchase_order_id >", value, "purchaseOrderId");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("purchase_order_id >=", value, "purchaseOrderId");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderIdLessThan(Long value) {
            addCriterion("purchase_order_id <", value, "purchaseOrderId");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderIdLessThanOrEqualTo(Long value) {
            addCriterion("purchase_order_id <=", value, "purchaseOrderId");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderIdIn(List<Long> values) {
            addCriterion("purchase_order_id in", values, "purchaseOrderId");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderIdNotIn(List<Long> values) {
            addCriterion("purchase_order_id not in", values, "purchaseOrderId");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderIdBetween(Long value1, Long value2) {
            addCriterion("purchase_order_id between", value1, value2, "purchaseOrderId");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderIdNotBetween(Long value1, Long value2) {
            addCriterion("purchase_order_id not between", value1, value2, "purchaseOrderId");
            return (Criteria) this;
        }

        public Criteria andNumIsNull() {
            addCriterion("num is null");
            return (Criteria) this;
        }

        public Criteria andNumIsNotNull() {
            addCriterion("num is not null");
            return (Criteria) this;
        }

        public Criteria andNumEqualTo(String value) {
            addCriterion("num =", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotEqualTo(String value) {
            addCriterion("num <>", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThan(String value) {
            addCriterion("num >", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThanOrEqualTo(String value) {
            addCriterion("num >=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThan(String value) {
            addCriterion("num <", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThanOrEqualTo(String value) {
            addCriterion("num <=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLike(String value) {
            addCriterion("num like", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotLike(String value) {
            addCriterion("num not like", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumIn(List<String> values) {
            addCriterion("num in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotIn(List<String> values) {
            addCriterion("num not in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumBetween(String value1, String value2) {
            addCriterion("num between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotBetween(String value1, String value2) {
            addCriterion("num not between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdIsNull() {
            addCriterion("project_wbs_receipts_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdIsNotNull() {
            addCriterion("project_wbs_receipts_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdEqualTo(Long value) {
            addCriterion("project_wbs_receipts_id =", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdNotEqualTo(Long value) {
            addCriterion("project_wbs_receipts_id <>", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdGreaterThan(Long value) {
            addCriterion("project_wbs_receipts_id >", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_wbs_receipts_id >=", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdLessThan(Long value) {
            addCriterion("project_wbs_receipts_id <", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdLessThanOrEqualTo(Long value) {
            addCriterion("project_wbs_receipts_id <=", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdIn(List<Long> values) {
            addCriterion("project_wbs_receipts_id in", values, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdNotIn(List<Long> values) {
            addCriterion("project_wbs_receipts_id not in", values, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdBetween(Long value1, Long value2) {
            addCriterion("project_wbs_receipts_id between", value1, value2, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdNotBetween(Long value1, Long value2) {
            addCriterion("project_wbs_receipts_id not between", value1, value2, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIsNull() {
            addCriterion("requirement_code is null");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIsNotNull() {
            addCriterion("requirement_code is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeEqualTo(String value) {
            addCriterion("requirement_code =", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotEqualTo(String value) {
            addCriterion("requirement_code <>", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeGreaterThan(String value) {
            addCriterion("requirement_code >", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeGreaterThanOrEqualTo(String value) {
            addCriterion("requirement_code >=", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLessThan(String value) {
            addCriterion("requirement_code <", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLessThanOrEqualTo(String value) {
            addCriterion("requirement_code <=", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLike(String value) {
            addCriterion("requirement_code like", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotLike(String value) {
            addCriterion("requirement_code not like", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIn(List<String> values) {
            addCriterion("requirement_code in", values, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotIn(List<String> values) {
            addCriterion("requirement_code not in", values, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeBetween(String value1, String value2) {
            addCriterion("requirement_code between", value1, value2, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotBetween(String value1, String value2) {
            addCriterion("requirement_code not between", value1, value2, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNull() {
            addCriterion("pam_code is null");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNotNull() {
            addCriterion("pam_code is not null");
            return (Criteria) this;
        }

        public Criteria andPamCodeEqualTo(String value) {
            addCriterion("pam_code =", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotEqualTo(String value) {
            addCriterion("pam_code <>", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThan(String value) {
            addCriterion("pam_code >", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("pam_code >=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThan(String value) {
            addCriterion("pam_code <", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThanOrEqualTo(String value) {
            addCriterion("pam_code <=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLike(String value) {
            addCriterion("pam_code like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotLike(String value) {
            addCriterion("pam_code not like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeIn(List<String> values) {
            addCriterion("pam_code in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotIn(List<String> values) {
            addCriterion("pam_code not in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeBetween(String value1, String value2) {
            addCriterion("pam_code between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotBetween(String value1, String value2) {
            addCriterion("pam_code not between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNull() {
            addCriterion("erp_code is null");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNotNull() {
            addCriterion("erp_code is not null");
            return (Criteria) this;
        }

        public Criteria andErpCodeEqualTo(String value) {
            addCriterion("erp_code =", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotEqualTo(String value) {
            addCriterion("erp_code <>", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThan(String value) {
            addCriterion("erp_code >", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("erp_code >=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThan(String value) {
            addCriterion("erp_code <", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThanOrEqualTo(String value) {
            addCriterion("erp_code <=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLike(String value) {
            addCriterion("erp_code like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotLike(String value) {
            addCriterion("erp_code not like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIn(List<String> values) {
            addCriterion("erp_code in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotIn(List<String> values) {
            addCriterion("erp_code not in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeBetween(String value1, String value2) {
            addCriterion("erp_code between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotBetween(String value1, String value2) {
            addCriterion("erp_code not between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andOrderNumIsNull() {
            addCriterion("order_num is null");
            return (Criteria) this;
        }

        public Criteria andOrderNumIsNotNull() {
            addCriterion("order_num is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNumEqualTo(BigDecimal value) {
            addCriterion("order_num =", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotEqualTo(BigDecimal value) {
            addCriterion("order_num <>", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumGreaterThan(BigDecimal value) {
            addCriterion("order_num >", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("order_num >=", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLessThan(BigDecimal value) {
            addCriterion("order_num <", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("order_num <=", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumIn(List<BigDecimal> values) {
            addCriterion("order_num in", values, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotIn(List<BigDecimal> values) {
            addCriterion("order_num not in", values, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("order_num between", value1, value2, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("order_num not between", value1, value2, "orderNum");
            return (Criteria) this;
        }

        public Criteria andStorageCountIsNull() {
            addCriterion("storage_count is null");
            return (Criteria) this;
        }

        public Criteria andStorageCountIsNotNull() {
            addCriterion("storage_count is not null");
            return (Criteria) this;
        }

        public Criteria andStorageCountEqualTo(BigDecimal value) {
            addCriterion("storage_count =", value, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountNotEqualTo(BigDecimal value) {
            addCriterion("storage_count <>", value, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountGreaterThan(BigDecimal value) {
            addCriterion("storage_count >", value, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("storage_count >=", value, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountLessThan(BigDecimal value) {
            addCriterion("storage_count <", value, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("storage_count <=", value, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountIn(List<BigDecimal> values) {
            addCriterion("storage_count in", values, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountNotIn(List<BigDecimal> values) {
            addCriterion("storage_count not in", values, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("storage_count between", value1, value2, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("storage_count not between", value1, value2, "storageCount");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceIsNull() {
            addCriterion("discount_price is null");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceIsNotNull() {
            addCriterion("discount_price is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceEqualTo(BigDecimal value) {
            addCriterion("discount_price =", value, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceNotEqualTo(BigDecimal value) {
            addCriterion("discount_price <>", value, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceGreaterThan(BigDecimal value) {
            addCriterion("discount_price >", value, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_price >=", value, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceLessThan(BigDecimal value) {
            addCriterion("discount_price <", value, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_price <=", value, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceIn(List<BigDecimal> values) {
            addCriterion("discount_price in", values, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceNotIn(List<BigDecimal> values) {
            addCriterion("discount_price not in", values, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_price between", value1, value2, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_price not between", value1, value2, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andPoTotalAmountIsNull() {
            addCriterion("po_total_amount is null");
            return (Criteria) this;
        }

        public Criteria andPoTotalAmountIsNotNull() {
            addCriterion("po_total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andPoTotalAmountEqualTo(BigDecimal value) {
            addCriterion("po_total_amount =", value, "poTotalAmount");
            return (Criteria) this;
        }

        public Criteria andPoTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("po_total_amount <>", value, "poTotalAmount");
            return (Criteria) this;
        }

        public Criteria andPoTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("po_total_amount >", value, "poTotalAmount");
            return (Criteria) this;
        }

        public Criteria andPoTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("po_total_amount >=", value, "poTotalAmount");
            return (Criteria) this;
        }

        public Criteria andPoTotalAmountLessThan(BigDecimal value) {
            addCriterion("po_total_amount <", value, "poTotalAmount");
            return (Criteria) this;
        }

        public Criteria andPoTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("po_total_amount <=", value, "poTotalAmount");
            return (Criteria) this;
        }

        public Criteria andPoTotalAmountIn(List<BigDecimal> values) {
            addCriterion("po_total_amount in", values, "poTotalAmount");
            return (Criteria) this;
        }

        public Criteria andPoTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("po_total_amount not in", values, "poTotalAmount");
            return (Criteria) this;
        }

        public Criteria andPoTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("po_total_amount between", value1, value2, "poTotalAmount");
            return (Criteria) this;
        }

        public Criteria andPoTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("po_total_amount not between", value1, value2, "poTotalAmount");
            return (Criteria) this;
        }

        public Criteria andPoTypeIsNull() {
            addCriterion("po_type is null");
            return (Criteria) this;
        }

        public Criteria andPoTypeIsNotNull() {
            addCriterion("po_type is not null");
            return (Criteria) this;
        }

        public Criteria andPoTypeEqualTo(Integer value) {
            addCriterion("po_type =", value, "poType");
            return (Criteria) this;
        }

        public Criteria andPoTypeNotEqualTo(Integer value) {
            addCriterion("po_type <>", value, "poType");
            return (Criteria) this;
        }

        public Criteria andPoTypeGreaterThan(Integer value) {
            addCriterion("po_type >", value, "poType");
            return (Criteria) this;
        }

        public Criteria andPoTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("po_type >=", value, "poType");
            return (Criteria) this;
        }

        public Criteria andPoTypeLessThan(Integer value) {
            addCriterion("po_type <", value, "poType");
            return (Criteria) this;
        }

        public Criteria andPoTypeLessThanOrEqualTo(Integer value) {
            addCriterion("po_type <=", value, "poType");
            return (Criteria) this;
        }

        public Criteria andPoTypeIn(List<Integer> values) {
            addCriterion("po_type in", values, "poType");
            return (Criteria) this;
        }

        public Criteria andPoTypeNotIn(List<Integer> values) {
            addCriterion("po_type not in", values, "poType");
            return (Criteria) this;
        }

        public Criteria andPoTypeBetween(Integer value1, Integer value2) {
            addCriterion("po_type between", value1, value2, "poType");
            return (Criteria) this;
        }

        public Criteria andPoTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("po_type not between", value1, value2, "poType");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNull() {
            addCriterion("wbs_summary_code is null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNotNull() {
            addCriterion("wbs_summary_code is not null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeEqualTo(String value) {
            addCriterion("wbs_summary_code =", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotEqualTo(String value) {
            addCriterion("wbs_summary_code <>", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThan(String value) {
            addCriterion("wbs_summary_code >", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code >=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThan(String value) {
            addCriterion("wbs_summary_code <", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code <=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLike(String value) {
            addCriterion("wbs_summary_code like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotLike(String value) {
            addCriterion("wbs_summary_code not like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIn(List<String> values) {
            addCriterion("wbs_summary_code in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotIn(List<String> values) {
            addCriterion("wbs_summary_code not in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeBetween(String value1, String value2) {
            addCriterion("wbs_summary_code between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotBetween(String value1, String value2) {
            addCriterion("wbs_summary_code not between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNull() {
            addCriterion("activity_code is null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNotNull() {
            addCriterion("activity_code is not null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeEqualTo(String value) {
            addCriterion("activity_code =", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotEqualTo(String value) {
            addCriterion("activity_code <>", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThan(String value) {
            addCriterion("activity_code >", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("activity_code >=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThan(String value) {
            addCriterion("activity_code <", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThanOrEqualTo(String value) {
            addCriterion("activity_code <=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLike(String value) {
            addCriterion("activity_code like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotLike(String value) {
            addCriterion("activity_code not like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIn(List<String> values) {
            addCriterion("activity_code in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotIn(List<String> values) {
            addCriterion("activity_code not in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeBetween(String value1, String value2) {
            addCriterion("activity_code between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotBetween(String value1, String value2) {
            addCriterion("activity_code not between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDataTimeIsNull() {
            addCriterion("data_time is null");
            return (Criteria) this;
        }

        public Criteria andDataTimeIsNotNull() {
            addCriterion("data_time is not null");
            return (Criteria) this;
        }

        public Criteria andDataTimeEqualTo(Date value) {
            addCriterion("data_time =", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotEqualTo(Date value) {
            addCriterion("data_time <>", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeGreaterThan(Date value) {
            addCriterion("data_time >", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("data_time >=", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeLessThan(Date value) {
            addCriterion("data_time <", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeLessThanOrEqualTo(Date value) {
            addCriterion("data_time <=", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeIn(List<Date> values) {
            addCriterion("data_time in", values, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotIn(List<Date> values) {
            addCriterion("data_time not in", values, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeBetween(Date value1, Date value2) {
            addCriterion("data_time between", value1, value2, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotBetween(Date value1, Date value2) {
            addCriterion("data_time not between", value1, value2, "dataTime");
            return (Criteria) this;
        }

        public Criteria andCancelNumIsNull() {
            addCriterion("cancel_num is null");
            return (Criteria) this;
        }

        public Criteria andCancelNumIsNotNull() {
            addCriterion("cancel_num is not null");
            return (Criteria) this;
        }

        public Criteria andCancelNumEqualTo(BigDecimal value) {
            addCriterion("cancel_num =", value, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumNotEqualTo(BigDecimal value) {
            addCriterion("cancel_num <>", value, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumGreaterThan(BigDecimal value) {
            addCriterion("cancel_num >", value, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cancel_num >=", value, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumLessThan(BigDecimal value) {
            addCriterion("cancel_num <", value, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cancel_num <=", value, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumIn(List<BigDecimal> values) {
            addCriterion("cancel_num in", values, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumNotIn(List<BigDecimal> values) {
            addCriterion("cancel_num not in", values, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cancel_num between", value1, value2, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cancel_num not between", value1, value2, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNull() {
            addCriterion("vendor_name is null");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNotNull() {
            addCriterion("vendor_name is not null");
            return (Criteria) this;
        }

        public Criteria andVendorNameEqualTo(String value) {
            addCriterion("vendor_name =", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotEqualTo(String value) {
            addCriterion("vendor_name <>", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThan(String value) {
            addCriterion("vendor_name >", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_name >=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThan(String value) {
            addCriterion("vendor_name <", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThanOrEqualTo(String value) {
            addCriterion("vendor_name <=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLike(String value) {
            addCriterion("vendor_name like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotLike(String value) {
            addCriterion("vendor_name not like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameIn(List<String> values) {
            addCriterion("vendor_name in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotIn(List<String> values) {
            addCriterion("vendor_name not in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameBetween(String value1, String value2) {
            addCriterion("vendor_name between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotBetween(String value1, String value2) {
            addCriterion("vendor_name not between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNull() {
            addCriterion("materiel_descr is null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNotNull() {
            addCriterion("materiel_descr is not null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrEqualTo(String value) {
            addCriterion("materiel_descr =", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotEqualTo(String value) {
            addCriterion("materiel_descr <>", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThan(String value) {
            addCriterion("materiel_descr >", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThanOrEqualTo(String value) {
            addCriterion("materiel_descr >=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThan(String value) {
            addCriterion("materiel_descr <", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThanOrEqualTo(String value) {
            addCriterion("materiel_descr <=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLike(String value) {
            addCriterion("materiel_descr like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotLike(String value) {
            addCriterion("materiel_descr not like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIn(List<String> values) {
            addCriterion("materiel_descr in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotIn(List<String> values) {
            addCriterion("materiel_descr not in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrBetween(String value1, String value2) {
            addCriterion("materiel_descr between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotBetween(String value1, String value2) {
            addCriterion("materiel_descr not between", value1, value2, "materielDescr");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}