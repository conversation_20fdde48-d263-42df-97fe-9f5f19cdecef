package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ReportProjectMonthlyQuery {
    private Long id;

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    @ApiModelProperty(value = "报表ID")
    private Long reportId;

    @ApiModelProperty(value = "销售部门id列表")
    private List<Long> unitIdList;

    @ApiModelProperty(value = "业务实体id列表")
    private List<Long> ouIdList;

    @ApiModelProperty(value = "业务模式id列表")
    private List<Long> projectTypeList;

    @ApiModelProperty(value = "项目状态")
    private List<Integer> projectStatusList;

    @ApiModelProperty(value = "截止月份")
    private String endTime;

    private Long personal;

    private Long companyId;

    private Long createBy;
}
