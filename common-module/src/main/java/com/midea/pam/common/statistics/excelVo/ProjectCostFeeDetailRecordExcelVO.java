package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/3/9
 * @description
 */
public class ProjectCostFeeDetailRecordExcelVO {

    @Excel(name = "序号", width = 10)
    private int num;

    @Excel(name = "项目编号", width = 20)
    private String projectCode;

    @Excel(name = "项目名称", width = 20)
    private String projectName;

    @Excel(name = "费用单号", width = 20)
    private String orderCode;

    @Excel(name = "申请日期", width = 15, format = "yyyy-MM-dd")
    private Date applyDate;

    @Excel(name = "申请人", width = 10)
    private String applyName;

    @Excel(name = "费用类型", width = 15)
    private String feeItemName;

    @Excel(name = "经济事项", width = 15)
    private String feeTypeName;

    @Excel(name = "金额（不含税）", width = 15)
    private BigDecimal feeAmount;

    @Excel(name = "币种", width = 10)
    private String currencyName;

    @Excel(name = "供应商名称", width = 30)
    private String vendorName;

    @Excel(name = "费用单业务描述", width = 30)
    private String orderDesc;

    @Excel(name = "入账日期", width = 15, format = "yyyy-MM-dd")
    private Date glDate;

    @Excel(name = "状态", width = 10)
    private String importErpStatus;

    @Excel(name = "是否跨年EC")
    private String isCrossYear_;

    @Excel(name = "CA单类型")
    private String apportionTypeName_;

    public String getIsCrossYear_() {
        return isCrossYear_;
    }

    public ProjectCostFeeDetailRecordExcelVO setIsCrossYear_(String isCrossYear_) {
        this.isCrossYear_ = isCrossYear_;
        return this;
    }

    public String getApportionTypeName_() {
        return apportionTypeName_;
    }

    public ProjectCostFeeDetailRecordExcelVO setApportionTypeName_(String apportionTypeName_) {
        this.apportionTypeName_ = apportionTypeName_;
        return this;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public String getApplyName() {
        return applyName;
    }

    public void setApplyName(String applyName) {
        this.applyName = applyName;
    }

    public String getFeeItemName() {
        return feeItemName;
    }

    public void setFeeItemName(String feeItemName) {
        this.feeItemName = feeItemName;
    }

    public String getFeeTypeName() {
        return feeTypeName;
    }

    public void setFeeTypeName(String feeTypeName) {
        this.feeTypeName = feeTypeName;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public String getCurrencyName() {
        return currencyName;
    }

    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getOrderDesc() {
        return orderDesc;
    }

    public void setOrderDesc(String orderDesc) {
        this.orderDesc = orderDesc;
    }

    public Date getGlDate() {
        return glDate;
    }

    public void setGlDate(Date glDate) {
        this.glDate = glDate;
    }

    public String getImportErpStatus() {
        return importErpStatus;
    }

    public void setImportErpStatus(String importErpStatus) {
        this.importErpStatus = importErpStatus;
    }
}
