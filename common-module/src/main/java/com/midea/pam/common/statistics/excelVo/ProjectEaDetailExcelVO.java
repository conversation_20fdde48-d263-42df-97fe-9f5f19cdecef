package com.midea.pam.common.statistics.excelVo;


import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-7-25
 * @description 需求预算-费用申请（EA单）未释放、剩余可用的金额
 */
@Getter
@Setter
public class ProjectEaDetailExcelVO {

    @Excel(name = "序号", width = 5)
    private Integer number;

    @Excel(name = "WBS", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项", width = 15)
    private String activityCode;

    @Excel(name = "EA单号", width = 25)
    private String feeApplyCode;

    @Excel(name = "剩余EA可用金额", width = 15)
    private BigDecimal amount;

    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date dataTime;

}
