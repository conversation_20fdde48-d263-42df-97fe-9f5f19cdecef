package com.midea.pam.common.statistics.excelVo;


import com.midea.pam.common.util.BigDecimalUtils;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022-12-9
 * @description 开票回款信息
 */
@Getter
@Setter
public class InvoiceReceiptInfoExcelVO {

    @Excel(name = "子合同编号", width = 20)
    private String code;

    @Excel(name = "子合同名称", width = 20)
    private String name;

    @Excel(name = "合同状态", width = 15, replace = {"待归档_0","草稿_1","审核中_2","驳回_3","待生效_4","生效_5","冻结_6","失效_7","已结束_8","作废_9","变更中_10","合同终止_11"})
    private Integer status;

    @Excel(name = "子合同-总金额（含税）", width = 20)
    private BigDecimal amount;

    @Excel(name = "子合同-总金额（不含税）", width = 20)
    private BigDecimal excludingTaxAmount;

    @Excel(name = "已开票", width = 15)
    private BigDecimal billed;

    @Excel(name = "剩余开票", width = 15)
    private BigDecimal surplusBill;

    @Excel(name = "开票率", width = 15, suffix = "%")
    private BigDecimal billRate;

    @Excel(name = "已回款", width = 15)
    private BigDecimal moneyBacked;

    @Excel(name = "剩余回款", width = 15)
    private BigDecimal surplusMoneyBack;

    @Excel(name = "回款率", width = 15, suffix = "%")
    private BigDecimal moneyBackRate;


    public BigDecimal getAmount() {
        return BigDecimalUtils.scale(amount);
    }

    public BigDecimal getExcludingTaxAmount() {
        return BigDecimalUtils.scale(excludingTaxAmount);
    }

    public BigDecimal getBilled() {
        return BigDecimalUtils.scale(billed);
    }

    public BigDecimal getSurplusBill() {
        return BigDecimalUtils.scale(surplusBill);
    }

    public BigDecimal getBillRate() {
        return BigDecimalUtils.scale(billRate);
    }

    public BigDecimal getMoneyBacked() {
        return BigDecimalUtils.scale(moneyBacked);
    }

    public BigDecimal getSurplusMoneyBack() {
        return BigDecimalUtils.scale(surplusMoneyBack);
    }

    public BigDecimal getMoneyBackRate() {
        return BigDecimalUtils.scale(moneyBackRate);
    }

}
