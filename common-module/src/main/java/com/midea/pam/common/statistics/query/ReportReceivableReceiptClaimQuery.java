package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/28
 */
@ApiModel(value = "InvoiceApplyReceivableAgesQuery", description = "管理口径应收报表")
public class ReportReceivableReceiptClaimQuery {

    private Long id;

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    @ApiModelProperty(value = "报表ID")
    private Long reportId;

    @ApiModelProperty(value = "销售部门id列表")
    private List<Long> unitIdList;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "客户名称或CRM编码")
    private String customerName;

    @ApiModelProperty(value = "业务实体id列表")
    private List<Long> ouIdList;

    @ApiModelProperty(value = "业务模式id列表")
    private List<Long> projectTypeList;

    private Date expireDate;

    private Long personal;

    private Long companyId;

    private Long createBy;

    public List<Long> getProjectTypeList() {
        return projectTypeList;
    }

    public void setProjectTypeList(List<Long> projectTypeList) {
        this.projectTypeList = projectTypeList;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public List<Long> getUnitIdList() {
        return unitIdList;
    }

    public void setUnitIdList(List<Long> unitIdList) {
        this.unitIdList = unitIdList;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public List<Long> getOuIdList() {
        return ouIdList;
    }

    public void setOuIdList(List<Long> ouIdList) {
        this.ouIdList = ouIdList;
    }

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getPersonal() {
        return personal;
    }

    public void setPersonal(Long personal) {
        this.personal = personal;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    @Override
    public String toString() {
        return "UnconfirmedIncomeQuery{" +
                "id=" + id +
                ", executeId=" + executeId +
                ", unitIdList=" + unitIdList +
                ", customerId=" + customerId +
                ", customerName='" + customerName + '\'' +
                ", ouIdList=" + ouIdList +
                '}';
    }
}
