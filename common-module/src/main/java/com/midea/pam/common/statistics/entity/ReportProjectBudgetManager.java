package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;

public class ReportProjectBudgetManager extends LongIdEntity implements Serializable {
    private Long id;

    private Long executeId;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private String projectType;

    private Long ownerUserId;

    private String ownerName;

    private Long managerId;

    private String projectManager;

    private Integer projectStatus;

    private String projectMilepostName;

    private BigDecimal amount;

    private BigDecimal materialQuotation;

    private BigDecimal humanQuotation;

    private BigDecimal travelQuotation;

    private BigDecimal noTravelQuotation;

    private BigDecimal quotationAmount;

    private BigDecimal winRate;

    private BigDecimal materialBudget;

    private BigDecimal humanBudget;

    private BigDecimal travelBudget;

    private BigDecimal noTravelBudget;

    private BigDecimal budgetAmount;

    private BigDecimal budgetProfitRate;

    private BigDecimal quotationBudget;

    private BigDecimal materialCostActual;

    private BigDecimal humanCostActual;

    private BigDecimal travelCostActual;

    private BigDecimal noTravelCostActual;

    private BigDecimal projectCostAmount;

    private BigDecimal performProfitRate;

    private BigDecimal budgetExecutionSchedule;

    private BigDecimal quotationActual;

    private Long ouId;

    private String ouName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType == null ? null : projectType.trim();
    }

    public Long getOwnerUserId() {
        return ownerUserId;
    }

    public void setOwnerUserId(Long ownerUserId) {
        this.ownerUserId = ownerUserId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName == null ? null : ownerName.trim();
    }

    public Long getManagerId() {
        return managerId;
    }

    public void setManagerId(Long managerId) {
        this.managerId = managerId;
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public Integer getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(Integer projectStatus) {
        this.projectStatus = projectStatus;
    }

    public String getProjectMilepostName() {
        return projectMilepostName;
    }

    public void setProjectMilepostName(String projectMilepostName) {
        this.projectMilepostName = projectMilepostName == null ? null : projectMilepostName.trim();
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getMaterialQuotation() {
        return materialQuotation;
    }

    public void setMaterialQuotation(BigDecimal materialQuotation) {
        this.materialQuotation = materialQuotation;
    }

    public BigDecimal getHumanQuotation() {
        return humanQuotation;
    }

    public void setHumanQuotation(BigDecimal humanQuotation) {
        this.humanQuotation = humanQuotation;
    }

    public BigDecimal getTravelQuotation() {
        return travelQuotation;
    }

    public void setTravelQuotation(BigDecimal travelQuotation) {
        this.travelQuotation = travelQuotation;
    }

    public BigDecimal getNoTravelQuotation() {
        return noTravelQuotation;
    }

    public void setNoTravelQuotation(BigDecimal noTravelQuotation) {
        this.noTravelQuotation = noTravelQuotation;
    }

    public BigDecimal getQuotationAmount() {
        return quotationAmount;
    }

    public void setQuotationAmount(BigDecimal quotationAmount) {
        this.quotationAmount = quotationAmount;
    }

    public BigDecimal getWinRate() {
        return winRate;
    }

    public void setWinRate(BigDecimal winRate) {
        this.winRate = winRate;
    }

    public BigDecimal getMaterialBudget() {
        return materialBudget;
    }

    public void setMaterialBudget(BigDecimal materialBudget) {
        this.materialBudget = materialBudget;
    }

    public BigDecimal getHumanBudget() {
        return humanBudget;
    }

    public void setHumanBudget(BigDecimal humanBudget) {
        this.humanBudget = humanBudget;
    }

    public BigDecimal getTravelBudget() {
        return travelBudget;
    }

    public void setTravelBudget(BigDecimal travelBudget) {
        this.travelBudget = travelBudget;
    }

    public BigDecimal getNoTravelBudget() {
        return noTravelBudget;
    }

    public void setNoTravelBudget(BigDecimal noTravelBudget) {
        this.noTravelBudget = noTravelBudget;
    }

    public BigDecimal getBudgetAmount() {
        return budgetAmount;
    }

    public void setBudgetAmount(BigDecimal budgetAmount) {
        this.budgetAmount = budgetAmount;
    }

    public BigDecimal getBudgetProfitRate() {
        return budgetProfitRate;
    }

    public void setBudgetProfitRate(BigDecimal budgetProfitRate) {
        this.budgetProfitRate = budgetProfitRate;
    }

    public BigDecimal getQuotationBudget() {
        return quotationBudget;
    }

    public void setQuotationBudget(BigDecimal quotationBudget) {
        this.quotationBudget = quotationBudget;
    }

    public BigDecimal getMaterialCostActual() {
        return materialCostActual;
    }

    public void setMaterialCostActual(BigDecimal materialCostActual) {
        this.materialCostActual = materialCostActual;
    }

    public BigDecimal getHumanCostActual() {
        return humanCostActual;
    }

    public void setHumanCostActual(BigDecimal humanCostActual) {
        this.humanCostActual = humanCostActual;
    }

    public BigDecimal getTravelCostActual() {
        return travelCostActual;
    }

    public void setTravelCostActual(BigDecimal travelCostActual) {
        this.travelCostActual = travelCostActual;
    }

    public BigDecimal getNoTravelCostActual() {
        return noTravelCostActual;
    }

    public void setNoTravelCostActual(BigDecimal noTravelCostActual) {
        this.noTravelCostActual = noTravelCostActual;
    }

    public BigDecimal getProjectCostAmount() {
        return projectCostAmount;
    }

    public void setProjectCostAmount(BigDecimal projectCostAmount) {
        this.projectCostAmount = projectCostAmount;
    }

    public BigDecimal getPerformProfitRate() {
        return performProfitRate;
    }

    public void setPerformProfitRate(BigDecimal performProfitRate) {
        this.performProfitRate = performProfitRate;
    }

    public BigDecimal getBudgetExecutionSchedule() {
        return budgetExecutionSchedule;
    }

    public void setBudgetExecutionSchedule(BigDecimal budgetExecutionSchedule) {
        this.budgetExecutionSchedule = budgetExecutionSchedule;
    }

    public BigDecimal getQuotationActual() {
        return quotationActual;
    }

    public void setQuotationActual(BigDecimal quotationActual) {
        this.quotationActual = quotationActual;
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectType=").append(projectType);
        sb.append(", ownerUserId=").append(ownerUserId);
        sb.append(", ownerName=").append(ownerName);
        sb.append(", managerId=").append(managerId);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", projectStatus=").append(projectStatus);
        sb.append(", projectMilepostName=").append(projectMilepostName);
        sb.append(", amount=").append(amount);
        sb.append(", materialQuotation=").append(materialQuotation);
        sb.append(", humanQuotation=").append(humanQuotation);
        sb.append(", travelQuotation=").append(travelQuotation);
        sb.append(", noTravelQuotation=").append(noTravelQuotation);
        sb.append(", quotationAmount=").append(quotationAmount);
        sb.append(", winRate=").append(winRate);
        sb.append(", materialBudget=").append(materialBudget);
        sb.append(", humanBudget=").append(humanBudget);
        sb.append(", travelBudget=").append(travelBudget);
        sb.append(", noTravelBudget=").append(noTravelBudget);
        sb.append(", budgetAmount=").append(budgetAmount);
        sb.append(", budgetProfitRate=").append(budgetProfitRate);
        sb.append(", quotationBudget=").append(quotationBudget);
        sb.append(", materialCostActual=").append(materialCostActual);
        sb.append(", humanCostActual=").append(humanCostActual);
        sb.append(", travelCostActual=").append(travelCostActual);
        sb.append(", noTravelCostActual=").append(noTravelCostActual);
        sb.append(", projectCostAmount=").append(projectCostAmount);
        sb.append(", performProfitRate=").append(performProfitRate);
        sb.append(", budgetExecutionSchedule=").append(budgetExecutionSchedule);
        sb.append(", quotationActual=").append(quotationActual);
        sb.append(", ouId=").append(ouId);
        sb.append(", ouName=").append(ouName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}