package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-09 10:48
 */
@ApiModel(value = "ReportProjectPurchaseOrderQuery", description = "项目PO成本明细报表")
public class ReportProjectPurchaseOrderQuery {

    private Long id;

    @ApiModelProperty(value = "项目状态，用于传参")
    private List<Integer> statuses;

    @ApiModelProperty(value = "业务部门ID列表，不为空时，只查业务部门属于此列表中的项目")
    private List<Long> unitIds;

    @ApiModelProperty(value = "项目code或名称")
    private String codeOrName;

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    @ApiModelProperty(value = "报表ID")
    private Long reportId;

    @ApiModelProperty(value = "销售部门id列表")
    private List<Long> unitIdList;

    @ApiModelProperty(value = "业务实体id列表")
    private List<Long> ouIdList;

    @ApiModelProperty(value = "项目经理")
    private String managerName;

    @ApiModelProperty(value = "项目经理")
    private Long managerId;

    @ApiModelProperty(value = "业务模式id列表")
    private List<Long> projectTypeList;

    private Date expireDate;

    private Long personal;

    private Long companyId;

    private Long createBy;

    @ApiModelProperty(value = "项目类型，不为空时，只查项目类型属于此列表中的项目")
    private List<Long> projectTypes;

    public Long getManagerId() {
        return managerId;
    }

    public void setManagerId(Long managerId) {
        this.managerId = managerId;
    }

    public List<Long> getUnitIds() {
        return unitIds;
    }

    public void setUnitIds(List<Long> unitIds) {
        this.unitIds = unitIds;
    }

    public List<Long> getProjectTypes() {
        return projectTypes;
    }

    public void setProjectTypes(List<Long> projectTypes) {
        this.projectTypes = projectTypes;
    }

    public List<Integer> getStatuses() {
        return statuses;
    }

    public void setStatuses(List<Integer> statuses) {
        this.statuses = statuses;
    }

    public String getCodeOrName() {
        return codeOrName;
    }

    public void setCodeOrName(String codeOrName) {
        this.codeOrName = codeOrName;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public List<Long> getProjectTypeList() {
        return projectTypeList;
    }

    public void setProjectTypeList(List<Long> projectTypeList) {
        this.projectTypeList = projectTypeList;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public List<Long> getUnitIdList() {
        return unitIdList;
    }

    public void setUnitIdList(List<Long> unitIdList) {
        this.unitIdList = unitIdList;
    }

    public List<Long> getOuIdList() {
        return ouIdList;
    }

    public void setOuIdList(List<Long> ouIdList) {
        this.ouIdList = ouIdList;
    }

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getPersonal() {
        return personal;
    }

    public void setPersonal(Long personal) {
        this.personal = personal;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    @Override
    public String toString() {
        return "ReportProjectPurchaseOrderQuery{" +
                "id=" + id +
                ", statuses=" + statuses +
                ", unitIds=" + unitIds +
                ", codeOrName='" + codeOrName + '\'' +
                ", executeId=" + executeId +
                ", reportId=" + reportId +
                ", unitIdList=" + unitIdList +
                ", ouIdList=" + ouIdList +
                ", managerName='" + managerName + '\'' +
                ", projectTypeList=" + projectTypeList +
                ", expireDate=" + expireDate +
                ", personal=" + personal +
                ", companyId=" + companyId +
                ", createBy=" + createBy +
                ", projectTypes=" + projectTypes +
                '}';
    }
}
