package com.midea.pam.common.statistics.dto;


import com.midea.pam.common.statistics.entity.*;
import com.midea.pam.common.statistics.vo.IncomeCalculateAutoCalculateVO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/17
 * @description
 */
public class IncomeCalculateDTO extends IncomeCalculate {

    /**
     * 预测月份
     */
    private String month;

    /**
     * 状态
     */
    private String statusesStr;

    private String projectNameOrCodeFuzzyLike;

    private List<Integer> statuses;

    private List<Long> projectIds;

    private List<Long> projectTypeIds;

    private Boolean extraFlag;

    private Date deadlineDateStart;

    private Date deadlineDateEnd;

    private List<Long> unitIds;

    private List<IncomeCalculateProject> incomeCalculateProjects;

    private List<IncomeCalculateProduct> incomeCalculateProducts;

    private List<IncomeCalculateProjectTaskSummaryDTO> incomeCalculateProjectTaskSummaryDTOS;

    private List<IncomeCalculateProjectSummaryDTO> incomeCalculateProjectSummaryDTOS;

    private List<IncomeCalculateProductTask> incomeCalculateProductTasks;

    private List<IncomeCalculateProjectTask> incomeCalculateProjectTasks;

    private List<IncomeCalculateAutoCalculateVO> incomeCalculateAutoCalculates;

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getStatusesStr() {
        return statusesStr;
    }

    public void setStatusesStr(String statusesStr) {
        this.statusesStr = statusesStr;
    }

    public List<Integer> getStatuses() {
        return statuses;
    }

    public void setStatuses(List<Integer> statuses) {
        this.statuses = statuses;
    }

    public List<Long> getProjectIds() {
        return projectIds;
    }

    public void setProjectIds(List<Long> projectIds) {
        this.projectIds = projectIds;
    }

    public List<Long> getProjectTypeIds() {
        return projectTypeIds;
    }

    public void setProjectTypeIds(List<Long> projectTypeIds) {
        this.projectTypeIds = projectTypeIds;
    }

    public String getProjectNameOrCodeFuzzyLike() {
        return projectNameOrCodeFuzzyLike;
    }

    public void setProjectNameOrCodeFuzzyLike(String projectNameOrCodeFuzzyLike) {
        this.projectNameOrCodeFuzzyLike = projectNameOrCodeFuzzyLike;
    }

    public Boolean getExtraFlag() {
        return extraFlag;
    }

    public void setExtraFlag(Boolean extraFlag) {
        this.extraFlag = extraFlag;
    }

    public Date getDeadlineDateStart() {
        return deadlineDateStart;
    }

    public void setDeadlineDateStart(Date deadlineDateStart) {
        this.deadlineDateStart = deadlineDateStart;
    }

    public Date getDeadlineDateEnd() {
        return deadlineDateEnd;
    }

    public void setDeadlineDateEnd(Date deadlineDateEnd) {
        this.deadlineDateEnd = deadlineDateEnd;
    }

    public List<Long> getUnitIds() {
        return unitIds;
    }

    public void setUnitIds(List<Long> unitIds) {
        this.unitIds = unitIds;
    }

    public List<IncomeCalculateProject> getIncomeCalculateProjects() {
        return incomeCalculateProjects;
    }

    public void setIncomeCalculateProjects(List<IncomeCalculateProject> incomeCalculateProjects) {
        this.incomeCalculateProjects = incomeCalculateProjects;
    }

    public List<IncomeCalculateProduct> getIncomeCalculateProducts() {
        return incomeCalculateProducts;
    }

    public void setIncomeCalculateProducts(List<IncomeCalculateProduct> incomeCalculateProducts) {
        this.incomeCalculateProducts = incomeCalculateProducts;
    }

    public List<IncomeCalculateProjectTaskSummaryDTO> getIncomeCalculateProjectTaskSummaryDTOS() {
        return incomeCalculateProjectTaskSummaryDTOS;
    }

    public void setIncomeCalculateProjectTaskSummaryDTOS(List<IncomeCalculateProjectTaskSummaryDTO> incomeCalculateProjectTaskSummaryDTOS) {
        this.incomeCalculateProjectTaskSummaryDTOS = incomeCalculateProjectTaskSummaryDTOS;
    }

    public List<IncomeCalculateProductTask> getIncomeCalculateProductTasks() {
        return incomeCalculateProductTasks;
    }

    public void setIncomeCalculateProductTasks(List<IncomeCalculateProductTask> incomeCalculateProductTasks) {
        this.incomeCalculateProductTasks = incomeCalculateProductTasks;
    }

    public List<IncomeCalculateProjectSummaryDTO> getIncomeCalculateProjectSummaryDTOS() {
        return incomeCalculateProjectSummaryDTOS;
    }

    public void setIncomeCalculateProjectSummaryDTOS(List<IncomeCalculateProjectSummaryDTO> incomeCalculateProjectSummaryDTOS) {
        this.incomeCalculateProjectSummaryDTOS = incomeCalculateProjectSummaryDTOS;
    }

    public List<IncomeCalculateProjectTask> getIncomeCalculateProjectTasks() {
        return incomeCalculateProjectTasks;
    }

    public void setIncomeCalculateProjectTasks(List<IncomeCalculateProjectTask> incomeCalculateProjectTasks) {
        this.incomeCalculateProjectTasks = incomeCalculateProjectTasks;
    }

    public List<IncomeCalculateAutoCalculateVO> getIncomeCalculateAutoCalculates() {
        return incomeCalculateAutoCalculates;
    }

    public void setIncomeCalculateAutoCalculates(List<IncomeCalculateAutoCalculateVO> incomeCalculateAutoCalculates) {
        this.incomeCalculateAutoCalculates = incomeCalculateAutoCalculates;
    }
}
