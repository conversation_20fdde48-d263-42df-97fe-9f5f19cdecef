package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportBusinessWinExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportBusinessWinExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeIsNull() {
            addCriterion("business_code is null");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeIsNotNull() {
            addCriterion("business_code is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeEqualTo(String value) {
            addCriterion("business_code =", value, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeNotEqualTo(String value) {
            addCriterion("business_code <>", value, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeGreaterThan(String value) {
            addCriterion("business_code >", value, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeGreaterThanOrEqualTo(String value) {
            addCriterion("business_code >=", value, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeLessThan(String value) {
            addCriterion("business_code <", value, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeLessThanOrEqualTo(String value) {
            addCriterion("business_code <=", value, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeLike(String value) {
            addCriterion("business_code like", value, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeNotLike(String value) {
            addCriterion("business_code not like", value, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeIn(List<String> values) {
            addCriterion("business_code in", values, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeNotIn(List<String> values) {
            addCriterion("business_code not in", values, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeBetween(String value1, String value2) {
            addCriterion("business_code between", value1, value2, "businessCode");
            return (Criteria) this;
        }

        public Criteria andBusinessCodeNotBetween(String value1, String value2) {
            addCriterion("business_code not between", value1, value2, "businessCode");
            return (Criteria) this;
        }

        public Criteria andOwnerNameIsNull() {
            addCriterion("owner_name is null");
            return (Criteria) this;
        }

        public Criteria andOwnerNameIsNotNull() {
            addCriterion("owner_name is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerNameEqualTo(String value) {
            addCriterion("owner_name =", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotEqualTo(String value) {
            addCriterion("owner_name <>", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameGreaterThan(String value) {
            addCriterion("owner_name >", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameGreaterThanOrEqualTo(String value) {
            addCriterion("owner_name >=", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameLessThan(String value) {
            addCriterion("owner_name <", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameLessThanOrEqualTo(String value) {
            addCriterion("owner_name <=", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameLike(String value) {
            addCriterion("owner_name like", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotLike(String value) {
            addCriterion("owner_name not like", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameIn(List<String> values) {
            addCriterion("owner_name in", values, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotIn(List<String> values) {
            addCriterion("owner_name not in", values, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameBetween(String value1, String value2) {
            addCriterion("owner_name between", value1, value2, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotBetween(String value1, String value2) {
            addCriterion("owner_name not between", value1, value2, "ownerName");
            return (Criteria) this;
        }

        public Criteria andCheckAtIsNull() {
            addCriterion("check_at is null");
            return (Criteria) this;
        }

        public Criteria andCheckAtIsNotNull() {
            addCriterion("check_at is not null");
            return (Criteria) this;
        }

        public Criteria andCheckAtEqualTo(Date value) {
            addCriterion("check_at =", value, "checkAt");
            return (Criteria) this;
        }

        public Criteria andCheckAtNotEqualTo(Date value) {
            addCriterion("check_at <>", value, "checkAt");
            return (Criteria) this;
        }

        public Criteria andCheckAtGreaterThan(Date value) {
            addCriterion("check_at >", value, "checkAt");
            return (Criteria) this;
        }

        public Criteria andCheckAtGreaterThanOrEqualTo(Date value) {
            addCriterion("check_at >=", value, "checkAt");
            return (Criteria) this;
        }

        public Criteria andCheckAtLessThan(Date value) {
            addCriterion("check_at <", value, "checkAt");
            return (Criteria) this;
        }

        public Criteria andCheckAtLessThanOrEqualTo(Date value) {
            addCriterion("check_at <=", value, "checkAt");
            return (Criteria) this;
        }

        public Criteria andCheckAtIn(List<Date> values) {
            addCriterion("check_at in", values, "checkAt");
            return (Criteria) this;
        }

        public Criteria andCheckAtNotIn(List<Date> values) {
            addCriterion("check_at not in", values, "checkAt");
            return (Criteria) this;
        }

        public Criteria andCheckAtBetween(Date value1, Date value2) {
            addCriterion("check_at between", value1, value2, "checkAt");
            return (Criteria) this;
        }

        public Criteria andCheckAtNotBetween(Date value1, Date value2) {
            addCriterion("check_at not between", value1, value2, "checkAt");
            return (Criteria) this;
        }

        public Criteria andSalesdepartmentnameIsNull() {
            addCriterion("salesDepartmentName is null");
            return (Criteria) this;
        }

        public Criteria andSalesdepartmentnameIsNotNull() {
            addCriterion("salesDepartmentName is not null");
            return (Criteria) this;
        }

        public Criteria andSalesdepartmentnameEqualTo(String value) {
            addCriterion("salesDepartmentName =", value, "salesdepartmentname");
            return (Criteria) this;
        }

        public Criteria andSalesdepartmentnameNotEqualTo(String value) {
            addCriterion("salesDepartmentName <>", value, "salesdepartmentname");
            return (Criteria) this;
        }

        public Criteria andSalesdepartmentnameGreaterThan(String value) {
            addCriterion("salesDepartmentName >", value, "salesdepartmentname");
            return (Criteria) this;
        }

        public Criteria andSalesdepartmentnameGreaterThanOrEqualTo(String value) {
            addCriterion("salesDepartmentName >=", value, "salesdepartmentname");
            return (Criteria) this;
        }

        public Criteria andSalesdepartmentnameLessThan(String value) {
            addCriterion("salesDepartmentName <", value, "salesdepartmentname");
            return (Criteria) this;
        }

        public Criteria andSalesdepartmentnameLessThanOrEqualTo(String value) {
            addCriterion("salesDepartmentName <=", value, "salesdepartmentname");
            return (Criteria) this;
        }

        public Criteria andSalesdepartmentnameLike(String value) {
            addCriterion("salesDepartmentName like", value, "salesdepartmentname");
            return (Criteria) this;
        }

        public Criteria andSalesdepartmentnameNotLike(String value) {
            addCriterion("salesDepartmentName not like", value, "salesdepartmentname");
            return (Criteria) this;
        }

        public Criteria andSalesdepartmentnameIn(List<String> values) {
            addCriterion("salesDepartmentName in", values, "salesdepartmentname");
            return (Criteria) this;
        }

        public Criteria andSalesdepartmentnameNotIn(List<String> values) {
            addCriterion("salesDepartmentName not in", values, "salesdepartmentname");
            return (Criteria) this;
        }

        public Criteria andSalesdepartmentnameBetween(String value1, String value2) {
            addCriterion("salesDepartmentName between", value1, value2, "salesdepartmentname");
            return (Criteria) this;
        }

        public Criteria andSalesdepartmentnameNotBetween(String value1, String value2) {
            addCriterion("salesDepartmentName not between", value1, value2, "salesdepartmentname");
            return (Criteria) this;
        }

        public Criteria andIndustryIsNull() {
            addCriterion("industry is null");
            return (Criteria) this;
        }

        public Criteria andIndustryIsNotNull() {
            addCriterion("industry is not null");
            return (Criteria) this;
        }

        public Criteria andIndustryEqualTo(String value) {
            addCriterion("industry =", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotEqualTo(String value) {
            addCriterion("industry <>", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryGreaterThan(String value) {
            addCriterion("industry >", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryGreaterThanOrEqualTo(String value) {
            addCriterion("industry >=", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLessThan(String value) {
            addCriterion("industry <", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLessThanOrEqualTo(String value) {
            addCriterion("industry <=", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLike(String value) {
            addCriterion("industry like", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotLike(String value) {
            addCriterion("industry not like", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryIn(List<String> values) {
            addCriterion("industry in", values, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotIn(List<String> values) {
            addCriterion("industry not in", values, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryBetween(String value1, String value2) {
            addCriterion("industry between", value1, value2, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotBetween(String value1, String value2) {
            addCriterion("industry not between", value1, value2, "industry");
            return (Criteria) this;
        }

        public Criteria andDepartmentIsNull() {
            addCriterion("department is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIsNotNull() {
            addCriterion("department is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentEqualTo(String value) {
            addCriterion("department =", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotEqualTo(String value) {
            addCriterion("department <>", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThan(String value) {
            addCriterion("department >", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThanOrEqualTo(String value) {
            addCriterion("department >=", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThan(String value) {
            addCriterion("department <", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThanOrEqualTo(String value) {
            addCriterion("department <=", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLike(String value) {
            addCriterion("department like", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotLike(String value) {
            addCriterion("department not like", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentIn(List<String> values) {
            addCriterion("department in", values, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotIn(List<String> values) {
            addCriterion("department not in", values, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentBetween(String value1, String value2) {
            addCriterion("department between", value1, value2, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotBetween(String value1, String value2) {
            addCriterion("department not between", value1, value2, "department");
            return (Criteria) this;
        }

        public Criteria andRegionnameIsNull() {
            addCriterion("regionName is null");
            return (Criteria) this;
        }

        public Criteria andRegionnameIsNotNull() {
            addCriterion("regionName is not null");
            return (Criteria) this;
        }

        public Criteria andRegionnameEqualTo(String value) {
            addCriterion("regionName =", value, "regionname");
            return (Criteria) this;
        }

        public Criteria andRegionnameNotEqualTo(String value) {
            addCriterion("regionName <>", value, "regionname");
            return (Criteria) this;
        }

        public Criteria andRegionnameGreaterThan(String value) {
            addCriterion("regionName >", value, "regionname");
            return (Criteria) this;
        }

        public Criteria andRegionnameGreaterThanOrEqualTo(String value) {
            addCriterion("regionName >=", value, "regionname");
            return (Criteria) this;
        }

        public Criteria andRegionnameLessThan(String value) {
            addCriterion("regionName <", value, "regionname");
            return (Criteria) this;
        }

        public Criteria andRegionnameLessThanOrEqualTo(String value) {
            addCriterion("regionName <=", value, "regionname");
            return (Criteria) this;
        }

        public Criteria andRegionnameLike(String value) {
            addCriterion("regionName like", value, "regionname");
            return (Criteria) this;
        }

        public Criteria andRegionnameNotLike(String value) {
            addCriterion("regionName not like", value, "regionname");
            return (Criteria) this;
        }

        public Criteria andRegionnameIn(List<String> values) {
            addCriterion("regionName in", values, "regionname");
            return (Criteria) this;
        }

        public Criteria andRegionnameNotIn(List<String> values) {
            addCriterion("regionName not in", values, "regionname");
            return (Criteria) this;
        }

        public Criteria andRegionnameBetween(String value1, String value2) {
            addCriterion("regionName between", value1, value2, "regionname");
            return (Criteria) this;
        }

        public Criteria andRegionnameNotBetween(String value1, String value2) {
            addCriterion("regionName not between", value1, value2, "regionname");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNull() {
            addCriterion("currency_code is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNotNull() {
            addCriterion("currency_code is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeEqualTo(String value) {
            addCriterion("currency_code =", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotEqualTo(String value) {
            addCriterion("currency_code <>", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThan(String value) {
            addCriterion("currency_code >", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("currency_code >=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThan(String value) {
            addCriterion("currency_code <", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThanOrEqualTo(String value) {
            addCriterion("currency_code <=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLike(String value) {
            addCriterion("currency_code like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotLike(String value) {
            addCriterion("currency_code not like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIn(List<String> values) {
            addCriterion("currency_code in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotIn(List<String> values) {
            addCriterion("currency_code not in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeBetween(String value1, String value2) {
            addCriterion("currency_code between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotBetween(String value1, String value2) {
            addCriterion("currency_code not between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andQuoteWithTaxIsNull() {
            addCriterion("quote_with_tax is null");
            return (Criteria) this;
        }

        public Criteria andQuoteWithTaxIsNotNull() {
            addCriterion("quote_with_tax is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteWithTaxEqualTo(BigDecimal value) {
            addCriterion("quote_with_tax =", value, "quoteWithTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithTaxNotEqualTo(BigDecimal value) {
            addCriterion("quote_with_tax <>", value, "quoteWithTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithTaxGreaterThan(BigDecimal value) {
            addCriterion("quote_with_tax >", value, "quoteWithTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_with_tax >=", value, "quoteWithTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithTaxLessThan(BigDecimal value) {
            addCriterion("quote_with_tax <", value, "quoteWithTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_with_tax <=", value, "quoteWithTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithTaxIn(List<BigDecimal> values) {
            addCriterion("quote_with_tax in", values, "quoteWithTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithTaxNotIn(List<BigDecimal> values) {
            addCriterion("quote_with_tax not in", values, "quoteWithTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_with_tax between", value1, value2, "quoteWithTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_with_tax not between", value1, value2, "quoteWithTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithoutTaxIsNull() {
            addCriterion("quote_without_tax is null");
            return (Criteria) this;
        }

        public Criteria andQuoteWithoutTaxIsNotNull() {
            addCriterion("quote_without_tax is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteWithoutTaxEqualTo(BigDecimal value) {
            addCriterion("quote_without_tax =", value, "quoteWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithoutTaxNotEqualTo(BigDecimal value) {
            addCriterion("quote_without_tax <>", value, "quoteWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithoutTaxGreaterThan(BigDecimal value) {
            addCriterion("quote_without_tax >", value, "quoteWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithoutTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_without_tax >=", value, "quoteWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithoutTaxLessThan(BigDecimal value) {
            addCriterion("quote_without_tax <", value, "quoteWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithoutTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_without_tax <=", value, "quoteWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithoutTaxIn(List<BigDecimal> values) {
            addCriterion("quote_without_tax in", values, "quoteWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithoutTaxNotIn(List<BigDecimal> values) {
            addCriterion("quote_without_tax not in", values, "quoteWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithoutTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_without_tax between", value1, value2, "quoteWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteWithoutTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_without_tax not between", value1, value2, "quoteWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxIsNull() {
            addCriterion("quote_cost_hardware_without_tax is null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxIsNotNull() {
            addCriterion("quote_cost_hardware_without_tax is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxEqualTo(BigDecimal value) {
            addCriterion("quote_cost_hardware_without_tax =", value, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxNotEqualTo(BigDecimal value) {
            addCriterion("quote_cost_hardware_without_tax <>", value, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxGreaterThan(BigDecimal value) {
            addCriterion("quote_cost_hardware_without_tax >", value, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_hardware_without_tax >=", value, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxLessThan(BigDecimal value) {
            addCriterion("quote_cost_hardware_without_tax <", value, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_hardware_without_tax <=", value, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxIn(List<BigDecimal> values) {
            addCriterion("quote_cost_hardware_without_tax in", values, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxNotIn(List<BigDecimal> values) {
            addCriterion("quote_cost_hardware_without_tax not in", values, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_hardware_without_tax between", value1, value2, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_hardware_without_tax not between", value1, value2, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxIsNull() {
            addCriterion("quote_cost_labour_without_tax is null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxIsNotNull() {
            addCriterion("quote_cost_labour_without_tax is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxEqualTo(BigDecimal value) {
            addCriterion("quote_cost_labour_without_tax =", value, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxNotEqualTo(BigDecimal value) {
            addCriterion("quote_cost_labour_without_tax <>", value, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxGreaterThan(BigDecimal value) {
            addCriterion("quote_cost_labour_without_tax >", value, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_labour_without_tax >=", value, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxLessThan(BigDecimal value) {
            addCriterion("quote_cost_labour_without_tax <", value, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_labour_without_tax <=", value, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxIn(List<BigDecimal> values) {
            addCriterion("quote_cost_labour_without_tax in", values, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxNotIn(List<BigDecimal> values) {
            addCriterion("quote_cost_labour_without_tax not in", values, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_labour_without_tax between", value1, value2, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_labour_without_tax not between", value1, value2, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxIsNull() {
            addCriterion("quote_cost_travel_without_tax is null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxIsNotNull() {
            addCriterion("quote_cost_travel_without_tax is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxEqualTo(BigDecimal value) {
            addCriterion("quote_cost_travel_without_tax =", value, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxNotEqualTo(BigDecimal value) {
            addCriterion("quote_cost_travel_without_tax <>", value, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxGreaterThan(BigDecimal value) {
            addCriterion("quote_cost_travel_without_tax >", value, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_travel_without_tax >=", value, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxLessThan(BigDecimal value) {
            addCriterion("quote_cost_travel_without_tax <", value, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_travel_without_tax <=", value, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxIn(List<BigDecimal> values) {
            addCriterion("quote_cost_travel_without_tax in", values, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxNotIn(List<BigDecimal> values) {
            addCriterion("quote_cost_travel_without_tax not in", values, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_travel_without_tax between", value1, value2, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_travel_without_tax not between", value1, value2, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxIsNull() {
            addCriterion("quote_cost_other_without_tax is null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxIsNotNull() {
            addCriterion("quote_cost_other_without_tax is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxEqualTo(BigDecimal value) {
            addCriterion("quote_cost_other_without_tax =", value, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxNotEqualTo(BigDecimal value) {
            addCriterion("quote_cost_other_without_tax <>", value, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxGreaterThan(BigDecimal value) {
            addCriterion("quote_cost_other_without_tax >", value, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_other_without_tax >=", value, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxLessThan(BigDecimal value) {
            addCriterion("quote_cost_other_without_tax <", value, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_other_without_tax <=", value, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxIn(List<BigDecimal> values) {
            addCriterion("quote_cost_other_without_tax in", values, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxNotIn(List<BigDecimal> values) {
            addCriterion("quote_cost_other_without_tax not in", values, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_other_without_tax between", value1, value2, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_other_without_tax not between", value1, value2, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalIsNull() {
            addCriterion("quote_cost_total is null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalIsNotNull() {
            addCriterion("quote_cost_total is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalEqualTo(BigDecimal value) {
            addCriterion("quote_cost_total =", value, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalNotEqualTo(BigDecimal value) {
            addCriterion("quote_cost_total <>", value, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalGreaterThan(BigDecimal value) {
            addCriterion("quote_cost_total >", value, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_total >=", value, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalLessThan(BigDecimal value) {
            addCriterion("quote_cost_total <", value, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_total <=", value, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalIn(List<BigDecimal> values) {
            addCriterion("quote_cost_total in", values, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalNotIn(List<BigDecimal> values) {
            addCriterion("quote_cost_total not in", values, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_total between", value1, value2, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_total not between", value1, value2, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitAmountWithoutTaxIsNull() {
            addCriterion("quote_profit_amount_without_tax is null");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitAmountWithoutTaxIsNotNull() {
            addCriterion("quote_profit_amount_without_tax is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitAmountWithoutTaxEqualTo(BigDecimal value) {
            addCriterion("quote_profit_amount_without_tax =", value, "quoteProfitAmountWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitAmountWithoutTaxNotEqualTo(BigDecimal value) {
            addCriterion("quote_profit_amount_without_tax <>", value, "quoteProfitAmountWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitAmountWithoutTaxGreaterThan(BigDecimal value) {
            addCriterion("quote_profit_amount_without_tax >", value, "quoteProfitAmountWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitAmountWithoutTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_profit_amount_without_tax >=", value, "quoteProfitAmountWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitAmountWithoutTaxLessThan(BigDecimal value) {
            addCriterion("quote_profit_amount_without_tax <", value, "quoteProfitAmountWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitAmountWithoutTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_profit_amount_without_tax <=", value, "quoteProfitAmountWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitAmountWithoutTaxIn(List<BigDecimal> values) {
            addCriterion("quote_profit_amount_without_tax in", values, "quoteProfitAmountWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitAmountWithoutTaxNotIn(List<BigDecimal> values) {
            addCriterion("quote_profit_amount_without_tax not in", values, "quoteProfitAmountWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitAmountWithoutTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_profit_amount_without_tax between", value1, value2, "quoteProfitAmountWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitAmountWithoutTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_profit_amount_without_tax not between", value1, value2, "quoteProfitAmountWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitPercentWithoutTaxIsNull() {
            addCriterion("quote_profit_percent_without_tax is null");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitPercentWithoutTaxIsNotNull() {
            addCriterion("quote_profit_percent_without_tax is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitPercentWithoutTaxEqualTo(BigDecimal value) {
            addCriterion("quote_profit_percent_without_tax =", value, "quoteProfitPercentWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitPercentWithoutTaxNotEqualTo(BigDecimal value) {
            addCriterion("quote_profit_percent_without_tax <>", value, "quoteProfitPercentWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitPercentWithoutTaxGreaterThan(BigDecimal value) {
            addCriterion("quote_profit_percent_without_tax >", value, "quoteProfitPercentWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitPercentWithoutTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_profit_percent_without_tax >=", value, "quoteProfitPercentWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitPercentWithoutTaxLessThan(BigDecimal value) {
            addCriterion("quote_profit_percent_without_tax <", value, "quoteProfitPercentWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitPercentWithoutTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_profit_percent_without_tax <=", value, "quoteProfitPercentWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitPercentWithoutTaxIn(List<BigDecimal> values) {
            addCriterion("quote_profit_percent_without_tax in", values, "quoteProfitPercentWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitPercentWithoutTaxNotIn(List<BigDecimal> values) {
            addCriterion("quote_profit_percent_without_tax not in", values, "quoteProfitPercentWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitPercentWithoutTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_profit_percent_without_tax between", value1, value2, "quoteProfitPercentWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteProfitPercentWithoutTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_profit_percent_without_tax not between", value1, value2, "quoteProfitPercentWithoutTax");
            return (Criteria) this;
        }

        public Criteria andCustomerPamCodeIsNull() {
            addCriterion("customer_pam_code is null");
            return (Criteria) this;
        }

        public Criteria andCustomerPamCodeIsNotNull() {
            addCriterion("customer_pam_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerPamCodeEqualTo(String value) {
            addCriterion("customer_pam_code =", value, "customerPamCode");
            return (Criteria) this;
        }

        public Criteria andCustomerPamCodeNotEqualTo(String value) {
            addCriterion("customer_pam_code <>", value, "customerPamCode");
            return (Criteria) this;
        }

        public Criteria andCustomerPamCodeGreaterThan(String value) {
            addCriterion("customer_pam_code >", value, "customerPamCode");
            return (Criteria) this;
        }

        public Criteria andCustomerPamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("customer_pam_code >=", value, "customerPamCode");
            return (Criteria) this;
        }

        public Criteria andCustomerPamCodeLessThan(String value) {
            addCriterion("customer_pam_code <", value, "customerPamCode");
            return (Criteria) this;
        }

        public Criteria andCustomerPamCodeLessThanOrEqualTo(String value) {
            addCriterion("customer_pam_code <=", value, "customerPamCode");
            return (Criteria) this;
        }

        public Criteria andCustomerPamCodeLike(String value) {
            addCriterion("customer_pam_code like", value, "customerPamCode");
            return (Criteria) this;
        }

        public Criteria andCustomerPamCodeNotLike(String value) {
            addCriterion("customer_pam_code not like", value, "customerPamCode");
            return (Criteria) this;
        }

        public Criteria andCustomerPamCodeIn(List<String> values) {
            addCriterion("customer_pam_code in", values, "customerPamCode");
            return (Criteria) this;
        }

        public Criteria andCustomerPamCodeNotIn(List<String> values) {
            addCriterion("customer_pam_code not in", values, "customerPamCode");
            return (Criteria) this;
        }

        public Criteria andCustomerPamCodeBetween(String value1, String value2) {
            addCriterion("customer_pam_code between", value1, value2, "customerPamCode");
            return (Criteria) this;
        }

        public Criteria andCustomerPamCodeNotBetween(String value1, String value2) {
            addCriterion("customer_pam_code not between", value1, value2, "customerPamCode");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNull() {
            addCriterion("contract_code is null");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNotNull() {
            addCriterion("contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andContractCodeEqualTo(String value) {
            addCriterion("contract_code =", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotEqualTo(String value) {
            addCriterion("contract_code <>", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThan(String value) {
            addCriterion("contract_code >", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_code >=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThan(String value) {
            addCriterion("contract_code <", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThanOrEqualTo(String value) {
            addCriterion("contract_code <=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLike(String value) {
            addCriterion("contract_code like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotLike(String value) {
            addCriterion("contract_code not like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeIn(List<String> values) {
            addCriterion("contract_code in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotIn(List<String> values) {
            addCriterion("contract_code not in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeBetween(String value1, String value2) {
            addCriterion("contract_code between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotBetween(String value1, String value2) {
            addCriterion("contract_code not between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractNameIsNull() {
            addCriterion("contract_name is null");
            return (Criteria) this;
        }

        public Criteria andContractNameIsNotNull() {
            addCriterion("contract_name is not null");
            return (Criteria) this;
        }

        public Criteria andContractNameEqualTo(String value) {
            addCriterion("contract_name =", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotEqualTo(String value) {
            addCriterion("contract_name <>", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThan(String value) {
            addCriterion("contract_name >", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThanOrEqualTo(String value) {
            addCriterion("contract_name >=", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameLessThan(String value) {
            addCriterion("contract_name <", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameLessThanOrEqualTo(String value) {
            addCriterion("contract_name <=", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameLike(String value) {
            addCriterion("contract_name like", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotLike(String value) {
            addCriterion("contract_name not like", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameIn(List<String> values) {
            addCriterion("contract_name in", values, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotIn(List<String> values) {
            addCriterion("contract_name not in", values, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameBetween(String value1, String value2) {
            addCriterion("contract_name between", value1, value2, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotBetween(String value1, String value2) {
            addCriterion("contract_name not between", value1, value2, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractAmountIsNull() {
            addCriterion("contract_amount is null");
            return (Criteria) this;
        }

        public Criteria andContractAmountIsNotNull() {
            addCriterion("contract_amount is not null");
            return (Criteria) this;
        }

        public Criteria andContractAmountEqualTo(BigDecimal value) {
            addCriterion("contract_amount =", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountNotEqualTo(BigDecimal value) {
            addCriterion("contract_amount <>", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountGreaterThan(BigDecimal value) {
            addCriterion("contract_amount >", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_amount >=", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountLessThan(BigDecimal value) {
            addCriterion("contract_amount <", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_amount <=", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountIn(List<BigDecimal> values) {
            addCriterion("contract_amount in", values, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountNotIn(List<BigDecimal> values) {
            addCriterion("contract_amount not in", values, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_amount between", value1, value2, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_amount not between", value1, value2, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractExcludingTaxAmountIsNull() {
            addCriterion("contract_excluding_tax_amount is null");
            return (Criteria) this;
        }

        public Criteria andContractExcludingTaxAmountIsNotNull() {
            addCriterion("contract_excluding_tax_amount is not null");
            return (Criteria) this;
        }

        public Criteria andContractExcludingTaxAmountEqualTo(BigDecimal value) {
            addCriterion("contract_excluding_tax_amount =", value, "contractExcludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andContractExcludingTaxAmountNotEqualTo(BigDecimal value) {
            addCriterion("contract_excluding_tax_amount <>", value, "contractExcludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andContractExcludingTaxAmountGreaterThan(BigDecimal value) {
            addCriterion("contract_excluding_tax_amount >", value, "contractExcludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andContractExcludingTaxAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_excluding_tax_amount >=", value, "contractExcludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andContractExcludingTaxAmountLessThan(BigDecimal value) {
            addCriterion("contract_excluding_tax_amount <", value, "contractExcludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andContractExcludingTaxAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_excluding_tax_amount <=", value, "contractExcludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andContractExcludingTaxAmountIn(List<BigDecimal> values) {
            addCriterion("contract_excluding_tax_amount in", values, "contractExcludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andContractExcludingTaxAmountNotIn(List<BigDecimal> values) {
            addCriterion("contract_excluding_tax_amount not in", values, "contractExcludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andContractExcludingTaxAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_excluding_tax_amount between", value1, value2, "contractExcludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andContractExcludingTaxAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_excluding_tax_amount not between", value1, value2, "contractExcludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeIsNull() {
            addCriterion("contract_start_time is null");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeIsNotNull() {
            addCriterion("contract_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeEqualTo(Date value) {
            addCriterion("contract_start_time =", value, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeNotEqualTo(Date value) {
            addCriterion("contract_start_time <>", value, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeGreaterThan(Date value) {
            addCriterion("contract_start_time >", value, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("contract_start_time >=", value, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeLessThan(Date value) {
            addCriterion("contract_start_time <", value, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("contract_start_time <=", value, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeIn(List<Date> values) {
            addCriterion("contract_start_time in", values, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeNotIn(List<Date> values) {
            addCriterion("contract_start_time not in", values, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeBetween(Date value1, Date value2) {
            addCriterion("contract_start_time between", value1, value2, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("contract_start_time not between", value1, value2, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectIncomeIsNull() {
            addCriterion("contract_total_project_income is null");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectIncomeIsNotNull() {
            addCriterion("contract_total_project_income is not null");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectIncomeEqualTo(BigDecimal value) {
            addCriterion("contract_total_project_income =", value, "contractTotalProjectIncome");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectIncomeNotEqualTo(BigDecimal value) {
            addCriterion("contract_total_project_income <>", value, "contractTotalProjectIncome");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectIncomeGreaterThan(BigDecimal value) {
            addCriterion("contract_total_project_income >", value, "contractTotalProjectIncome");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_total_project_income >=", value, "contractTotalProjectIncome");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectIncomeLessThan(BigDecimal value) {
            addCriterion("contract_total_project_income <", value, "contractTotalProjectIncome");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_total_project_income <=", value, "contractTotalProjectIncome");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectIncomeIn(List<BigDecimal> values) {
            addCriterion("contract_total_project_income in", values, "contractTotalProjectIncome");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectIncomeNotIn(List<BigDecimal> values) {
            addCriterion("contract_total_project_income not in", values, "contractTotalProjectIncome");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_total_project_income between", value1, value2, "contractTotalProjectIncome");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_total_project_income not between", value1, value2, "contractTotalProjectIncome");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectCostIsNull() {
            addCriterion("contract_total_project_cost is null");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectCostIsNotNull() {
            addCriterion("contract_total_project_cost is not null");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectCostEqualTo(BigDecimal value) {
            addCriterion("contract_total_project_cost =", value, "contractTotalProjectCost");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectCostNotEqualTo(BigDecimal value) {
            addCriterion("contract_total_project_cost <>", value, "contractTotalProjectCost");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectCostGreaterThan(BigDecimal value) {
            addCriterion("contract_total_project_cost >", value, "contractTotalProjectCost");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_total_project_cost >=", value, "contractTotalProjectCost");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectCostLessThan(BigDecimal value) {
            addCriterion("contract_total_project_cost <", value, "contractTotalProjectCost");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_total_project_cost <=", value, "contractTotalProjectCost");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectCostIn(List<BigDecimal> values) {
            addCriterion("contract_total_project_cost in", values, "contractTotalProjectCost");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectCostNotIn(List<BigDecimal> values) {
            addCriterion("contract_total_project_cost not in", values, "contractTotalProjectCost");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_total_project_cost between", value1, value2, "contractTotalProjectCost");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_total_project_cost not between", value1, value2, "contractTotalProjectCost");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectProfitAmountIsNull() {
            addCriterion("contract_total_project_profit_amount is null");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectProfitAmountIsNotNull() {
            addCriterion("contract_total_project_profit_amount is not null");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectProfitAmountEqualTo(BigDecimal value) {
            addCriterion("contract_total_project_profit_amount =", value, "contractTotalProjectProfitAmount");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectProfitAmountNotEqualTo(BigDecimal value) {
            addCriterion("contract_total_project_profit_amount <>", value, "contractTotalProjectProfitAmount");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectProfitAmountGreaterThan(BigDecimal value) {
            addCriterion("contract_total_project_profit_amount >", value, "contractTotalProjectProfitAmount");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectProfitAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_total_project_profit_amount >=", value, "contractTotalProjectProfitAmount");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectProfitAmountLessThan(BigDecimal value) {
            addCriterion("contract_total_project_profit_amount <", value, "contractTotalProjectProfitAmount");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectProfitAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_total_project_profit_amount <=", value, "contractTotalProjectProfitAmount");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectProfitAmountIn(List<BigDecimal> values) {
            addCriterion("contract_total_project_profit_amount in", values, "contractTotalProjectProfitAmount");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectProfitAmountNotIn(List<BigDecimal> values) {
            addCriterion("contract_total_project_profit_amount not in", values, "contractTotalProjectProfitAmount");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectProfitAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_total_project_profit_amount between", value1, value2, "contractTotalProjectProfitAmount");
            return (Criteria) this;
        }

        public Criteria andContractTotalProjectProfitAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_total_project_profit_amount not between", value1, value2, "contractTotalProjectProfitAmount");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}