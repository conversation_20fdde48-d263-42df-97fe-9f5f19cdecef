package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.ProjectCostHumanDetailRecord;

public class ProjectCostHumanDetailRecordDto extends ProjectCostHumanDetailRecord {

    //是否开票
    private Integer invoiceApplyFlag = 0;

    //是否结转
    private Integer costCollectionFlag = 0;


    public Integer getInvoiceApplyFlag() {
        return invoiceApplyFlag;
    }

    public void setInvoiceApplyFlag(Integer invoiceApplyFlag) {
        this.invoiceApplyFlag = invoiceApplyFlag;
    }

    public Integer getCostCollectionFlag() {
        return costCollectionFlag;
    }

    public void setCostCollectionFlag(Integer costCollectionFlag) {
        this.costCollectionFlag = costCollectionFlag;
    }

}

