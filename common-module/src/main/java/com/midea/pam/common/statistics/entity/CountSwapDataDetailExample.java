package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CountSwapDataDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public CountSwapDataDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andDivisionIsNull() {
            addCriterion("division is null");
            return (Criteria) this;
        }

        public Criteria andDivisionIsNotNull() {
            addCriterion("division is not null");
            return (Criteria) this;
        }

        public Criteria andDivisionEqualTo(String value) {
            addCriterion("division =", value, "division");
            return (Criteria) this;
        }

        public Criteria andDivisionNotEqualTo(String value) {
            addCriterion("division <>", value, "division");
            return (Criteria) this;
        }

        public Criteria andDivisionGreaterThan(String value) {
            addCriterion("division >", value, "division");
            return (Criteria) this;
        }

        public Criteria andDivisionGreaterThanOrEqualTo(String value) {
            addCriterion("division >=", value, "division");
            return (Criteria) this;
        }

        public Criteria andDivisionLessThan(String value) {
            addCriterion("division <", value, "division");
            return (Criteria) this;
        }

        public Criteria andDivisionLessThanOrEqualTo(String value) {
            addCriterion("division <=", value, "division");
            return (Criteria) this;
        }

        public Criteria andDivisionLike(String value) {
            addCriterion("division like", value, "division");
            return (Criteria) this;
        }

        public Criteria andDivisionNotLike(String value) {
            addCriterion("division not like", value, "division");
            return (Criteria) this;
        }

        public Criteria andDivisionIn(List<String> values) {
            addCriterion("division in", values, "division");
            return (Criteria) this;
        }

        public Criteria andDivisionNotIn(List<String> values) {
            addCriterion("division not in", values, "division");
            return (Criteria) this;
        }

        public Criteria andDivisionBetween(String value1, String value2) {
            addCriterion("division between", value1, value2, "division");
            return (Criteria) this;
        }

        public Criteria andDivisionNotBetween(String value1, String value2) {
            addCriterion("division not between", value1, value2, "division");
            return (Criteria) this;
        }

        public Criteria andProduectDepartmentIsNull() {
            addCriterion("produect_department is null");
            return (Criteria) this;
        }

        public Criteria andProduectDepartmentIsNotNull() {
            addCriterion("produect_department is not null");
            return (Criteria) this;
        }

        public Criteria andProduectDepartmentEqualTo(String value) {
            addCriterion("produect_department =", value, "produectDepartment");
            return (Criteria) this;
        }

        public Criteria andProduectDepartmentNotEqualTo(String value) {
            addCriterion("produect_department <>", value, "produectDepartment");
            return (Criteria) this;
        }

        public Criteria andProduectDepartmentGreaterThan(String value) {
            addCriterion("produect_department >", value, "produectDepartment");
            return (Criteria) this;
        }

        public Criteria andProduectDepartmentGreaterThanOrEqualTo(String value) {
            addCriterion("produect_department >=", value, "produectDepartment");
            return (Criteria) this;
        }

        public Criteria andProduectDepartmentLessThan(String value) {
            addCriterion("produect_department <", value, "produectDepartment");
            return (Criteria) this;
        }

        public Criteria andProduectDepartmentLessThanOrEqualTo(String value) {
            addCriterion("produect_department <=", value, "produectDepartment");
            return (Criteria) this;
        }

        public Criteria andProduectDepartmentLike(String value) {
            addCriterion("produect_department like", value, "produectDepartment");
            return (Criteria) this;
        }

        public Criteria andProduectDepartmentNotLike(String value) {
            addCriterion("produect_department not like", value, "produectDepartment");
            return (Criteria) this;
        }

        public Criteria andProduectDepartmentIn(List<String> values) {
            addCriterion("produect_department in", values, "produectDepartment");
            return (Criteria) this;
        }

        public Criteria andProduectDepartmentNotIn(List<String> values) {
            addCriterion("produect_department not in", values, "produectDepartment");
            return (Criteria) this;
        }

        public Criteria andProduectDepartmentBetween(String value1, String value2) {
            addCriterion("produect_department between", value1, value2, "produectDepartment");
            return (Criteria) this;
        }

        public Criteria andProduectDepartmentNotBetween(String value1, String value2) {
            addCriterion("produect_department not between", value1, value2, "produectDepartment");
            return (Criteria) this;
        }

        public Criteria andContractAmountIsNull() {
            addCriterion("contract_amount is null");
            return (Criteria) this;
        }

        public Criteria andContractAmountIsNotNull() {
            addCriterion("contract_amount is not null");
            return (Criteria) this;
        }

        public Criteria andContractAmountEqualTo(BigDecimal value) {
            addCriterion("contract_amount =", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountNotEqualTo(BigDecimal value) {
            addCriterion("contract_amount <>", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountGreaterThan(BigDecimal value) {
            addCriterion("contract_amount >", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_amount >=", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountLessThan(BigDecimal value) {
            addCriterion("contract_amount <", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_amount <=", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountIn(List<BigDecimal> values) {
            addCriterion("contract_amount in", values, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountNotIn(List<BigDecimal> values) {
            addCriterion("contract_amount not in", values, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_amount between", value1, value2, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_amount not between", value1, value2, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedContractAmountIsNull() {
            addCriterion("accrued_contract_amount is null");
            return (Criteria) this;
        }

        public Criteria andAccruedContractAmountIsNotNull() {
            addCriterion("accrued_contract_amount is not null");
            return (Criteria) this;
        }

        public Criteria andAccruedContractAmountEqualTo(BigDecimal value) {
            addCriterion("accrued_contract_amount =", value, "accruedContractAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedContractAmountNotEqualTo(BigDecimal value) {
            addCriterion("accrued_contract_amount <>", value, "accruedContractAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedContractAmountGreaterThan(BigDecimal value) {
            addCriterion("accrued_contract_amount >", value, "accruedContractAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedContractAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("accrued_contract_amount >=", value, "accruedContractAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedContractAmountLessThan(BigDecimal value) {
            addCriterion("accrued_contract_amount <", value, "accruedContractAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedContractAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("accrued_contract_amount <=", value, "accruedContractAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedContractAmountIn(List<BigDecimal> values) {
            addCriterion("accrued_contract_amount in", values, "accruedContractAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedContractAmountNotIn(List<BigDecimal> values) {
            addCriterion("accrued_contract_amount not in", values, "accruedContractAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedContractAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("accrued_contract_amount between", value1, value2, "accruedContractAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedContractAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("accrued_contract_amount not between", value1, value2, "accruedContractAmount");
            return (Criteria) this;
        }

        public Criteria andLeftSwapReceiptAmountIsNull() {
            addCriterion("left_swap_receipt_amount is null");
            return (Criteria) this;
        }

        public Criteria andLeftSwapReceiptAmountIsNotNull() {
            addCriterion("left_swap_receipt_amount is not null");
            return (Criteria) this;
        }

        public Criteria andLeftSwapReceiptAmountEqualTo(BigDecimal value) {
            addCriterion("left_swap_receipt_amount =", value, "leftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andLeftSwapReceiptAmountNotEqualTo(BigDecimal value) {
            addCriterion("left_swap_receipt_amount <>", value, "leftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andLeftSwapReceiptAmountGreaterThan(BigDecimal value) {
            addCriterion("left_swap_receipt_amount >", value, "leftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andLeftSwapReceiptAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("left_swap_receipt_amount >=", value, "leftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andLeftSwapReceiptAmountLessThan(BigDecimal value) {
            addCriterion("left_swap_receipt_amount <", value, "leftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andLeftSwapReceiptAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("left_swap_receipt_amount <=", value, "leftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andLeftSwapReceiptAmountIn(List<BigDecimal> values) {
            addCriterion("left_swap_receipt_amount in", values, "leftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andLeftSwapReceiptAmountNotIn(List<BigDecimal> values) {
            addCriterion("left_swap_receipt_amount not in", values, "leftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andLeftSwapReceiptAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("left_swap_receipt_amount between", value1, value2, "leftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andLeftSwapReceiptAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("left_swap_receipt_amount not between", value1, value2, "leftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedLeftSwapReceiptAmountIsNull() {
            addCriterion("accrued_left_swap_receipt_amount is null");
            return (Criteria) this;
        }

        public Criteria andAccruedLeftSwapReceiptAmountIsNotNull() {
            addCriterion("accrued_left_swap_receipt_amount is not null");
            return (Criteria) this;
        }

        public Criteria andAccruedLeftSwapReceiptAmountEqualTo(BigDecimal value) {
            addCriterion("accrued_left_swap_receipt_amount =", value, "accruedLeftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedLeftSwapReceiptAmountNotEqualTo(BigDecimal value) {
            addCriterion("accrued_left_swap_receipt_amount <>", value, "accruedLeftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedLeftSwapReceiptAmountGreaterThan(BigDecimal value) {
            addCriterion("accrued_left_swap_receipt_amount >", value, "accruedLeftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedLeftSwapReceiptAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("accrued_left_swap_receipt_amount >=", value, "accruedLeftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedLeftSwapReceiptAmountLessThan(BigDecimal value) {
            addCriterion("accrued_left_swap_receipt_amount <", value, "accruedLeftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedLeftSwapReceiptAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("accrued_left_swap_receipt_amount <=", value, "accruedLeftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedLeftSwapReceiptAmountIn(List<BigDecimal> values) {
            addCriterion("accrued_left_swap_receipt_amount in", values, "accruedLeftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedLeftSwapReceiptAmountNotIn(List<BigDecimal> values) {
            addCriterion("accrued_left_swap_receipt_amount not in", values, "accruedLeftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedLeftSwapReceiptAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("accrued_left_swap_receipt_amount between", value1, value2, "accruedLeftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedLeftSwapReceiptAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("accrued_left_swap_receipt_amount not between", value1, value2, "accruedLeftSwapReceiptAmount");
            return (Criteria) this;
        }

        public Criteria andSwapExecuteTotalAmountIsNull() {
            addCriterion("swap_execute_total_amount is null");
            return (Criteria) this;
        }

        public Criteria andSwapExecuteTotalAmountIsNotNull() {
            addCriterion("swap_execute_total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSwapExecuteTotalAmountEqualTo(BigDecimal value) {
            addCriterion("swap_execute_total_amount =", value, "swapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andSwapExecuteTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("swap_execute_total_amount <>", value, "swapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andSwapExecuteTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("swap_execute_total_amount >", value, "swapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andSwapExecuteTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("swap_execute_total_amount >=", value, "swapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andSwapExecuteTotalAmountLessThan(BigDecimal value) {
            addCriterion("swap_execute_total_amount <", value, "swapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andSwapExecuteTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("swap_execute_total_amount <=", value, "swapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andSwapExecuteTotalAmountIn(List<BigDecimal> values) {
            addCriterion("swap_execute_total_amount in", values, "swapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andSwapExecuteTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("swap_execute_total_amount not in", values, "swapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andSwapExecuteTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("swap_execute_total_amount between", value1, value2, "swapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andSwapExecuteTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("swap_execute_total_amount not between", value1, value2, "swapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedSwapExecuteTotalAmountIsNull() {
            addCriterion("accrued_swap_execute_total_amount is null");
            return (Criteria) this;
        }

        public Criteria andAccruedSwapExecuteTotalAmountIsNotNull() {
            addCriterion("accrued_swap_execute_total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andAccruedSwapExecuteTotalAmountEqualTo(BigDecimal value) {
            addCriterion("accrued_swap_execute_total_amount =", value, "accruedSwapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedSwapExecuteTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("accrued_swap_execute_total_amount <>", value, "accruedSwapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedSwapExecuteTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("accrued_swap_execute_total_amount >", value, "accruedSwapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedSwapExecuteTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("accrued_swap_execute_total_amount >=", value, "accruedSwapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedSwapExecuteTotalAmountLessThan(BigDecimal value) {
            addCriterion("accrued_swap_execute_total_amount <", value, "accruedSwapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedSwapExecuteTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("accrued_swap_execute_total_amount <=", value, "accruedSwapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedSwapExecuteTotalAmountIn(List<BigDecimal> values) {
            addCriterion("accrued_swap_execute_total_amount in", values, "accruedSwapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedSwapExecuteTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("accrued_swap_execute_total_amount not in", values, "accruedSwapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedSwapExecuteTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("accrued_swap_execute_total_amount between", value1, value2, "accruedSwapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andAccruedSwapExecuteTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("accrued_swap_execute_total_amount not between", value1, value2, "accruedSwapExecuteTotalAmount");
            return (Criteria) this;
        }

        public Criteria andMemberSwapCostIsNull() {
            addCriterion("member_swap_cost is null");
            return (Criteria) this;
        }

        public Criteria andMemberSwapCostIsNotNull() {
            addCriterion("member_swap_cost is not null");
            return (Criteria) this;
        }

        public Criteria andMemberSwapCostEqualTo(BigDecimal value) {
            addCriterion("member_swap_cost =", value, "memberSwapCost");
            return (Criteria) this;
        }

        public Criteria andMemberSwapCostNotEqualTo(BigDecimal value) {
            addCriterion("member_swap_cost <>", value, "memberSwapCost");
            return (Criteria) this;
        }

        public Criteria andMemberSwapCostGreaterThan(BigDecimal value) {
            addCriterion("member_swap_cost >", value, "memberSwapCost");
            return (Criteria) this;
        }

        public Criteria andMemberSwapCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("member_swap_cost >=", value, "memberSwapCost");
            return (Criteria) this;
        }

        public Criteria andMemberSwapCostLessThan(BigDecimal value) {
            addCriterion("member_swap_cost <", value, "memberSwapCost");
            return (Criteria) this;
        }

        public Criteria andMemberSwapCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("member_swap_cost <=", value, "memberSwapCost");
            return (Criteria) this;
        }

        public Criteria andMemberSwapCostIn(List<BigDecimal> values) {
            addCriterion("member_swap_cost in", values, "memberSwapCost");
            return (Criteria) this;
        }

        public Criteria andMemberSwapCostNotIn(List<BigDecimal> values) {
            addCriterion("member_swap_cost not in", values, "memberSwapCost");
            return (Criteria) this;
        }

        public Criteria andMemberSwapCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("member_swap_cost between", value1, value2, "memberSwapCost");
            return (Criteria) this;
        }

        public Criteria andMemberSwapCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("member_swap_cost not between", value1, value2, "memberSwapCost");
            return (Criteria) this;
        }

        public Criteria andAccruedMemberSwapCostIsNull() {
            addCriterion("accrued_member_swap_cost is null");
            return (Criteria) this;
        }

        public Criteria andAccruedMemberSwapCostIsNotNull() {
            addCriterion("accrued_member_swap_cost is not null");
            return (Criteria) this;
        }

        public Criteria andAccruedMemberSwapCostEqualTo(BigDecimal value) {
            addCriterion("accrued_member_swap_cost =", value, "accruedMemberSwapCost");
            return (Criteria) this;
        }

        public Criteria andAccruedMemberSwapCostNotEqualTo(BigDecimal value) {
            addCriterion("accrued_member_swap_cost <>", value, "accruedMemberSwapCost");
            return (Criteria) this;
        }

        public Criteria andAccruedMemberSwapCostGreaterThan(BigDecimal value) {
            addCriterion("accrued_member_swap_cost >", value, "accruedMemberSwapCost");
            return (Criteria) this;
        }

        public Criteria andAccruedMemberSwapCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("accrued_member_swap_cost >=", value, "accruedMemberSwapCost");
            return (Criteria) this;
        }

        public Criteria andAccruedMemberSwapCostLessThan(BigDecimal value) {
            addCriterion("accrued_member_swap_cost <", value, "accruedMemberSwapCost");
            return (Criteria) this;
        }

        public Criteria andAccruedMemberSwapCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("accrued_member_swap_cost <=", value, "accruedMemberSwapCost");
            return (Criteria) this;
        }

        public Criteria andAccruedMemberSwapCostIn(List<BigDecimal> values) {
            addCriterion("accrued_member_swap_cost in", values, "accruedMemberSwapCost");
            return (Criteria) this;
        }

        public Criteria andAccruedMemberSwapCostNotIn(List<BigDecimal> values) {
            addCriterion("accrued_member_swap_cost not in", values, "accruedMemberSwapCost");
            return (Criteria) this;
        }

        public Criteria andAccruedMemberSwapCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("accrued_member_swap_cost between", value1, value2, "accruedMemberSwapCost");
            return (Criteria) this;
        }

        public Criteria andAccruedMemberSwapCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("accrued_member_swap_cost not between", value1, value2, "accruedMemberSwapCost");
            return (Criteria) this;
        }

        public Criteria andFeeTotalCostIsNull() {
            addCriterion("fee_total_cost is null");
            return (Criteria) this;
        }

        public Criteria andFeeTotalCostIsNotNull() {
            addCriterion("fee_total_cost is not null");
            return (Criteria) this;
        }

        public Criteria andFeeTotalCostEqualTo(BigDecimal value) {
            addCriterion("fee_total_cost =", value, "feeTotalCost");
            return (Criteria) this;
        }

        public Criteria andFeeTotalCostNotEqualTo(BigDecimal value) {
            addCriterion("fee_total_cost <>", value, "feeTotalCost");
            return (Criteria) this;
        }

        public Criteria andFeeTotalCostGreaterThan(BigDecimal value) {
            addCriterion("fee_total_cost >", value, "feeTotalCost");
            return (Criteria) this;
        }

        public Criteria andFeeTotalCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("fee_total_cost >=", value, "feeTotalCost");
            return (Criteria) this;
        }

        public Criteria andFeeTotalCostLessThan(BigDecimal value) {
            addCriterion("fee_total_cost <", value, "feeTotalCost");
            return (Criteria) this;
        }

        public Criteria andFeeTotalCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("fee_total_cost <=", value, "feeTotalCost");
            return (Criteria) this;
        }

        public Criteria andFeeTotalCostIn(List<BigDecimal> values) {
            addCriterion("fee_total_cost in", values, "feeTotalCost");
            return (Criteria) this;
        }

        public Criteria andFeeTotalCostNotIn(List<BigDecimal> values) {
            addCriterion("fee_total_cost not in", values, "feeTotalCost");
            return (Criteria) this;
        }

        public Criteria andFeeTotalCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee_total_cost between", value1, value2, "feeTotalCost");
            return (Criteria) this;
        }

        public Criteria andFeeTotalCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee_total_cost not between", value1, value2, "feeTotalCost");
            return (Criteria) this;
        }

        public Criteria andAccruedFeeTotalCostIsNull() {
            addCriterion("accrued_fee_total_cost is null");
            return (Criteria) this;
        }

        public Criteria andAccruedFeeTotalCostIsNotNull() {
            addCriterion("accrued_fee_total_cost is not null");
            return (Criteria) this;
        }

        public Criteria andAccruedFeeTotalCostEqualTo(BigDecimal value) {
            addCriterion("accrued_fee_total_cost =", value, "accruedFeeTotalCost");
            return (Criteria) this;
        }

        public Criteria andAccruedFeeTotalCostNotEqualTo(BigDecimal value) {
            addCriterion("accrued_fee_total_cost <>", value, "accruedFeeTotalCost");
            return (Criteria) this;
        }

        public Criteria andAccruedFeeTotalCostGreaterThan(BigDecimal value) {
            addCriterion("accrued_fee_total_cost >", value, "accruedFeeTotalCost");
            return (Criteria) this;
        }

        public Criteria andAccruedFeeTotalCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("accrued_fee_total_cost >=", value, "accruedFeeTotalCost");
            return (Criteria) this;
        }

        public Criteria andAccruedFeeTotalCostLessThan(BigDecimal value) {
            addCriterion("accrued_fee_total_cost <", value, "accruedFeeTotalCost");
            return (Criteria) this;
        }

        public Criteria andAccruedFeeTotalCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("accrued_fee_total_cost <=", value, "accruedFeeTotalCost");
            return (Criteria) this;
        }

        public Criteria andAccruedFeeTotalCostIn(List<BigDecimal> values) {
            addCriterion("accrued_fee_total_cost in", values, "accruedFeeTotalCost");
            return (Criteria) this;
        }

        public Criteria andAccruedFeeTotalCostNotIn(List<BigDecimal> values) {
            addCriterion("accrued_fee_total_cost not in", values, "accruedFeeTotalCost");
            return (Criteria) this;
        }

        public Criteria andAccruedFeeTotalCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("accrued_fee_total_cost between", value1, value2, "accruedFeeTotalCost");
            return (Criteria) this;
        }

        public Criteria andAccruedFeeTotalCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("accrued_fee_total_cost not between", value1, value2, "accruedFeeTotalCost");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}