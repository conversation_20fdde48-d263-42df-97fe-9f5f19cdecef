package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportProjectProcessProfitExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportProjectProcessProfitExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNull() {
            addCriterion("contract_code is null");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNotNull() {
            addCriterion("contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andContractCodeEqualTo(String value) {
            addCriterion("contract_code =", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotEqualTo(String value) {
            addCriterion("contract_code <>", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThan(String value) {
            addCriterion("contract_code >", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_code >=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThan(String value) {
            addCriterion("contract_code <", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThanOrEqualTo(String value) {
            addCriterion("contract_code <=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLike(String value) {
            addCriterion("contract_code like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotLike(String value) {
            addCriterion("contract_code not like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeIn(List<String> values) {
            addCriterion("contract_code in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotIn(List<String> values) {
            addCriterion("contract_code not in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeBetween(String value1, String value2) {
            addCriterion("contract_code between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotBetween(String value1, String value2) {
            addCriterion("contract_code not between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNull() {
            addCriterion("unit_name is null");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNotNull() {
            addCriterion("unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andUnitNameEqualTo(String value) {
            addCriterion("unit_name =", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotEqualTo(String value) {
            addCriterion("unit_name <>", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThan(String value) {
            addCriterion("unit_name >", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("unit_name >=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThan(String value) {
            addCriterion("unit_name <", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThanOrEqualTo(String value) {
            addCriterion("unit_name <=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLike(String value) {
            addCriterion("unit_name like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotLike(String value) {
            addCriterion("unit_name not like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIn(List<String> values) {
            addCriterion("unit_name in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotIn(List<String> values) {
            addCriterion("unit_name not in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameBetween(String value1, String value2) {
            addCriterion("unit_name between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotBetween(String value1, String value2) {
            addCriterion("unit_name not between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNull() {
            addCriterion("project_type is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNotNull() {
            addCriterion("project_type is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeEqualTo(String value) {
            addCriterion("project_type =", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotEqualTo(String value) {
            addCriterion("project_type <>", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThan(String value) {
            addCriterion("project_type >", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThanOrEqualTo(String value) {
            addCriterion("project_type >=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThan(String value) {
            addCriterion("project_type <", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThanOrEqualTo(String value) {
            addCriterion("project_type <=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLike(String value) {
            addCriterion("project_type like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotLike(String value) {
            addCriterion("project_type not like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIn(List<String> values) {
            addCriterion("project_type in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotIn(List<String> values) {
            addCriterion("project_type not in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeBetween(String value1, String value2) {
            addCriterion("project_type between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotBetween(String value1, String value2) {
            addCriterion("project_type not between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andProjectLevelIsNull() {
            addCriterion("project_level is null");
            return (Criteria) this;
        }

        public Criteria andProjectLevelIsNotNull() {
            addCriterion("project_level is not null");
            return (Criteria) this;
        }

        public Criteria andProjectLevelEqualTo(Integer value) {
            addCriterion("project_level =", value, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelNotEqualTo(Integer value) {
            addCriterion("project_level <>", value, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelGreaterThan(Integer value) {
            addCriterion("project_level >", value, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelGreaterThanOrEqualTo(Integer value) {
            addCriterion("project_level >=", value, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelLessThan(Integer value) {
            addCriterion("project_level <", value, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelLessThanOrEqualTo(Integer value) {
            addCriterion("project_level <=", value, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelIn(List<Integer> values) {
            addCriterion("project_level in", values, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelNotIn(List<Integer> values) {
            addCriterion("project_level not in", values, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelBetween(Integer value1, Integer value2) {
            addCriterion("project_level between", value1, value2, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelNotBetween(Integer value1, Integer value2) {
            addCriterion("project_level not between", value1, value2, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNull() {
            addCriterion("project_manager is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNotNull() {
            addCriterion("project_manager is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerEqualTo(String value) {
            addCriterion("project_manager =", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotEqualTo(String value) {
            addCriterion("project_manager <>", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThan(String value) {
            addCriterion("project_manager >", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThanOrEqualTo(String value) {
            addCriterion("project_manager >=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThan(String value) {
            addCriterion("project_manager <", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThanOrEqualTo(String value) {
            addCriterion("project_manager <=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLike(String value) {
            addCriterion("project_manager like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotLike(String value) {
            addCriterion("project_manager not like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIn(List<String> values) {
            addCriterion("project_manager in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotIn(List<String> values) {
            addCriterion("project_manager not in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerBetween(String value1, String value2) {
            addCriterion("project_manager between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotBetween(String value1, String value2) {
            addCriterion("project_manager not between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerIsNull() {
            addCriterion("sales_manager is null");
            return (Criteria) this;
        }

        public Criteria andSalesManagerIsNotNull() {
            addCriterion("sales_manager is not null");
            return (Criteria) this;
        }

        public Criteria andSalesManagerEqualTo(String value) {
            addCriterion("sales_manager =", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNotEqualTo(String value) {
            addCriterion("sales_manager <>", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerGreaterThan(String value) {
            addCriterion("sales_manager >", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerGreaterThanOrEqualTo(String value) {
            addCriterion("sales_manager >=", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerLessThan(String value) {
            addCriterion("sales_manager <", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerLessThanOrEqualTo(String value) {
            addCriterion("sales_manager <=", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerLike(String value) {
            addCriterion("sales_manager like", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNotLike(String value) {
            addCriterion("sales_manager not like", value, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerIn(List<String> values) {
            addCriterion("sales_manager in", values, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNotIn(List<String> values) {
            addCriterion("sales_manager not in", values, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerBetween(String value1, String value2) {
            addCriterion("sales_manager between", value1, value2, "salesManager");
            return (Criteria) this;
        }

        public Criteria andSalesManagerNotBetween(String value1, String value2) {
            addCriterion("sales_manager not between", value1, value2, "salesManager");
            return (Criteria) this;
        }

        public Criteria andFinancialIsNull() {
            addCriterion("financial is null");
            return (Criteria) this;
        }

        public Criteria andFinancialIsNotNull() {
            addCriterion("financial is not null");
            return (Criteria) this;
        }

        public Criteria andFinancialEqualTo(String value) {
            addCriterion("financial =", value, "financial");
            return (Criteria) this;
        }

        public Criteria andFinancialNotEqualTo(String value) {
            addCriterion("financial <>", value, "financial");
            return (Criteria) this;
        }

        public Criteria andFinancialGreaterThan(String value) {
            addCriterion("financial >", value, "financial");
            return (Criteria) this;
        }

        public Criteria andFinancialGreaterThanOrEqualTo(String value) {
            addCriterion("financial >=", value, "financial");
            return (Criteria) this;
        }

        public Criteria andFinancialLessThan(String value) {
            addCriterion("financial <", value, "financial");
            return (Criteria) this;
        }

        public Criteria andFinancialLessThanOrEqualTo(String value) {
            addCriterion("financial <=", value, "financial");
            return (Criteria) this;
        }

        public Criteria andFinancialLike(String value) {
            addCriterion("financial like", value, "financial");
            return (Criteria) this;
        }

        public Criteria andFinancialNotLike(String value) {
            addCriterion("financial not like", value, "financial");
            return (Criteria) this;
        }

        public Criteria andFinancialIn(List<String> values) {
            addCriterion("financial in", values, "financial");
            return (Criteria) this;
        }

        public Criteria andFinancialNotIn(List<String> values) {
            addCriterion("financial not in", values, "financial");
            return (Criteria) this;
        }

        public Criteria andFinancialBetween(String value1, String value2) {
            addCriterion("financial between", value1, value2, "financial");
            return (Criteria) this;
        }

        public Criteria andFinancialNotBetween(String value1, String value2) {
            addCriterion("financial not between", value1, value2, "financial");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNameIsNull() {
            addCriterion("plan_designer_name is null");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNameIsNotNull() {
            addCriterion("plan_designer_name is not null");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNameEqualTo(String value) {
            addCriterion("plan_designer_name =", value, "planDesignerName");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNameNotEqualTo(String value) {
            addCriterion("plan_designer_name <>", value, "planDesignerName");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNameGreaterThan(String value) {
            addCriterion("plan_designer_name >", value, "planDesignerName");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNameGreaterThanOrEqualTo(String value) {
            addCriterion("plan_designer_name >=", value, "planDesignerName");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNameLessThan(String value) {
            addCriterion("plan_designer_name <", value, "planDesignerName");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNameLessThanOrEqualTo(String value) {
            addCriterion("plan_designer_name <=", value, "planDesignerName");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNameLike(String value) {
            addCriterion("plan_designer_name like", value, "planDesignerName");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNameNotLike(String value) {
            addCriterion("plan_designer_name not like", value, "planDesignerName");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNameIn(List<String> values) {
            addCriterion("plan_designer_name in", values, "planDesignerName");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNameNotIn(List<String> values) {
            addCriterion("plan_designer_name not in", values, "planDesignerName");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNameBetween(String value1, String value2) {
            addCriterion("plan_designer_name between", value1, value2, "planDesignerName");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNameNotBetween(String value1, String value2) {
            addCriterion("plan_designer_name not between", value1, value2, "planDesignerName");
            return (Criteria) this;
        }

        public Criteria andTechnologyLeaderNameIsNull() {
            addCriterion("technology_leader_name is null");
            return (Criteria) this;
        }

        public Criteria andTechnologyLeaderNameIsNotNull() {
            addCriterion("technology_leader_name is not null");
            return (Criteria) this;
        }

        public Criteria andTechnologyLeaderNameEqualTo(String value) {
            addCriterion("technology_leader_name =", value, "technologyLeaderName");
            return (Criteria) this;
        }

        public Criteria andTechnologyLeaderNameNotEqualTo(String value) {
            addCriterion("technology_leader_name <>", value, "technologyLeaderName");
            return (Criteria) this;
        }

        public Criteria andTechnologyLeaderNameGreaterThan(String value) {
            addCriterion("technology_leader_name >", value, "technologyLeaderName");
            return (Criteria) this;
        }

        public Criteria andTechnologyLeaderNameGreaterThanOrEqualTo(String value) {
            addCriterion("technology_leader_name >=", value, "technologyLeaderName");
            return (Criteria) this;
        }

        public Criteria andTechnologyLeaderNameLessThan(String value) {
            addCriterion("technology_leader_name <", value, "technologyLeaderName");
            return (Criteria) this;
        }

        public Criteria andTechnologyLeaderNameLessThanOrEqualTo(String value) {
            addCriterion("technology_leader_name <=", value, "technologyLeaderName");
            return (Criteria) this;
        }

        public Criteria andTechnologyLeaderNameLike(String value) {
            addCriterion("technology_leader_name like", value, "technologyLeaderName");
            return (Criteria) this;
        }

        public Criteria andTechnologyLeaderNameNotLike(String value) {
            addCriterion("technology_leader_name not like", value, "technologyLeaderName");
            return (Criteria) this;
        }

        public Criteria andTechnologyLeaderNameIn(List<String> values) {
            addCriterion("technology_leader_name in", values, "technologyLeaderName");
            return (Criteria) this;
        }

        public Criteria andTechnologyLeaderNameNotIn(List<String> values) {
            addCriterion("technology_leader_name not in", values, "technologyLeaderName");
            return (Criteria) this;
        }

        public Criteria andTechnologyLeaderNameBetween(String value1, String value2) {
            addCriterion("technology_leader_name between", value1, value2, "technologyLeaderName");
            return (Criteria) this;
        }

        public Criteria andTechnologyLeaderNameNotBetween(String value1, String value2) {
            addCriterion("technology_leader_name not between", value1, value2, "technologyLeaderName");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNull() {
            addCriterion("project_status is null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNotNull() {
            addCriterion("project_status is not null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusEqualTo(Integer value) {
            addCriterion("project_status =", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotEqualTo(Integer value) {
            addCriterion("project_status <>", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThan(Integer value) {
            addCriterion("project_status >", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("project_status >=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThan(Integer value) {
            addCriterion("project_status <", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThanOrEqualTo(Integer value) {
            addCriterion("project_status <=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIn(List<Integer> values) {
            addCriterion("project_status in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotIn(List<Integer> values) {
            addCriterion("project_status not in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusBetween(Integer value1, Integer value2) {
            addCriterion("project_status between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("project_status not between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andCurrentMilepostIsNull() {
            addCriterion("current_milepost is null");
            return (Criteria) this;
        }

        public Criteria andCurrentMilepostIsNotNull() {
            addCriterion("current_milepost is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentMilepostEqualTo(String value) {
            addCriterion("current_milepost =", value, "currentMilepost");
            return (Criteria) this;
        }

        public Criteria andCurrentMilepostNotEqualTo(String value) {
            addCriterion("current_milepost <>", value, "currentMilepost");
            return (Criteria) this;
        }

        public Criteria andCurrentMilepostGreaterThan(String value) {
            addCriterion("current_milepost >", value, "currentMilepost");
            return (Criteria) this;
        }

        public Criteria andCurrentMilepostGreaterThanOrEqualTo(String value) {
            addCriterion("current_milepost >=", value, "currentMilepost");
            return (Criteria) this;
        }

        public Criteria andCurrentMilepostLessThan(String value) {
            addCriterion("current_milepost <", value, "currentMilepost");
            return (Criteria) this;
        }

        public Criteria andCurrentMilepostLessThanOrEqualTo(String value) {
            addCriterion("current_milepost <=", value, "currentMilepost");
            return (Criteria) this;
        }

        public Criteria andCurrentMilepostLike(String value) {
            addCriterion("current_milepost like", value, "currentMilepost");
            return (Criteria) this;
        }

        public Criteria andCurrentMilepostNotLike(String value) {
            addCriterion("current_milepost not like", value, "currentMilepost");
            return (Criteria) this;
        }

        public Criteria andCurrentMilepostIn(List<String> values) {
            addCriterion("current_milepost in", values, "currentMilepost");
            return (Criteria) this;
        }

        public Criteria andCurrentMilepostNotIn(List<String> values) {
            addCriterion("current_milepost not in", values, "currentMilepost");
            return (Criteria) this;
        }

        public Criteria andCurrentMilepostBetween(String value1, String value2) {
            addCriterion("current_milepost between", value1, value2, "currentMilepost");
            return (Criteria) this;
        }

        public Criteria andCurrentMilepostNotBetween(String value1, String value2) {
            addCriterion("current_milepost not between", value1, value2, "currentMilepost");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andProjectAmountIsNull() {
            addCriterion("project_amount is null");
            return (Criteria) this;
        }

        public Criteria andProjectAmountIsNotNull() {
            addCriterion("project_amount is not null");
            return (Criteria) this;
        }

        public Criteria andProjectAmountEqualTo(BigDecimal value) {
            addCriterion("project_amount =", value, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountNotEqualTo(BigDecimal value) {
            addCriterion("project_amount <>", value, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountGreaterThan(BigDecimal value) {
            addCriterion("project_amount >", value, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_amount >=", value, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountLessThan(BigDecimal value) {
            addCriterion("project_amount <", value, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_amount <=", value, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountIn(List<BigDecimal> values) {
            addCriterion("project_amount in", values, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountNotIn(List<BigDecimal> values) {
            addCriterion("project_amount not in", values, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_amount between", value1, value2, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_amount not between", value1, value2, "projectAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountIsNull() {
            addCriterion("project_contract_standard_amount is null");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountIsNotNull() {
            addCriterion("project_contract_standard_amount is not null");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountEqualTo(BigDecimal value) {
            addCriterion("project_contract_standard_amount =", value, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountNotEqualTo(BigDecimal value) {
            addCriterion("project_contract_standard_amount <>", value, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountGreaterThan(BigDecimal value) {
            addCriterion("project_contract_standard_amount >", value, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_contract_standard_amount >=", value, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountLessThan(BigDecimal value) {
            addCriterion("project_contract_standard_amount <", value, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_contract_standard_amount <=", value, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountIn(List<BigDecimal> values) {
            addCriterion("project_contract_standard_amount in", values, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountNotIn(List<BigDecimal> values) {
            addCriterion("project_contract_standard_amount not in", values, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_contract_standard_amount between", value1, value2, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andProjectContractStandardAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_contract_standard_amount not between", value1, value2, "projectContractStandardAmount");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNull() {
            addCriterion("start_date is null");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNotNull() {
            addCriterion("start_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateEqualTo(Date value) {
            addCriterion("start_date =", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotEqualTo(Date value) {
            addCriterion("start_date <>", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThan(Date value) {
            addCriterion("start_date >", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("start_date >=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThan(Date value) {
            addCriterion("start_date <", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThanOrEqualTo(Date value) {
            addCriterion("start_date <=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateIn(List<Date> values) {
            addCriterion("start_date in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotIn(List<Date> values) {
            addCriterion("start_date not in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateBetween(Date value1, Date value2) {
            addCriterion("start_date between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotBetween(Date value1, Date value2) {
            addCriterion("start_date not between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andMilepostNameInfoIsNull() {
            addCriterion("milepost_name_info is null");
            return (Criteria) this;
        }

        public Criteria andMilepostNameInfoIsNotNull() {
            addCriterion("milepost_name_info is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostNameInfoEqualTo(String value) {
            addCriterion("milepost_name_info =", value, "milepostNameInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostNameInfoNotEqualTo(String value) {
            addCriterion("milepost_name_info <>", value, "milepostNameInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostNameInfoGreaterThan(String value) {
            addCriterion("milepost_name_info >", value, "milepostNameInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostNameInfoGreaterThanOrEqualTo(String value) {
            addCriterion("milepost_name_info >=", value, "milepostNameInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostNameInfoLessThan(String value) {
            addCriterion("milepost_name_info <", value, "milepostNameInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostNameInfoLessThanOrEqualTo(String value) {
            addCriterion("milepost_name_info <=", value, "milepostNameInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostNameInfoLike(String value) {
            addCriterion("milepost_name_info like", value, "milepostNameInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostNameInfoNotLike(String value) {
            addCriterion("milepost_name_info not like", value, "milepostNameInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostNameInfoIn(List<String> values) {
            addCriterion("milepost_name_info in", values, "milepostNameInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostNameInfoNotIn(List<String> values) {
            addCriterion("milepost_name_info not in", values, "milepostNameInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostNameInfoBetween(String value1, String value2) {
            addCriterion("milepost_name_info between", value1, value2, "milepostNameInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostNameInfoNotBetween(String value1, String value2) {
            addCriterion("milepost_name_info not between", value1, value2, "milepostNameInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusInfoIsNull() {
            addCriterion("milepost_status_info is null");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusInfoIsNotNull() {
            addCriterion("milepost_status_info is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusInfoEqualTo(String value) {
            addCriterion("milepost_status_info =", value, "milepostStatusInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusInfoNotEqualTo(String value) {
            addCriterion("milepost_status_info <>", value, "milepostStatusInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusInfoGreaterThan(String value) {
            addCriterion("milepost_status_info >", value, "milepostStatusInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusInfoGreaterThanOrEqualTo(String value) {
            addCriterion("milepost_status_info >=", value, "milepostStatusInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusInfoLessThan(String value) {
            addCriterion("milepost_status_info <", value, "milepostStatusInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusInfoLessThanOrEqualTo(String value) {
            addCriterion("milepost_status_info <=", value, "milepostStatusInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusInfoLike(String value) {
            addCriterion("milepost_status_info like", value, "milepostStatusInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusInfoNotLike(String value) {
            addCriterion("milepost_status_info not like", value, "milepostStatusInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusInfoIn(List<String> values) {
            addCriterion("milepost_status_info in", values, "milepostStatusInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusInfoNotIn(List<String> values) {
            addCriterion("milepost_status_info not in", values, "milepostStatusInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusInfoBetween(String value1, String value2) {
            addCriterion("milepost_status_info between", value1, value2, "milepostStatusInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostStatusInfoNotBetween(String value1, String value2) {
            addCriterion("milepost_status_info not between", value1, value2, "milepostStatusInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostEndDateInfoIsNull() {
            addCriterion("milepost_end_date_info is null");
            return (Criteria) this;
        }

        public Criteria andMilepostEndDateInfoIsNotNull() {
            addCriterion("milepost_end_date_info is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostEndDateInfoEqualTo(String value) {
            addCriterion("milepost_end_date_info =", value, "milepostEndDateInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostEndDateInfoNotEqualTo(String value) {
            addCriterion("milepost_end_date_info <>", value, "milepostEndDateInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostEndDateInfoGreaterThan(String value) {
            addCriterion("milepost_end_date_info >", value, "milepostEndDateInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostEndDateInfoGreaterThanOrEqualTo(String value) {
            addCriterion("milepost_end_date_info >=", value, "milepostEndDateInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostEndDateInfoLessThan(String value) {
            addCriterion("milepost_end_date_info <", value, "milepostEndDateInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostEndDateInfoLessThanOrEqualTo(String value) {
            addCriterion("milepost_end_date_info <=", value, "milepostEndDateInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostEndDateInfoLike(String value) {
            addCriterion("milepost_end_date_info like", value, "milepostEndDateInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostEndDateInfoNotLike(String value) {
            addCriterion("milepost_end_date_info not like", value, "milepostEndDateInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostEndDateInfoIn(List<String> values) {
            addCriterion("milepost_end_date_info in", values, "milepostEndDateInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostEndDateInfoNotIn(List<String> values) {
            addCriterion("milepost_end_date_info not in", values, "milepostEndDateInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostEndDateInfoBetween(String value1, String value2) {
            addCriterion("milepost_end_date_info between", value1, value2, "milepostEndDateInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostEndDateInfoNotBetween(String value1, String value2) {
            addCriterion("milepost_end_date_info not between", value1, value2, "milepostEndDateInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostActualEndTimeInfoIsNull() {
            addCriterion("milepost_actual_end_time_info is null");
            return (Criteria) this;
        }

        public Criteria andMilepostActualEndTimeInfoIsNotNull() {
            addCriterion("milepost_actual_end_time_info is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostActualEndTimeInfoEqualTo(String value) {
            addCriterion("milepost_actual_end_time_info =", value, "milepostActualEndTimeInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostActualEndTimeInfoNotEqualTo(String value) {
            addCriterion("milepost_actual_end_time_info <>", value, "milepostActualEndTimeInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostActualEndTimeInfoGreaterThan(String value) {
            addCriterion("milepost_actual_end_time_info >", value, "milepostActualEndTimeInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostActualEndTimeInfoGreaterThanOrEqualTo(String value) {
            addCriterion("milepost_actual_end_time_info >=", value, "milepostActualEndTimeInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostActualEndTimeInfoLessThan(String value) {
            addCriterion("milepost_actual_end_time_info <", value, "milepostActualEndTimeInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostActualEndTimeInfoLessThanOrEqualTo(String value) {
            addCriterion("milepost_actual_end_time_info <=", value, "milepostActualEndTimeInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostActualEndTimeInfoLike(String value) {
            addCriterion("milepost_actual_end_time_info like", value, "milepostActualEndTimeInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostActualEndTimeInfoNotLike(String value) {
            addCriterion("milepost_actual_end_time_info not like", value, "milepostActualEndTimeInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostActualEndTimeInfoIn(List<String> values) {
            addCriterion("milepost_actual_end_time_info in", values, "milepostActualEndTimeInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostActualEndTimeInfoNotIn(List<String> values) {
            addCriterion("milepost_actual_end_time_info not in", values, "milepostActualEndTimeInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostActualEndTimeInfoBetween(String value1, String value2) {
            addCriterion("milepost_actual_end_time_info between", value1, value2, "milepostActualEndTimeInfo");
            return (Criteria) this;
        }

        public Criteria andMilepostActualEndTimeInfoNotBetween(String value1, String value2) {
            addCriterion("milepost_actual_end_time_info not between", value1, value2, "milepostActualEndTimeInfo");
            return (Criteria) this;
        }

        public Criteria andInitMaterialTargetCostIsNull() {
            addCriterion("init_material_target_cost is null");
            return (Criteria) this;
        }

        public Criteria andInitMaterialTargetCostIsNotNull() {
            addCriterion("init_material_target_cost is not null");
            return (Criteria) this;
        }

        public Criteria andInitMaterialTargetCostEqualTo(BigDecimal value) {
            addCriterion("init_material_target_cost =", value, "initMaterialTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitMaterialTargetCostNotEqualTo(BigDecimal value) {
            addCriterion("init_material_target_cost <>", value, "initMaterialTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitMaterialTargetCostGreaterThan(BigDecimal value) {
            addCriterion("init_material_target_cost >", value, "initMaterialTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitMaterialTargetCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("init_material_target_cost >=", value, "initMaterialTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitMaterialTargetCostLessThan(BigDecimal value) {
            addCriterion("init_material_target_cost <", value, "initMaterialTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitMaterialTargetCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("init_material_target_cost <=", value, "initMaterialTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitMaterialTargetCostIn(List<BigDecimal> values) {
            addCriterion("init_material_target_cost in", values, "initMaterialTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitMaterialTargetCostNotIn(List<BigDecimal> values) {
            addCriterion("init_material_target_cost not in", values, "initMaterialTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitMaterialTargetCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("init_material_target_cost between", value1, value2, "initMaterialTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitMaterialTargetCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("init_material_target_cost not between", value1, value2, "initMaterialTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitHumanTargetCostIsNull() {
            addCriterion("init_human_target_cost is null");
            return (Criteria) this;
        }

        public Criteria andInitHumanTargetCostIsNotNull() {
            addCriterion("init_human_target_cost is not null");
            return (Criteria) this;
        }

        public Criteria andInitHumanTargetCostEqualTo(BigDecimal value) {
            addCriterion("init_human_target_cost =", value, "initHumanTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitHumanTargetCostNotEqualTo(BigDecimal value) {
            addCriterion("init_human_target_cost <>", value, "initHumanTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitHumanTargetCostGreaterThan(BigDecimal value) {
            addCriterion("init_human_target_cost >", value, "initHumanTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitHumanTargetCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("init_human_target_cost >=", value, "initHumanTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitHumanTargetCostLessThan(BigDecimal value) {
            addCriterion("init_human_target_cost <", value, "initHumanTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitHumanTargetCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("init_human_target_cost <=", value, "initHumanTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitHumanTargetCostIn(List<BigDecimal> values) {
            addCriterion("init_human_target_cost in", values, "initHumanTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitHumanTargetCostNotIn(List<BigDecimal> values) {
            addCriterion("init_human_target_cost not in", values, "initHumanTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitHumanTargetCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("init_human_target_cost between", value1, value2, "initHumanTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitHumanTargetCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("init_human_target_cost not between", value1, value2, "initHumanTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTravelTargetCostIsNull() {
            addCriterion("init_travel_target_cost is null");
            return (Criteria) this;
        }

        public Criteria andInitTravelTargetCostIsNotNull() {
            addCriterion("init_travel_target_cost is not null");
            return (Criteria) this;
        }

        public Criteria andInitTravelTargetCostEqualTo(BigDecimal value) {
            addCriterion("init_travel_target_cost =", value, "initTravelTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTravelTargetCostNotEqualTo(BigDecimal value) {
            addCriterion("init_travel_target_cost <>", value, "initTravelTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTravelTargetCostGreaterThan(BigDecimal value) {
            addCriterion("init_travel_target_cost >", value, "initTravelTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTravelTargetCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("init_travel_target_cost >=", value, "initTravelTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTravelTargetCostLessThan(BigDecimal value) {
            addCriterion("init_travel_target_cost <", value, "initTravelTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTravelTargetCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("init_travel_target_cost <=", value, "initTravelTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTravelTargetCostIn(List<BigDecimal> values) {
            addCriterion("init_travel_target_cost in", values, "initTravelTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTravelTargetCostNotIn(List<BigDecimal> values) {
            addCriterion("init_travel_target_cost not in", values, "initTravelTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTravelTargetCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("init_travel_target_cost between", value1, value2, "initTravelTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTravelTargetCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("init_travel_target_cost not between", value1, value2, "initTravelTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitOtherTargetCostIsNull() {
            addCriterion("init_other_target_cost is null");
            return (Criteria) this;
        }

        public Criteria andInitOtherTargetCostIsNotNull() {
            addCriterion("init_other_target_cost is not null");
            return (Criteria) this;
        }

        public Criteria andInitOtherTargetCostEqualTo(BigDecimal value) {
            addCriterion("init_other_target_cost =", value, "initOtherTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitOtherTargetCostNotEqualTo(BigDecimal value) {
            addCriterion("init_other_target_cost <>", value, "initOtherTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitOtherTargetCostGreaterThan(BigDecimal value) {
            addCriterion("init_other_target_cost >", value, "initOtherTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitOtherTargetCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("init_other_target_cost >=", value, "initOtherTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitOtherTargetCostLessThan(BigDecimal value) {
            addCriterion("init_other_target_cost <", value, "initOtherTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitOtherTargetCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("init_other_target_cost <=", value, "initOtherTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitOtherTargetCostIn(List<BigDecimal> values) {
            addCriterion("init_other_target_cost in", values, "initOtherTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitOtherTargetCostNotIn(List<BigDecimal> values) {
            addCriterion("init_other_target_cost not in", values, "initOtherTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitOtherTargetCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("init_other_target_cost between", value1, value2, "initOtherTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitOtherTargetCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("init_other_target_cost not between", value1, value2, "initOtherTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTotalTargetCostIsNull() {
            addCriterion("init_total_target_cost is null");
            return (Criteria) this;
        }

        public Criteria andInitTotalTargetCostIsNotNull() {
            addCriterion("init_total_target_cost is not null");
            return (Criteria) this;
        }

        public Criteria andInitTotalTargetCostEqualTo(BigDecimal value) {
            addCriterion("init_total_target_cost =", value, "initTotalTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTotalTargetCostNotEqualTo(BigDecimal value) {
            addCriterion("init_total_target_cost <>", value, "initTotalTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTotalTargetCostGreaterThan(BigDecimal value) {
            addCriterion("init_total_target_cost >", value, "initTotalTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTotalTargetCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("init_total_target_cost >=", value, "initTotalTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTotalTargetCostLessThan(BigDecimal value) {
            addCriterion("init_total_target_cost <", value, "initTotalTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTotalTargetCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("init_total_target_cost <=", value, "initTotalTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTotalTargetCostIn(List<BigDecimal> values) {
            addCriterion("init_total_target_cost in", values, "initTotalTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTotalTargetCostNotIn(List<BigDecimal> values) {
            addCriterion("init_total_target_cost not in", values, "initTotalTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTotalTargetCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("init_total_target_cost between", value1, value2, "initTotalTargetCost");
            return (Criteria) this;
        }

        public Criteria andInitTotalTargetCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("init_total_target_cost not between", value1, value2, "initTotalTargetCost");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioIsNull() {
            addCriterion("target_cost_grass_profit_ratio is null");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioIsNotNull() {
            addCriterion("target_cost_grass_profit_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioEqualTo(BigDecimal value) {
            addCriterion("target_cost_grass_profit_ratio =", value, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioNotEqualTo(BigDecimal value) {
            addCriterion("target_cost_grass_profit_ratio <>", value, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioGreaterThan(BigDecimal value) {
            addCriterion("target_cost_grass_profit_ratio >", value, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("target_cost_grass_profit_ratio >=", value, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioLessThan(BigDecimal value) {
            addCriterion("target_cost_grass_profit_ratio <", value, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("target_cost_grass_profit_ratio <=", value, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioIn(List<BigDecimal> values) {
            addCriterion("target_cost_grass_profit_ratio in", values, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioNotIn(List<BigDecimal> values) {
            addCriterion("target_cost_grass_profit_ratio not in", values, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("target_cost_grass_profit_ratio between", value1, value2, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostGrassProfitRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("target_cost_grass_profit_ratio not between", value1, value2, "targetCostGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andMaterialCostIsNull() {
            addCriterion("material_cost is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCostIsNotNull() {
            addCriterion("material_cost is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCostEqualTo(BigDecimal value) {
            addCriterion("material_cost =", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostNotEqualTo(BigDecimal value) {
            addCriterion("material_cost <>", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostGreaterThan(BigDecimal value) {
            addCriterion("material_cost >", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_cost >=", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostLessThan(BigDecimal value) {
            addCriterion("material_cost <", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_cost <=", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostIn(List<BigDecimal> values) {
            addCriterion("material_cost in", values, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostNotIn(List<BigDecimal> values) {
            addCriterion("material_cost not in", values, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_cost between", value1, value2, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_cost not between", value1, value2, "materialCost");
            return (Criteria) this;
        }

        public Criteria andHumanCostIsNull() {
            addCriterion("human_cost is null");
            return (Criteria) this;
        }

        public Criteria andHumanCostIsNotNull() {
            addCriterion("human_cost is not null");
            return (Criteria) this;
        }

        public Criteria andHumanCostEqualTo(BigDecimal value) {
            addCriterion("human_cost =", value, "humanCost");
            return (Criteria) this;
        }

        public Criteria andHumanCostNotEqualTo(BigDecimal value) {
            addCriterion("human_cost <>", value, "humanCost");
            return (Criteria) this;
        }

        public Criteria andHumanCostGreaterThan(BigDecimal value) {
            addCriterion("human_cost >", value, "humanCost");
            return (Criteria) this;
        }

        public Criteria andHumanCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("human_cost >=", value, "humanCost");
            return (Criteria) this;
        }

        public Criteria andHumanCostLessThan(BigDecimal value) {
            addCriterion("human_cost <", value, "humanCost");
            return (Criteria) this;
        }

        public Criteria andHumanCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("human_cost <=", value, "humanCost");
            return (Criteria) this;
        }

        public Criteria andHumanCostIn(List<BigDecimal> values) {
            addCriterion("human_cost in", values, "humanCost");
            return (Criteria) this;
        }

        public Criteria andHumanCostNotIn(List<BigDecimal> values) {
            addCriterion("human_cost not in", values, "humanCost");
            return (Criteria) this;
        }

        public Criteria andHumanCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("human_cost between", value1, value2, "humanCost");
            return (Criteria) this;
        }

        public Criteria andHumanCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("human_cost not between", value1, value2, "humanCost");
            return (Criteria) this;
        }

        public Criteria andTravelCostIsNull() {
            addCriterion("travel_cost is null");
            return (Criteria) this;
        }

        public Criteria andTravelCostIsNotNull() {
            addCriterion("travel_cost is not null");
            return (Criteria) this;
        }

        public Criteria andTravelCostEqualTo(BigDecimal value) {
            addCriterion("travel_cost =", value, "travelCost");
            return (Criteria) this;
        }

        public Criteria andTravelCostNotEqualTo(BigDecimal value) {
            addCriterion("travel_cost <>", value, "travelCost");
            return (Criteria) this;
        }

        public Criteria andTravelCostGreaterThan(BigDecimal value) {
            addCriterion("travel_cost >", value, "travelCost");
            return (Criteria) this;
        }

        public Criteria andTravelCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_cost >=", value, "travelCost");
            return (Criteria) this;
        }

        public Criteria andTravelCostLessThan(BigDecimal value) {
            addCriterion("travel_cost <", value, "travelCost");
            return (Criteria) this;
        }

        public Criteria andTravelCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_cost <=", value, "travelCost");
            return (Criteria) this;
        }

        public Criteria andTravelCostIn(List<BigDecimal> values) {
            addCriterion("travel_cost in", values, "travelCost");
            return (Criteria) this;
        }

        public Criteria andTravelCostNotIn(List<BigDecimal> values) {
            addCriterion("travel_cost not in", values, "travelCost");
            return (Criteria) this;
        }

        public Criteria andTravelCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_cost between", value1, value2, "travelCost");
            return (Criteria) this;
        }

        public Criteria andTravelCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_cost not between", value1, value2, "travelCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostIsNull() {
            addCriterion("fee_cost is null");
            return (Criteria) this;
        }

        public Criteria andFeeCostIsNotNull() {
            addCriterion("fee_cost is not null");
            return (Criteria) this;
        }

        public Criteria andFeeCostEqualTo(BigDecimal value) {
            addCriterion("fee_cost =", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostNotEqualTo(BigDecimal value) {
            addCriterion("fee_cost <>", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostGreaterThan(BigDecimal value) {
            addCriterion("fee_cost >", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("fee_cost >=", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostLessThan(BigDecimal value) {
            addCriterion("fee_cost <", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("fee_cost <=", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostIn(List<BigDecimal> values) {
            addCriterion("fee_cost in", values, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostNotIn(List<BigDecimal> values) {
            addCriterion("fee_cost not in", values, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee_cost between", value1, value2, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee_cost not between", value1, value2, "feeCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostIsNull() {
            addCriterion("total_cost is null");
            return (Criteria) this;
        }

        public Criteria andTotalCostIsNotNull() {
            addCriterion("total_cost is not null");
            return (Criteria) this;
        }

        public Criteria andTotalCostEqualTo(BigDecimal value) {
            addCriterion("total_cost =", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostNotEqualTo(BigDecimal value) {
            addCriterion("total_cost <>", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostGreaterThan(BigDecimal value) {
            addCriterion("total_cost >", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_cost >=", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostLessThan(BigDecimal value) {
            addCriterion("total_cost <", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_cost <=", value, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostIn(List<BigDecimal> values) {
            addCriterion("total_cost in", values, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostNotIn(List<BigDecimal> values) {
            addCriterion("total_cost not in", values, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_cost between", value1, value2, "totalCost");
            return (Criteria) this;
        }

        public Criteria andTotalCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_cost not between", value1, value2, "totalCost");
            return (Criteria) this;
        }

        public Criteria andActualGrassProfitRatioIsNull() {
            addCriterion("actual_grass_profit_ratio is null");
            return (Criteria) this;
        }

        public Criteria andActualGrassProfitRatioIsNotNull() {
            addCriterion("actual_grass_profit_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andActualGrassProfitRatioEqualTo(BigDecimal value) {
            addCriterion("actual_grass_profit_ratio =", value, "actualGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andActualGrassProfitRatioNotEqualTo(BigDecimal value) {
            addCriterion("actual_grass_profit_ratio <>", value, "actualGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andActualGrassProfitRatioGreaterThan(BigDecimal value) {
            addCriterion("actual_grass_profit_ratio >", value, "actualGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andActualGrassProfitRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_grass_profit_ratio >=", value, "actualGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andActualGrassProfitRatioLessThan(BigDecimal value) {
            addCriterion("actual_grass_profit_ratio <", value, "actualGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andActualGrassProfitRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_grass_profit_ratio <=", value, "actualGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andActualGrassProfitRatioIn(List<BigDecimal> values) {
            addCriterion("actual_grass_profit_ratio in", values, "actualGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andActualGrassProfitRatioNotIn(List<BigDecimal> values) {
            addCriterion("actual_grass_profit_ratio not in", values, "actualGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andActualGrassProfitRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_grass_profit_ratio between", value1, value2, "actualGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andActualGrassProfitRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_grass_profit_ratio not between", value1, value2, "actualGrassProfitRatio");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateIsNull() {
            addCriterion("target_cost_deviation_rate is null");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateIsNotNull() {
            addCriterion("target_cost_deviation_rate is not null");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateEqualTo(BigDecimal value) {
            addCriterion("target_cost_deviation_rate =", value, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateNotEqualTo(BigDecimal value) {
            addCriterion("target_cost_deviation_rate <>", value, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateGreaterThan(BigDecimal value) {
            addCriterion("target_cost_deviation_rate >", value, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("target_cost_deviation_rate >=", value, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateLessThan(BigDecimal value) {
            addCriterion("target_cost_deviation_rate <", value, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("target_cost_deviation_rate <=", value, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateIn(List<BigDecimal> values) {
            addCriterion("target_cost_deviation_rate in", values, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateNotIn(List<BigDecimal> values) {
            addCriterion("target_cost_deviation_rate not in", values, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("target_cost_deviation_rate between", value1, value2, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andTargetCostDeviationRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("target_cost_deviation_rate not between", value1, value2, "targetCostDeviationRate");
            return (Criteria) this;
        }

        public Criteria andBudgetCostIsNull() {
            addCriterion("budget_cost is null");
            return (Criteria) this;
        }

        public Criteria andBudgetCostIsNotNull() {
            addCriterion("budget_cost is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetCostEqualTo(BigDecimal value) {
            addCriterion("budget_cost =", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostNotEqualTo(BigDecimal value) {
            addCriterion("budget_cost <>", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostGreaterThan(BigDecimal value) {
            addCriterion("budget_cost >", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_cost >=", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostLessThan(BigDecimal value) {
            addCriterion("budget_cost <", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_cost <=", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostIn(List<BigDecimal> values) {
            addCriterion("budget_cost in", values, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostNotIn(List<BigDecimal> values) {
            addCriterion("budget_cost not in", values, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_cost between", value1, value2, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_cost not between", value1, value2, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRatioIsNull() {
            addCriterion("budget_profit_ratio is null");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRatioIsNotNull() {
            addCriterion("budget_profit_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRatioEqualTo(BigDecimal value) {
            addCriterion("budget_profit_ratio =", value, "budgetProfitRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRatioNotEqualTo(BigDecimal value) {
            addCriterion("budget_profit_ratio <>", value, "budgetProfitRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRatioGreaterThan(BigDecimal value) {
            addCriterion("budget_profit_ratio >", value, "budgetProfitRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_profit_ratio >=", value, "budgetProfitRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRatioLessThan(BigDecimal value) {
            addCriterion("budget_profit_ratio <", value, "budgetProfitRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_profit_ratio <=", value, "budgetProfitRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRatioIn(List<BigDecimal> values) {
            addCriterion("budget_profit_ratio in", values, "budgetProfitRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRatioNotIn(List<BigDecimal> values) {
            addCriterion("budget_profit_ratio not in", values, "budgetProfitRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_profit_ratio between", value1, value2, "budgetProfitRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetProfitRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_profit_ratio not between", value1, value2, "budgetProfitRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetDeviationRateIsNull() {
            addCriterion("budget_deviation_rate is null");
            return (Criteria) this;
        }

        public Criteria andBudgetDeviationRateIsNotNull() {
            addCriterion("budget_deviation_rate is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetDeviationRateEqualTo(BigDecimal value) {
            addCriterion("budget_deviation_rate =", value, "budgetDeviationRate");
            return (Criteria) this;
        }

        public Criteria andBudgetDeviationRateNotEqualTo(BigDecimal value) {
            addCriterion("budget_deviation_rate <>", value, "budgetDeviationRate");
            return (Criteria) this;
        }

        public Criteria andBudgetDeviationRateGreaterThan(BigDecimal value) {
            addCriterion("budget_deviation_rate >", value, "budgetDeviationRate");
            return (Criteria) this;
        }

        public Criteria andBudgetDeviationRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_deviation_rate >=", value, "budgetDeviationRate");
            return (Criteria) this;
        }

        public Criteria andBudgetDeviationRateLessThan(BigDecimal value) {
            addCriterion("budget_deviation_rate <", value, "budgetDeviationRate");
            return (Criteria) this;
        }

        public Criteria andBudgetDeviationRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_deviation_rate <=", value, "budgetDeviationRate");
            return (Criteria) this;
        }

        public Criteria andBudgetDeviationRateIn(List<BigDecimal> values) {
            addCriterion("budget_deviation_rate in", values, "budgetDeviationRate");
            return (Criteria) this;
        }

        public Criteria andBudgetDeviationRateNotIn(List<BigDecimal> values) {
            addCriterion("budget_deviation_rate not in", values, "budgetDeviationRate");
            return (Criteria) this;
        }

        public Criteria andBudgetDeviationRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_deviation_rate between", value1, value2, "budgetDeviationRate");
            return (Criteria) this;
        }

        public Criteria andBudgetDeviationRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_deviation_rate not between", value1, value2, "budgetDeviationRate");
            return (Criteria) this;
        }

        public Criteria andReceiptClaimAmountIsNull() {
            addCriterion("receipt_claim_amount is null");
            return (Criteria) this;
        }

        public Criteria andReceiptClaimAmountIsNotNull() {
            addCriterion("receipt_claim_amount is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptClaimAmountEqualTo(BigDecimal value) {
            addCriterion("receipt_claim_amount =", value, "receiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptClaimAmountNotEqualTo(BigDecimal value) {
            addCriterion("receipt_claim_amount <>", value, "receiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptClaimAmountGreaterThan(BigDecimal value) {
            addCriterion("receipt_claim_amount >", value, "receiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptClaimAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("receipt_claim_amount >=", value, "receiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptClaimAmountLessThan(BigDecimal value) {
            addCriterion("receipt_claim_amount <", value, "receiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptClaimAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("receipt_claim_amount <=", value, "receiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptClaimAmountIn(List<BigDecimal> values) {
            addCriterion("receipt_claim_amount in", values, "receiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptClaimAmountNotIn(List<BigDecimal> values) {
            addCriterion("receipt_claim_amount not in", values, "receiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptClaimAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("receipt_claim_amount between", value1, value2, "receiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptClaimAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("receipt_claim_amount not between", value1, value2, "receiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andStandardReceiptClaimAmountIsNull() {
            addCriterion("standard_receipt_claim_amount is null");
            return (Criteria) this;
        }

        public Criteria andStandardReceiptClaimAmountIsNotNull() {
            addCriterion("standard_receipt_claim_amount is not null");
            return (Criteria) this;
        }

        public Criteria andStandardReceiptClaimAmountEqualTo(BigDecimal value) {
            addCriterion("standard_receipt_claim_amount =", value, "standardReceiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andStandardReceiptClaimAmountNotEqualTo(BigDecimal value) {
            addCriterion("standard_receipt_claim_amount <>", value, "standardReceiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andStandardReceiptClaimAmountGreaterThan(BigDecimal value) {
            addCriterion("standard_receipt_claim_amount >", value, "standardReceiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andStandardReceiptClaimAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_receipt_claim_amount >=", value, "standardReceiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andStandardReceiptClaimAmountLessThan(BigDecimal value) {
            addCriterion("standard_receipt_claim_amount <", value, "standardReceiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andStandardReceiptClaimAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_receipt_claim_amount <=", value, "standardReceiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andStandardReceiptClaimAmountIn(List<BigDecimal> values) {
            addCriterion("standard_receipt_claim_amount in", values, "standardReceiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andStandardReceiptClaimAmountNotIn(List<BigDecimal> values) {
            addCriterion("standard_receipt_claim_amount not in", values, "standardReceiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andStandardReceiptClaimAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_receipt_claim_amount between", value1, value2, "standardReceiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andStandardReceiptClaimAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_receipt_claim_amount not between", value1, value2, "standardReceiptClaimAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountIsNull() {
            addCriterion("payment_amount is null");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountIsNotNull() {
            addCriterion("payment_amount is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountEqualTo(BigDecimal value) {
            addCriterion("payment_amount =", value, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountNotEqualTo(BigDecimal value) {
            addCriterion("payment_amount <>", value, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountGreaterThan(BigDecimal value) {
            addCriterion("payment_amount >", value, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("payment_amount >=", value, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountLessThan(BigDecimal value) {
            addCriterion("payment_amount <", value, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("payment_amount <=", value, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountIn(List<BigDecimal> values) {
            addCriterion("payment_amount in", values, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountNotIn(List<BigDecimal> values) {
            addCriterion("payment_amount not in", values, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("payment_amount between", value1, value2, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("payment_amount not between", value1, value2, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andProjectCashIsNull() {
            addCriterion("project_cash is null");
            return (Criteria) this;
        }

        public Criteria andProjectCashIsNotNull() {
            addCriterion("project_cash is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCashEqualTo(BigDecimal value) {
            addCriterion("project_cash =", value, "projectCash");
            return (Criteria) this;
        }

        public Criteria andProjectCashNotEqualTo(BigDecimal value) {
            addCriterion("project_cash <>", value, "projectCash");
            return (Criteria) this;
        }

        public Criteria andProjectCashGreaterThan(BigDecimal value) {
            addCriterion("project_cash >", value, "projectCash");
            return (Criteria) this;
        }

        public Criteria andProjectCashGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_cash >=", value, "projectCash");
            return (Criteria) this;
        }

        public Criteria andProjectCashLessThan(BigDecimal value) {
            addCriterion("project_cash <", value, "projectCash");
            return (Criteria) this;
        }

        public Criteria andProjectCashLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_cash <=", value, "projectCash");
            return (Criteria) this;
        }

        public Criteria andProjectCashIn(List<BigDecimal> values) {
            addCriterion("project_cash in", values, "projectCash");
            return (Criteria) this;
        }

        public Criteria andProjectCashNotIn(List<BigDecimal> values) {
            addCriterion("project_cash not in", values, "projectCash");
            return (Criteria) this;
        }

        public Criteria andProjectCashBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_cash between", value1, value2, "projectCash");
            return (Criteria) this;
        }

        public Criteria andProjectCashNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_cash not between", value1, value2, "projectCash");
            return (Criteria) this;
        }

        public Criteria andReceivableAmountIsNull() {
            addCriterion("receivable_amount is null");
            return (Criteria) this;
        }

        public Criteria andReceivableAmountIsNotNull() {
            addCriterion("receivable_amount is not null");
            return (Criteria) this;
        }

        public Criteria andReceivableAmountEqualTo(BigDecimal value) {
            addCriterion("receivable_amount =", value, "receivableAmount");
            return (Criteria) this;
        }

        public Criteria andReceivableAmountNotEqualTo(BigDecimal value) {
            addCriterion("receivable_amount <>", value, "receivableAmount");
            return (Criteria) this;
        }

        public Criteria andReceivableAmountGreaterThan(BigDecimal value) {
            addCriterion("receivable_amount >", value, "receivableAmount");
            return (Criteria) this;
        }

        public Criteria andReceivableAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("receivable_amount >=", value, "receivableAmount");
            return (Criteria) this;
        }

        public Criteria andReceivableAmountLessThan(BigDecimal value) {
            addCriterion("receivable_amount <", value, "receivableAmount");
            return (Criteria) this;
        }

        public Criteria andReceivableAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("receivable_amount <=", value, "receivableAmount");
            return (Criteria) this;
        }

        public Criteria andReceivableAmountIn(List<BigDecimal> values) {
            addCriterion("receivable_amount in", values, "receivableAmount");
            return (Criteria) this;
        }

        public Criteria andReceivableAmountNotIn(List<BigDecimal> values) {
            addCriterion("receivable_amount not in", values, "receivableAmount");
            return (Criteria) this;
        }

        public Criteria andReceivableAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("receivable_amount between", value1, value2, "receivableAmount");
            return (Criteria) this;
        }

        public Criteria andReceivableAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("receivable_amount not between", value1, value2, "receivableAmount");
            return (Criteria) this;
        }

        public Criteria andPreviewAmountIsNull() {
            addCriterion("preview_amount is null");
            return (Criteria) this;
        }

        public Criteria andPreviewAmountIsNotNull() {
            addCriterion("preview_amount is not null");
            return (Criteria) this;
        }

        public Criteria andPreviewAmountEqualTo(BigDecimal value) {
            addCriterion("preview_amount =", value, "previewAmount");
            return (Criteria) this;
        }

        public Criteria andPreviewAmountNotEqualTo(BigDecimal value) {
            addCriterion("preview_amount <>", value, "previewAmount");
            return (Criteria) this;
        }

        public Criteria andPreviewAmountGreaterThan(BigDecimal value) {
            addCriterion("preview_amount >", value, "previewAmount");
            return (Criteria) this;
        }

        public Criteria andPreviewAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("preview_amount >=", value, "previewAmount");
            return (Criteria) this;
        }

        public Criteria andPreviewAmountLessThan(BigDecimal value) {
            addCriterion("preview_amount <", value, "previewAmount");
            return (Criteria) this;
        }

        public Criteria andPreviewAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("preview_amount <=", value, "previewAmount");
            return (Criteria) this;
        }

        public Criteria andPreviewAmountIn(List<BigDecimal> values) {
            addCriterion("preview_amount in", values, "previewAmount");
            return (Criteria) this;
        }

        public Criteria andPreviewAmountNotIn(List<BigDecimal> values) {
            addCriterion("preview_amount not in", values, "previewAmount");
            return (Criteria) this;
        }

        public Criteria andPreviewAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("preview_amount between", value1, value2, "previewAmount");
            return (Criteria) this;
        }

        public Criteria andPreviewAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("preview_amount not between", value1, value2, "previewAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeAmountIsNull() {
            addCriterion("carryover_income_amount is null");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeAmountIsNotNull() {
            addCriterion("carryover_income_amount is not null");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeAmountEqualTo(BigDecimal value) {
            addCriterion("carryover_income_amount =", value, "carryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeAmountNotEqualTo(BigDecimal value) {
            addCriterion("carryover_income_amount <>", value, "carryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeAmountGreaterThan(BigDecimal value) {
            addCriterion("carryover_income_amount >", value, "carryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("carryover_income_amount >=", value, "carryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeAmountLessThan(BigDecimal value) {
            addCriterion("carryover_income_amount <", value, "carryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("carryover_income_amount <=", value, "carryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeAmountIn(List<BigDecimal> values) {
            addCriterion("carryover_income_amount in", values, "carryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeAmountNotIn(List<BigDecimal> values) {
            addCriterion("carryover_income_amount not in", values, "carryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carryover_income_amount between", value1, value2, "carryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carryover_income_amount not between", value1, value2, "carryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andStandardCarryoverIncomeAmountIsNull() {
            addCriterion("standard_carryover_income_amount is null");
            return (Criteria) this;
        }

        public Criteria andStandardCarryoverIncomeAmountIsNotNull() {
            addCriterion("standard_carryover_income_amount is not null");
            return (Criteria) this;
        }

        public Criteria andStandardCarryoverIncomeAmountEqualTo(BigDecimal value) {
            addCriterion("standard_carryover_income_amount =", value, "standardCarryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andStandardCarryoverIncomeAmountNotEqualTo(BigDecimal value) {
            addCriterion("standard_carryover_income_amount <>", value, "standardCarryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andStandardCarryoverIncomeAmountGreaterThan(BigDecimal value) {
            addCriterion("standard_carryover_income_amount >", value, "standardCarryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andStandardCarryoverIncomeAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_carryover_income_amount >=", value, "standardCarryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andStandardCarryoverIncomeAmountLessThan(BigDecimal value) {
            addCriterion("standard_carryover_income_amount <", value, "standardCarryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andStandardCarryoverIncomeAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_carryover_income_amount <=", value, "standardCarryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andStandardCarryoverIncomeAmountIn(List<BigDecimal> values) {
            addCriterion("standard_carryover_income_amount in", values, "standardCarryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andStandardCarryoverIncomeAmountNotIn(List<BigDecimal> values) {
            addCriterion("standard_carryover_income_amount not in", values, "standardCarryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andStandardCarryoverIncomeAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_carryover_income_amount between", value1, value2, "standardCarryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andStandardCarryoverIncomeAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_carryover_income_amount not between", value1, value2, "standardCarryoverIncomeAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeRatioIsNull() {
            addCriterion("carryover_income_ratio is null");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeRatioIsNotNull() {
            addCriterion("carryover_income_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeRatioEqualTo(BigDecimal value) {
            addCriterion("carryover_income_ratio =", value, "carryoverIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeRatioNotEqualTo(BigDecimal value) {
            addCriterion("carryover_income_ratio <>", value, "carryoverIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeRatioGreaterThan(BigDecimal value) {
            addCriterion("carryover_income_ratio >", value, "carryoverIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("carryover_income_ratio >=", value, "carryoverIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeRatioLessThan(BigDecimal value) {
            addCriterion("carryover_income_ratio <", value, "carryoverIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("carryover_income_ratio <=", value, "carryoverIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeRatioIn(List<BigDecimal> values) {
            addCriterion("carryover_income_ratio in", values, "carryoverIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeRatioNotIn(List<BigDecimal> values) {
            addCriterion("carryover_income_ratio not in", values, "carryoverIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carryover_income_ratio between", value1, value2, "carryoverIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andCarryoverIncomeRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carryover_income_ratio not between", value1, value2, "carryoverIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostAmountIsNull() {
            addCriterion("carryover_cost_amount is null");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostAmountIsNotNull() {
            addCriterion("carryover_cost_amount is not null");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostAmountEqualTo(BigDecimal value) {
            addCriterion("carryover_cost_amount =", value, "carryoverCostAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostAmountNotEqualTo(BigDecimal value) {
            addCriterion("carryover_cost_amount <>", value, "carryoverCostAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostAmountGreaterThan(BigDecimal value) {
            addCriterion("carryover_cost_amount >", value, "carryoverCostAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("carryover_cost_amount >=", value, "carryoverCostAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostAmountLessThan(BigDecimal value) {
            addCriterion("carryover_cost_amount <", value, "carryoverCostAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("carryover_cost_amount <=", value, "carryoverCostAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostAmountIn(List<BigDecimal> values) {
            addCriterion("carryover_cost_amount in", values, "carryoverCostAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostAmountNotIn(List<BigDecimal> values) {
            addCriterion("carryover_cost_amount not in", values, "carryoverCostAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carryover_cost_amount between", value1, value2, "carryoverCostAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carryover_cost_amount not between", value1, value2, "carryoverCostAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostGrassAmountIsNull() {
            addCriterion("carryover_cost_grass_amount is null");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostGrassAmountIsNotNull() {
            addCriterion("carryover_cost_grass_amount is not null");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostGrassAmountEqualTo(BigDecimal value) {
            addCriterion("carryover_cost_grass_amount =", value, "carryoverCostGrassAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostGrassAmountNotEqualTo(BigDecimal value) {
            addCriterion("carryover_cost_grass_amount <>", value, "carryoverCostGrassAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostGrassAmountGreaterThan(BigDecimal value) {
            addCriterion("carryover_cost_grass_amount >", value, "carryoverCostGrassAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostGrassAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("carryover_cost_grass_amount >=", value, "carryoverCostGrassAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostGrassAmountLessThan(BigDecimal value) {
            addCriterion("carryover_cost_grass_amount <", value, "carryoverCostGrassAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostGrassAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("carryover_cost_grass_amount <=", value, "carryoverCostGrassAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostGrassAmountIn(List<BigDecimal> values) {
            addCriterion("carryover_cost_grass_amount in", values, "carryoverCostGrassAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostGrassAmountNotIn(List<BigDecimal> values) {
            addCriterion("carryover_cost_grass_amount not in", values, "carryoverCostGrassAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostGrassAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carryover_cost_grass_amount between", value1, value2, "carryoverCostGrassAmount");
            return (Criteria) this;
        }

        public Criteria andCarryoverCostGrassAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carryover_cost_grass_amount not between", value1, value2, "carryoverCostGrassAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountIsNull() {
            addCriterion("invoice_amount is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountIsNotNull() {
            addCriterion("invoice_amount is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountEqualTo(BigDecimal value) {
            addCriterion("invoice_amount =", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountNotEqualTo(BigDecimal value) {
            addCriterion("invoice_amount <>", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountGreaterThan(BigDecimal value) {
            addCriterion("invoice_amount >", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("invoice_amount >=", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountLessThan(BigDecimal value) {
            addCriterion("invoice_amount <", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("invoice_amount <=", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountIn(List<BigDecimal> values) {
            addCriterion("invoice_amount in", values, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountNotIn(List<BigDecimal> values) {
            addCriterion("invoice_amount not in", values, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invoice_amount between", value1, value2, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invoice_amount not between", value1, value2, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andExclusiveInvoiceAmountIsNull() {
            addCriterion("exclusive_invoice_amount is null");
            return (Criteria) this;
        }

        public Criteria andExclusiveInvoiceAmountIsNotNull() {
            addCriterion("exclusive_invoice_amount is not null");
            return (Criteria) this;
        }

        public Criteria andExclusiveInvoiceAmountEqualTo(BigDecimal value) {
            addCriterion("exclusive_invoice_amount =", value, "exclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andExclusiveInvoiceAmountNotEqualTo(BigDecimal value) {
            addCriterion("exclusive_invoice_amount <>", value, "exclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andExclusiveInvoiceAmountGreaterThan(BigDecimal value) {
            addCriterion("exclusive_invoice_amount >", value, "exclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andExclusiveInvoiceAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("exclusive_invoice_amount >=", value, "exclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andExclusiveInvoiceAmountLessThan(BigDecimal value) {
            addCriterion("exclusive_invoice_amount <", value, "exclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andExclusiveInvoiceAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("exclusive_invoice_amount <=", value, "exclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andExclusiveInvoiceAmountIn(List<BigDecimal> values) {
            addCriterion("exclusive_invoice_amount in", values, "exclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andExclusiveInvoiceAmountNotIn(List<BigDecimal> values) {
            addCriterion("exclusive_invoice_amount not in", values, "exclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andExclusiveInvoiceAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("exclusive_invoice_amount between", value1, value2, "exclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andExclusiveInvoiceAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("exclusive_invoice_amount not between", value1, value2, "exclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountIsNull() {
            addCriterion("standard_invoice_amount is null");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountIsNotNull() {
            addCriterion("standard_invoice_amount is not null");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountEqualTo(BigDecimal value) {
            addCriterion("standard_invoice_amount =", value, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountNotEqualTo(BigDecimal value) {
            addCriterion("standard_invoice_amount <>", value, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountGreaterThan(BigDecimal value) {
            addCriterion("standard_invoice_amount >", value, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_invoice_amount >=", value, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountLessThan(BigDecimal value) {
            addCriterion("standard_invoice_amount <", value, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_invoice_amount <=", value, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountIn(List<BigDecimal> values) {
            addCriterion("standard_invoice_amount in", values, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountNotIn(List<BigDecimal> values) {
            addCriterion("standard_invoice_amount not in", values, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_invoice_amount between", value1, value2, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_invoice_amount not between", value1, value2, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardExclusiveInvoiceAmountIsNull() {
            addCriterion("standard_exclusive_invoice_amount is null");
            return (Criteria) this;
        }

        public Criteria andStandardExclusiveInvoiceAmountIsNotNull() {
            addCriterion("standard_exclusive_invoice_amount is not null");
            return (Criteria) this;
        }

        public Criteria andStandardExclusiveInvoiceAmountEqualTo(BigDecimal value) {
            addCriterion("standard_exclusive_invoice_amount =", value, "standardExclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardExclusiveInvoiceAmountNotEqualTo(BigDecimal value) {
            addCriterion("standard_exclusive_invoice_amount <>", value, "standardExclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardExclusiveInvoiceAmountGreaterThan(BigDecimal value) {
            addCriterion("standard_exclusive_invoice_amount >", value, "standardExclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardExclusiveInvoiceAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_exclusive_invoice_amount >=", value, "standardExclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardExclusiveInvoiceAmountLessThan(BigDecimal value) {
            addCriterion("standard_exclusive_invoice_amount <", value, "standardExclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardExclusiveInvoiceAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_exclusive_invoice_amount <=", value, "standardExclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardExclusiveInvoiceAmountIn(List<BigDecimal> values) {
            addCriterion("standard_exclusive_invoice_amount in", values, "standardExclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardExclusiveInvoiceAmountNotIn(List<BigDecimal> values) {
            addCriterion("standard_exclusive_invoice_amount not in", values, "standardExclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardExclusiveInvoiceAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_exclusive_invoice_amount between", value1, value2, "standardExclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardExclusiveInvoiceAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_exclusive_invoice_amount not between", value1, value2, "standardExclusiveInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceRatioIsNull() {
            addCriterion("invoice_ratio is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceRatioIsNotNull() {
            addCriterion("invoice_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceRatioEqualTo(BigDecimal value) {
            addCriterion("invoice_ratio =", value, "invoiceRatio");
            return (Criteria) this;
        }

        public Criteria andInvoiceRatioNotEqualTo(BigDecimal value) {
            addCriterion("invoice_ratio <>", value, "invoiceRatio");
            return (Criteria) this;
        }

        public Criteria andInvoiceRatioGreaterThan(BigDecimal value) {
            addCriterion("invoice_ratio >", value, "invoiceRatio");
            return (Criteria) this;
        }

        public Criteria andInvoiceRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("invoice_ratio >=", value, "invoiceRatio");
            return (Criteria) this;
        }

        public Criteria andInvoiceRatioLessThan(BigDecimal value) {
            addCriterion("invoice_ratio <", value, "invoiceRatio");
            return (Criteria) this;
        }

        public Criteria andInvoiceRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("invoice_ratio <=", value, "invoiceRatio");
            return (Criteria) this;
        }

        public Criteria andInvoiceRatioIn(List<BigDecimal> values) {
            addCriterion("invoice_ratio in", values, "invoiceRatio");
            return (Criteria) this;
        }

        public Criteria andInvoiceRatioNotIn(List<BigDecimal> values) {
            addCriterion("invoice_ratio not in", values, "invoiceRatio");
            return (Criteria) this;
        }

        public Criteria andInvoiceRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invoice_ratio between", value1, value2, "invoiceRatio");
            return (Criteria) this;
        }

        public Criteria andInvoiceRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invoice_ratio not between", value1, value2, "invoiceRatio");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumIsNull() {
            addCriterion("assessment_num is null");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumIsNotNull() {
            addCriterion("assessment_num is not null");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumEqualTo(BigDecimal value) {
            addCriterion("assessment_num =", value, "assessmentNum");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumNotEqualTo(BigDecimal value) {
            addCriterion("assessment_num <>", value, "assessmentNum");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumGreaterThan(BigDecimal value) {
            addCriterion("assessment_num >", value, "assessmentNum");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("assessment_num >=", value, "assessmentNum");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumLessThan(BigDecimal value) {
            addCriterion("assessment_num <", value, "assessmentNum");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("assessment_num <=", value, "assessmentNum");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumIn(List<BigDecimal> values) {
            addCriterion("assessment_num in", values, "assessmentNum");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumNotIn(List<BigDecimal> values) {
            addCriterion("assessment_num not in", values, "assessmentNum");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("assessment_num between", value1, value2, "assessmentNum");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("assessment_num not between", value1, value2, "assessmentNum");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentIsNull() {
            addCriterion("assessment_percent is null");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentIsNotNull() {
            addCriterion("assessment_percent is not null");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentEqualTo(BigDecimal value) {
            addCriterion("assessment_percent =", value, "assessmentPercent");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentNotEqualTo(BigDecimal value) {
            addCriterion("assessment_percent <>", value, "assessmentPercent");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentGreaterThan(BigDecimal value) {
            addCriterion("assessment_percent >", value, "assessmentPercent");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("assessment_percent >=", value, "assessmentPercent");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentLessThan(BigDecimal value) {
            addCriterion("assessment_percent <", value, "assessmentPercent");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentLessThanOrEqualTo(BigDecimal value) {
            addCriterion("assessment_percent <=", value, "assessmentPercent");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentIn(List<BigDecimal> values) {
            addCriterion("assessment_percent in", values, "assessmentPercent");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentNotIn(List<BigDecimal> values) {
            addCriterion("assessment_percent not in", values, "assessmentPercent");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("assessment_percent between", value1, value2, "assessmentPercent");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("assessment_percent not between", value1, value2, "assessmentPercent");
            return (Criteria) this;
        }

        public Criteria andDelayDayInfoIsNull() {
            addCriterion("delay_day_info is null");
            return (Criteria) this;
        }

        public Criteria andDelayDayInfoIsNotNull() {
            addCriterion("delay_day_info is not null");
            return (Criteria) this;
        }

        public Criteria andDelayDayInfoEqualTo(String value) {
            addCriterion("delay_day_info =", value, "delayDayInfo");
            return (Criteria) this;
        }

        public Criteria andDelayDayInfoNotEqualTo(String value) {
            addCriterion("delay_day_info <>", value, "delayDayInfo");
            return (Criteria) this;
        }

        public Criteria andDelayDayInfoGreaterThan(String value) {
            addCriterion("delay_day_info >", value, "delayDayInfo");
            return (Criteria) this;
        }

        public Criteria andDelayDayInfoGreaterThanOrEqualTo(String value) {
            addCriterion("delay_day_info >=", value, "delayDayInfo");
            return (Criteria) this;
        }

        public Criteria andDelayDayInfoLessThan(String value) {
            addCriterion("delay_day_info <", value, "delayDayInfo");
            return (Criteria) this;
        }

        public Criteria andDelayDayInfoLessThanOrEqualTo(String value) {
            addCriterion("delay_day_info <=", value, "delayDayInfo");
            return (Criteria) this;
        }

        public Criteria andDelayDayInfoLike(String value) {
            addCriterion("delay_day_info like", value, "delayDayInfo");
            return (Criteria) this;
        }

        public Criteria andDelayDayInfoNotLike(String value) {
            addCriterion("delay_day_info not like", value, "delayDayInfo");
            return (Criteria) this;
        }

        public Criteria andDelayDayInfoIn(List<String> values) {
            addCriterion("delay_day_info in", values, "delayDayInfo");
            return (Criteria) this;
        }

        public Criteria andDelayDayInfoNotIn(List<String> values) {
            addCriterion("delay_day_info not in", values, "delayDayInfo");
            return (Criteria) this;
        }

        public Criteria andDelayDayInfoBetween(String value1, String value2) {
            addCriterion("delay_day_info between", value1, value2, "delayDayInfo");
            return (Criteria) this;
        }

        public Criteria andDelayDayInfoNotBetween(String value1, String value2) {
            addCriterion("delay_day_info not between", value1, value2, "delayDayInfo");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumTotalIsNull() {
            addCriterion("assessment_num_total is null");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumTotalIsNotNull() {
            addCriterion("assessment_num_total is not null");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumTotalEqualTo(BigDecimal value) {
            addCriterion("assessment_num_total =", value, "assessmentNumTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumTotalNotEqualTo(BigDecimal value) {
            addCriterion("assessment_num_total <>", value, "assessmentNumTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumTotalGreaterThan(BigDecimal value) {
            addCriterion("assessment_num_total >", value, "assessmentNumTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("assessment_num_total >=", value, "assessmentNumTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumTotalLessThan(BigDecimal value) {
            addCriterion("assessment_num_total <", value, "assessmentNumTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("assessment_num_total <=", value, "assessmentNumTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumTotalIn(List<BigDecimal> values) {
            addCriterion("assessment_num_total in", values, "assessmentNumTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumTotalNotIn(List<BigDecimal> values) {
            addCriterion("assessment_num_total not in", values, "assessmentNumTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("assessment_num_total between", value1, value2, "assessmentNumTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentNumTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("assessment_num_total not between", value1, value2, "assessmentNumTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentTotalIsNull() {
            addCriterion("assessment_percent_total is null");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentTotalIsNotNull() {
            addCriterion("assessment_percent_total is not null");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentTotalEqualTo(BigDecimal value) {
            addCriterion("assessment_percent_total =", value, "assessmentPercentTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentTotalNotEqualTo(BigDecimal value) {
            addCriterion("assessment_percent_total <>", value, "assessmentPercentTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentTotalGreaterThan(BigDecimal value) {
            addCriterion("assessment_percent_total >", value, "assessmentPercentTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("assessment_percent_total >=", value, "assessmentPercentTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentTotalLessThan(BigDecimal value) {
            addCriterion("assessment_percent_total <", value, "assessmentPercentTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("assessment_percent_total <=", value, "assessmentPercentTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentTotalIn(List<BigDecimal> values) {
            addCriterion("assessment_percent_total in", values, "assessmentPercentTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentTotalNotIn(List<BigDecimal> values) {
            addCriterion("assessment_percent_total not in", values, "assessmentPercentTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("assessment_percent_total between", value1, value2, "assessmentPercentTotal");
            return (Criteria) this;
        }

        public Criteria andAssessmentPercentTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("assessment_percent_total not between", value1, value2, "assessmentPercentTotal");
            return (Criteria) this;
        }

        public Criteria andReceiptCurrencyIsNull() {
            addCriterion("receipt_currency is null");
            return (Criteria) this;
        }

        public Criteria andReceiptCurrencyIsNotNull() {
            addCriterion("receipt_currency is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptCurrencyEqualTo(String value) {
            addCriterion("receipt_currency =", value, "receiptCurrency");
            return (Criteria) this;
        }

        public Criteria andReceiptCurrencyNotEqualTo(String value) {
            addCriterion("receipt_currency <>", value, "receiptCurrency");
            return (Criteria) this;
        }

        public Criteria andReceiptCurrencyGreaterThan(String value) {
            addCriterion("receipt_currency >", value, "receiptCurrency");
            return (Criteria) this;
        }

        public Criteria andReceiptCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("receipt_currency >=", value, "receiptCurrency");
            return (Criteria) this;
        }

        public Criteria andReceiptCurrencyLessThan(String value) {
            addCriterion("receipt_currency <", value, "receiptCurrency");
            return (Criteria) this;
        }

        public Criteria andReceiptCurrencyLessThanOrEqualTo(String value) {
            addCriterion("receipt_currency <=", value, "receiptCurrency");
            return (Criteria) this;
        }

        public Criteria andReceiptCurrencyLike(String value) {
            addCriterion("receipt_currency like", value, "receiptCurrency");
            return (Criteria) this;
        }

        public Criteria andReceiptCurrencyNotLike(String value) {
            addCriterion("receipt_currency not like", value, "receiptCurrency");
            return (Criteria) this;
        }

        public Criteria andReceiptCurrencyIn(List<String> values) {
            addCriterion("receipt_currency in", values, "receiptCurrency");
            return (Criteria) this;
        }

        public Criteria andReceiptCurrencyNotIn(List<String> values) {
            addCriterion("receipt_currency not in", values, "receiptCurrency");
            return (Criteria) this;
        }

        public Criteria andReceiptCurrencyBetween(String value1, String value2) {
            addCriterion("receipt_currency between", value1, value2, "receiptCurrency");
            return (Criteria) this;
        }

        public Criteria andReceiptCurrencyNotBetween(String value1, String value2) {
            addCriterion("receipt_currency not between", value1, value2, "receiptCurrency");
            return (Criteria) this;
        }

        public Criteria andInvoiceCurrencyIsNull() {
            addCriterion("invoice_currency is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceCurrencyIsNotNull() {
            addCriterion("invoice_currency is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceCurrencyEqualTo(String value) {
            addCriterion("invoice_currency =", value, "invoiceCurrency");
            return (Criteria) this;
        }

        public Criteria andInvoiceCurrencyNotEqualTo(String value) {
            addCriterion("invoice_currency <>", value, "invoiceCurrency");
            return (Criteria) this;
        }

        public Criteria andInvoiceCurrencyGreaterThan(String value) {
            addCriterion("invoice_currency >", value, "invoiceCurrency");
            return (Criteria) this;
        }

        public Criteria andInvoiceCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("invoice_currency >=", value, "invoiceCurrency");
            return (Criteria) this;
        }

        public Criteria andInvoiceCurrencyLessThan(String value) {
            addCriterion("invoice_currency <", value, "invoiceCurrency");
            return (Criteria) this;
        }

        public Criteria andInvoiceCurrencyLessThanOrEqualTo(String value) {
            addCriterion("invoice_currency <=", value, "invoiceCurrency");
            return (Criteria) this;
        }

        public Criteria andInvoiceCurrencyLike(String value) {
            addCriterion("invoice_currency like", value, "invoiceCurrency");
            return (Criteria) this;
        }

        public Criteria andInvoiceCurrencyNotLike(String value) {
            addCriterion("invoice_currency not like", value, "invoiceCurrency");
            return (Criteria) this;
        }

        public Criteria andInvoiceCurrencyIn(List<String> values) {
            addCriterion("invoice_currency in", values, "invoiceCurrency");
            return (Criteria) this;
        }

        public Criteria andInvoiceCurrencyNotIn(List<String> values) {
            addCriterion("invoice_currency not in", values, "invoiceCurrency");
            return (Criteria) this;
        }

        public Criteria andInvoiceCurrencyBetween(String value1, String value2) {
            addCriterion("invoice_currency between", value1, value2, "invoiceCurrency");
            return (Criteria) this;
        }

        public Criteria andInvoiceCurrencyNotBetween(String value1, String value2) {
            addCriterion("invoice_currency not between", value1, value2, "invoiceCurrency");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}