package com.midea.pam.common.statistics.excelVo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-7-25
 * @description 在途成本-po的未接收金额
 */
@Getter
@Setter
public class RequirementPoDetailExcelVO {

    @Excel(name = "序号", width = 5)
    private Integer number;

    @Excel(name = "WBS", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项", width = 15)
    private String activityCode;

    @Excel(name = "采购订单号", width = 25)
    private String num;

    @Excel(name = "物料PAM编码", width = 25)
    private String pamCode;

    @Excel(name = "物料ERP编码", width = 25)
    private String erpCode;

    @Excel(name = "下达数量", width = 15)
    private BigDecimal orderNum;

    @Excel(name = "入库数量", width = 15)
    private BigDecimal storageCount;

    @Excel(name = "取消数量", width = 15)
    private BigDecimal cancelNum;

    @Excel(name = "折后价（不含税）", width = 15)
    private BigDecimal discountPrice;

    @Excel(name = "po的未接收数量*po单价金额", width = 15)
    private BigDecimal poTotalAmount;

    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date dataTime;
}
