package com.midea.pam.common.statistics.excelVo;


import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022-7-25
 * @description 项目成本按wbs活动行汇总导出 ---暂未使用
 */
@Getter
@Setter
public class ProjectWbsCostSummaryExcelVO {

    @Excel(name = "项目id", width = 15)
    private Long projectId;

    @Excel(name = "项目名称", width = 15)
    private String projectCode;

    @Excel(name = "线体", width = 15)
    private String lineboby;

    @Excel(name = "工位", width = 15)
    private String station;

    @Excel(name = "工具", width = 15)
    private String tool;

    @Excel(name = "描述", width = 15)
    private String description;

    @Excel(name = "活动事项编码", width = 15)
    private String activityCode;

    @Excel(name = "活动类别名称", width = 15)
    private String activityName;

    @Excel(name = "活动类别属性", width = 15)
    private String activityType;

    @Excel(name = "wbs编码", width = 15)
    private String wbsSummaryCode;

    @Excel(name = "预算金额", width = 15)
    private BigDecimal price;

    @Excel(name = "预算基线", width = 15)
    private BigDecimal baselineCost;

    @Excel(name = "需求预算", width = 15)
    private BigDecimal demandCost;

    @Excel(name = "在途成本", width = 15)
    private BigDecimal onTheWayCost;

    @Excel(name = "已发生成本", width = 15)
    private BigDecimal incurredCost;

    @Excel(name = "剩余可用预算", width = 15)
    private BigDecimal remainingCost;

    @Excel(name = "累计变更金额", width = 15)
    private BigDecimal changeAccumulateCost;

}
