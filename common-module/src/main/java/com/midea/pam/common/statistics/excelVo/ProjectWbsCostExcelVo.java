package com.midea.pam.common.statistics.excelVo;

import com.midea.pam.common.ctc.dto.ContractDTO;
import com.midea.pam.common.statistics.entity.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-07-20
 * @description 项目成本报表汇总
 */
@Getter
@Setter
public class ProjectWbsCostExcelVo {

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "已确认收入总额（原币）")
    private BigDecimal confirmedIncome;

    @ApiModelProperty(value = "已确认收入总额（本位币）")
    private BigDecimal standardConfirmedIncome;

    @ApiModelProperty(value = "已确认成本总额")
    private BigDecimal confirmedCost;

    @ApiModelProperty(value = "已确认毛利率")
    private BigDecimal confirmedRate;

    @ApiModelProperty(value = "项目总预算")
    private BigDecimal projectTotalBudget;

    @ApiModelProperty(value = "项目总需求预算")
    private BigDecimal totalDemandBudget;

    @ApiModelProperty(value = "在途总成本")
    private BigDecimal totalCostInTransit;

    @ApiModelProperty(value = "已发生总成本")
    private BigDecimal totalCostIncurred;

    @ApiModelProperty(value = "剩余总预算")
    private BigDecimal remainingTotalBudget;

    @ApiModelProperty(value = "启用WBS（0否1是）")
    private Boolean wbsEnabled;

    @ApiModelProperty(value = "wbs模板信息id")
    private Long wbsTemplateInfoId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "执行时间")
    private Date executeTime;

    @ApiModelProperty(value = "汇总")
    List<Map<String, Object>> mapList;

    @ApiModelProperty(value = "需求预算-物料采购需求预算")
    List<RequirementBudget> purchaseList;

    @ApiModelProperty(value = "需求预算-物料外包（整包）需求预算")
    List<RequirementBudget> outsourceList;

    @ApiModelProperty(value = "需求预算-审批中的需求发布单据")
    List<RequirementBudget> approvalList;

    @ApiModelProperty(value = "在途成本-po的未接收金额")
    List<RequirementPoDetail> poOnTheWay;

    @ApiModelProperty(value = "在途成本-采购合同未进度执行金额")
    List<ProjectPurchaseContractDetail> contractOnTheWay;

    @ApiModelProperty(value = "已发生成本-采购合同进度执行金额")
    List<ProjectPurchaseContractDetail> contractComplete;

    @ApiModelProperty(value = "已发生成本-领退料成本")
    List<ProjectMaterialDetail> getMaterial;

    @ApiModelProperty(value = "需求预算-费用申请（EA单）未释放、剩余可用的金额")
    List<ProjectEaDetail> ea;

    @ApiModelProperty(value = "需求预算-人力点工需求预算")
    List<HroRequirementBudget> hroRequirementBudgets;

    @ApiModelProperty(value = "需求预算-审批中的人力点工需求单据")
    List<HroRequirementBudget> hroRequirementBudgetApprovals;

    @ApiModelProperty(value = "在途成本-费用报销（EC单）已申请未报销")
    List<ProjectEaDetail> ecOnTheWay;

    @ApiModelProperty(value = "已发生成本-费用报销（EC单）审核通过并已入账")
    List<ProjectEaDetail> ecComplete;

    @ApiModelProperty(value = "在途成本-已填报待审批的工时金额")
    List<ProjectWorkingHourDetail> wkOnTheWay;

    @ApiModelProperty(value = "已发生成本-审批通过的工时金额")
    List<ProjectWorkingHourDetail> wkComplete;

    @ApiModelProperty(value = "在途成本-点工采购合同分配金额-已对账金额")
    List<HroPurchaseContractTotal> hroPurchaseContractOnTheWay;

    @ApiModelProperty(value = "已发生成本-点工采购合同已对账金额")
    List<HroPurchaseContractTotal> hroPurchaseContractComplete;

    @ApiModelProperty(value = "开票回款信息")
    List<ContractDTO> invoiceReceiptInfoList;

    @ApiModelProperty(value = "供应商罚扣项目成本汇总")
    List<VendorPenaltyProjectCostSummary> vendorPenaltyProjectCostSummaries;

    @ApiModelProperty(value = "此项目是否手工更新中")
    private Boolean updating;

    @ApiModelProperty(value = "手工更新失败原因")
    private String failMsg;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "本位币")
    private String localCurrency;

    @ApiModelProperty(value = "已确认汇兑损益")
    private BigDecimal confirmedExchangeAmount;
}
