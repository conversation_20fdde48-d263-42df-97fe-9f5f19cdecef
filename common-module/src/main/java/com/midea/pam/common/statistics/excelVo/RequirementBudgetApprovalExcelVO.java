package com.midea.pam.common.statistics.excelVo;


import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-7-25
 * @description 需求预算-审批中的需求发布单据
 */
@Getter
@Setter
public class RequirementBudgetApprovalExcelVO {

    @Excel(name = "序号", width = 5)
    private Integer number;

    @Excel(name = "WBS", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项", width = 15)
    private String activityCode;

    @Excel(name = "需求发布单据编号", width = 25)
    private String requirementCode;

    @Excel(name = "WBS'预算占用金额'", width = 15)
    private BigDecimal budgetOccupiedAmount;

    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date dataTime;
}
