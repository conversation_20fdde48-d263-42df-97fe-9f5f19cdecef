package com.midea.pam.common.statistics.excelVo;


import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-7-25
 * @description 已发生成本-审批通过的工时金额
 */
@Getter
@Setter
public class ProjectWorkingHourCompleteExcelVO {

    @Excel(name = "序号", width = 5)
    private Integer number;

    @Excel(name = "WBS", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项", width = 15)
    private String activityCode;

    @Excel(name = "人员", width = 15)
    private String userMip;

    @Excel(name = "角色", width = 25)
    private String level;

    @Excel(name = "时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date applyDate;

    @Excel(name = "审批通过的工时（h）", width = 15)
    private BigDecimal workingHours;

    @Excel(name = "标准费率（d）", width = 15)
    private BigDecimal costMoney;

    @Excel(name = "审批通过的工时*标准费率/8", width = 15)
    private BigDecimal totalAmount;

    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date dataTime;
}
