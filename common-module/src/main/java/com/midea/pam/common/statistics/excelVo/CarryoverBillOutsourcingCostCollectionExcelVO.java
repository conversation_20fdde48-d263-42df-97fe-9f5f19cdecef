package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: common-module
 * @description: 实际成本归集-物料外包成本明细导出实体
 * @author:zhongpeng
 * @create:2020-03-24 16:32
 **/
public class CarryoverBillOutsourcingCostCollectionExcelVO {
    @Excel(name = "序号",width = 10)
    private Integer num;

    @Excel(name = "归集日期",width = 20,format = "yyyy-MM-dd")
    private Date collectionDate;

    @Excel(name = "成本发生日期",width = 20,format = "yyyy-MM-dd")
    private Date costDate;

    @Excel(name = "结转状态",width = 20,replace = {"未结转_0", "已结转_1","未结转_null"})
    private Integer carryStatus;

    @Excel(name = "结转期间",width = 20)
    private String glPeriod;

    @Excel(name = "项目编号",width = 30)
    private String projectCode;

    @Excel(name = "项目名称",width = 30)
    private String projectName;

    @Excel(name = "里程碑节点",width = 20)
    private String annex;

    @Excel(name = "采购合同编号",width = 30)
    private String purchaseContractCode;

    @Excel(name = "采购合同名称",width = 30)
    private String purchaseContractName;

    @Excel(name = "合同金额(不含税)",width = 20)
    private BigDecimal excludingTaxAmount;

    @Excel(name = "成本结转比例",width = 20)
    private BigDecimal costRatioConfigDetailCostRatio;

    @Excel(name = "实际结转金额(不含税)",width = 20)
    private BigDecimal currentOutsourcingContractCost;

    @Excel(name = "币种",width = 10)
    private String currency;

    @Excel(name = "业务实体",width = 30)
    private String ouName;

    private BigDecimal outsourcingContractCost;

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Date getCollectionDate() {
        return collectionDate;
    }

    public void setCollectionDate(Date collectionDate) {
        this.collectionDate = collectionDate;
    }

    public Date getCostDate() {
        return costDate;
    }

    public void setCostDate(Date costDate) {
        this.costDate = costDate;
    }

    public Integer getCarryStatus() {
        return carryStatus;
    }

    public void setCarryStatus(Integer carryStatus) {
        this.carryStatus = carryStatus;
    }

    public String getGlPeriod() {
        return glPeriod;
    }

    public void setGlPeriod(String glPeriod) {
        this.glPeriod = glPeriod;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getAnnex() {
        return annex;
    }

    public void setAnnex(String annex) {
        this.annex = annex;
    }

    public String getPurchaseContractCode() {
        return purchaseContractCode;
    }

    public void setPurchaseContractCode(String purchaseContractCode) {
        this.purchaseContractCode = purchaseContractCode;
    }

    public String getPurchaseContractName() {
        return purchaseContractName;
    }

    public void setPurchaseContractName(String purchaseContractName) {
        this.purchaseContractName = purchaseContractName;
    }

    public BigDecimal getExcludingTaxAmount() {
        return excludingTaxAmount;
    }

    public void setExcludingTaxAmount(BigDecimal excludingTaxAmount) {
        this.excludingTaxAmount = excludingTaxAmount;
    }

    public BigDecimal getCostRatioConfigDetailCostRatio() {
        return costRatioConfigDetailCostRatio;
    }

    public void setCostRatioConfigDetailCostRatio(BigDecimal costRatioConfigDetailCostRatio) {
        this.costRatioConfigDetailCostRatio = costRatioConfigDetailCostRatio;
    }

    public BigDecimal getOutsourcingContractCost() {
        return outsourcingContractCost;
    }

    public void setOutsourcingContractCost(BigDecimal outsourcingContractCost) {
        this.outsourcingContractCost = outsourcingContractCost;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName;
    }

    public BigDecimal getCurrentOutsourcingContractCost() {
        return currentOutsourcingContractCost;
    }

    public void setCurrentOutsourcingContractCost(BigDecimal currentOutsourcingContractCost) {
        this.currentOutsourcingContractCost = currentOutsourcingContractCost;
    }
}
