package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectCurrentIncomeSummaryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectCurrentIncomeSummaryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountIsNull() {
            addCriterion("confirmed_income_total_amount is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountIsNotNull() {
            addCriterion("confirmed_income_total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountEqualTo(BigDecimal value) {
            addCriterion("confirmed_income_total_amount =", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_income_total_amount <>", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("confirmed_income_total_amount >", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_income_total_amount >=", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountLessThan(BigDecimal value) {
            addCriterion("confirmed_income_total_amount <", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_income_total_amount <=", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountIn(List<BigDecimal> values) {
            addCriterion("confirmed_income_total_amount in", values, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_income_total_amount not in", values, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_income_total_amount between", value1, value2, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_income_total_amount not between", value1, value2, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountIsNull() {
            addCriterion("standard_confirmed_income_total_amount is null");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountIsNotNull() {
            addCriterion("standard_confirmed_income_total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountEqualTo(BigDecimal value) {
            addCriterion("standard_confirmed_income_total_amount =", value, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("standard_confirmed_income_total_amount <>", value, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("standard_confirmed_income_total_amount >", value, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_confirmed_income_total_amount >=", value, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountLessThan(BigDecimal value) {
            addCriterion("standard_confirmed_income_total_amount <", value, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_confirmed_income_total_amount <=", value, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountIn(List<BigDecimal> values) {
            addCriterion("standard_confirmed_income_total_amount in", values, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("standard_confirmed_income_total_amount not in", values, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_confirmed_income_total_amount between", value1, value2, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andStandardConfirmedIncomeTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_confirmed_income_total_amount not between", value1, value2, "standardConfirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountIsNull() {
            addCriterion("confirmed_cost_total_amount is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountIsNotNull() {
            addCriterion("confirmed_cost_total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount =", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount <>", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount >", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount >=", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountLessThan(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount <", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount <=", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountIn(List<BigDecimal> values) {
            addCriterion("confirmed_cost_total_amount in", values, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_cost_total_amount not in", values, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_cost_total_amount between", value1, value2, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_cost_total_amount not between", value1, value2, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioIsNull() {
            addCriterion("confirmed_gross_profit_ratio is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioIsNotNull() {
            addCriterion("confirmed_gross_profit_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioEqualTo(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio =", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio <>", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioGreaterThan(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio >", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio >=", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioLessThan(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio <", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio <=", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioIn(List<BigDecimal> values) {
            addCriterion("confirmed_gross_profit_ratio in", values, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_gross_profit_ratio not in", values, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_gross_profit_ratio between", value1, value2, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_gross_profit_ratio not between", value1, value2, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountIsNull() {
            addCriterion("confirmed_exchange_amount is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountIsNotNull() {
            addCriterion("confirmed_exchange_amount is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountEqualTo(BigDecimal value) {
            addCriterion("confirmed_exchange_amount =", value, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_exchange_amount <>", value, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountGreaterThan(BigDecimal value) {
            addCriterion("confirmed_exchange_amount >", value, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_exchange_amount >=", value, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountLessThan(BigDecimal value) {
            addCriterion("confirmed_exchange_amount <", value, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_exchange_amount <=", value, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountIn(List<BigDecimal> values) {
            addCriterion("confirmed_exchange_amount in", values, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_exchange_amount not in", values, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_exchange_amount between", value1, value2, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedExchangeAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_exchange_amount not between", value1, value2, "confirmedExchangeAmount");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}