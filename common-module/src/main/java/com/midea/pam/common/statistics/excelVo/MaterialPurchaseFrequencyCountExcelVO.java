package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-05-17
 * 物料采购频率统计表ExcelVO
 */
@Getter
@Setter
public class MaterialPurchaseFrequencyCountExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer number;

    @Excel(name = "采购次数", width = 10)
    private Integer purchaseNum;

    @Excel(name = "PAM编码", width = 20)
    private String pamCode;

    @Excel(name = "ERP编码", width = 20)
    private String erpCode;

    @Excel(name = "物料描述", width = 30)
    private String itemInfo;

    @Excel(name = "基本计量单位", width = 10)
    private String unit;

    @Excel(name = "物料状态", width = 15)
    private String itemStatusName;

    @Excel(name = "是否退市物料", width = 10)
    private String delistFlagName;

    @Excel(name = "物料大类", width = 20)
    private String materialClassification;

    @Excel(name = "物料中类", width = 20)
    private String codingMiddleclass;

    @Excel(name = "物料小类", width = 20)
    private String materialType;

    @Excel(name = "名称", width = 20)
    private String name;

    @Excel(name = "型号/规格", width = 20)
    private String model;

    @Excel(name = "品牌", width = 20)
    private String brand;

    @Excel(name = "加工分类", width = 20)
    private String machiningPartType;

    @Excel(name = "材质", width = 20)
    private String material;

    @Excel(name = "单位重量(Kg)", width = 20)
    private BigDecimal unitWeight;

    @Excel(name = "材质处理", width = 20)
    private String materialProcessing;

    @Excel(name = "最小订货量", width = 20)
    private Long minimumOrderQuantity;

    @Excel(name = "接受子库存", width = 20)
    private String recevingSubinventory;

    @Excel(name = "货架", width = 20)
    private String shelves;

    @Excel(name = "配套人员", width = 20)
    private String sourcer;

    @Excel(name = "安全库存", width = 20)
    private Long safetyStockQuantity;

    @Excel(name = "用户物料类型", width = 15)
    private String itemTypeName;

    @Excel(name = "采购员", width = 20)
    private String buyerNumber;

    @Excel(name = "采购周期", width = 20)
    private String buyerRound;

    @Excel(name = "库存分类", width = 20)
    private String inventoryType;

    @Excel(name = "采购分类", width = 20)
    private String purType;

    @Excel(name = "库存组织名称", width = 20)
    private String organizationName;

    @Excel(name = "创建时间", format = "yyyy-MM-dd", width = 15)
    private Date createAt;

    @Excel(name = "创建人", width = 20)
    private String creatorName;

    @Excel(name = "最后更新时间", format = "yyyy-MM-dd", width = 15)
    private Date updateAt;

    @Excel(name = "更新人", width = 20)
    private String updaterName;

    @Excel(name = "图号", width = 20)
    private String figureNumber;

    @Excel(name = "图纸版本号", width = 20)
    private String chartVersion;

    @Excel(name = "品牌商物料编码", width = 20)
    private String brandMaterialCode;

}