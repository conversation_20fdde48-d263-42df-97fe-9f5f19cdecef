package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WorkingHourDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WorkingHourDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNull() {
            addCriterion("project_status is null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNotNull() {
            addCriterion("project_status is not null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusEqualTo(Integer value) {
            addCriterion("project_status =", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotEqualTo(Integer value) {
            addCriterion("project_status <>", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThan(Integer value) {
            addCriterion("project_status >", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("project_status >=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThan(Integer value) {
            addCriterion("project_status <", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThanOrEqualTo(Integer value) {
            addCriterion("project_status <=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIn(List<Integer> values) {
            addCriterion("project_status in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotIn(List<Integer> values) {
            addCriterion("project_status not in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusBetween(Integer value1, Integer value2) {
            addCriterion("project_status between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("project_status not between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNull() {
            addCriterion("department_id is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNotNull() {
            addCriterion("department_id is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdEqualTo(Long value) {
            addCriterion("department_id =", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotEqualTo(Long value) {
            addCriterion("department_id <>", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThan(Long value) {
            addCriterion("department_id >", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("department_id >=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThan(Long value) {
            addCriterion("department_id <", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThanOrEqualTo(Long value) {
            addCriterion("department_id <=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIn(List<Long> values) {
            addCriterion("department_id in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotIn(List<Long> values) {
            addCriterion("department_id not in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdBetween(Long value1, Long value2) {
            addCriterion("department_id between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotBetween(Long value1, Long value2) {
            addCriterion("department_id not between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIsNull() {
            addCriterion("department_name is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIsNotNull() {
            addCriterion("department_name is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameEqualTo(String value) {
            addCriterion("department_name =", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotEqualTo(String value) {
            addCriterion("department_name <>", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameGreaterThan(String value) {
            addCriterion("department_name >", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameGreaterThanOrEqualTo(String value) {
            addCriterion("department_name >=", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLessThan(String value) {
            addCriterion("department_name <", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLessThanOrEqualTo(String value) {
            addCriterion("department_name <=", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLike(String value) {
            addCriterion("department_name like", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotLike(String value) {
            addCriterion("department_name not like", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIn(List<String> values) {
            addCriterion("department_name in", values, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotIn(List<String> values) {
            addCriterion("department_name not in", values, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameBetween(String value1, String value2) {
            addCriterion("department_name between", value1, value2, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotBetween(String value1, String value2) {
            addCriterion("department_name not between", value1, value2, "departmentName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNull() {
            addCriterion("project_manager is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNotNull() {
            addCriterion("project_manager is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerEqualTo(String value) {
            addCriterion("project_manager =", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotEqualTo(String value) {
            addCriterion("project_manager <>", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThan(String value) {
            addCriterion("project_manager >", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThanOrEqualTo(String value) {
            addCriterion("project_manager >=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThan(String value) {
            addCriterion("project_manager <", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThanOrEqualTo(String value) {
            addCriterion("project_manager <=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLike(String value) {
            addCriterion("project_manager like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotLike(String value) {
            addCriterion("project_manager not like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIn(List<String> values) {
            addCriterion("project_manager in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotIn(List<String> values) {
            addCriterion("project_manager not in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerBetween(String value1, String value2) {
            addCriterion("project_manager between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotBetween(String value1, String value2) {
            addCriterion("project_manager not between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andFillByNameIsNull() {
            addCriterion("fill_by_name is null");
            return (Criteria) this;
        }

        public Criteria andFillByNameIsNotNull() {
            addCriterion("fill_by_name is not null");
            return (Criteria) this;
        }

        public Criteria andFillByNameEqualTo(String value) {
            addCriterion("fill_by_name =", value, "fillByName");
            return (Criteria) this;
        }

        public Criteria andFillByNameNotEqualTo(String value) {
            addCriterion("fill_by_name <>", value, "fillByName");
            return (Criteria) this;
        }

        public Criteria andFillByNameGreaterThan(String value) {
            addCriterion("fill_by_name >", value, "fillByName");
            return (Criteria) this;
        }

        public Criteria andFillByNameGreaterThanOrEqualTo(String value) {
            addCriterion("fill_by_name >=", value, "fillByName");
            return (Criteria) this;
        }

        public Criteria andFillByNameLessThan(String value) {
            addCriterion("fill_by_name <", value, "fillByName");
            return (Criteria) this;
        }

        public Criteria andFillByNameLessThanOrEqualTo(String value) {
            addCriterion("fill_by_name <=", value, "fillByName");
            return (Criteria) this;
        }

        public Criteria andFillByNameLike(String value) {
            addCriterion("fill_by_name like", value, "fillByName");
            return (Criteria) this;
        }

        public Criteria andFillByNameNotLike(String value) {
            addCriterion("fill_by_name not like", value, "fillByName");
            return (Criteria) this;
        }

        public Criteria andFillByNameIn(List<String> values) {
            addCriterion("fill_by_name in", values, "fillByName");
            return (Criteria) this;
        }

        public Criteria andFillByNameNotIn(List<String> values) {
            addCriterion("fill_by_name not in", values, "fillByName");
            return (Criteria) this;
        }

        public Criteria andFillByNameBetween(String value1, String value2) {
            addCriterion("fill_by_name between", value1, value2, "fillByName");
            return (Criteria) this;
        }

        public Criteria andFillByNameNotBetween(String value1, String value2) {
            addCriterion("fill_by_name not between", value1, value2, "fillByName");
            return (Criteria) this;
        }

        public Criteria andFillByMipIsNull() {
            addCriterion("fill_by_mip is null");
            return (Criteria) this;
        }

        public Criteria andFillByMipIsNotNull() {
            addCriterion("fill_by_mip is not null");
            return (Criteria) this;
        }

        public Criteria andFillByMipEqualTo(String value) {
            addCriterion("fill_by_mip =", value, "fillByMip");
            return (Criteria) this;
        }

        public Criteria andFillByMipNotEqualTo(String value) {
            addCriterion("fill_by_mip <>", value, "fillByMip");
            return (Criteria) this;
        }

        public Criteria andFillByMipGreaterThan(String value) {
            addCriterion("fill_by_mip >", value, "fillByMip");
            return (Criteria) this;
        }

        public Criteria andFillByMipGreaterThanOrEqualTo(String value) {
            addCriterion("fill_by_mip >=", value, "fillByMip");
            return (Criteria) this;
        }

        public Criteria andFillByMipLessThan(String value) {
            addCriterion("fill_by_mip <", value, "fillByMip");
            return (Criteria) this;
        }

        public Criteria andFillByMipLessThanOrEqualTo(String value) {
            addCriterion("fill_by_mip <=", value, "fillByMip");
            return (Criteria) this;
        }

        public Criteria andFillByMipLike(String value) {
            addCriterion("fill_by_mip like", value, "fillByMip");
            return (Criteria) this;
        }

        public Criteria andFillByMipNotLike(String value) {
            addCriterion("fill_by_mip not like", value, "fillByMip");
            return (Criteria) this;
        }

        public Criteria andFillByMipIn(List<String> values) {
            addCriterion("fill_by_mip in", values, "fillByMip");
            return (Criteria) this;
        }

        public Criteria andFillByMipNotIn(List<String> values) {
            addCriterion("fill_by_mip not in", values, "fillByMip");
            return (Criteria) this;
        }

        public Criteria andFillByMipBetween(String value1, String value2) {
            addCriterion("fill_by_mip between", value1, value2, "fillByMip");
            return (Criteria) this;
        }

        public Criteria andFillByMipNotBetween(String value1, String value2) {
            addCriterion("fill_by_mip not between", value1, value2, "fillByMip");
            return (Criteria) this;
        }

        public Criteria andApplyOrgIsNull() {
            addCriterion("apply_org is null");
            return (Criteria) this;
        }

        public Criteria andApplyOrgIsNotNull() {
            addCriterion("apply_org is not null");
            return (Criteria) this;
        }

        public Criteria andApplyOrgEqualTo(String value) {
            addCriterion("apply_org =", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNotEqualTo(String value) {
            addCriterion("apply_org <>", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgGreaterThan(String value) {
            addCriterion("apply_org >", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgGreaterThanOrEqualTo(String value) {
            addCriterion("apply_org >=", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgLessThan(String value) {
            addCriterion("apply_org <", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgLessThanOrEqualTo(String value) {
            addCriterion("apply_org <=", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgLike(String value) {
            addCriterion("apply_org like", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNotLike(String value) {
            addCriterion("apply_org not like", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgIn(List<String> values) {
            addCriterion("apply_org in", values, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNotIn(List<String> values) {
            addCriterion("apply_org not in", values, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgBetween(String value1, String value2) {
            addCriterion("apply_org between", value1, value2, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNotBetween(String value1, String value2) {
            addCriterion("apply_org not between", value1, value2, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andAttendanceDateIsNull() {
            addCriterion("attendance_date is null");
            return (Criteria) this;
        }

        public Criteria andAttendanceDateIsNotNull() {
            addCriterion("attendance_date is not null");
            return (Criteria) this;
        }

        public Criteria andAttendanceDateEqualTo(Date value) {
            addCriterion("attendance_date =", value, "attendanceDate");
            return (Criteria) this;
        }

        public Criteria andAttendanceDateNotEqualTo(Date value) {
            addCriterion("attendance_date <>", value, "attendanceDate");
            return (Criteria) this;
        }

        public Criteria andAttendanceDateGreaterThan(Date value) {
            addCriterion("attendance_date >", value, "attendanceDate");
            return (Criteria) this;
        }

        public Criteria andAttendanceDateGreaterThanOrEqualTo(Date value) {
            addCriterion("attendance_date >=", value, "attendanceDate");
            return (Criteria) this;
        }

        public Criteria andAttendanceDateLessThan(Date value) {
            addCriterion("attendance_date <", value, "attendanceDate");
            return (Criteria) this;
        }

        public Criteria andAttendanceDateLessThanOrEqualTo(Date value) {
            addCriterion("attendance_date <=", value, "attendanceDate");
            return (Criteria) this;
        }

        public Criteria andAttendanceDateIn(List<Date> values) {
            addCriterion("attendance_date in", values, "attendanceDate");
            return (Criteria) this;
        }

        public Criteria andAttendanceDateNotIn(List<Date> values) {
            addCriterion("attendance_date not in", values, "attendanceDate");
            return (Criteria) this;
        }

        public Criteria andAttendanceDateBetween(Date value1, Date value2) {
            addCriterion("attendance_date between", value1, value2, "attendanceDate");
            return (Criteria) this;
        }

        public Criteria andAttendanceDateNotBetween(Date value1, Date value2) {
            addCriterion("attendance_date not between", value1, value2, "attendanceDate");
            return (Criteria) this;
        }

        public Criteria andFillDateIsNull() {
            addCriterion("fill_date is null");
            return (Criteria) this;
        }

        public Criteria andFillDateIsNotNull() {
            addCriterion("fill_date is not null");
            return (Criteria) this;
        }

        public Criteria andFillDateEqualTo(Date value) {
            addCriterion("fill_date =", value, "fillDate");
            return (Criteria) this;
        }

        public Criteria andFillDateNotEqualTo(Date value) {
            addCriterion("fill_date <>", value, "fillDate");
            return (Criteria) this;
        }

        public Criteria andFillDateGreaterThan(Date value) {
            addCriterion("fill_date >", value, "fillDate");
            return (Criteria) this;
        }

        public Criteria andFillDateGreaterThanOrEqualTo(Date value) {
            addCriterion("fill_date >=", value, "fillDate");
            return (Criteria) this;
        }

        public Criteria andFillDateLessThan(Date value) {
            addCriterion("fill_date <", value, "fillDate");
            return (Criteria) this;
        }

        public Criteria andFillDateLessThanOrEqualTo(Date value) {
            addCriterion("fill_date <=", value, "fillDate");
            return (Criteria) this;
        }

        public Criteria andFillDateIn(List<Date> values) {
            addCriterion("fill_date in", values, "fillDate");
            return (Criteria) this;
        }

        public Criteria andFillDateNotIn(List<Date> values) {
            addCriterion("fill_date not in", values, "fillDate");
            return (Criteria) this;
        }

        public Criteria andFillDateBetween(Date value1, Date value2) {
            addCriterion("fill_date between", value1, value2, "fillDate");
            return (Criteria) this;
        }

        public Criteria andFillDateNotBetween(Date value1, Date value2) {
            addCriterion("fill_date not between", value1, value2, "fillDate");
            return (Criteria) this;
        }

        public Criteria andFillWorkingHoursIsNull() {
            addCriterion("fill_working_hours is null");
            return (Criteria) this;
        }

        public Criteria andFillWorkingHoursIsNotNull() {
            addCriterion("fill_working_hours is not null");
            return (Criteria) this;
        }

        public Criteria andFillWorkingHoursEqualTo(BigDecimal value) {
            addCriterion("fill_working_hours =", value, "fillWorkingHours");
            return (Criteria) this;
        }

        public Criteria andFillWorkingHoursNotEqualTo(BigDecimal value) {
            addCriterion("fill_working_hours <>", value, "fillWorkingHours");
            return (Criteria) this;
        }

        public Criteria andFillWorkingHoursGreaterThan(BigDecimal value) {
            addCriterion("fill_working_hours >", value, "fillWorkingHours");
            return (Criteria) this;
        }

        public Criteria andFillWorkingHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("fill_working_hours >=", value, "fillWorkingHours");
            return (Criteria) this;
        }

        public Criteria andFillWorkingHoursLessThan(BigDecimal value) {
            addCriterion("fill_working_hours <", value, "fillWorkingHours");
            return (Criteria) this;
        }

        public Criteria andFillWorkingHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("fill_working_hours <=", value, "fillWorkingHours");
            return (Criteria) this;
        }

        public Criteria andFillWorkingHoursIn(List<BigDecimal> values) {
            addCriterion("fill_working_hours in", values, "fillWorkingHours");
            return (Criteria) this;
        }

        public Criteria andFillWorkingHoursNotIn(List<BigDecimal> values) {
            addCriterion("fill_working_hours not in", values, "fillWorkingHours");
            return (Criteria) this;
        }

        public Criteria andFillWorkingHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fill_working_hours between", value1, value2, "fillWorkingHours");
            return (Criteria) this;
        }

        public Criteria andFillWorkingHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fill_working_hours not between", value1, value2, "fillWorkingHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursIsNull() {
            addCriterion("ihr_attend_hours is null");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursIsNotNull() {
            addCriterion("ihr_attend_hours is not null");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursEqualTo(BigDecimal value) {
            addCriterion("ihr_attend_hours =", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursNotEqualTo(BigDecimal value) {
            addCriterion("ihr_attend_hours <>", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursGreaterThan(BigDecimal value) {
            addCriterion("ihr_attend_hours >", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ihr_attend_hours >=", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursLessThan(BigDecimal value) {
            addCriterion("ihr_attend_hours <", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ihr_attend_hours <=", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursIn(List<BigDecimal> values) {
            addCriterion("ihr_attend_hours in", values, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursNotIn(List<BigDecimal> values) {
            addCriterion("ihr_attend_hours not in", values, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ihr_attend_hours between", value1, value2, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ihr_attend_hours not between", value1, value2, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andApproveByNameIsNull() {
            addCriterion("approve_by_name is null");
            return (Criteria) this;
        }

        public Criteria andApproveByNameIsNotNull() {
            addCriterion("approve_by_name is not null");
            return (Criteria) this;
        }

        public Criteria andApproveByNameEqualTo(String value) {
            addCriterion("approve_by_name =", value, "approveByName");
            return (Criteria) this;
        }

        public Criteria andApproveByNameNotEqualTo(String value) {
            addCriterion("approve_by_name <>", value, "approveByName");
            return (Criteria) this;
        }

        public Criteria andApproveByNameGreaterThan(String value) {
            addCriterion("approve_by_name >", value, "approveByName");
            return (Criteria) this;
        }

        public Criteria andApproveByNameGreaterThanOrEqualTo(String value) {
            addCriterion("approve_by_name >=", value, "approveByName");
            return (Criteria) this;
        }

        public Criteria andApproveByNameLessThan(String value) {
            addCriterion("approve_by_name <", value, "approveByName");
            return (Criteria) this;
        }

        public Criteria andApproveByNameLessThanOrEqualTo(String value) {
            addCriterion("approve_by_name <=", value, "approveByName");
            return (Criteria) this;
        }

        public Criteria andApproveByNameLike(String value) {
            addCriterion("approve_by_name like", value, "approveByName");
            return (Criteria) this;
        }

        public Criteria andApproveByNameNotLike(String value) {
            addCriterion("approve_by_name not like", value, "approveByName");
            return (Criteria) this;
        }

        public Criteria andApproveByNameIn(List<String> values) {
            addCriterion("approve_by_name in", values, "approveByName");
            return (Criteria) this;
        }

        public Criteria andApproveByNameNotIn(List<String> values) {
            addCriterion("approve_by_name not in", values, "approveByName");
            return (Criteria) this;
        }

        public Criteria andApproveByNameBetween(String value1, String value2) {
            addCriterion("approve_by_name between", value1, value2, "approveByName");
            return (Criteria) this;
        }

        public Criteria andApproveByNameNotBetween(String value1, String value2) {
            addCriterion("approve_by_name not between", value1, value2, "approveByName");
            return (Criteria) this;
        }

        public Criteria andApproveByMipIsNull() {
            addCriterion("approve_by_mip is null");
            return (Criteria) this;
        }

        public Criteria andApproveByMipIsNotNull() {
            addCriterion("approve_by_mip is not null");
            return (Criteria) this;
        }

        public Criteria andApproveByMipEqualTo(String value) {
            addCriterion("approve_by_mip =", value, "approveByMip");
            return (Criteria) this;
        }

        public Criteria andApproveByMipNotEqualTo(String value) {
            addCriterion("approve_by_mip <>", value, "approveByMip");
            return (Criteria) this;
        }

        public Criteria andApproveByMipGreaterThan(String value) {
            addCriterion("approve_by_mip >", value, "approveByMip");
            return (Criteria) this;
        }

        public Criteria andApproveByMipGreaterThanOrEqualTo(String value) {
            addCriterion("approve_by_mip >=", value, "approveByMip");
            return (Criteria) this;
        }

        public Criteria andApproveByMipLessThan(String value) {
            addCriterion("approve_by_mip <", value, "approveByMip");
            return (Criteria) this;
        }

        public Criteria andApproveByMipLessThanOrEqualTo(String value) {
            addCriterion("approve_by_mip <=", value, "approveByMip");
            return (Criteria) this;
        }

        public Criteria andApproveByMipLike(String value) {
            addCriterion("approve_by_mip like", value, "approveByMip");
            return (Criteria) this;
        }

        public Criteria andApproveByMipNotLike(String value) {
            addCriterion("approve_by_mip not like", value, "approveByMip");
            return (Criteria) this;
        }

        public Criteria andApproveByMipIn(List<String> values) {
            addCriterion("approve_by_mip in", values, "approveByMip");
            return (Criteria) this;
        }

        public Criteria andApproveByMipNotIn(List<String> values) {
            addCriterion("approve_by_mip not in", values, "approveByMip");
            return (Criteria) this;
        }

        public Criteria andApproveByMipBetween(String value1, String value2) {
            addCriterion("approve_by_mip between", value1, value2, "approveByMip");
            return (Criteria) this;
        }

        public Criteria andApproveByMipNotBetween(String value1, String value2) {
            addCriterion("approve_by_mip not between", value1, value2, "approveByMip");
            return (Criteria) this;
        }

        public Criteria andApproveDateIsNull() {
            addCriterion("approve_date is null");
            return (Criteria) this;
        }

        public Criteria andApproveDateIsNotNull() {
            addCriterion("approve_date is not null");
            return (Criteria) this;
        }

        public Criteria andApproveDateEqualTo(Date value) {
            addCriterion("approve_date =", value, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateNotEqualTo(Date value) {
            addCriterion("approve_date <>", value, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateGreaterThan(Date value) {
            addCriterion("approve_date >", value, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateGreaterThanOrEqualTo(Date value) {
            addCriterion("approve_date >=", value, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateLessThan(Date value) {
            addCriterion("approve_date <", value, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateLessThanOrEqualTo(Date value) {
            addCriterion("approve_date <=", value, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateIn(List<Date> values) {
            addCriterion("approve_date in", values, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateNotIn(List<Date> values) {
            addCriterion("approve_date not in", values, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateBetween(Date value1, Date value2) {
            addCriterion("approve_date between", value1, value2, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateNotBetween(Date value1, Date value2) {
            addCriterion("approve_date not between", value1, value2, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursIsNull() {
            addCriterion("approve_working_hours is null");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursIsNotNull() {
            addCriterion("approve_working_hours is not null");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursEqualTo(BigDecimal value) {
            addCriterion("approve_working_hours =", value, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursNotEqualTo(BigDecimal value) {
            addCriterion("approve_working_hours <>", value, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursGreaterThan(BigDecimal value) {
            addCriterion("approve_working_hours >", value, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("approve_working_hours >=", value, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursLessThan(BigDecimal value) {
            addCriterion("approve_working_hours <", value, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("approve_working_hours <=", value, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursIn(List<BigDecimal> values) {
            addCriterion("approve_working_hours in", values, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursNotIn(List<BigDecimal> values) {
            addCriterion("approve_working_hours not in", values, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("approve_working_hours between", value1, value2, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("approve_working_hours not between", value1, value2, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursIsNull() {
            addCriterion("rdm_working_hours is null");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursIsNotNull() {
            addCriterion("rdm_working_hours is not null");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursEqualTo(BigDecimal value) {
            addCriterion("rdm_working_hours =", value, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursNotEqualTo(BigDecimal value) {
            addCriterion("rdm_working_hours <>", value, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursGreaterThan(BigDecimal value) {
            addCriterion("rdm_working_hours >", value, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("rdm_working_hours >=", value, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursLessThan(BigDecimal value) {
            addCriterion("rdm_working_hours <", value, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("rdm_working_hours <=", value, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursIn(List<BigDecimal> values) {
            addCriterion("rdm_working_hours in", values, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursNotIn(List<BigDecimal> values) {
            addCriterion("rdm_working_hours not in", values, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("rdm_working_hours between", value1, value2, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("rdm_working_hours not between", value1, value2, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNull() {
            addCriterion("project_type is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNotNull() {
            addCriterion("project_type is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeEqualTo(String value) {
            addCriterion("project_type =", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotEqualTo(String value) {
            addCriterion("project_type <>", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThan(String value) {
            addCriterion("project_type >", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThanOrEqualTo(String value) {
            addCriterion("project_type >=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThan(String value) {
            addCriterion("project_type <", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThanOrEqualTo(String value) {
            addCriterion("project_type <=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLike(String value) {
            addCriterion("project_type like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotLike(String value) {
            addCriterion("project_type not like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIn(List<String> values) {
            addCriterion("project_type in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotIn(List<String> values) {
            addCriterion("project_type not in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeBetween(String value1, String value2) {
            addCriterion("project_type between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotBetween(String value1, String value2) {
            addCriterion("project_type not between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Long value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Long value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Long value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Long value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Long> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Long> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Long value1, Long value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNull() {
            addCriterion("unit_name is null");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNotNull() {
            addCriterion("unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andUnitNameEqualTo(String value) {
            addCriterion("unit_name =", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotEqualTo(String value) {
            addCriterion("unit_name <>", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThan(String value) {
            addCriterion("unit_name >", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("unit_name >=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThan(String value) {
            addCriterion("unit_name <", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThanOrEqualTo(String value) {
            addCriterion("unit_name <=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLike(String value) {
            addCriterion("unit_name like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotLike(String value) {
            addCriterion("unit_name not like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIn(List<String> values) {
            addCriterion("unit_name in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotIn(List<String> values) {
            addCriterion("unit_name not in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameBetween(String value1, String value2) {
            addCriterion("unit_name between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotBetween(String value1, String value2) {
            addCriterion("unit_name not between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNull() {
            addCriterion("ou_name is null");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNotNull() {
            addCriterion("ou_name is not null");
            return (Criteria) this;
        }

        public Criteria andOuNameEqualTo(String value) {
            addCriterion("ou_name =", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotEqualTo(String value) {
            addCriterion("ou_name <>", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThan(String value) {
            addCriterion("ou_name >", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThanOrEqualTo(String value) {
            addCriterion("ou_name >=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThan(String value) {
            addCriterion("ou_name <", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThanOrEqualTo(String value) {
            addCriterion("ou_name <=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLike(String value) {
            addCriterion("ou_name like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotLike(String value) {
            addCriterion("ou_name not like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameIn(List<String> values) {
            addCriterion("ou_name in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotIn(List<String> values) {
            addCriterion("ou_name not in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameBetween(String value1, String value2) {
            addCriterion("ou_name between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotBetween(String value1, String value2) {
            addCriterion("ou_name not between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagIsNull() {
            addCriterion("cost_collection_flag is null");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagIsNotNull() {
            addCriterion("cost_collection_flag is not null");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagEqualTo(Boolean value) {
            addCriterion("cost_collection_flag =", value, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagNotEqualTo(Boolean value) {
            addCriterion("cost_collection_flag <>", value, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagGreaterThan(Boolean value) {
            addCriterion("cost_collection_flag >", value, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("cost_collection_flag >=", value, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagLessThan(Boolean value) {
            addCriterion("cost_collection_flag <", value, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("cost_collection_flag <=", value, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagIn(List<Boolean> values) {
            addCriterion("cost_collection_flag in", values, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagNotIn(List<Boolean> values) {
            addCriterion("cost_collection_flag not in", values, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("cost_collection_flag between", value1, value2, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andCostCollectionFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("cost_collection_flag not between", value1, value2, "costCollectionFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagIsNull() {
            addCriterion("invoice_apply_flag is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagIsNotNull() {
            addCriterion("invoice_apply_flag is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagEqualTo(Boolean value) {
            addCriterion("invoice_apply_flag =", value, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagNotEqualTo(Boolean value) {
            addCriterion("invoice_apply_flag <>", value, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagGreaterThan(Boolean value) {
            addCriterion("invoice_apply_flag >", value, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("invoice_apply_flag >=", value, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagLessThan(Boolean value) {
            addCriterion("invoice_apply_flag <", value, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("invoice_apply_flag <=", value, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagIn(List<Boolean> values) {
            addCriterion("invoice_apply_flag in", values, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagNotIn(List<Boolean> values) {
            addCriterion("invoice_apply_flag not in", values, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("invoice_apply_flag between", value1, value2, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("invoice_apply_flag not between", value1, value2, "invoiceApplyFlag");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIsNull() {
            addCriterion("vendor_code is null");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIsNotNull() {
            addCriterion("vendor_code is not null");
            return (Criteria) this;
        }

        public Criteria andVendorCodeEqualTo(String value) {
            addCriterion("vendor_code =", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotEqualTo(String value) {
            addCriterion("vendor_code <>", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThan(String value) {
            addCriterion("vendor_code >", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_code >=", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThan(String value) {
            addCriterion("vendor_code <", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThanOrEqualTo(String value) {
            addCriterion("vendor_code <=", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLike(String value) {
            addCriterion("vendor_code like", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotLike(String value) {
            addCriterion("vendor_code not like", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIn(List<String> values) {
            addCriterion("vendor_code in", values, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotIn(List<String> values) {
            addCriterion("vendor_code not in", values, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeBetween(String value1, String value2) {
            addCriterion("vendor_code between", value1, value2, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotBetween(String value1, String value2) {
            addCriterion("vendor_code not between", value1, value2, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andRoleNameIsNull() {
            addCriterion("role_name is null");
            return (Criteria) this;
        }

        public Criteria andRoleNameIsNotNull() {
            addCriterion("role_name is not null");
            return (Criteria) this;
        }

        public Criteria andRoleNameEqualTo(String value) {
            addCriterion("role_name =", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameNotEqualTo(String value) {
            addCriterion("role_name <>", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameGreaterThan(String value) {
            addCriterion("role_name >", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameGreaterThanOrEqualTo(String value) {
            addCriterion("role_name >=", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameLessThan(String value) {
            addCriterion("role_name <", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameLessThanOrEqualTo(String value) {
            addCriterion("role_name <=", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameLike(String value) {
            addCriterion("role_name like", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameNotLike(String value) {
            addCriterion("role_name not like", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameIn(List<String> values) {
            addCriterion("role_name in", values, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameNotIn(List<String> values) {
            addCriterion("role_name not in", values, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameBetween(String value1, String value2) {
            addCriterion("role_name between", value1, value2, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameNotBetween(String value1, String value2) {
            addCriterion("role_name not between", value1, value2, "roleName");
            return (Criteria) this;
        }

        public Criteria andCrossUnitLaborCostIsNull() {
            addCriterion("cross_unit_labor_cost is null");
            return (Criteria) this;
        }

        public Criteria andCrossUnitLaborCostIsNotNull() {
            addCriterion("cross_unit_labor_cost is not null");
            return (Criteria) this;
        }

        public Criteria andCrossUnitLaborCostEqualTo(Boolean value) {
            addCriterion("cross_unit_labor_cost =", value, "crossUnitLaborCost");
            return (Criteria) this;
        }

        public Criteria andCrossUnitLaborCostNotEqualTo(Boolean value) {
            addCriterion("cross_unit_labor_cost <>", value, "crossUnitLaborCost");
            return (Criteria) this;
        }

        public Criteria andCrossUnitLaborCostGreaterThan(Boolean value) {
            addCriterion("cross_unit_labor_cost >", value, "crossUnitLaborCost");
            return (Criteria) this;
        }

        public Criteria andCrossUnitLaborCostGreaterThanOrEqualTo(Boolean value) {
            addCriterion("cross_unit_labor_cost >=", value, "crossUnitLaborCost");
            return (Criteria) this;
        }

        public Criteria andCrossUnitLaborCostLessThan(Boolean value) {
            addCriterion("cross_unit_labor_cost <", value, "crossUnitLaborCost");
            return (Criteria) this;
        }

        public Criteria andCrossUnitLaborCostLessThanOrEqualTo(Boolean value) {
            addCriterion("cross_unit_labor_cost <=", value, "crossUnitLaborCost");
            return (Criteria) this;
        }

        public Criteria andCrossUnitLaborCostIn(List<Boolean> values) {
            addCriterion("cross_unit_labor_cost in", values, "crossUnitLaborCost");
            return (Criteria) this;
        }

        public Criteria andCrossUnitLaborCostNotIn(List<Boolean> values) {
            addCriterion("cross_unit_labor_cost not in", values, "crossUnitLaborCost");
            return (Criteria) this;
        }

        public Criteria andCrossUnitLaborCostBetween(Boolean value1, Boolean value2) {
            addCriterion("cross_unit_labor_cost between", value1, value2, "crossUnitLaborCost");
            return (Criteria) this;
        }

        public Criteria andCrossUnitLaborCostNotBetween(Boolean value1, Boolean value2) {
            addCriterion("cross_unit_labor_cost not between", value1, value2, "crossUnitLaborCost");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeIsNull() {
            addCriterion("wbs_budget_code is null");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeIsNotNull() {
            addCriterion("wbs_budget_code is not null");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeEqualTo(String value) {
            addCriterion("wbs_budget_code =", value, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeNotEqualTo(String value) {
            addCriterion("wbs_budget_code <>", value, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeGreaterThan(String value) {
            addCriterion("wbs_budget_code >", value, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeGreaterThanOrEqualTo(String value) {
            addCriterion("wbs_budget_code >=", value, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeLessThan(String value) {
            addCriterion("wbs_budget_code <", value, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeLessThanOrEqualTo(String value) {
            addCriterion("wbs_budget_code <=", value, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeLike(String value) {
            addCriterion("wbs_budget_code like", value, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeNotLike(String value) {
            addCriterion("wbs_budget_code not like", value, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeIn(List<String> values) {
            addCriterion("wbs_budget_code in", values, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeNotIn(List<String> values) {
            addCriterion("wbs_budget_code not in", values, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeBetween(String value1, String value2) {
            addCriterion("wbs_budget_code between", value1, value2, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeNotBetween(String value1, String value2) {
            addCriterion("wbs_budget_code not between", value1, value2, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameIsNull() {
            addCriterion("labor_wbs_cost_name is null");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameIsNotNull() {
            addCriterion("labor_wbs_cost_name is not null");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameEqualTo(String value) {
            addCriterion("labor_wbs_cost_name =", value, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameNotEqualTo(String value) {
            addCriterion("labor_wbs_cost_name <>", value, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameGreaterThan(String value) {
            addCriterion("labor_wbs_cost_name >", value, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameGreaterThanOrEqualTo(String value) {
            addCriterion("labor_wbs_cost_name >=", value, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameLessThan(String value) {
            addCriterion("labor_wbs_cost_name <", value, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameLessThanOrEqualTo(String value) {
            addCriterion("labor_wbs_cost_name <=", value, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameLike(String value) {
            addCriterion("labor_wbs_cost_name like", value, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameNotLike(String value) {
            addCriterion("labor_wbs_cost_name not like", value, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameIn(List<String> values) {
            addCriterion("labor_wbs_cost_name in", values, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameNotIn(List<String> values) {
            addCriterion("labor_wbs_cost_name not in", values, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameBetween(String value1, String value2) {
            addCriterion("labor_wbs_cost_name between", value1, value2, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameNotBetween(String value1, String value2) {
            addCriterion("labor_wbs_cost_name not between", value1, value2, "laborWbsCostName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}