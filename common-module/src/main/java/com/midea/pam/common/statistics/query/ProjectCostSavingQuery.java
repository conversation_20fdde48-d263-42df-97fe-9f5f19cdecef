package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * Description
 * Created by chenchong
 * Date 2022/11/5 16:07
 */
@Getter
@Setter
@ApiModel(value = "ProjectCostSavingQuery", description = "项目成本节约报表")
public class ProjectCostSavingQuery {

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    private Long personal;

    private Long companyId;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "需求分类(0物料采购、1外包)")
    private Integer demandType;

    @ApiModelProperty(value = "需求审批通过开始时间")
    private Date approvalStartTime;

    @ApiModelProperty(value = "需求审批通过结束时间")
    private Date approvalEndTime;

    @Override
    public String toString() {
        return "ProjectCostSavingQuery{" +
                "executeId=" + executeId +
                ", personal=" + personal +
                ", companyId=" + companyId +
                ", projectId='" + projectId + '\'' +
                ", demandType=" + demandType +
                ", approvalStartTime=" + approvalStartTime +
                ", approvalEndTime=" + approvalEndTime +
                '}';
    }
}

