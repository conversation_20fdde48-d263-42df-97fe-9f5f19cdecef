package com.midea.pam.common.statistics.excelVo;


import com.midea.pam.common.util.BigDecimalUtils;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-12-9
 * @description 已发生成本-合并
 */
@Getter
@Setter
public class IncurredCostMergeExcelVO {

    @Excel(name = "序号", width = 5)
    private Integer number;

    @Excel(name = "类型", width = 20)
    private String type;

    @Excel(name = "WBS", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项", width = 15)
    private String activityCode;

    @Excel(name = "领退料单编号/采购合同编号/EC单号/罚扣编号", width = 25)
    private String billCode;

    //采购合同编号
    private String purchaseContractCode;

    //EC单号
    private String feeApplyCode;

    @Excel(name = "合同名称", width = 25)
    private String purchaseContractName;

    @Excel(name = "供应商名称/人员", width = 25)
    private String vendorName;

    //人员
    private String userMip;

    @Excel(name = "进度执行单号", width = 25)
    private String progressCode;

    @Excel(name = "物料PAM编码/时间", width = 25)
    private String pamCode;

    //时间
    private Date applyDate;

    @Excel(name = "物料ERP编码", width = 25)
    private String materialCode;

    @Excel(name = "物料描述/角色", width = 25)
    private String materielDescr;

    //角色(工时)
    private String level;

    //角色(点工)
    private String roleName;

    @Excel(name = "实际领退料数/审批通过的工时（h）/已对账工时金额（不含税）", width = 20)
    private BigDecimal actualAmount;

    //审批通过的工时（h）
    private BigDecimal workingHours;

    //已对账工时金额（不含税）
    private BigDecimal billMhAmount;

    @Excel(name = "成本单价/标准费率（d）/已对账费用（不含税）", width = 20)
    private BigDecimal actualCost;

    //标准费率（d）
    private BigDecimal costMoney;

    //已对账费用（含税）
    private BigDecimal billCostAmount;

    @Excel(name = "已发生成本", width = 20)
    private BigDecimal incurredCost;

    //领退料单*单位成本  |  审批通过的工时*标准费率/8
    private BigDecimal totalAmount;

    //累计进度执行金额（不含税）
    private BigDecimal budgetExecuteAmountTotal;

    //审核通过并已入账金额
    private BigDecimal amount;

    //点工采购合同已对账金额
    private BigDecimal surplusAmount;

    @Excel(name = "领退料单实际发料日期/合同进度执行时间/工时创建时间/费用入账日期/合同对账单对账时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date dataTime;

    @Excel(name = "合同进度创建时间/合同对账单创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date billCreateTime;

    //合同进度创建时间
    private Date contractCreateTime;

    private Long projectId;


    public BigDecimal getActualAmount() {
        return BigDecimalUtils.scale(actualAmount);
    }

    public BigDecimal getActualCost() {
        return BigDecimalUtils.scale(actualCost);
    }

    public BigDecimal getIncurredCost() {
        return BigDecimalUtils.scale(incurredCost);
    }

}
