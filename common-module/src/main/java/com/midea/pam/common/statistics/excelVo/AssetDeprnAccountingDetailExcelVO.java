package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;

@Getter
@Setter
public class AssetDeprnAccountingDetailExcelVO {

    @Excel(name = "序号")
    private Integer num;

    @Excel(name = "入账单号", width = 25)
    private String code;

    @Excel(name = "项目编号", width = 25)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "资产编号", width = 30)
    private String assetNumber;

    @Excel(name = "资产说明", width = 50)
    private String description;

    @Excel(name = "折旧期间", width = 20)
    private String periodName;

    @Excel(name = "折旧金额", width = 30)
    private BigDecimal deprnAmount;

    @Excel(name = "币种")
    private String currency;

    @Excel(name = "会计期间", width = 20)
    private String glPeriod;

    @Excel(name = "业务实体", width = 30)
    private String ouName;
}
