package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目人力成本明细")
public class ProjectCostHumanDetailRecord extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "执行ID")
    private Long executeId;

    @ApiModelProperty(value = "汇总ID")
    private Long summaryId;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "填报人名称")
    private String applyName;

    @ApiModelProperty(value = "填报人MIP")
    private String applyMip;

    @ApiModelProperty(value = "部门")
    private String applyOrg;

    @ApiModelProperty(value = "年度")
    private String year;

    @ApiModelProperty(value = "月底")
    private String month;

    @ApiModelProperty(value = "出勤日期")
    private Date applyDate;

    @ApiModelProperty(value = "填报工时(天)")
    private BigDecimal applyWorkingHours;

    @ApiModelProperty(value = "人天单价")
    private BigDecimal costMoney;

    @ApiModelProperty(value = "填报日期")
    private Date fillDate;

    @ApiModelProperty(value = "审核工时(天)")
    private BigDecimal actualWorkingHours;

    @ApiModelProperty(value = "审批日期")
    private Date approveTime;

    @ApiModelProperty(value = "工时成本")
    private BigDecimal laborCost;

    @ApiModelProperty(value = "币种")
    private String currencyName;

    @ApiModelProperty(value = "状态：1未提交、2审批中、3被驳回、4通过、5变更中、6变更驳回")
    private Integer status;

    @ApiModelProperty(value = "用户类型，1内部2外部")
    private String userType;

    @ApiModelProperty(value = "是否被删除标志")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "供应商编号")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    private static final long serialVersionUID = 1L;

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getSummaryId() {
        return summaryId;
    }

    public void setSummaryId(Long summaryId) {
        this.summaryId = summaryId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getApplyName() {
        return applyName;
    }

    public void setApplyName(String applyName) {
        this.applyName = applyName == null ? null : applyName.trim();
    }

    public String getApplyMip() {
        return applyMip;
    }

    public void setApplyMip(String applyMip) {
        this.applyMip = applyMip == null ? null : applyMip.trim();
    }

    public String getApplyOrg() {
        return applyOrg;
    }

    public void setApplyOrg(String applyOrg) {
        this.applyOrg = applyOrg == null ? null : applyOrg.trim();
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year == null ? null : year.trim();
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month == null ? null : month.trim();
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public BigDecimal getApplyWorkingHours() {
        return applyWorkingHours;
    }

    public void setApplyWorkingHours(BigDecimal applyWorkingHours) {
        this.applyWorkingHours = applyWorkingHours;
    }

    public BigDecimal getCostMoney() {
        return costMoney;
    }

    public void setCostMoney(BigDecimal costMoney) {
        this.costMoney = costMoney;
    }

    public Date getFillDate() {
        return fillDate;
    }

    public void setFillDate(Date fillDate) {
        this.fillDate = fillDate;
    }

    public BigDecimal getActualWorkingHours() {
        return actualWorkingHours;
    }

    public void setActualWorkingHours(BigDecimal actualWorkingHours) {
        this.actualWorkingHours = actualWorkingHours;
    }

    public Date getApproveTime() {
        return approveTime;
    }

    public void setApproveTime(Date approveTime) {
        this.approveTime = approveTime;
    }

    public BigDecimal getLaborCost() {
        return laborCost;
    }

    public void setLaborCost(BigDecimal laborCost) {
        this.laborCost = laborCost;
    }

    public String getCurrencyName() {
        return currencyName;
    }

    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName == null ? null : currencyName.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType == null ? null : userType.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode == null ? null : vendorCode.trim();
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName == null ? null : vendorName.trim();
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName == null ? null : roleName.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", executeId=").append(executeId);
        sb.append(", summaryId=").append(summaryId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", applyName=").append(applyName);
        sb.append(", applyMip=").append(applyMip);
        sb.append(", applyOrg=").append(applyOrg);
        sb.append(", year=").append(year);
        sb.append(", month=").append(month);
        sb.append(", applyDate=").append(applyDate);
        sb.append(", applyWorkingHours=").append(applyWorkingHours);
        sb.append(", costMoney=").append(costMoney);
        sb.append(", fillDate=").append(fillDate);
        sb.append(", actualWorkingHours=").append(actualWorkingHours);
        sb.append(", approveTime=").append(approveTime);
        sb.append(", laborCost=").append(laborCost);
        sb.append(", currencyName=").append(currencyName);
        sb.append(", status=").append(status);
        sb.append(", userType=").append(userType);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", vendorCode=").append(vendorCode);
        sb.append(", vendorName=").append(vendorName);
        sb.append(", roleName=").append(roleName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}