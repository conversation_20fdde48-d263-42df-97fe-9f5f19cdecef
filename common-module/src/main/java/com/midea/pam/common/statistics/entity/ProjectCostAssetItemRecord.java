package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目资产成本类型汇总")
public class ProjectCostAssetItemRecord extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "执行ID")
    private Long executeId;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "汇总ID")
    private Long summaryId;

    @ApiModelProperty(value = "资产编号")
    private String assetNumber;

    @ApiModelProperty(value = "资产说明")
    private String description;

    @ApiModelProperty(value = "启用日期")
    private Date datePlacedInService;

    @ApiModelProperty(value = "月数")
    private Integer lifeInMonths;

    @ApiModelProperty(value = "折旧期间")
    private String lastPeriodName;

    @ApiModelProperty(value = "成本")
    private BigDecimal cost;

    @ApiModelProperty(value = "累计折旧")
    private BigDecimal deprnReserve;

    @ApiModelProperty(value = "残值")
    private BigDecimal salvageValue;

    @ApiModelProperty(value = "是否被删除标志")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public Long getSummaryId() {
        return summaryId;
    }

    public void setSummaryId(Long summaryId) {
        this.summaryId = summaryId;
    }

    public String getAssetNumber() {
        return assetNumber;
    }

    public void setAssetNumber(String assetNumber) {
        this.assetNumber = assetNumber == null ? null : assetNumber.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public Date getDatePlacedInService() {
        return datePlacedInService;
    }

    public void setDatePlacedInService(Date datePlacedInService) {
        this.datePlacedInService = datePlacedInService;
    }

    public Integer getLifeInMonths() {
        return lifeInMonths;
    }

    public void setLifeInMonths(Integer lifeInMonths) {
        this.lifeInMonths = lifeInMonths;
    }

    public String getLastPeriodName() {
        return lastPeriodName;
    }

    public void setLastPeriodName(String lastPeriodName) {
        this.lastPeriodName = lastPeriodName == null ? null : lastPeriodName.trim();
    }

    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    public BigDecimal getDeprnReserve() {
        return deprnReserve;
    }

    public void setDeprnReserve(BigDecimal deprnReserve) {
        this.deprnReserve = deprnReserve;
    }

    public BigDecimal getSalvageValue() {
        return salvageValue;
    }

    public void setSalvageValue(BigDecimal salvageValue) {
        this.salvageValue = salvageValue;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", summaryId=").append(summaryId);
        sb.append(", assetNumber=").append(assetNumber);
        sb.append(", description=").append(description);
        sb.append(", datePlacedInService=").append(datePlacedInService);
        sb.append(", lifeInMonths=").append(lifeInMonths);
        sb.append(", lastPeriodName=").append(lastPeriodName);
        sb.append(", cost=").append(cost);
        sb.append(", deprnReserve=").append(deprnReserve);
        sb.append(", salvageValue=").append(salvageValue);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}