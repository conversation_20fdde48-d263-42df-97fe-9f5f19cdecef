package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class ReportWorkingHourAccountingExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportWorkingHourAccountingExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNull() {
            addCriterion("project_status is null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNotNull() {
            addCriterion("project_status is not null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusEqualTo(Integer value) {
            addCriterion("project_status =", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotEqualTo(Integer value) {
            addCriterion("project_status <>", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThan(Integer value) {
            addCriterion("project_status >", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("project_status >=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThan(Integer value) {
            addCriterion("project_status <", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThanOrEqualTo(Integer value) {
            addCriterion("project_status <=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIn(List<Integer> values) {
            addCriterion("project_status in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotIn(List<Integer> values) {
            addCriterion("project_status not in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusBetween(Integer value1, Integer value2) {
            addCriterion("project_status between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("project_status not between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeIsNull() {
            addCriterion("project_price_type is null");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeIsNotNull() {
            addCriterion("project_price_type is not null");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeEqualTo(Integer value) {
            addCriterion("project_price_type =", value, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeNotEqualTo(Integer value) {
            addCriterion("project_price_type <>", value, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeGreaterThan(Integer value) {
            addCriterion("project_price_type >", value, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("project_price_type >=", value, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeLessThan(Integer value) {
            addCriterion("project_price_type <", value, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeLessThanOrEqualTo(Integer value) {
            addCriterion("project_price_type <=", value, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeIn(List<Integer> values) {
            addCriterion("project_price_type in", values, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeNotIn(List<Integer> values) {
            addCriterion("project_price_type not in", values, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeBetween(Integer value1, Integer value2) {
            addCriterion("project_price_type between", value1, value2, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectPriceTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("project_price_type not between", value1, value2, "projectPriceType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdIsNull() {
            addCriterion("project_type_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdIsNotNull() {
            addCriterion("project_type_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdEqualTo(Long value) {
            addCriterion("project_type_id =", value, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdNotEqualTo(Long value) {
            addCriterion("project_type_id <>", value, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdGreaterThan(Long value) {
            addCriterion("project_type_id >", value, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_type_id >=", value, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdLessThan(Long value) {
            addCriterion("project_type_id <", value, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdLessThanOrEqualTo(Long value) {
            addCriterion("project_type_id <=", value, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdIn(List<Long> values) {
            addCriterion("project_type_id in", values, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdNotIn(List<Long> values) {
            addCriterion("project_type_id not in", values, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdBetween(Long value1, Long value2) {
            addCriterion("project_type_id between", value1, value2, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIdNotBetween(Long value1, Long value2) {
            addCriterion("project_type_id not between", value1, value2, "projectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameIsNull() {
            addCriterion("project_type_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameIsNotNull() {
            addCriterion("project_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameEqualTo(String value) {
            addCriterion("project_type_name =", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotEqualTo(String value) {
            addCriterion("project_type_name <>", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameGreaterThan(String value) {
            addCriterion("project_type_name >", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_type_name >=", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameLessThan(String value) {
            addCriterion("project_type_name <", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameLessThanOrEqualTo(String value) {
            addCriterion("project_type_name <=", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameLike(String value) {
            addCriterion("project_type_name like", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotLike(String value) {
            addCriterion("project_type_name not like", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameIn(List<String> values) {
            addCriterion("project_type_name in", values, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotIn(List<String> values) {
            addCriterion("project_type_name not in", values, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameBetween(String value1, String value2) {
            addCriterion("project_type_name between", value1, value2, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotBetween(String value1, String value2) {
            addCriterion("project_type_name not between", value1, value2, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNull() {
            addCriterion("department_id is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNotNull() {
            addCriterion("department_id is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdEqualTo(Long value) {
            addCriterion("department_id =", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotEqualTo(Long value) {
            addCriterion("department_id <>", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThan(Long value) {
            addCriterion("department_id >", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("department_id >=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThan(Long value) {
            addCriterion("department_id <", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThanOrEqualTo(Long value) {
            addCriterion("department_id <=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIn(List<Long> values) {
            addCriterion("department_id in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotIn(List<Long> values) {
            addCriterion("department_id not in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdBetween(Long value1, Long value2) {
            addCriterion("department_id between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotBetween(Long value1, Long value2) {
            addCriterion("department_id not between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIsNull() {
            addCriterion("department_name is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIsNotNull() {
            addCriterion("department_name is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameEqualTo(String value) {
            addCriterion("department_name =", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotEqualTo(String value) {
            addCriterion("department_name <>", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameGreaterThan(String value) {
            addCriterion("department_name >", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameGreaterThanOrEqualTo(String value) {
            addCriterion("department_name >=", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLessThan(String value) {
            addCriterion("department_name <", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLessThanOrEqualTo(String value) {
            addCriterion("department_name <=", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLike(String value) {
            addCriterion("department_name like", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotLike(String value) {
            addCriterion("department_name not like", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIn(List<String> values) {
            addCriterion("department_name in", values, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotIn(List<String> values) {
            addCriterion("department_name not in", values, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameBetween(String value1, String value2) {
            addCriterion("department_name between", value1, value2, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotBetween(String value1, String value2) {
            addCriterion("department_name not between", value1, value2, "departmentName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIdIsNull() {
            addCriterion("project_manager_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIdIsNotNull() {
            addCriterion("project_manager_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIdEqualTo(String value) {
            addCriterion("project_manager_id =", value, "projectManagerId");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIdNotEqualTo(String value) {
            addCriterion("project_manager_id <>", value, "projectManagerId");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIdGreaterThan(String value) {
            addCriterion("project_manager_id >", value, "projectManagerId");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIdGreaterThanOrEqualTo(String value) {
            addCriterion("project_manager_id >=", value, "projectManagerId");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIdLessThan(String value) {
            addCriterion("project_manager_id <", value, "projectManagerId");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIdLessThanOrEqualTo(String value) {
            addCriterion("project_manager_id <=", value, "projectManagerId");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIdLike(String value) {
            addCriterion("project_manager_id like", value, "projectManagerId");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIdNotLike(String value) {
            addCriterion("project_manager_id not like", value, "projectManagerId");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIdIn(List<String> values) {
            addCriterion("project_manager_id in", values, "projectManagerId");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIdNotIn(List<String> values) {
            addCriterion("project_manager_id not in", values, "projectManagerId");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIdBetween(String value1, String value2) {
            addCriterion("project_manager_id between", value1, value2, "projectManagerId");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIdNotBetween(String value1, String value2) {
            addCriterion("project_manager_id not between", value1, value2, "projectManagerId");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameIsNull() {
            addCriterion("project_manager_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameIsNotNull() {
            addCriterion("project_manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameEqualTo(String value) {
            addCriterion("project_manager_name =", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameNotEqualTo(String value) {
            addCriterion("project_manager_name <>", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameGreaterThan(String value) {
            addCriterion("project_manager_name >", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_manager_name >=", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameLessThan(String value) {
            addCriterion("project_manager_name <", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameLessThanOrEqualTo(String value) {
            addCriterion("project_manager_name <=", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameLike(String value) {
            addCriterion("project_manager_name like", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameNotLike(String value) {
            addCriterion("project_manager_name not like", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameIn(List<String> values) {
            addCriterion("project_manager_name in", values, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameNotIn(List<String> values) {
            addCriterion("project_manager_name not in", values, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameBetween(String value1, String value2) {
            addCriterion("project_manager_name between", value1, value2, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameNotBetween(String value1, String value2) {
            addCriterion("project_manager_name not between", value1, value2, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdIsNull() {
            addCriterion("apply_user_id is null");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdIsNotNull() {
            addCriterion("apply_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdEqualTo(Long value) {
            addCriterion("apply_user_id =", value, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdNotEqualTo(Long value) {
            addCriterion("apply_user_id <>", value, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdGreaterThan(Long value) {
            addCriterion("apply_user_id >", value, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("apply_user_id >=", value, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdLessThan(Long value) {
            addCriterion("apply_user_id <", value, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdLessThanOrEqualTo(Long value) {
            addCriterion("apply_user_id <=", value, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdIn(List<Long> values) {
            addCriterion("apply_user_id in", values, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdNotIn(List<Long> values) {
            addCriterion("apply_user_id not in", values, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdBetween(Long value1, Long value2) {
            addCriterion("apply_user_id between", value1, value2, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdNotBetween(Long value1, Long value2) {
            addCriterion("apply_user_id not between", value1, value2, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameIsNull() {
            addCriterion("apply_user_name is null");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameIsNotNull() {
            addCriterion("apply_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameEqualTo(String value) {
            addCriterion("apply_user_name =", value, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameNotEqualTo(String value) {
            addCriterion("apply_user_name <>", value, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameGreaterThan(String value) {
            addCriterion("apply_user_name >", value, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("apply_user_name >=", value, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameLessThan(String value) {
            addCriterion("apply_user_name <", value, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameLessThanOrEqualTo(String value) {
            addCriterion("apply_user_name <=", value, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameLike(String value) {
            addCriterion("apply_user_name like", value, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameNotLike(String value) {
            addCriterion("apply_user_name not like", value, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameIn(List<String> values) {
            addCriterion("apply_user_name in", values, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameNotIn(List<String> values) {
            addCriterion("apply_user_name not in", values, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameBetween(String value1, String value2) {
            addCriterion("apply_user_name between", value1, value2, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameNotBetween(String value1, String value2) {
            addCriterion("apply_user_name not between", value1, value2, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserMipIsNull() {
            addCriterion("apply_user_mip is null");
            return (Criteria) this;
        }

        public Criteria andApplyUserMipIsNotNull() {
            addCriterion("apply_user_mip is not null");
            return (Criteria) this;
        }

        public Criteria andApplyUserMipEqualTo(String value) {
            addCriterion("apply_user_mip =", value, "applyUserMip");
            return (Criteria) this;
        }

        public Criteria andApplyUserMipNotEqualTo(String value) {
            addCriterion("apply_user_mip <>", value, "applyUserMip");
            return (Criteria) this;
        }

        public Criteria andApplyUserMipGreaterThan(String value) {
            addCriterion("apply_user_mip >", value, "applyUserMip");
            return (Criteria) this;
        }

        public Criteria andApplyUserMipGreaterThanOrEqualTo(String value) {
            addCriterion("apply_user_mip >=", value, "applyUserMip");
            return (Criteria) this;
        }

        public Criteria andApplyUserMipLessThan(String value) {
            addCriterion("apply_user_mip <", value, "applyUserMip");
            return (Criteria) this;
        }

        public Criteria andApplyUserMipLessThanOrEqualTo(String value) {
            addCriterion("apply_user_mip <=", value, "applyUserMip");
            return (Criteria) this;
        }

        public Criteria andApplyUserMipLike(String value) {
            addCriterion("apply_user_mip like", value, "applyUserMip");
            return (Criteria) this;
        }

        public Criteria andApplyUserMipNotLike(String value) {
            addCriterion("apply_user_mip not like", value, "applyUserMip");
            return (Criteria) this;
        }

        public Criteria andApplyUserMipIn(List<String> values) {
            addCriterion("apply_user_mip in", values, "applyUserMip");
            return (Criteria) this;
        }

        public Criteria andApplyUserMipNotIn(List<String> values) {
            addCriterion("apply_user_mip not in", values, "applyUserMip");
            return (Criteria) this;
        }

        public Criteria andApplyUserMipBetween(String value1, String value2) {
            addCriterion("apply_user_mip between", value1, value2, "applyUserMip");
            return (Criteria) this;
        }

        public Criteria andApplyUserMipNotBetween(String value1, String value2) {
            addCriterion("apply_user_mip not between", value1, value2, "applyUserMip");
            return (Criteria) this;
        }

        public Criteria andApplyOrgIsNull() {
            addCriterion("apply_org is null");
            return (Criteria) this;
        }

        public Criteria andApplyOrgIsNotNull() {
            addCriterion("apply_org is not null");
            return (Criteria) this;
        }

        public Criteria andApplyOrgEqualTo(String value) {
            addCriterion("apply_org =", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNotEqualTo(String value) {
            addCriterion("apply_org <>", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgGreaterThan(String value) {
            addCriterion("apply_org >", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgGreaterThanOrEqualTo(String value) {
            addCriterion("apply_org >=", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgLessThan(String value) {
            addCriterion("apply_org <", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgLessThanOrEqualTo(String value) {
            addCriterion("apply_org <=", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgLike(String value) {
            addCriterion("apply_org like", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNotLike(String value) {
            addCriterion("apply_org not like", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgIn(List<String> values) {
            addCriterion("apply_org in", values, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNotIn(List<String> values) {
            addCriterion("apply_org not in", values, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgBetween(String value1, String value2) {
            addCriterion("apply_org between", value1, value2, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNotBetween(String value1, String value2) {
            addCriterion("apply_org not between", value1, value2, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNull() {
            addCriterion("apply_date is null");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNotNull() {
            addCriterion("apply_date is not null");
            return (Criteria) this;
        }

        public Criteria andApplyDateEqualTo(Date value) {
            addCriterionForJDBCDate("apply_date =", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("apply_date <>", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThan(Date value) {
            addCriterionForJDBCDate("apply_date >", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("apply_date >=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThan(Date value) {
            addCriterionForJDBCDate("apply_date <", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("apply_date <=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateIn(List<Date> values) {
            addCriterionForJDBCDate("apply_date in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("apply_date not in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("apply_date between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("apply_date not between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Date value) {
            addCriterionForJDBCDate("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Date value) {
            addCriterionForJDBCDate("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Date value) {
            addCriterionForJDBCDate("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Date> values) {
            addCriterionForJDBCDate("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursIsNull() {
            addCriterion("apply_working_hours is null");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursIsNotNull() {
            addCriterion("apply_working_hours is not null");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursEqualTo(BigDecimal value) {
            addCriterion("apply_working_hours =", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursNotEqualTo(BigDecimal value) {
            addCriterion("apply_working_hours <>", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursGreaterThan(BigDecimal value) {
            addCriterion("apply_working_hours >", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_working_hours >=", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursLessThan(BigDecimal value) {
            addCriterion("apply_working_hours <", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_working_hours <=", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursIn(List<BigDecimal> values) {
            addCriterion("apply_working_hours in", values, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursNotIn(List<BigDecimal> values) {
            addCriterion("apply_working_hours not in", values, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_working_hours between", value1, value2, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_working_hours not between", value1, value2, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveDateIsNull() {
            addCriterion("approve_date is null");
            return (Criteria) this;
        }

        public Criteria andApproveDateIsNotNull() {
            addCriterion("approve_date is not null");
            return (Criteria) this;
        }

        public Criteria andApproveDateEqualTo(Date value) {
            addCriterionForJDBCDate("approve_date =", value, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("approve_date <>", value, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateGreaterThan(Date value) {
            addCriterionForJDBCDate("approve_date >", value, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("approve_date >=", value, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateLessThan(Date value) {
            addCriterionForJDBCDate("approve_date <", value, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("approve_date <=", value, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateIn(List<Date> values) {
            addCriterionForJDBCDate("approve_date in", values, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("approve_date not in", values, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("approve_date between", value1, value2, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("approve_date not between", value1, value2, "approveDate");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursIsNull() {
            addCriterion("approve_working_hours is null");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursIsNotNull() {
            addCriterion("approve_working_hours is not null");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursEqualTo(BigDecimal value) {
            addCriterion("approve_working_hours =", value, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursNotEqualTo(BigDecimal value) {
            addCriterion("approve_working_hours <>", value, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursGreaterThan(BigDecimal value) {
            addCriterion("approve_working_hours >", value, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("approve_working_hours >=", value, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursLessThan(BigDecimal value) {
            addCriterion("approve_working_hours <", value, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("approve_working_hours <=", value, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursIn(List<BigDecimal> values) {
            addCriterion("approve_working_hours in", values, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursNotIn(List<BigDecimal> values) {
            addCriterion("approve_working_hours not in", values, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("approve_working_hours between", value1, value2, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApproveWorkingHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("approve_working_hours not between", value1, value2, "approveWorkingHours");
            return (Criteria) this;
        }

        public Criteria andPlanCostMoneyIsNull() {
            addCriterion("plan_cost_money is null");
            return (Criteria) this;
        }

        public Criteria andPlanCostMoneyIsNotNull() {
            addCriterion("plan_cost_money is not null");
            return (Criteria) this;
        }

        public Criteria andPlanCostMoneyEqualTo(BigDecimal value) {
            addCriterion("plan_cost_money =", value, "planCostMoney");
            return (Criteria) this;
        }

        public Criteria andPlanCostMoneyNotEqualTo(BigDecimal value) {
            addCriterion("plan_cost_money <>", value, "planCostMoney");
            return (Criteria) this;
        }

        public Criteria andPlanCostMoneyGreaterThan(BigDecimal value) {
            addCriterion("plan_cost_money >", value, "planCostMoney");
            return (Criteria) this;
        }

        public Criteria andPlanCostMoneyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("plan_cost_money >=", value, "planCostMoney");
            return (Criteria) this;
        }

        public Criteria andPlanCostMoneyLessThan(BigDecimal value) {
            addCriterion("plan_cost_money <", value, "planCostMoney");
            return (Criteria) this;
        }

        public Criteria andPlanCostMoneyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("plan_cost_money <=", value, "planCostMoney");
            return (Criteria) this;
        }

        public Criteria andPlanCostMoneyIn(List<BigDecimal> values) {
            addCriterion("plan_cost_money in", values, "planCostMoney");
            return (Criteria) this;
        }

        public Criteria andPlanCostMoneyNotIn(List<BigDecimal> values) {
            addCriterion("plan_cost_money not in", values, "planCostMoney");
            return (Criteria) this;
        }

        public Criteria andPlanCostMoneyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("plan_cost_money between", value1, value2, "planCostMoney");
            return (Criteria) this;
        }

        public Criteria andPlanCostMoneyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("plan_cost_money not between", value1, value2, "planCostMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyIsNull() {
            addCriterion("cost_money is null");
            return (Criteria) this;
        }

        public Criteria andCostMoneyIsNotNull() {
            addCriterion("cost_money is not null");
            return (Criteria) this;
        }

        public Criteria andCostMoneyEqualTo(BigDecimal value) {
            addCriterion("cost_money =", value, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyNotEqualTo(BigDecimal value) {
            addCriterion("cost_money <>", value, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyGreaterThan(BigDecimal value) {
            addCriterion("cost_money >", value, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cost_money >=", value, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyLessThan(BigDecimal value) {
            addCriterion("cost_money <", value, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cost_money <=", value, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyIn(List<BigDecimal> values) {
            addCriterion("cost_money in", values, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyNotIn(List<BigDecimal> values) {
            addCriterion("cost_money not in", values, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost_money between", value1, value2, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost_money not between", value1, value2, "costMoney");
            return (Criteria) this;
        }

        public Criteria andPlanCostTotalIsNull() {
            addCriterion("plan_cost_total is null");
            return (Criteria) this;
        }

        public Criteria andPlanCostTotalIsNotNull() {
            addCriterion("plan_cost_total is not null");
            return (Criteria) this;
        }

        public Criteria andPlanCostTotalEqualTo(BigDecimal value) {
            addCriterion("plan_cost_total =", value, "planCostTotal");
            return (Criteria) this;
        }

        public Criteria andPlanCostTotalNotEqualTo(BigDecimal value) {
            addCriterion("plan_cost_total <>", value, "planCostTotal");
            return (Criteria) this;
        }

        public Criteria andPlanCostTotalGreaterThan(BigDecimal value) {
            addCriterion("plan_cost_total >", value, "planCostTotal");
            return (Criteria) this;
        }

        public Criteria andPlanCostTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("plan_cost_total >=", value, "planCostTotal");
            return (Criteria) this;
        }

        public Criteria andPlanCostTotalLessThan(BigDecimal value) {
            addCriterion("plan_cost_total <", value, "planCostTotal");
            return (Criteria) this;
        }

        public Criteria andPlanCostTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("plan_cost_total <=", value, "planCostTotal");
            return (Criteria) this;
        }

        public Criteria andPlanCostTotalIn(List<BigDecimal> values) {
            addCriterion("plan_cost_total in", values, "planCostTotal");
            return (Criteria) this;
        }

        public Criteria andPlanCostTotalNotIn(List<BigDecimal> values) {
            addCriterion("plan_cost_total not in", values, "planCostTotal");
            return (Criteria) this;
        }

        public Criteria andPlanCostTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("plan_cost_total between", value1, value2, "planCostTotal");
            return (Criteria) this;
        }

        public Criteria andPlanCostTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("plan_cost_total not between", value1, value2, "planCostTotal");
            return (Criteria) this;
        }

        public Criteria andCostTotalIsNull() {
            addCriterion("cost_total is null");
            return (Criteria) this;
        }

        public Criteria andCostTotalIsNotNull() {
            addCriterion("cost_total is not null");
            return (Criteria) this;
        }

        public Criteria andCostTotalEqualTo(BigDecimal value) {
            addCriterion("cost_total =", value, "costTotal");
            return (Criteria) this;
        }

        public Criteria andCostTotalNotEqualTo(BigDecimal value) {
            addCriterion("cost_total <>", value, "costTotal");
            return (Criteria) this;
        }

        public Criteria andCostTotalGreaterThan(BigDecimal value) {
            addCriterion("cost_total >", value, "costTotal");
            return (Criteria) this;
        }

        public Criteria andCostTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cost_total >=", value, "costTotal");
            return (Criteria) this;
        }

        public Criteria andCostTotalLessThan(BigDecimal value) {
            addCriterion("cost_total <", value, "costTotal");
            return (Criteria) this;
        }

        public Criteria andCostTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cost_total <=", value, "costTotal");
            return (Criteria) this;
        }

        public Criteria andCostTotalIn(List<BigDecimal> values) {
            addCriterion("cost_total in", values, "costTotal");
            return (Criteria) this;
        }

        public Criteria andCostTotalNotIn(List<BigDecimal> values) {
            addCriterion("cost_total not in", values, "costTotal");
            return (Criteria) this;
        }

        public Criteria andCostTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost_total between", value1, value2, "costTotal");
            return (Criteria) this;
        }

        public Criteria andCostTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost_total not between", value1, value2, "costTotal");
            return (Criteria) this;
        }

        public Criteria andAccountingGlPeriodIsNull() {
            addCriterion("accounting_gl_period is null");
            return (Criteria) this;
        }

        public Criteria andAccountingGlPeriodIsNotNull() {
            addCriterion("accounting_gl_period is not null");
            return (Criteria) this;
        }

        public Criteria andAccountingGlPeriodEqualTo(String value) {
            addCriterion("accounting_gl_period =", value, "accountingGlPeriod");
            return (Criteria) this;
        }

        public Criteria andAccountingGlPeriodNotEqualTo(String value) {
            addCriterion("accounting_gl_period <>", value, "accountingGlPeriod");
            return (Criteria) this;
        }

        public Criteria andAccountingGlPeriodGreaterThan(String value) {
            addCriterion("accounting_gl_period >", value, "accountingGlPeriod");
            return (Criteria) this;
        }

        public Criteria andAccountingGlPeriodGreaterThanOrEqualTo(String value) {
            addCriterion("accounting_gl_period >=", value, "accountingGlPeriod");
            return (Criteria) this;
        }

        public Criteria andAccountingGlPeriodLessThan(String value) {
            addCriterion("accounting_gl_period <", value, "accountingGlPeriod");
            return (Criteria) this;
        }

        public Criteria andAccountingGlPeriodLessThanOrEqualTo(String value) {
            addCriterion("accounting_gl_period <=", value, "accountingGlPeriod");
            return (Criteria) this;
        }

        public Criteria andAccountingGlPeriodLike(String value) {
            addCriterion("accounting_gl_period like", value, "accountingGlPeriod");
            return (Criteria) this;
        }

        public Criteria andAccountingGlPeriodNotLike(String value) {
            addCriterion("accounting_gl_period not like", value, "accountingGlPeriod");
            return (Criteria) this;
        }

        public Criteria andAccountingGlPeriodIn(List<String> values) {
            addCriterion("accounting_gl_period in", values, "accountingGlPeriod");
            return (Criteria) this;
        }

        public Criteria andAccountingGlPeriodNotIn(List<String> values) {
            addCriterion("accounting_gl_period not in", values, "accountingGlPeriod");
            return (Criteria) this;
        }

        public Criteria andAccountingGlPeriodBetween(String value1, String value2) {
            addCriterion("accounting_gl_period between", value1, value2, "accountingGlPeriod");
            return (Criteria) this;
        }

        public Criteria andAccountingGlPeriodNotBetween(String value1, String value2) {
            addCriterion("accounting_gl_period not between", value1, value2, "accountingGlPeriod");
            return (Criteria) this;
        }

        public Criteria andCarryoverWorkingHoursIsNull() {
            addCriterion("carryover_working_hours is null");
            return (Criteria) this;
        }

        public Criteria andCarryoverWorkingHoursIsNotNull() {
            addCriterion("carryover_working_hours is not null");
            return (Criteria) this;
        }

        public Criteria andCarryoverWorkingHoursEqualTo(BigDecimal value) {
            addCriterion("carryover_working_hours =", value, "carryoverWorkingHours");
            return (Criteria) this;
        }

        public Criteria andCarryoverWorkingHoursNotEqualTo(BigDecimal value) {
            addCriterion("carryover_working_hours <>", value, "carryoverWorkingHours");
            return (Criteria) this;
        }

        public Criteria andCarryoverWorkingHoursGreaterThan(BigDecimal value) {
            addCriterion("carryover_working_hours >", value, "carryoverWorkingHours");
            return (Criteria) this;
        }

        public Criteria andCarryoverWorkingHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("carryover_working_hours >=", value, "carryoverWorkingHours");
            return (Criteria) this;
        }

        public Criteria andCarryoverWorkingHoursLessThan(BigDecimal value) {
            addCriterion("carryover_working_hours <", value, "carryoverWorkingHours");
            return (Criteria) this;
        }

        public Criteria andCarryoverWorkingHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("carryover_working_hours <=", value, "carryoverWorkingHours");
            return (Criteria) this;
        }

        public Criteria andCarryoverWorkingHoursIn(List<BigDecimal> values) {
            addCriterion("carryover_working_hours in", values, "carryoverWorkingHours");
            return (Criteria) this;
        }

        public Criteria andCarryoverWorkingHoursNotIn(List<BigDecimal> values) {
            addCriterion("carryover_working_hours not in", values, "carryoverWorkingHours");
            return (Criteria) this;
        }

        public Criteria andCarryoverWorkingHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carryover_working_hours between", value1, value2, "carryoverWorkingHours");
            return (Criteria) this;
        }

        public Criteria andCarryoverWorkingHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carryover_working_hours not between", value1, value2, "carryoverWorkingHours");
            return (Criteria) this;
        }

        public Criteria andCarryoverLaborCostIsNull() {
            addCriterion("carryover_labor_cost is null");
            return (Criteria) this;
        }

        public Criteria andCarryoverLaborCostIsNotNull() {
            addCriterion("carryover_labor_cost is not null");
            return (Criteria) this;
        }

        public Criteria andCarryoverLaborCostEqualTo(BigDecimal value) {
            addCriterion("carryover_labor_cost =", value, "carryoverLaborCost");
            return (Criteria) this;
        }

        public Criteria andCarryoverLaborCostNotEqualTo(BigDecimal value) {
            addCriterion("carryover_labor_cost <>", value, "carryoverLaborCost");
            return (Criteria) this;
        }

        public Criteria andCarryoverLaborCostGreaterThan(BigDecimal value) {
            addCriterion("carryover_labor_cost >", value, "carryoverLaborCost");
            return (Criteria) this;
        }

        public Criteria andCarryoverLaborCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("carryover_labor_cost >=", value, "carryoverLaborCost");
            return (Criteria) this;
        }

        public Criteria andCarryoverLaborCostLessThan(BigDecimal value) {
            addCriterion("carryover_labor_cost <", value, "carryoverLaborCost");
            return (Criteria) this;
        }

        public Criteria andCarryoverLaborCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("carryover_labor_cost <=", value, "carryoverLaborCost");
            return (Criteria) this;
        }

        public Criteria andCarryoverLaborCostIn(List<BigDecimal> values) {
            addCriterion("carryover_labor_cost in", values, "carryoverLaborCost");
            return (Criteria) this;
        }

        public Criteria andCarryoverLaborCostNotIn(List<BigDecimal> values) {
            addCriterion("carryover_labor_cost not in", values, "carryoverLaborCost");
            return (Criteria) this;
        }

        public Criteria andCarryoverLaborCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carryover_labor_cost between", value1, value2, "carryoverLaborCost");
            return (Criteria) this;
        }

        public Criteria andCarryoverLaborCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carryover_labor_cost not between", value1, value2, "carryoverLaborCost");
            return (Criteria) this;
        }

        public Criteria andCarryoverGlPeriodIsNull() {
            addCriterion("carryover_gl_period is null");
            return (Criteria) this;
        }

        public Criteria andCarryoverGlPeriodIsNotNull() {
            addCriterion("carryover_gl_period is not null");
            return (Criteria) this;
        }

        public Criteria andCarryoverGlPeriodEqualTo(String value) {
            addCriterion("carryover_gl_period =", value, "carryoverGlPeriod");
            return (Criteria) this;
        }

        public Criteria andCarryoverGlPeriodNotEqualTo(String value) {
            addCriterion("carryover_gl_period <>", value, "carryoverGlPeriod");
            return (Criteria) this;
        }

        public Criteria andCarryoverGlPeriodGreaterThan(String value) {
            addCriterion("carryover_gl_period >", value, "carryoverGlPeriod");
            return (Criteria) this;
        }

        public Criteria andCarryoverGlPeriodGreaterThanOrEqualTo(String value) {
            addCriterion("carryover_gl_period >=", value, "carryoverGlPeriod");
            return (Criteria) this;
        }

        public Criteria andCarryoverGlPeriodLessThan(String value) {
            addCriterion("carryover_gl_period <", value, "carryoverGlPeriod");
            return (Criteria) this;
        }

        public Criteria andCarryoverGlPeriodLessThanOrEqualTo(String value) {
            addCriterion("carryover_gl_period <=", value, "carryoverGlPeriod");
            return (Criteria) this;
        }

        public Criteria andCarryoverGlPeriodLike(String value) {
            addCriterion("carryover_gl_period like", value, "carryoverGlPeriod");
            return (Criteria) this;
        }

        public Criteria andCarryoverGlPeriodNotLike(String value) {
            addCriterion("carryover_gl_period not like", value, "carryoverGlPeriod");
            return (Criteria) this;
        }

        public Criteria andCarryoverGlPeriodIn(List<String> values) {
            addCriterion("carryover_gl_period in", values, "carryoverGlPeriod");
            return (Criteria) this;
        }

        public Criteria andCarryoverGlPeriodNotIn(List<String> values) {
            addCriterion("carryover_gl_period not in", values, "carryoverGlPeriod");
            return (Criteria) this;
        }

        public Criteria andCarryoverGlPeriodBetween(String value1, String value2) {
            addCriterion("carryover_gl_period between", value1, value2, "carryoverGlPeriod");
            return (Criteria) this;
        }

        public Criteria andCarryoverGlPeriodNotBetween(String value1, String value2) {
            addCriterion("carryover_gl_period not between", value1, value2, "carryoverGlPeriod");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursIsNull() {
            addCriterion("ihr_attend_hours is null");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursIsNotNull() {
            addCriterion("ihr_attend_hours is not null");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursEqualTo(BigDecimal value) {
            addCriterion("ihr_attend_hours =", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursNotEqualTo(BigDecimal value) {
            addCriterion("ihr_attend_hours <>", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursGreaterThan(BigDecimal value) {
            addCriterion("ihr_attend_hours >", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ihr_attend_hours >=", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursLessThan(BigDecimal value) {
            addCriterion("ihr_attend_hours <", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ihr_attend_hours <=", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursIn(List<BigDecimal> values) {
            addCriterion("ihr_attend_hours in", values, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursNotIn(List<BigDecimal> values) {
            addCriterion("ihr_attend_hours not in", values, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ihr_attend_hours between", value1, value2, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ihr_attend_hours not between", value1, value2, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursIsNull() {
            addCriterion("rdm_working_hours is null");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursIsNotNull() {
            addCriterion("rdm_working_hours is not null");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursEqualTo(BigDecimal value) {
            addCriterion("rdm_working_hours =", value, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursNotEqualTo(BigDecimal value) {
            addCriterion("rdm_working_hours <>", value, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursGreaterThan(BigDecimal value) {
            addCriterion("rdm_working_hours >", value, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("rdm_working_hours >=", value, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursLessThan(BigDecimal value) {
            addCriterion("rdm_working_hours <", value, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("rdm_working_hours <=", value, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursIn(List<BigDecimal> values) {
            addCriterion("rdm_working_hours in", values, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursNotIn(List<BigDecimal> values) {
            addCriterion("rdm_working_hours not in", values, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("rdm_working_hours between", value1, value2, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmWorkingHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("rdm_working_hours not between", value1, value2, "rdmWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRdmCostMoneyIsNull() {
            addCriterion("rdm_cost_money is null");
            return (Criteria) this;
        }

        public Criteria andRdmCostMoneyIsNotNull() {
            addCriterion("rdm_cost_money is not null");
            return (Criteria) this;
        }

        public Criteria andRdmCostMoneyEqualTo(BigDecimal value) {
            addCriterion("rdm_cost_money =", value, "rdmCostMoney");
            return (Criteria) this;
        }

        public Criteria andRdmCostMoneyNotEqualTo(BigDecimal value) {
            addCriterion("rdm_cost_money <>", value, "rdmCostMoney");
            return (Criteria) this;
        }

        public Criteria andRdmCostMoneyGreaterThan(BigDecimal value) {
            addCriterion("rdm_cost_money >", value, "rdmCostMoney");
            return (Criteria) this;
        }

        public Criteria andRdmCostMoneyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("rdm_cost_money >=", value, "rdmCostMoney");
            return (Criteria) this;
        }

        public Criteria andRdmCostMoneyLessThan(BigDecimal value) {
            addCriterion("rdm_cost_money <", value, "rdmCostMoney");
            return (Criteria) this;
        }

        public Criteria andRdmCostMoneyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("rdm_cost_money <=", value, "rdmCostMoney");
            return (Criteria) this;
        }

        public Criteria andRdmCostMoneyIn(List<BigDecimal> values) {
            addCriterion("rdm_cost_money in", values, "rdmCostMoney");
            return (Criteria) this;
        }

        public Criteria andRdmCostMoneyNotIn(List<BigDecimal> values) {
            addCriterion("rdm_cost_money not in", values, "rdmCostMoney");
            return (Criteria) this;
        }

        public Criteria andRdmCostMoneyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("rdm_cost_money between", value1, value2, "rdmCostMoney");
            return (Criteria) this;
        }

        public Criteria andRdmCostMoneyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("rdm_cost_money not between", value1, value2, "rdmCostMoney");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Long value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Long value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Long value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Long value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Long> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Long> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Long value1, Long value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNull() {
            addCriterion("unit_name is null");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNotNull() {
            addCriterion("unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andUnitNameEqualTo(String value) {
            addCriterion("unit_name =", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotEqualTo(String value) {
            addCriterion("unit_name <>", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThan(String value) {
            addCriterion("unit_name >", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("unit_name >=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThan(String value) {
            addCriterion("unit_name <", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThanOrEqualTo(String value) {
            addCriterion("unit_name <=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLike(String value) {
            addCriterion("unit_name like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotLike(String value) {
            addCriterion("unit_name not like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIn(List<String> values) {
            addCriterion("unit_name in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotIn(List<String> values) {
            addCriterion("unit_name not in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameBetween(String value1, String value2) {
            addCriterion("unit_name between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotBetween(String value1, String value2) {
            addCriterion("unit_name not between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNull() {
            addCriterion("ou_name is null");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNotNull() {
            addCriterion("ou_name is not null");
            return (Criteria) this;
        }

        public Criteria andOuNameEqualTo(String value) {
            addCriterion("ou_name =", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotEqualTo(String value) {
            addCriterion("ou_name <>", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThan(String value) {
            addCriterion("ou_name >", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThanOrEqualTo(String value) {
            addCriterion("ou_name >=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThan(String value) {
            addCriterion("ou_name <", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThanOrEqualTo(String value) {
            addCriterion("ou_name <=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLike(String value) {
            addCriterion("ou_name like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotLike(String value) {
            addCriterion("ou_name not like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameIn(List<String> values) {
            addCriterion("ou_name in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotIn(List<String> values) {
            addCriterion("ou_name not in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameBetween(String value1, String value2) {
            addCriterion("ou_name between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotBetween(String value1, String value2) {
            addCriterion("ou_name not between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}