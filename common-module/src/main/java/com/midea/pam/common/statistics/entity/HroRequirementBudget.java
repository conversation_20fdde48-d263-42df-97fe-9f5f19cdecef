package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "")
public class HroRequirementBudget extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "人力点工需求id")
    private Long hroRequirementId;

    @ApiModelProperty(value = "人力点工需求单据编号")
    private String requirementCode;

    @ApiModelProperty(value = "人力点工角色名称")
    private String roleName;

    @ApiModelProperty(value = "需求发布单据情况")
    private String receiptsType;

    @ApiModelProperty(value = "需求预算")
    private BigDecimal budgetOccupiedAmount;

    @ApiModelProperty(value = "累计采购合同占用金额")
    private BigDecimal downAmount;

    @ApiModelProperty(value = "当前人力点工需求预算金额")
    private BigDecimal remainingCostAmount;

    @ApiModelProperty(value = "类型 0-需求预算,1-审批中")
    private Integer rowRecordType;

    @ApiModelProperty(value = "wbs编码")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "该需求业务生成时间")
    private Date dataTime;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public Long getHroRequirementId() {
        return hroRequirementId;
    }

    public void setHroRequirementId(Long hroRequirementId) {
        this.hroRequirementId = hroRequirementId;
    }

    public String getRequirementCode() {
        return requirementCode;
    }

    public void setRequirementCode(String requirementCode) {
        this.requirementCode = requirementCode == null ? null : requirementCode.trim();
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName == null ? null : roleName.trim();
    }

    public String getReceiptsType() {
        return receiptsType;
    }

    public void setReceiptsType(String receiptsType) {
        this.receiptsType = receiptsType == null ? null : receiptsType.trim();
    }

    public BigDecimal getBudgetOccupiedAmount() {
        return budgetOccupiedAmount;
    }

    public void setBudgetOccupiedAmount(BigDecimal budgetOccupiedAmount) {
        this.budgetOccupiedAmount = budgetOccupiedAmount;
    }

    public BigDecimal getDownAmount() {
        return downAmount;
    }

    public void setDownAmount(BigDecimal downAmount) {
        this.downAmount = downAmount;
    }

    public BigDecimal getRemainingCostAmount() {
        return remainingCostAmount;
    }

    public void setRemainingCostAmount(BigDecimal remainingCostAmount) {
        this.remainingCostAmount = remainingCostAmount;
    }

    public Integer getRowRecordType() {
        return rowRecordType;
    }

    public void setRowRecordType(Integer rowRecordType) {
        this.rowRecordType = rowRecordType;
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public Date getDataTime() {
        return dataTime;
    }

    public void setDataTime(Date dataTime) {
        this.dataTime = dataTime;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", hroRequirementId=").append(hroRequirementId);
        sb.append(", requirementCode=").append(requirementCode);
        sb.append(", roleName=").append(roleName);
        sb.append(", receiptsType=").append(receiptsType);
        sb.append(", budgetOccupiedAmount=").append(budgetOccupiedAmount);
        sb.append(", downAmount=").append(downAmount);
        sb.append(", remainingCostAmount=").append(remainingCostAmount);
        sb.append(", rowRecordType=").append(rowRecordType);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", dataTime=").append(dataTime);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}