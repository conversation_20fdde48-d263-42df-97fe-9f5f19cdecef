package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.ReportGroupGrantUser;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/20
 * @description 报表组授权用户
 */
public class ReportGroupGrantUserDTO extends ReportGroupGrantUser {

    /**
     * 用户MIP账号
     */
    private String userMip;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 部门
     */
    private String orgName;

    /**
     * 报表组编号
     */
    private String reportGroupCode;

    /**
     * 报表组名称
     */
    private String reportGroupName;

    /**
     * 报表组描述
     */
    private String reportGroupRemark;

    /**
     * 报表组权限
     */
    private String reportGroupPermission;

    /**
     * 报表组ID，多个用,隔开
     */
    private String reportGroupIdsStr;

    private List<ReportGroupGrantUserDTO> reportGroupGrantUsers;

    public String getUserMip() {
        return userMip;
    }

    public void setUserMip(String userMip) {
        this.userMip = userMip;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getReportGroupCode() {
        return reportGroupCode;
    }

    public void setReportGroupCode(String reportGroupCode) {
        this.reportGroupCode = reportGroupCode;
    }

    public String getReportGroupName() {
        return reportGroupName;
    }

    public void setReportGroupName(String reportGroupName) {
        this.reportGroupName = reportGroupName;
    }

    public String getReportGroupRemark() {
        return reportGroupRemark;
    }

    public void setReportGroupRemark(String reportGroupRemark) {
        this.reportGroupRemark = reportGroupRemark;
    }

    public String getReportGroupPermission() {
        return reportGroupPermission;
    }

    public void setReportGroupPermission(String reportGroupPermission) {
        this.reportGroupPermission = reportGroupPermission;
    }

    public String getReportGroupIdsStr() {
        return reportGroupIdsStr;
    }

    public void setReportGroupIdsStr(String reportGroupIdsStr) {
        this.reportGroupIdsStr = reportGroupIdsStr;
    }

    public List<ReportGroupGrantUserDTO> getReportGroupGrantUsers() {
        return reportGroupGrantUsers;
    }

    public void setReportGroupGrantUsers(List<ReportGroupGrantUserDTO> reportGroupGrantUsers) {
        this.reportGroupGrantUsers = reportGroupGrantUsers;
    }
}
