package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class WorkingHourIhrAttendExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer num;

    @Excel(name = "填报人姓名", width = 20)
    private String userMipName;

    @Excel(name = "填报人MIP", width = 20)
    private String userMip;

    @Excel(name = "填报人部门", width = 40)
    private String applyOrg;

    @Excel(name = "考勤日期", width = 20, format = "yyyy-MM-dd")
    private Date applyDate;

    @Excel(name = "实际考勤工时", width = 20)
    private BigDecimal ihrAttendHours;

    @Excel(name = "填报项目工时（已审工时数）", width = 30)
    private BigDecimal actualWorkingHours;

    @Excel(name = "待审工时数", width = 20)
    private BigDecimal pendingWorkingHours;

    @Excel(name = "差异", width = 10)
    private BigDecimal hoursDifferent;

}