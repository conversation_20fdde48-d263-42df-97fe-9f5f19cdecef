package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ReportProjectNoCarryoverCost extends LongIdEntity implements Serializable {
    private Long id;

    private Long reportId;

    private Long executeId;

    private String projectCode;

    private String projectName;

    private String projectManager;

    private Integer projectStatus;

    private String projectType;

    private String customerCode;

    private String customerName;

    private String unitName;

    private String code;

    private String name;

    private BigDecimal materialActualCostCollect;

    private BigDecimal materialActualCostCarryover;

    private BigDecimal materialActualCostNoCarryover;

    private BigDecimal materialOutsourceCostCollect;

    private BigDecimal materialOutsourceCostCarryover;

    private BigDecimal materialOutsourceCostNoCarryover;

    private BigDecimal materialDifferenceCostCollect;

    private BigDecimal materialDifferenceCostCarryover;

    private BigDecimal materialDifferenceCostNoCarryover;

    private BigDecimal innerLaborCostCollect;

    private BigDecimal innerLaborCostCarryover;

    private BigDecimal innerLaborCostNoAccount;

    private BigDecimal innerLaborCostNoCarryover;

    private BigDecimal feeCostCollect;

    private BigDecimal feeCostCarryover;

    private BigDecimal feeCostNoCarryover;

    private BigDecimal totalCostNoCarryover;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public Integer getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(Integer projectStatus) {
        this.projectStatus = projectStatus;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType == null ? null : projectType.trim();
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode == null ? null : customerCode.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public BigDecimal getMaterialActualCostCollect() {
        return materialActualCostCollect;
    }

    public void setMaterialActualCostCollect(BigDecimal materialActualCostCollect) {
        this.materialActualCostCollect = materialActualCostCollect;
    }

    public BigDecimal getMaterialActualCostCarryover() {
        return materialActualCostCarryover;
    }

    public void setMaterialActualCostCarryover(BigDecimal materialActualCostCarryover) {
        this.materialActualCostCarryover = materialActualCostCarryover;
    }

    public BigDecimal getMaterialActualCostNoCarryover() {
        return materialActualCostNoCarryover;
    }

    public void setMaterialActualCostNoCarryover(BigDecimal materialActualCostNoCarryover) {
        this.materialActualCostNoCarryover = materialActualCostNoCarryover;
    }

    public BigDecimal getMaterialOutsourceCostCollect() {
        return materialOutsourceCostCollect;
    }

    public void setMaterialOutsourceCostCollect(BigDecimal materialOutsourceCostCollect) {
        this.materialOutsourceCostCollect = materialOutsourceCostCollect;
    }

    public BigDecimal getMaterialOutsourceCostCarryover() {
        return materialOutsourceCostCarryover;
    }

    public void setMaterialOutsourceCostCarryover(BigDecimal materialOutsourceCostCarryover) {
        this.materialOutsourceCostCarryover = materialOutsourceCostCarryover;
    }

    public BigDecimal getMaterialOutsourceCostNoCarryover() {
        return materialOutsourceCostNoCarryover;
    }

    public void setMaterialOutsourceCostNoCarryover(BigDecimal materialOutsourceCostNoCarryover) {
        this.materialOutsourceCostNoCarryover = materialOutsourceCostNoCarryover;
    }

    public BigDecimal getMaterialDifferenceCostCollect() {
        return materialDifferenceCostCollect;
    }

    public void setMaterialDifferenceCostCollect(BigDecimal materialDifferenceCostCollect) {
        this.materialDifferenceCostCollect = materialDifferenceCostCollect;
    }

    public BigDecimal getMaterialDifferenceCostCarryover() {
        return materialDifferenceCostCarryover;
    }

    public void setMaterialDifferenceCostCarryover(BigDecimal materialDifferenceCostCarryover) {
        this.materialDifferenceCostCarryover = materialDifferenceCostCarryover;
    }

    public BigDecimal getMaterialDifferenceCostNoCarryover() {
        return materialDifferenceCostNoCarryover;
    }

    public void setMaterialDifferenceCostNoCarryover(BigDecimal materialDifferenceCostNoCarryover) {
        this.materialDifferenceCostNoCarryover = materialDifferenceCostNoCarryover;
    }

    public BigDecimal getInnerLaborCostCollect() {
        return innerLaborCostCollect;
    }

    public void setInnerLaborCostCollect(BigDecimal innerLaborCostCollect) {
        this.innerLaborCostCollect = innerLaborCostCollect;
    }

    public BigDecimal getInnerLaborCostCarryover() {
        return innerLaborCostCarryover;
    }

    public void setInnerLaborCostCarryover(BigDecimal innerLaborCostCarryover) {
        this.innerLaborCostCarryover = innerLaborCostCarryover;
    }

    public BigDecimal getInnerLaborCostNoAccount() {
        return innerLaborCostNoAccount;
    }

    public void setInnerLaborCostNoAccount(BigDecimal innerLaborCostNoAccount) {
        this.innerLaborCostNoAccount = innerLaborCostNoAccount;
    }

    public BigDecimal getInnerLaborCostNoCarryover() {
        return innerLaborCostNoCarryover;
    }

    public void setInnerLaborCostNoCarryover(BigDecimal innerLaborCostNoCarryover) {
        this.innerLaborCostNoCarryover = innerLaborCostNoCarryover;
    }

    public BigDecimal getFeeCostCollect() {
        return feeCostCollect;
    }

    public void setFeeCostCollect(BigDecimal feeCostCollect) {
        this.feeCostCollect = feeCostCollect;
    }

    public BigDecimal getFeeCostCarryover() {
        return feeCostCarryover;
    }

    public void setFeeCostCarryover(BigDecimal feeCostCarryover) {
        this.feeCostCarryover = feeCostCarryover;
    }

    public BigDecimal getFeeCostNoCarryover() {
        return feeCostNoCarryover;
    }

    public void setFeeCostNoCarryover(BigDecimal feeCostNoCarryover) {
        this.feeCostNoCarryover = feeCostNoCarryover;
    }

    public BigDecimal getTotalCostNoCarryover() {
        return totalCostNoCarryover;
    }

    public void setTotalCostNoCarryover(BigDecimal totalCostNoCarryover) {
        this.totalCostNoCarryover = totalCostNoCarryover;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", projectStatus=").append(projectStatus);
        sb.append(", projectType=").append(projectType);
        sb.append(", customerCode=").append(customerCode);
        sb.append(", customerName=").append(customerName);
        sb.append(", unitName=").append(unitName);
        sb.append(", code=").append(code);
        sb.append(", name=").append(name);
        sb.append(", materialActualCostCollect=").append(materialActualCostCollect);
        sb.append(", materialActualCostCarryover=").append(materialActualCostCarryover);
        sb.append(", materialActualCostNoCarryover=").append(materialActualCostNoCarryover);
        sb.append(", materialOutsourceCostCollect=").append(materialOutsourceCostCollect);
        sb.append(", materialOutsourceCostCarryover=").append(materialOutsourceCostCarryover);
        sb.append(", materialOutsourceCostNoCarryover=").append(materialOutsourceCostNoCarryover);
        sb.append(", materialDifferenceCostCollect=").append(materialDifferenceCostCollect);
        sb.append(", materialDifferenceCostCarryover=").append(materialDifferenceCostCarryover);
        sb.append(", materialDifferenceCostNoCarryover=").append(materialDifferenceCostNoCarryover);
        sb.append(", innerLaborCostCollect=").append(innerLaborCostCollect);
        sb.append(", innerLaborCostCarryover=").append(innerLaborCostCarryover);
        sb.append(", innerLaborCostNoAccount=").append(innerLaborCostNoAccount);
        sb.append(", innerLaborCostNoCarryover=").append(innerLaborCostNoCarryover);
        sb.append(", feeCostCollect=").append(feeCostCollect);
        sb.append(", feeCostCarryover=").append(feeCostCarryover);
        sb.append(", feeCostNoCarryover=").append(feeCostNoCarryover);
        sb.append(", totalCostNoCarryover=").append(totalCostNoCarryover);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}