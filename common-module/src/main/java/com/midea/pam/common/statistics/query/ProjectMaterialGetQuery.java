package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel(value = "ProjectMaterialGetQuery", description = "项目物料领料进度表")
public class ProjectMaterialGetQuery {

    private Long id;

    @ApiModelProperty(value = "项目状态，用于传参")
    private List<Integer> statuses;

    @ApiModelProperty(value = "业务分类：业务部门ID列表，不为空时，只查业务部门属于此列表中的项目")
    private List<Long> unitIds;

    @ApiModelProperty(value = "项目code或名称")
    private String codeOrName;

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    @ApiModelProperty(value = "报表ID")
    private Long reportId;

    @ApiModelProperty(value = "业务实体id列表")
    private List<Long> ouIdList;

    @ApiModelProperty(value = "项目经理")
    private String managerName;

    @ApiModelProperty(value = "项目经理")
    private Long managerId;

    private Date expireDate;

    private Long personal;

    private Long companyId;

    private Long createBy;

    @ApiModelProperty(value = "项目类型，不为空时，只查项目类型属于此列表中的项目")
    private List<Long> projectTypes;

    @Override
    public String toString() {
        return "ProjectMaterialGetQuery{" +
                "id=" + id +
                ", statuses=" + statuses +
                ", unitIds=" + unitIds +
                ", codeOrName='" + codeOrName + '\'' +
                ", executeId=" + executeId +
                ", reportId=" + reportId +
                ", ouIdList=" + ouIdList +
                ", managerName='" + managerName + '\'' +
                ", managerId=" + managerId +
                ", expireDate=" + expireDate +
                ", personal=" + personal +
                ", companyId=" + companyId +
                ", createBy=" + createBy +
                ", projectTypes=" + projectTypes +
                '}';
    }
}
