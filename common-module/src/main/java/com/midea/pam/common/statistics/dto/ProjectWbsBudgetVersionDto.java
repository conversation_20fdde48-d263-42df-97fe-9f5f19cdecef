package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersion;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;


@Getter
@Setter
public class ProjectWbsBudgetVersionDto extends ProjectWbsBudgetVersion {

    @ApiModelProperty(value = "明细")
    List<Map<String, Object>> mapList;

}