package com.midea.pam.common.statistics.excelVo;

import com.midea.pam.common.util.DateUtils;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 **/
public class ReportProjectProfitConfirmedCostExcelVO {
    // 已结转成本
    @Excel(name = "物料成本",width = 20)
    private BigDecimal confirmedCostHardware;

    @Excel(name = "人力成本",width = 20)
    private BigDecimal confirmedCostLabour;

    @Excel(name = "差旅成本",width = 20)
    private BigDecimal confirmedCostTravel;

    @Excel(name = "非差旅成本",width = 20)
    private BigDecimal confirmedCostOther;

    public BigDecimal getConfirmedCostHardware() {
        return confirmedCostHardware;
    }

    public void setConfirmedCostHardware(BigDecimal confirmedCostHardware) {
        this.confirmedCostHardware = confirmedCostHardware;
    }

    public BigDecimal getConfirmedCostLabour() {
        return confirmedCostLabour;
    }

    public void setConfirmedCostLabour(BigDecimal confirmedCostLabour) {
        this.confirmedCostLabour = confirmedCostLabour;
    }

    public BigDecimal getConfirmedCostTravel() {
        return confirmedCostTravel;
    }

    public void setConfirmedCostTravel(BigDecimal confirmedCostTravel) {
        this.confirmedCostTravel = confirmedCostTravel;
    }

    public BigDecimal getConfirmedCostOther() {
        return confirmedCostOther;
    }

    public void setConfirmedCostOther(BigDecimal confirmedCostOther) {
        this.confirmedCostOther = confirmedCostOther;
    }
}