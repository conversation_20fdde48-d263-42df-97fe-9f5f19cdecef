package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/5/13
 * @description
 */
public class ReportWorkingHourAccountingExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer num;

    @Excel(name = "项目编号", width = 25)
    private String projectCode;

    @Excel(name = "项目名称", width = 25)
    private String projectName;

    @Excel(name = "业务分类", width = 20)
    private String unitName;

    @Excel(name = "项目类型", width = 20)
    private String projectTypeName;

    @Excel(name = "项目属性", width = 10, replace = {"内部_1", "外部_2", "研发_3"})
    private String projectPriceType;

    @Excel(name = "事业部", width = 15)
    private String departmentName;

    @Excel(name = "项目经理", width = 15)
    private String projectManagerName;

    @Excel(name = "项目状态", width = 20, replace = {"审批驳回_-2", "财务驳回_-1", "草稿_0", "审批中_3", "项目进行中_4", "项目变更中_9", "结项_10",
            "预立项转正驳回_-3", "预立项审批驳回_-4", "审批撤回_11", "预立项审批撤回_13", "作废_12", "预立项转正审批中_7", "预立项转正审批撤回_14", "终止_16"})
    private String projectStatus;

    @Excel(name = "填报人", width = 20)
    private String applyUserName;

    @Excel(name = "填报人MIP", width = 20)
    private String applyUserMip;

    @Excel(name = "填报人部门", width = 40)
    private String applyOrg;

    @Excel(name = "出勤日期", width = 15, format = "yyyy-MM-dd")
    private Date applyDate;

    @Excel(name = "填报日期", width = 15, format = "yyyy-MM-dd")
    private Date createDate;

    @Excel(name = "审批日期", width = 15, format = "yyyy-MM-dd")
    private Date approveDate;

    @Excel(name = "审批工时(H)", width = 15)
    private BigDecimal approveWorkingHours;

    @Excel(name = "标准人天单价", width = 15)
    private BigDecimal planCostMoney;

    @Excel(name = "实际人天单价", width = 15)
    private BigDecimal costMoney;

    @Excel(name = "标准成本", width = 15)
    private BigDecimal planCostTotal;

    @Excel(name = "归集入账成本", width = 15)
    private BigDecimal costTotal;

    @Excel(name = "归集入账日期", width = 15)
    private String accountingGlPeriod;

    @Excel(name = "已结转工时(H)", width = 15)
    private BigDecimal carryoverWorkingHours;

    @Excel(name = "已结转工时成本", width = 15)
    private BigDecimal carryoverLaborCost;

    @Excel(name = "结转日期", width = 15)
    private String carryoverGlPeriod;

    @Excel(name = "考勤工时(H)", width = 15)
    private BigDecimal ihrAttendHours;

    @Excel(name = "RDM确认工时", width = 15)
    private BigDecimal rdmWorkingHours;

    @Excel(name = "RDM结算单价", width = 15)
    private BigDecimal rdmCostMoney;

    @Excel(name = "业务实体", width = 60)
    private String ouName;

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getProjectTypeName() {
        return projectTypeName;
    }

    public void setProjectTypeName(String projectTypeName) {
        this.projectTypeName = projectTypeName;
    }

    public String getProjectPriceType() {
        return projectPriceType;
    }

    public void setProjectPriceType(String projectPriceType) {
        this.projectPriceType = projectPriceType;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getProjectManagerName() {
        return projectManagerName;
    }

    public void setProjectManagerName(String projectManagerName) {
        this.projectManagerName = projectManagerName;
    }

    public String getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(String projectStatus) {
        this.projectStatus = projectStatus;
    }

    public String getApplyUserName() {
        return applyUserName;
    }

    public void setApplyUserName(String applyUserName) {
        this.applyUserName = applyUserName;
    }

    public String getApplyUserMip() {
        return applyUserMip;
    }

    public void setApplyUserMip(String applyUserMip) {
        this.applyUserMip = applyUserMip;
    }

    public String getApplyOrg() {
        return applyOrg;
    }

    public void setApplyOrg(String applyOrg) {
        this.applyOrg = applyOrg;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getApproveDate() {
        return approveDate;
    }

    public void setApproveDate(Date approveDate) {
        this.approveDate = approveDate;
    }

    public BigDecimal getApproveWorkingHours() {
        return approveWorkingHours;
    }

    public void setApproveWorkingHours(BigDecimal approveWorkingHours) {
        this.approveWorkingHours = approveWorkingHours;
    }

    public BigDecimal getPlanCostMoney() {
        return planCostMoney;
    }

    public void setPlanCostMoney(BigDecimal planCostMoney) {
        this.planCostMoney = planCostMoney;
    }

    public BigDecimal getCostMoney() {
        return costMoney;
    }

    public void setCostMoney(BigDecimal costMoney) {
        this.costMoney = costMoney;
    }

    public BigDecimal getPlanCostTotal() {
        return planCostTotal;
    }

    public void setPlanCostTotal(BigDecimal planCostTotal) {
        this.planCostTotal = planCostTotal;
    }

    public BigDecimal getCostTotal() {
        return costTotal;
    }

    public void setCostTotal(BigDecimal costTotal) {
        this.costTotal = costTotal;
    }

    public String getAccountingGlPeriod() {
        return accountingGlPeriod;
    }

    public void setAccountingGlPeriod(String accountingGlPeriod) {
        this.accountingGlPeriod = accountingGlPeriod;
    }

    public BigDecimal getCarryoverWorkingHours() {
        return carryoverWorkingHours;
    }

    public void setCarryoverWorkingHours(BigDecimal carryoverWorkingHours) {
        this.carryoverWorkingHours = carryoverWorkingHours;
    }

    public BigDecimal getCarryoverLaborCost() {
        return carryoverLaborCost;
    }

    public void setCarryoverLaborCost(BigDecimal carryoverLaborCost) {
        this.carryoverLaborCost = carryoverLaborCost;
    }

    public String getCarryoverGlPeriod() {
        return carryoverGlPeriod;
    }

    public void setCarryoverGlPeriod(String carryoverGlPeriod) {
        this.carryoverGlPeriod = carryoverGlPeriod;
    }

    public BigDecimal getRdmWorkingHours() {
        return rdmWorkingHours;
    }

    public void setRdmWorkingHours(BigDecimal rdmWorkingHours) {
        this.rdmWorkingHours = rdmWorkingHours;
    }

    public BigDecimal getRdmCostMoney() {
        return rdmCostMoney;
    }

    public void setRdmCostMoney(BigDecimal rdmCostMoney) {
        this.rdmCostMoney = rdmCostMoney;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName;
    }

    public BigDecimal getIhrAttendHours() {
        return ihrAttendHours;
    }

    public void setIhrAttendHours(BigDecimal ihrAttendHours) {
        this.ihrAttendHours = ihrAttendHours;
    }
}
