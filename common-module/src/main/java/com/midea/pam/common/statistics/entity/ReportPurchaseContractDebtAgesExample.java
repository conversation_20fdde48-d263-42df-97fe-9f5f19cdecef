package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportPurchaseContractDebtAgesExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportPurchaseContractDebtAgesExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNull() {
            addCriterion("ou_name is null");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNotNull() {
            addCriterion("ou_name is not null");
            return (Criteria) this;
        }

        public Criteria andOuNameEqualTo(String value) {
            addCriterion("ou_name =", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotEqualTo(String value) {
            addCriterion("ou_name <>", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThan(String value) {
            addCriterion("ou_name >", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThanOrEqualTo(String value) {
            addCriterion("ou_name >=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThan(String value) {
            addCriterion("ou_name <", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThanOrEqualTo(String value) {
            addCriterion("ou_name <=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLike(String value) {
            addCriterion("ou_name like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotLike(String value) {
            addCriterion("ou_name not like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameIn(List<String> values) {
            addCriterion("ou_name in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotIn(List<String> values) {
            addCriterion("ou_name not in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameBetween(String value1, String value2) {
            addCriterion("ou_name between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotBetween(String value1, String value2) {
            addCriterion("ou_name not between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIsNull() {
            addCriterion("vendor_code is null");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIsNotNull() {
            addCriterion("vendor_code is not null");
            return (Criteria) this;
        }

        public Criteria andVendorCodeEqualTo(String value) {
            addCriterion("vendor_code =", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotEqualTo(String value) {
            addCriterion("vendor_code <>", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThan(String value) {
            addCriterion("vendor_code >", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_code >=", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThan(String value) {
            addCriterion("vendor_code <", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThanOrEqualTo(String value) {
            addCriterion("vendor_code <=", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLike(String value) {
            addCriterion("vendor_code like", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotLike(String value) {
            addCriterion("vendor_code not like", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIn(List<String> values) {
            addCriterion("vendor_code in", values, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotIn(List<String> values) {
            addCriterion("vendor_code not in", values, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeBetween(String value1, String value2) {
            addCriterion("vendor_code between", value1, value2, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotBetween(String value1, String value2) {
            addCriterion("vendor_code not between", value1, value2, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNull() {
            addCriterion("vendor_name is null");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNotNull() {
            addCriterion("vendor_name is not null");
            return (Criteria) this;
        }

        public Criteria andVendorNameEqualTo(String value) {
            addCriterion("vendor_name =", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotEqualTo(String value) {
            addCriterion("vendor_name <>", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThan(String value) {
            addCriterion("vendor_name >", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_name >=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThan(String value) {
            addCriterion("vendor_name <", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThanOrEqualTo(String value) {
            addCriterion("vendor_name <=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLike(String value) {
            addCriterion("vendor_name like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotLike(String value) {
            addCriterion("vendor_name not like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameIn(List<String> values) {
            addCriterion("vendor_name in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotIn(List<String> values) {
            addCriterion("vendor_name not in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameBetween(String value1, String value2) {
            addCriterion("vendor_name between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotBetween(String value1, String value2) {
            addCriterion("vendor_name not between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeIsNull() {
            addCriterion("vendor_site_code is null");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeIsNotNull() {
            addCriterion("vendor_site_code is not null");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeEqualTo(String value) {
            addCriterion("vendor_site_code =", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotEqualTo(String value) {
            addCriterion("vendor_site_code <>", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeGreaterThan(String value) {
            addCriterion("vendor_site_code >", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_site_code >=", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeLessThan(String value) {
            addCriterion("vendor_site_code <", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeLessThanOrEqualTo(String value) {
            addCriterion("vendor_site_code <=", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeLike(String value) {
            addCriterion("vendor_site_code like", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotLike(String value) {
            addCriterion("vendor_site_code not like", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeIn(List<String> values) {
            addCriterion("vendor_site_code in", values, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotIn(List<String> values) {
            addCriterion("vendor_site_code not in", values, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeBetween(String value1, String value2) {
            addCriterion("vendor_site_code between", value1, value2, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotBetween(String value1, String value2) {
            addCriterion("vendor_site_code not between", value1, value2, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andConversionRateIsNull() {
            addCriterion("conversion_rate is null");
            return (Criteria) this;
        }

        public Criteria andConversionRateIsNotNull() {
            addCriterion("conversion_rate is not null");
            return (Criteria) this;
        }

        public Criteria andConversionRateEqualTo(BigDecimal value) {
            addCriterion("conversion_rate =", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotEqualTo(BigDecimal value) {
            addCriterion("conversion_rate <>", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateGreaterThan(BigDecimal value) {
            addCriterion("conversion_rate >", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("conversion_rate >=", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateLessThan(BigDecimal value) {
            addCriterion("conversion_rate <", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("conversion_rate <=", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateIn(List<BigDecimal> values) {
            addCriterion("conversion_rate in", values, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotIn(List<BigDecimal> values) {
            addCriterion("conversion_rate not in", values, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("conversion_rate between", value1, value2, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("conversion_rate not between", value1, value2, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountIsNull() {
            addCriterion("invoice_amount is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountIsNotNull() {
            addCriterion("invoice_amount is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountEqualTo(BigDecimal value) {
            addCriterion("invoice_amount =", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountNotEqualTo(BigDecimal value) {
            addCriterion("invoice_amount <>", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountGreaterThan(BigDecimal value) {
            addCriterion("invoice_amount >", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("invoice_amount >=", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountLessThan(BigDecimal value) {
            addCriterion("invoice_amount <", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("invoice_amount <=", value, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountIn(List<BigDecimal> values) {
            addCriterion("invoice_amount in", values, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountNotIn(List<BigDecimal> values) {
            addCriterion("invoice_amount not in", values, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invoice_amount between", value1, value2, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andInvoiceAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invoice_amount not between", value1, value2, "invoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountIsNull() {
            addCriterion("standard_invoice_amount is null");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountIsNotNull() {
            addCriterion("standard_invoice_amount is not null");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountEqualTo(BigDecimal value) {
            addCriterion("standard_invoice_amount =", value, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountNotEqualTo(BigDecimal value) {
            addCriterion("standard_invoice_amount <>", value, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountGreaterThan(BigDecimal value) {
            addCriterion("standard_invoice_amount >", value, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_invoice_amount >=", value, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountLessThan(BigDecimal value) {
            addCriterion("standard_invoice_amount <", value, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_invoice_amount <=", value, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountIn(List<BigDecimal> values) {
            addCriterion("standard_invoice_amount in", values, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountNotIn(List<BigDecimal> values) {
            addCriterion("standard_invoice_amount not in", values, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_invoice_amount between", value1, value2, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andStandardInvoiceAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_invoice_amount not between", value1, value2, "standardInvoiceAmount");
            return (Criteria) this;
        }

        public Criteria andProgressAmountIsNull() {
            addCriterion("progress_amount is null");
            return (Criteria) this;
        }

        public Criteria andProgressAmountIsNotNull() {
            addCriterion("progress_amount is not null");
            return (Criteria) this;
        }

        public Criteria andProgressAmountEqualTo(BigDecimal value) {
            addCriterion("progress_amount =", value, "progressAmount");
            return (Criteria) this;
        }

        public Criteria andProgressAmountNotEqualTo(BigDecimal value) {
            addCriterion("progress_amount <>", value, "progressAmount");
            return (Criteria) this;
        }

        public Criteria andProgressAmountGreaterThan(BigDecimal value) {
            addCriterion("progress_amount >", value, "progressAmount");
            return (Criteria) this;
        }

        public Criteria andProgressAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("progress_amount >=", value, "progressAmount");
            return (Criteria) this;
        }

        public Criteria andProgressAmountLessThan(BigDecimal value) {
            addCriterion("progress_amount <", value, "progressAmount");
            return (Criteria) this;
        }

        public Criteria andProgressAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("progress_amount <=", value, "progressAmount");
            return (Criteria) this;
        }

        public Criteria andProgressAmountIn(List<BigDecimal> values) {
            addCriterion("progress_amount in", values, "progressAmount");
            return (Criteria) this;
        }

        public Criteria andProgressAmountNotIn(List<BigDecimal> values) {
            addCriterion("progress_amount not in", values, "progressAmount");
            return (Criteria) this;
        }

        public Criteria andProgressAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("progress_amount between", value1, value2, "progressAmount");
            return (Criteria) this;
        }

        public Criteria andProgressAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("progress_amount not between", value1, value2, "progressAmount");
            return (Criteria) this;
        }

        public Criteria andStandardProgressAmountIsNull() {
            addCriterion("standard_progress_amount is null");
            return (Criteria) this;
        }

        public Criteria andStandardProgressAmountIsNotNull() {
            addCriterion("standard_progress_amount is not null");
            return (Criteria) this;
        }

        public Criteria andStandardProgressAmountEqualTo(BigDecimal value) {
            addCriterion("standard_progress_amount =", value, "standardProgressAmount");
            return (Criteria) this;
        }

        public Criteria andStandardProgressAmountNotEqualTo(BigDecimal value) {
            addCriterion("standard_progress_amount <>", value, "standardProgressAmount");
            return (Criteria) this;
        }

        public Criteria andStandardProgressAmountGreaterThan(BigDecimal value) {
            addCriterion("standard_progress_amount >", value, "standardProgressAmount");
            return (Criteria) this;
        }

        public Criteria andStandardProgressAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_progress_amount >=", value, "standardProgressAmount");
            return (Criteria) this;
        }

        public Criteria andStandardProgressAmountLessThan(BigDecimal value) {
            addCriterion("standard_progress_amount <", value, "standardProgressAmount");
            return (Criteria) this;
        }

        public Criteria andStandardProgressAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_progress_amount <=", value, "standardProgressAmount");
            return (Criteria) this;
        }

        public Criteria andStandardProgressAmountIn(List<BigDecimal> values) {
            addCriterion("standard_progress_amount in", values, "standardProgressAmount");
            return (Criteria) this;
        }

        public Criteria andStandardProgressAmountNotIn(List<BigDecimal> values) {
            addCriterion("standard_progress_amount not in", values, "standardProgressAmount");
            return (Criteria) this;
        }

        public Criteria andStandardProgressAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_progress_amount between", value1, value2, "standardProgressAmount");
            return (Criteria) this;
        }

        public Criteria andStandardProgressAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_progress_amount not between", value1, value2, "standardProgressAmount");
            return (Criteria) this;
        }

        public Criteria andRemainAmountIsNull() {
            addCriterion("remain_amount is null");
            return (Criteria) this;
        }

        public Criteria andRemainAmountIsNotNull() {
            addCriterion("remain_amount is not null");
            return (Criteria) this;
        }

        public Criteria andRemainAmountEqualTo(BigDecimal value) {
            addCriterion("remain_amount =", value, "remainAmount");
            return (Criteria) this;
        }

        public Criteria andRemainAmountNotEqualTo(BigDecimal value) {
            addCriterion("remain_amount <>", value, "remainAmount");
            return (Criteria) this;
        }

        public Criteria andRemainAmountGreaterThan(BigDecimal value) {
            addCriterion("remain_amount >", value, "remainAmount");
            return (Criteria) this;
        }

        public Criteria andRemainAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("remain_amount >=", value, "remainAmount");
            return (Criteria) this;
        }

        public Criteria andRemainAmountLessThan(BigDecimal value) {
            addCriterion("remain_amount <", value, "remainAmount");
            return (Criteria) this;
        }

        public Criteria andRemainAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("remain_amount <=", value, "remainAmount");
            return (Criteria) this;
        }

        public Criteria andRemainAmountIn(List<BigDecimal> values) {
            addCriterion("remain_amount in", values, "remainAmount");
            return (Criteria) this;
        }

        public Criteria andRemainAmountNotIn(List<BigDecimal> values) {
            addCriterion("remain_amount not in", values, "remainAmount");
            return (Criteria) this;
        }

        public Criteria andRemainAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("remain_amount between", value1, value2, "remainAmount");
            return (Criteria) this;
        }

        public Criteria andRemainAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("remain_amount not between", value1, value2, "remainAmount");
            return (Criteria) this;
        }

        public Criteria andStandardRemainAmountIsNull() {
            addCriterion("standard_remain_amount is null");
            return (Criteria) this;
        }

        public Criteria andStandardRemainAmountIsNotNull() {
            addCriterion("standard_remain_amount is not null");
            return (Criteria) this;
        }

        public Criteria andStandardRemainAmountEqualTo(BigDecimal value) {
            addCriterion("standard_remain_amount =", value, "standardRemainAmount");
            return (Criteria) this;
        }

        public Criteria andStandardRemainAmountNotEqualTo(BigDecimal value) {
            addCriterion("standard_remain_amount <>", value, "standardRemainAmount");
            return (Criteria) this;
        }

        public Criteria andStandardRemainAmountGreaterThan(BigDecimal value) {
            addCriterion("standard_remain_amount >", value, "standardRemainAmount");
            return (Criteria) this;
        }

        public Criteria andStandardRemainAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_remain_amount >=", value, "standardRemainAmount");
            return (Criteria) this;
        }

        public Criteria andStandardRemainAmountLessThan(BigDecimal value) {
            addCriterion("standard_remain_amount <", value, "standardRemainAmount");
            return (Criteria) this;
        }

        public Criteria andStandardRemainAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_remain_amount <=", value, "standardRemainAmount");
            return (Criteria) this;
        }

        public Criteria andStandardRemainAmountIn(List<BigDecimal> values) {
            addCriterion("standard_remain_amount in", values, "standardRemainAmount");
            return (Criteria) this;
        }

        public Criteria andStandardRemainAmountNotIn(List<BigDecimal> values) {
            addCriterion("standard_remain_amount not in", values, "standardRemainAmount");
            return (Criteria) this;
        }

        public Criteria andStandardRemainAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_remain_amount between", value1, value2, "standardRemainAmount");
            return (Criteria) this;
        }

        public Criteria andStandardRemainAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_remain_amount not between", value1, value2, "standardRemainAmount");
            return (Criteria) this;
        }

        public Criteria andEvaluateRemainAmountIsNull() {
            addCriterion("evaluate_remain_amount is null");
            return (Criteria) this;
        }

        public Criteria andEvaluateRemainAmountIsNotNull() {
            addCriterion("evaluate_remain_amount is not null");
            return (Criteria) this;
        }

        public Criteria andEvaluateRemainAmountEqualTo(BigDecimal value) {
            addCriterion("evaluate_remain_amount =", value, "evaluateRemainAmount");
            return (Criteria) this;
        }

        public Criteria andEvaluateRemainAmountNotEqualTo(BigDecimal value) {
            addCriterion("evaluate_remain_amount <>", value, "evaluateRemainAmount");
            return (Criteria) this;
        }

        public Criteria andEvaluateRemainAmountGreaterThan(BigDecimal value) {
            addCriterion("evaluate_remain_amount >", value, "evaluateRemainAmount");
            return (Criteria) this;
        }

        public Criteria andEvaluateRemainAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("evaluate_remain_amount >=", value, "evaluateRemainAmount");
            return (Criteria) this;
        }

        public Criteria andEvaluateRemainAmountLessThan(BigDecimal value) {
            addCriterion("evaluate_remain_amount <", value, "evaluateRemainAmount");
            return (Criteria) this;
        }

        public Criteria andEvaluateRemainAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("evaluate_remain_amount <=", value, "evaluateRemainAmount");
            return (Criteria) this;
        }

        public Criteria andEvaluateRemainAmountIn(List<BigDecimal> values) {
            addCriterion("evaluate_remain_amount in", values, "evaluateRemainAmount");
            return (Criteria) this;
        }

        public Criteria andEvaluateRemainAmountNotIn(List<BigDecimal> values) {
            addCriterion("evaluate_remain_amount not in", values, "evaluateRemainAmount");
            return (Criteria) this;
        }

        public Criteria andEvaluateRemainAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("evaluate_remain_amount between", value1, value2, "evaluateRemainAmount");
            return (Criteria) this;
        }

        public Criteria andEvaluateRemainAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("evaluate_remain_amount not between", value1, value2, "evaluateRemainAmount");
            return (Criteria) this;
        }

        public Criteria andRuleValueIsNull() {
            addCriterion("rule_value is null");
            return (Criteria) this;
        }

        public Criteria andRuleValueIsNotNull() {
            addCriterion("rule_value is not null");
            return (Criteria) this;
        }

        public Criteria andRuleValueEqualTo(String value) {
            addCriterion("rule_value =", value, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueNotEqualTo(String value) {
            addCriterion("rule_value <>", value, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueGreaterThan(String value) {
            addCriterion("rule_value >", value, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueGreaterThanOrEqualTo(String value) {
            addCriterion("rule_value >=", value, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueLessThan(String value) {
            addCriterion("rule_value <", value, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueLessThanOrEqualTo(String value) {
            addCriterion("rule_value <=", value, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueLike(String value) {
            addCriterion("rule_value like", value, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueNotLike(String value) {
            addCriterion("rule_value not like", value, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueIn(List<String> values) {
            addCriterion("rule_value in", values, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueNotIn(List<String> values) {
            addCriterion("rule_value not in", values, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueBetween(String value1, String value2) {
            addCriterion("rule_value between", value1, value2, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueNotBetween(String value1, String value2) {
            addCriterion("rule_value not between", value1, value2, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleScopeIsNull() {
            addCriterion("rule_scope is null");
            return (Criteria) this;
        }

        public Criteria andRuleScopeIsNotNull() {
            addCriterion("rule_scope is not null");
            return (Criteria) this;
        }

        public Criteria andRuleScopeEqualTo(String value) {
            addCriterion("rule_scope =", value, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeNotEqualTo(String value) {
            addCriterion("rule_scope <>", value, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeGreaterThan(String value) {
            addCriterion("rule_scope >", value, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeGreaterThanOrEqualTo(String value) {
            addCriterion("rule_scope >=", value, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeLessThan(String value) {
            addCriterion("rule_scope <", value, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeLessThanOrEqualTo(String value) {
            addCriterion("rule_scope <=", value, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeLike(String value) {
            addCriterion("rule_scope like", value, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeNotLike(String value) {
            addCriterion("rule_scope not like", value, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeIn(List<String> values) {
            addCriterion("rule_scope in", values, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeNotIn(List<String> values) {
            addCriterion("rule_scope not in", values, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeBetween(String value1, String value2) {
            addCriterion("rule_scope between", value1, value2, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeNotBetween(String value1, String value2) {
            addCriterion("rule_scope not between", value1, value2, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleNameIsNull() {
            addCriterion("rule_name is null");
            return (Criteria) this;
        }

        public Criteria andRuleNameIsNotNull() {
            addCriterion("rule_name is not null");
            return (Criteria) this;
        }

        public Criteria andRuleNameEqualTo(String value) {
            addCriterion("rule_name =", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameNotEqualTo(String value) {
            addCriterion("rule_name <>", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameGreaterThan(String value) {
            addCriterion("rule_name >", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameGreaterThanOrEqualTo(String value) {
            addCriterion("rule_name >=", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameLessThan(String value) {
            addCriterion("rule_name <", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameLessThanOrEqualTo(String value) {
            addCriterion("rule_name <=", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameLike(String value) {
            addCriterion("rule_name like", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameNotLike(String value) {
            addCriterion("rule_name not like", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameIn(List<String> values) {
            addCriterion("rule_name in", values, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameNotIn(List<String> values) {
            addCriterion("rule_name not in", values, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameBetween(String value1, String value2) {
            addCriterion("rule_name between", value1, value2, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameNotBetween(String value1, String value2) {
            addCriterion("rule_name not between", value1, value2, "ruleName");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}