package com.midea.pam.common.statistics.excelVo;


import com.midea.pam.common.util.BigDecimalUtils;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-12-9
 * @description 需求预算-合并
 */
@Getter
@Setter
public class RequirementBudgetMergeExcelVO {

    @Excel(name = "序号", width = 5)
    private Integer number;

    @Excel(name = "类型", width = 20)
    private String type;

    @Excel(name = "WBS", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项", width = 15)
    private String activityCode;

    @Excel(name = "需求发布单据编号/EA单号", width = 25)
    private String requirementCode;

    //EA单号
    private String feeApplyCode;

    @Excel(name = "角色", width = 25)
    private String roleName;

    @Excel(name = "需求发布单据情况", width = 15)
    private String receiptsType;

    @Excel(name = "需求预算", width = 15)  //WBS'预算占用金额'  ||  总预算（需求预算）
    private BigDecimal budgetOccupiedAmount;

    @Excel(name = "已采购金额", width = 15)
    private BigDecimal downAmount;

    @Excel(name = "当前剩余需求预算", width = 15)
    private BigDecimal remainingCost;

    //当前物料采购需求预算金额  ||  需求预算剩余金额  ||  当前人力点工需求预算金额
    private BigDecimal remainingCostAmount;

    //剩余EA可用金额
    private BigDecimal amount;

    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date dataTime;


    public BigDecimal getBudgetOccupiedAmount() {
        return BigDecimalUtils.scale(budgetOccupiedAmount);
    }

    public BigDecimal getDownAmount() {
        return BigDecimalUtils.scale(downAmount);
    }

    public BigDecimal getRemainingCost() {
        return BigDecimalUtils.scale(remainingCost);
    }

}
