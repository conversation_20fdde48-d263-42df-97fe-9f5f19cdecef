package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目成本节约报表")
public class ProjectCostSaving extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目号")
    private String projectCode;

    @ApiModelProperty(value = "需求单号")
    private String requirementCode;

    @ApiModelProperty(value = "wbs编码")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "详设单据状态(详见详细设计单据表)")
    private Integer requirementStatus;

    @ApiModelProperty(value = "需求分类(0物料采购、1外包)")
    private Integer demandType;

    @ApiModelProperty(value = "需求单据状态(0未完成；1已完成)")
    private Boolean receiptsStatus;

    @ApiModelProperty(value = "采购需求状态(0已完成；1未完成)")
    private Integer purchaseStatus;

    @ApiModelProperty(value = "需求审批通过日期")
    private Date demandApprovalTime;

    @ApiModelProperty(value = "需求预算总额")
    private BigDecimal budgetOccupiedAmount;

    @ApiModelProperty(value = "采购订单下达汇总数量")
    private BigDecimal releasedQuantity;

    @ApiModelProperty(value = "采购订单下达汇总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "节约金额Saving")
    private BigDecimal saving;

    @ApiModelProperty(value = "累计收货数量")
    private BigDecimal receiptQuantity;

    @ApiModelProperty(value = "累计收货金额")
    private BigDecimal receiptAmount;

    @ApiModelProperty(value = "对应订单或合同")
    private String purchaseCodes;

    @ApiModelProperty(value = "供应商编号")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "供应商地点")
    private String vendorSiteCode;

    @ApiModelProperty(value = "采购员")
    private String buyerNames;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "删除标志")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "PAM物料编码")
    private String pamCode;

    @ApiModelProperty(value = "ERP物料编码")
    private String erpCode;

    @ApiModelProperty(value = "物料描述")
    private String materielDescr;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "超预算说明")
    private String overBudgetDes;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getRequirementCode() {
        return requirementCode;
    }

    public void setRequirementCode(String requirementCode) {
        this.requirementCode = requirementCode == null ? null : requirementCode.trim();
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public Integer getRequirementStatus() {
        return requirementStatus;
    }

    public void setRequirementStatus(Integer requirementStatus) {
        this.requirementStatus = requirementStatus;
    }

    public Integer getDemandType() {
        return demandType;
    }

    public void setDemandType(Integer demandType) {
        this.demandType = demandType;
    }

    public Boolean getReceiptsStatus() {
        return receiptsStatus;
    }

    public void setReceiptsStatus(Boolean receiptsStatus) {
        this.receiptsStatus = receiptsStatus;
    }

    public Integer getPurchaseStatus() {
        return purchaseStatus;
    }

    public void setPurchaseStatus(Integer purchaseStatus) {
        this.purchaseStatus = purchaseStatus;
    }

    public Date getDemandApprovalTime() {
        return demandApprovalTime;
    }

    public void setDemandApprovalTime(Date demandApprovalTime) {
        this.demandApprovalTime = demandApprovalTime;
    }

    public BigDecimal getBudgetOccupiedAmount() {
        return budgetOccupiedAmount;
    }

    public void setBudgetOccupiedAmount(BigDecimal budgetOccupiedAmount) {
        this.budgetOccupiedAmount = budgetOccupiedAmount;
    }

    public BigDecimal getReleasedQuantity() {
        return releasedQuantity;
    }

    public void setReleasedQuantity(BigDecimal releasedQuantity) {
        this.releasedQuantity = releasedQuantity;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getSaving() {
        return saving;
    }

    public void setSaving(BigDecimal saving) {
        this.saving = saving;
    }

    public BigDecimal getReceiptQuantity() {
        return receiptQuantity;
    }

    public void setReceiptQuantity(BigDecimal receiptQuantity) {
        this.receiptQuantity = receiptQuantity;
    }

    public BigDecimal getReceiptAmount() {
        return receiptAmount;
    }

    public void setReceiptAmount(BigDecimal receiptAmount) {
        this.receiptAmount = receiptAmount;
    }

    public String getPurchaseCodes() {
        return purchaseCodes;
    }

    public void setPurchaseCodes(String purchaseCodes) {
        this.purchaseCodes = purchaseCodes == null ? null : purchaseCodes.trim();
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode == null ? null : vendorCode.trim();
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName == null ? null : vendorName.trim();
    }

    public String getVendorSiteCode() {
        return vendorSiteCode;
    }

    public void setVendorSiteCode(String vendorSiteCode) {
        this.vendorSiteCode = vendorSiteCode == null ? null : vendorSiteCode.trim();
    }

    public String getBuyerNames() {
        return buyerNames;
    }

    public void setBuyerNames(String buyerNames) {
        this.buyerNames = buyerNames == null ? null : buyerNames.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode == null ? null : pamCode.trim();
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode == null ? null : erpCode.trim();
    }

    public String getMaterielDescr() {
        return materielDescr;
    }

    public void setMaterielDescr(String materielDescr) {
        this.materielDescr = materielDescr == null ? null : materielDescr.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getOverBudgetDes() {
        return overBudgetDes;
    }

    public void setOverBudgetDes(String overBudgetDes) {
        this.overBudgetDes = overBudgetDes == null ? null : overBudgetDes.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", requirementCode=").append(requirementCode);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", requirementStatus=").append(requirementStatus);
        sb.append(", demandType=").append(demandType);
        sb.append(", receiptsStatus=").append(receiptsStatus);
        sb.append(", purchaseStatus=").append(purchaseStatus);
        sb.append(", demandApprovalTime=").append(demandApprovalTime);
        sb.append(", budgetOccupiedAmount=").append(budgetOccupiedAmount);
        sb.append(", releasedQuantity=").append(releasedQuantity);
        sb.append(", totalAmount=").append(totalAmount);
        sb.append(", saving=").append(saving);
        sb.append(", receiptQuantity=").append(receiptQuantity);
        sb.append(", receiptAmount=").append(receiptAmount);
        sb.append(", purchaseCodes=").append(purchaseCodes);
        sb.append(", vendorCode=").append(vendorCode);
        sb.append(", vendorName=").append(vendorName);
        sb.append(", vendorSiteCode=").append(vendorSiteCode);
        sb.append(", buyerNames=").append(buyerNames);
        sb.append(", brand=").append(brand);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", pamCode=").append(pamCode);
        sb.append(", erpCode=").append(erpCode);
        sb.append(", materielDescr=").append(materielDescr);
        sb.append(", remark=").append(remark);
        sb.append(", overBudgetDes=").append(overBudgetDes);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}