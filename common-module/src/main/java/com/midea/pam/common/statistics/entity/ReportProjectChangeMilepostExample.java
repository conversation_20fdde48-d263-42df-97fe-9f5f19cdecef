package com.midea.pam.common.statistics.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportProjectChangeMilepostExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportProjectChangeMilepostExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNull() {
            addCriterion("project_manager is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNotNull() {
            addCriterion("project_manager is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerEqualTo(String value) {
            addCriterion("project_manager =", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotEqualTo(String value) {
            addCriterion("project_manager <>", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThan(String value) {
            addCriterion("project_manager >", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThanOrEqualTo(String value) {
            addCriterion("project_manager >=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThan(String value) {
            addCriterion("project_manager <", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThanOrEqualTo(String value) {
            addCriterion("project_manager <=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLike(String value) {
            addCriterion("project_manager like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotLike(String value) {
            addCriterion("project_manager not like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIn(List<String> values) {
            addCriterion("project_manager in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotIn(List<String> values) {
            addCriterion("project_manager not in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerBetween(String value1, String value2) {
            addCriterion("project_manager between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotBetween(String value1, String value2) {
            addCriterion("project_manager not between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andOperationTypeIsNull() {
            addCriterion("operation_type is null");
            return (Criteria) this;
        }

        public Criteria andOperationTypeIsNotNull() {
            addCriterion("operation_type is not null");
            return (Criteria) this;
        }

        public Criteria andOperationTypeEqualTo(String value) {
            addCriterion("operation_type =", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeNotEqualTo(String value) {
            addCriterion("operation_type <>", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeGreaterThan(String value) {
            addCriterion("operation_type >", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeGreaterThanOrEqualTo(String value) {
            addCriterion("operation_type >=", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeLessThan(String value) {
            addCriterion("operation_type <", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeLessThanOrEqualTo(String value) {
            addCriterion("operation_type <=", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeLike(String value) {
            addCriterion("operation_type like", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeNotLike(String value) {
            addCriterion("operation_type not like", value, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeIn(List<String> values) {
            addCriterion("operation_type in", values, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeNotIn(List<String> values) {
            addCriterion("operation_type not in", values, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeBetween(String value1, String value2) {
            addCriterion("operation_type between", value1, value2, "operationType");
            return (Criteria) this;
        }

        public Criteria andOperationTypeNotBetween(String value1, String value2) {
            addCriterion("operation_type not between", value1, value2, "operationType");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByIsNull() {
            addCriterion("change_submit_by is null");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByIsNotNull() {
            addCriterion("change_submit_by is not null");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByEqualTo(String value) {
            addCriterion("change_submit_by =", value, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByNotEqualTo(String value) {
            addCriterion("change_submit_by <>", value, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByGreaterThan(String value) {
            addCriterion("change_submit_by >", value, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByGreaterThanOrEqualTo(String value) {
            addCriterion("change_submit_by >=", value, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByLessThan(String value) {
            addCriterion("change_submit_by <", value, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByLessThanOrEqualTo(String value) {
            addCriterion("change_submit_by <=", value, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByLike(String value) {
            addCriterion("change_submit_by like", value, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByNotLike(String value) {
            addCriterion("change_submit_by not like", value, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByIn(List<String> values) {
            addCriterion("change_submit_by in", values, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByNotIn(List<String> values) {
            addCriterion("change_submit_by not in", values, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByBetween(String value1, String value2) {
            addCriterion("change_submit_by between", value1, value2, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeSubmitByNotBetween(String value1, String value2) {
            addCriterion("change_submit_by not between", value1, value2, "changeSubmitBy");
            return (Criteria) this;
        }

        public Criteria andChangeTimeIsNull() {
            addCriterion("change_time is null");
            return (Criteria) this;
        }

        public Criteria andChangeTimeIsNotNull() {
            addCriterion("change_time is not null");
            return (Criteria) this;
        }

        public Criteria andChangeTimeEqualTo(Date value) {
            addCriterion("change_time =", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeNotEqualTo(Date value) {
            addCriterion("change_time <>", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeGreaterThan(Date value) {
            addCriterion("change_time >", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("change_time >=", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeLessThan(Date value) {
            addCriterion("change_time <", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeLessThanOrEqualTo(Date value) {
            addCriterion("change_time <=", value, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeIn(List<Date> values) {
            addCriterion("change_time in", values, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeNotIn(List<Date> values) {
            addCriterion("change_time not in", values, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeBetween(Date value1, Date value2) {
            addCriterion("change_time between", value1, value2, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeTimeNotBetween(Date value1, Date value2) {
            addCriterion("change_time not between", value1, value2, "changeTime");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIsNull() {
            addCriterion("change_reason is null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIsNotNull() {
            addCriterion("change_reason is not null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonEqualTo(String value) {
            addCriterion("change_reason =", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotEqualTo(String value) {
            addCriterion("change_reason <>", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonGreaterThan(String value) {
            addCriterion("change_reason >", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonGreaterThanOrEqualTo(String value) {
            addCriterion("change_reason >=", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLessThan(String value) {
            addCriterion("change_reason <", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLessThanOrEqualTo(String value) {
            addCriterion("change_reason <=", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLike(String value) {
            addCriterion("change_reason like", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotLike(String value) {
            addCriterion("change_reason not like", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIn(List<String> values) {
            addCriterion("change_reason in", values, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotIn(List<String> values) {
            addCriterion("change_reason not in", values, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonBetween(String value1, String value2) {
            addCriterion("change_reason between", value1, value2, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotBetween(String value1, String value2) {
            addCriterion("change_reason not between", value1, value2, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeIsNull() {
            addCriterion("change_reason_describe is null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeIsNotNull() {
            addCriterion("change_reason_describe is not null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeEqualTo(String value) {
            addCriterion("change_reason_describe =", value, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeNotEqualTo(String value) {
            addCriterion("change_reason_describe <>", value, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeGreaterThan(String value) {
            addCriterion("change_reason_describe >", value, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeGreaterThanOrEqualTo(String value) {
            addCriterion("change_reason_describe >=", value, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeLessThan(String value) {
            addCriterion("change_reason_describe <", value, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeLessThanOrEqualTo(String value) {
            addCriterion("change_reason_describe <=", value, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeLike(String value) {
            addCriterion("change_reason_describe like", value, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeNotLike(String value) {
            addCriterion("change_reason_describe not like", value, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeIn(List<String> values) {
            addCriterion("change_reason_describe in", values, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeNotIn(List<String> values) {
            addCriterion("change_reason_describe not in", values, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeBetween(String value1, String value2) {
            addCriterion("change_reason_describe between", value1, value2, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescribeNotBetween(String value1, String value2) {
            addCriterion("change_reason_describe not between", value1, value2, "changeReasonDescribe");
            return (Criteria) this;
        }

        public Criteria andAfterNameIsNull() {
            addCriterion("after_name is null");
            return (Criteria) this;
        }

        public Criteria andAfterNameIsNotNull() {
            addCriterion("after_name is not null");
            return (Criteria) this;
        }

        public Criteria andAfterNameEqualTo(String value) {
            addCriterion("after_name =", value, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameNotEqualTo(String value) {
            addCriterion("after_name <>", value, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameGreaterThan(String value) {
            addCriterion("after_name >", value, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameGreaterThanOrEqualTo(String value) {
            addCriterion("after_name >=", value, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameLessThan(String value) {
            addCriterion("after_name <", value, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameLessThanOrEqualTo(String value) {
            addCriterion("after_name <=", value, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameLike(String value) {
            addCriterion("after_name like", value, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameNotLike(String value) {
            addCriterion("after_name not like", value, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameIn(List<String> values) {
            addCriterion("after_name in", values, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameNotIn(List<String> values) {
            addCriterion("after_name not in", values, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameBetween(String value1, String value2) {
            addCriterion("after_name between", value1, value2, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterNameNotBetween(String value1, String value2) {
            addCriterion("after_name not between", value1, value2, "afterName");
            return (Criteria) this;
        }

        public Criteria andAfterPlanStartTimeIsNull() {
            addCriterion("after_plan_start_time is null");
            return (Criteria) this;
        }

        public Criteria andAfterPlanStartTimeIsNotNull() {
            addCriterion("after_plan_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andAfterPlanStartTimeEqualTo(Date value) {
            addCriterion("after_plan_start_time =", value, "afterPlanStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanStartTimeNotEqualTo(Date value) {
            addCriterion("after_plan_start_time <>", value, "afterPlanStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanStartTimeGreaterThan(Date value) {
            addCriterion("after_plan_start_time >", value, "afterPlanStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("after_plan_start_time >=", value, "afterPlanStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanStartTimeLessThan(Date value) {
            addCriterion("after_plan_start_time <", value, "afterPlanStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("after_plan_start_time <=", value, "afterPlanStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanStartTimeIn(List<Date> values) {
            addCriterion("after_plan_start_time in", values, "afterPlanStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanStartTimeNotIn(List<Date> values) {
            addCriterion("after_plan_start_time not in", values, "afterPlanStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanStartTimeBetween(Date value1, Date value2) {
            addCriterion("after_plan_start_time between", value1, value2, "afterPlanStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("after_plan_start_time not between", value1, value2, "afterPlanStartTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanEndTimeIsNull() {
            addCriterion("after_plan_end_time is null");
            return (Criteria) this;
        }

        public Criteria andAfterPlanEndTimeIsNotNull() {
            addCriterion("after_plan_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andAfterPlanEndTimeEqualTo(Date value) {
            addCriterion("after_plan_end_time =", value, "afterPlanEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanEndTimeNotEqualTo(Date value) {
            addCriterion("after_plan_end_time <>", value, "afterPlanEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanEndTimeGreaterThan(Date value) {
            addCriterion("after_plan_end_time >", value, "afterPlanEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("after_plan_end_time >=", value, "afterPlanEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanEndTimeLessThan(Date value) {
            addCriterion("after_plan_end_time <", value, "afterPlanEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("after_plan_end_time <=", value, "afterPlanEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanEndTimeIn(List<Date> values) {
            addCriterion("after_plan_end_time in", values, "afterPlanEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanEndTimeNotIn(List<Date> values) {
            addCriterion("after_plan_end_time not in", values, "afterPlanEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanEndTimeBetween(Date value1, Date value2) {
            addCriterion("after_plan_end_time between", value1, value2, "afterPlanEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterPlanEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("after_plan_end_time not between", value1, value2, "afterPlanEndTime");
            return (Criteria) this;
        }

        public Criteria andAfterResponsibleIsNull() {
            addCriterion("after_responsible is null");
            return (Criteria) this;
        }

        public Criteria andAfterResponsibleIsNotNull() {
            addCriterion("after_responsible is not null");
            return (Criteria) this;
        }

        public Criteria andAfterResponsibleEqualTo(String value) {
            addCriterion("after_responsible =", value, "afterResponsible");
            return (Criteria) this;
        }

        public Criteria andAfterResponsibleNotEqualTo(String value) {
            addCriterion("after_responsible <>", value, "afterResponsible");
            return (Criteria) this;
        }

        public Criteria andAfterResponsibleGreaterThan(String value) {
            addCriterion("after_responsible >", value, "afterResponsible");
            return (Criteria) this;
        }

        public Criteria andAfterResponsibleGreaterThanOrEqualTo(String value) {
            addCriterion("after_responsible >=", value, "afterResponsible");
            return (Criteria) this;
        }

        public Criteria andAfterResponsibleLessThan(String value) {
            addCriterion("after_responsible <", value, "afterResponsible");
            return (Criteria) this;
        }

        public Criteria andAfterResponsibleLessThanOrEqualTo(String value) {
            addCriterion("after_responsible <=", value, "afterResponsible");
            return (Criteria) this;
        }

        public Criteria andAfterResponsibleLike(String value) {
            addCriterion("after_responsible like", value, "afterResponsible");
            return (Criteria) this;
        }

        public Criteria andAfterResponsibleNotLike(String value) {
            addCriterion("after_responsible not like", value, "afterResponsible");
            return (Criteria) this;
        }

        public Criteria andAfterResponsibleIn(List<String> values) {
            addCriterion("after_responsible in", values, "afterResponsible");
            return (Criteria) this;
        }

        public Criteria andAfterResponsibleNotIn(List<String> values) {
            addCriterion("after_responsible not in", values, "afterResponsible");
            return (Criteria) this;
        }

        public Criteria andAfterResponsibleBetween(String value1, String value2) {
            addCriterion("after_responsible between", value1, value2, "afterResponsible");
            return (Criteria) this;
        }

        public Criteria andAfterResponsibleNotBetween(String value1, String value2) {
            addCriterion("after_responsible not between", value1, value2, "afterResponsible");
            return (Criteria) this;
        }

        public Criteria andBeforeNameIsNull() {
            addCriterion("before_name is null");
            return (Criteria) this;
        }

        public Criteria andBeforeNameIsNotNull() {
            addCriterion("before_name is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeNameEqualTo(String value) {
            addCriterion("before_name =", value, "beforeName");
            return (Criteria) this;
        }

        public Criteria andBeforeNameNotEqualTo(String value) {
            addCriterion("before_name <>", value, "beforeName");
            return (Criteria) this;
        }

        public Criteria andBeforeNameGreaterThan(String value) {
            addCriterion("before_name >", value, "beforeName");
            return (Criteria) this;
        }

        public Criteria andBeforeNameGreaterThanOrEqualTo(String value) {
            addCriterion("before_name >=", value, "beforeName");
            return (Criteria) this;
        }

        public Criteria andBeforeNameLessThan(String value) {
            addCriterion("before_name <", value, "beforeName");
            return (Criteria) this;
        }

        public Criteria andBeforeNameLessThanOrEqualTo(String value) {
            addCriterion("before_name <=", value, "beforeName");
            return (Criteria) this;
        }

        public Criteria andBeforeNameLike(String value) {
            addCriterion("before_name like", value, "beforeName");
            return (Criteria) this;
        }

        public Criteria andBeforeNameNotLike(String value) {
            addCriterion("before_name not like", value, "beforeName");
            return (Criteria) this;
        }

        public Criteria andBeforeNameIn(List<String> values) {
            addCriterion("before_name in", values, "beforeName");
            return (Criteria) this;
        }

        public Criteria andBeforeNameNotIn(List<String> values) {
            addCriterion("before_name not in", values, "beforeName");
            return (Criteria) this;
        }

        public Criteria andBeforeNameBetween(String value1, String value2) {
            addCriterion("before_name between", value1, value2, "beforeName");
            return (Criteria) this;
        }

        public Criteria andBeforeNameNotBetween(String value1, String value2) {
            addCriterion("before_name not between", value1, value2, "beforeName");
            return (Criteria) this;
        }

        public Criteria andBeforePlanStartTimeIsNull() {
            addCriterion("before_plan_start_time is null");
            return (Criteria) this;
        }

        public Criteria andBeforePlanStartTimeIsNotNull() {
            addCriterion("before_plan_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andBeforePlanStartTimeEqualTo(Date value) {
            addCriterion("before_plan_start_time =", value, "beforePlanStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanStartTimeNotEqualTo(Date value) {
            addCriterion("before_plan_start_time <>", value, "beforePlanStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanStartTimeGreaterThan(Date value) {
            addCriterion("before_plan_start_time >", value, "beforePlanStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("before_plan_start_time >=", value, "beforePlanStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanStartTimeLessThan(Date value) {
            addCriterion("before_plan_start_time <", value, "beforePlanStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("before_plan_start_time <=", value, "beforePlanStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanStartTimeIn(List<Date> values) {
            addCriterion("before_plan_start_time in", values, "beforePlanStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanStartTimeNotIn(List<Date> values) {
            addCriterion("before_plan_start_time not in", values, "beforePlanStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanStartTimeBetween(Date value1, Date value2) {
            addCriterion("before_plan_start_time between", value1, value2, "beforePlanStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("before_plan_start_time not between", value1, value2, "beforePlanStartTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanEndTimeIsNull() {
            addCriterion("before_plan_end_time is null");
            return (Criteria) this;
        }

        public Criteria andBeforePlanEndTimeIsNotNull() {
            addCriterion("before_plan_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andBeforePlanEndTimeEqualTo(Date value) {
            addCriterion("before_plan_end_time =", value, "beforePlanEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanEndTimeNotEqualTo(Date value) {
            addCriterion("before_plan_end_time <>", value, "beforePlanEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanEndTimeGreaterThan(Date value) {
            addCriterion("before_plan_end_time >", value, "beforePlanEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("before_plan_end_time >=", value, "beforePlanEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanEndTimeLessThan(Date value) {
            addCriterion("before_plan_end_time <", value, "beforePlanEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("before_plan_end_time <=", value, "beforePlanEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanEndTimeIn(List<Date> values) {
            addCriterion("before_plan_end_time in", values, "beforePlanEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanEndTimeNotIn(List<Date> values) {
            addCriterion("before_plan_end_time not in", values, "beforePlanEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanEndTimeBetween(Date value1, Date value2) {
            addCriterion("before_plan_end_time between", value1, value2, "beforePlanEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforePlanEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("before_plan_end_time not between", value1, value2, "beforePlanEndTime");
            return (Criteria) this;
        }

        public Criteria andBeforeResponsibleIsNull() {
            addCriterion("before_responsible is null");
            return (Criteria) this;
        }

        public Criteria andBeforeResponsibleIsNotNull() {
            addCriterion("before_responsible is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeResponsibleEqualTo(String value) {
            addCriterion("before_responsible =", value, "beforeResponsible");
            return (Criteria) this;
        }

        public Criteria andBeforeResponsibleNotEqualTo(String value) {
            addCriterion("before_responsible <>", value, "beforeResponsible");
            return (Criteria) this;
        }

        public Criteria andBeforeResponsibleGreaterThan(String value) {
            addCriterion("before_responsible >", value, "beforeResponsible");
            return (Criteria) this;
        }

        public Criteria andBeforeResponsibleGreaterThanOrEqualTo(String value) {
            addCriterion("before_responsible >=", value, "beforeResponsible");
            return (Criteria) this;
        }

        public Criteria andBeforeResponsibleLessThan(String value) {
            addCriterion("before_responsible <", value, "beforeResponsible");
            return (Criteria) this;
        }

        public Criteria andBeforeResponsibleLessThanOrEqualTo(String value) {
            addCriterion("before_responsible <=", value, "beforeResponsible");
            return (Criteria) this;
        }

        public Criteria andBeforeResponsibleLike(String value) {
            addCriterion("before_responsible like", value, "beforeResponsible");
            return (Criteria) this;
        }

        public Criteria andBeforeResponsibleNotLike(String value) {
            addCriterion("before_responsible not like", value, "beforeResponsible");
            return (Criteria) this;
        }

        public Criteria andBeforeResponsibleIn(List<String> values) {
            addCriterion("before_responsible in", values, "beforeResponsible");
            return (Criteria) this;
        }

        public Criteria andBeforeResponsibleNotIn(List<String> values) {
            addCriterion("before_responsible not in", values, "beforeResponsible");
            return (Criteria) this;
        }

        public Criteria andBeforeResponsibleBetween(String value1, String value2) {
            addCriterion("before_responsible between", value1, value2, "beforeResponsible");
            return (Criteria) this;
        }

        public Criteria andBeforeResponsibleNotBetween(String value1, String value2) {
            addCriterion("before_responsible not between", value1, value2, "beforeResponsible");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}