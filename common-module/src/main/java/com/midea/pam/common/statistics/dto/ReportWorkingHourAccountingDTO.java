package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.ReportWorkingHourAccounting;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/13
 * @description
 */
public class ReportWorkingHourAccountingDTO extends ReportWorkingHourAccounting {

    private String attendanceStartDate;

    private String attendanceEndDate;

    private List<Integer> projectStatuses;

    private List<Long> departmentIds;

    private List<Long> orgIds;

    private List<Long> unitIds;

    private String workingHourAccountingStartDate;

    private String workingHourAccountingEndDate;

    private List<Long> ouIds;

    private String projectCodeOrName;

    private String orgName;

    /**
     * 创建用户ID
     */
    private Long currentUserId;

    /**
     * 使用单位
     */
    private Long companyId;

    /**
     * 数据权限类型
     */
    private String permission;

    public String getAttendanceStartDate() {
        return attendanceStartDate;
    }

    public void setAttendanceStartDate(String attendanceStartDate) {
        this.attendanceStartDate = attendanceStartDate;
    }

    public String getAttendanceEndDate() {
        return attendanceEndDate;
    }

    public void setAttendanceEndDate(String attendanceEndDate) {
        this.attendanceEndDate = attendanceEndDate;
    }

    public List<Integer> getProjectStatuses() {
        return projectStatuses;
    }

    public void setProjectStatuses(List<Integer> projectStatuses) {
        this.projectStatuses = projectStatuses;
    }

    public List<Long> getDepartmentIds() {
        return departmentIds;
    }

    public void setDepartmentIds(List<Long> departmentIds) {
        this.departmentIds = departmentIds;
    }

    public List<Long> getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(List<Long> orgIds) {
        this.orgIds = orgIds;
    }

    public List<Long> getUnitIds() {
        return unitIds;
    }

    public void setUnitIds(List<Long> unitIds) {
        this.unitIds = unitIds;
    }

    public String getWorkingHourAccountingStartDate() {
        return workingHourAccountingStartDate;
    }

    public void setWorkingHourAccountingStartDate(String workingHourAccountingStartDate) {
        this.workingHourAccountingStartDate = workingHourAccountingStartDate;
    }

    public String getWorkingHourAccountingEndDate() {
        return workingHourAccountingEndDate;
    }

    public void setWorkingHourAccountingEndDate(String workingHourAccountingEndDate) {
        this.workingHourAccountingEndDate = workingHourAccountingEndDate;
    }

    public List<Long> getOuIds() {
        return ouIds;
    }

    public void setOuIds(List<Long> ouIds) {
        this.ouIds = ouIds;
    }

    public String getProjectCodeOrName() {
        return projectCodeOrName;
    }

    public void setProjectCodeOrName(String projectCodeOrName) {
        this.projectCodeOrName = projectCodeOrName;
    }

    public Long getCurrentUserId() {
        return currentUserId;
    }

    public void setCurrentUserId(Long currentUserId) {
        this.currentUserId = currentUserId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}
