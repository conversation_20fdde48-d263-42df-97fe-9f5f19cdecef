package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportProjectContractReceivableExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportProjectContractReceivableExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameIsNull() {
            addCriterion("project_type_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameIsNotNull() {
            addCriterion("project_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameEqualTo(String value) {
            addCriterion("project_type_name =", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotEqualTo(String value) {
            addCriterion("project_type_name <>", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameGreaterThan(String value) {
            addCriterion("project_type_name >", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_type_name >=", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameLessThan(String value) {
            addCriterion("project_type_name <", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameLessThanOrEqualTo(String value) {
            addCriterion("project_type_name <=", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameLike(String value) {
            addCriterion("project_type_name like", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotLike(String value) {
            addCriterion("project_type_name not like", value, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameIn(List<String> values) {
            addCriterion("project_type_name in", values, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotIn(List<String> values) {
            addCriterion("project_type_name not in", values, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameBetween(String value1, String value2) {
            addCriterion("project_type_name between", value1, value2, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNameNotBetween(String value1, String value2) {
            addCriterion("project_type_name not between", value1, value2, "projectTypeName");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNull() {
            addCriterion("project_status is null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNotNull() {
            addCriterion("project_status is not null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusEqualTo(String value) {
            addCriterion("project_status =", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotEqualTo(String value) {
            addCriterion("project_status <>", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThan(String value) {
            addCriterion("project_status >", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThanOrEqualTo(String value) {
            addCriterion("project_status >=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThan(String value) {
            addCriterion("project_status <", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThanOrEqualTo(String value) {
            addCriterion("project_status <=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLike(String value) {
            addCriterion("project_status like", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotLike(String value) {
            addCriterion("project_status not like", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIn(List<String> values) {
            addCriterion("project_status in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotIn(List<String> values) {
            addCriterion("project_status not in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusBetween(String value1, String value2) {
            addCriterion("project_status between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotBetween(String value1, String value2) {
            addCriterion("project_status not between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andContractIdsIsNull() {
            addCriterion("contract_ids is null");
            return (Criteria) this;
        }

        public Criteria andContractIdsIsNotNull() {
            addCriterion("contract_ids is not null");
            return (Criteria) this;
        }

        public Criteria andContractIdsEqualTo(String value) {
            addCriterion("contract_ids =", value, "contractIds");
            return (Criteria) this;
        }

        public Criteria andContractIdsNotEqualTo(String value) {
            addCriterion("contract_ids <>", value, "contractIds");
            return (Criteria) this;
        }

        public Criteria andContractIdsGreaterThan(String value) {
            addCriterion("contract_ids >", value, "contractIds");
            return (Criteria) this;
        }

        public Criteria andContractIdsGreaterThanOrEqualTo(String value) {
            addCriterion("contract_ids >=", value, "contractIds");
            return (Criteria) this;
        }

        public Criteria andContractIdsLessThan(String value) {
            addCriterion("contract_ids <", value, "contractIds");
            return (Criteria) this;
        }

        public Criteria andContractIdsLessThanOrEqualTo(String value) {
            addCriterion("contract_ids <=", value, "contractIds");
            return (Criteria) this;
        }

        public Criteria andContractIdsLike(String value) {
            addCriterion("contract_ids like", value, "contractIds");
            return (Criteria) this;
        }

        public Criteria andContractIdsNotLike(String value) {
            addCriterion("contract_ids not like", value, "contractIds");
            return (Criteria) this;
        }

        public Criteria andContractIdsIn(List<String> values) {
            addCriterion("contract_ids in", values, "contractIds");
            return (Criteria) this;
        }

        public Criteria andContractIdsNotIn(List<String> values) {
            addCriterion("contract_ids not in", values, "contractIds");
            return (Criteria) this;
        }

        public Criteria andContractIdsBetween(String value1, String value2) {
            addCriterion("contract_ids between", value1, value2, "contractIds");
            return (Criteria) this;
        }

        public Criteria andContractIdsNotBetween(String value1, String value2) {
            addCriterion("contract_ids not between", value1, value2, "contractIds");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNull() {
            addCriterion("customer_code is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNotNull() {
            addCriterion("customer_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeEqualTo(String value) {
            addCriterion("customer_code =", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotEqualTo(String value) {
            addCriterion("customer_code <>", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThan(String value) {
            addCriterion("customer_code >", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("customer_code >=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThan(String value) {
            addCriterion("customer_code <", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThanOrEqualTo(String value) {
            addCriterion("customer_code <=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLike(String value) {
            addCriterion("customer_code like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotLike(String value) {
            addCriterion("customer_code not like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIn(List<String> values) {
            addCriterion("customer_code in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotIn(List<String> values) {
            addCriterion("customer_code not in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBetween(String value1, String value2) {
            addCriterion("customer_code between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotBetween(String value1, String value2) {
            addCriterion("customer_code not between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentIsNull() {
            addCriterion("business_segment is null");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentIsNotNull() {
            addCriterion("business_segment is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentEqualTo(String value) {
            addCriterion("business_segment =", value, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentNotEqualTo(String value) {
            addCriterion("business_segment <>", value, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentGreaterThan(String value) {
            addCriterion("business_segment >", value, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentGreaterThanOrEqualTo(String value) {
            addCriterion("business_segment >=", value, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentLessThan(String value) {
            addCriterion("business_segment <", value, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentLessThanOrEqualTo(String value) {
            addCriterion("business_segment <=", value, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentLike(String value) {
            addCriterion("business_segment like", value, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentNotLike(String value) {
            addCriterion("business_segment not like", value, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentIn(List<String> values) {
            addCriterion("business_segment in", values, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentNotIn(List<String> values) {
            addCriterion("business_segment not in", values, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentBetween(String value1, String value2) {
            addCriterion("business_segment between", value1, value2, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andBusinessSegmentNotBetween(String value1, String value2) {
            addCriterion("business_segment not between", value1, value2, "businessSegment");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNull() {
            addCriterion("ou_name is null");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNotNull() {
            addCriterion("ou_name is not null");
            return (Criteria) this;
        }

        public Criteria andOuNameEqualTo(String value) {
            addCriterion("ou_name =", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotEqualTo(String value) {
            addCriterion("ou_name <>", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThan(String value) {
            addCriterion("ou_name >", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThanOrEqualTo(String value) {
            addCriterion("ou_name >=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThan(String value) {
            addCriterion("ou_name <", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThanOrEqualTo(String value) {
            addCriterion("ou_name <=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLike(String value) {
            addCriterion("ou_name like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotLike(String value) {
            addCriterion("ou_name not like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameIn(List<String> values) {
            addCriterion("ou_name in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotIn(List<String> values) {
            addCriterion("ou_name not in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameBetween(String value1, String value2) {
            addCriterion("ou_name between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotBetween(String value1, String value2) {
            addCriterion("ou_name not between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andIndustryIsNull() {
            addCriterion("industry is null");
            return (Criteria) this;
        }

        public Criteria andIndustryIsNotNull() {
            addCriterion("industry is not null");
            return (Criteria) this;
        }

        public Criteria andIndustryEqualTo(String value) {
            addCriterion("industry =", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotEqualTo(String value) {
            addCriterion("industry <>", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryGreaterThan(String value) {
            addCriterion("industry >", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryGreaterThanOrEqualTo(String value) {
            addCriterion("industry >=", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLessThan(String value) {
            addCriterion("industry <", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLessThanOrEqualTo(String value) {
            addCriterion("industry <=", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLike(String value) {
            addCriterion("industry like", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotLike(String value) {
            addCriterion("industry not like", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryIn(List<String> values) {
            addCriterion("industry in", values, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotIn(List<String> values) {
            addCriterion("industry not in", values, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryBetween(String value1, String value2) {
            addCriterion("industry between", value1, value2, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotBetween(String value1, String value2) {
            addCriterion("industry not between", value1, value2, "industry");
            return (Criteria) this;
        }

        public Criteria andSigningCenterIsNull() {
            addCriterion("signing_center is null");
            return (Criteria) this;
        }

        public Criteria andSigningCenterIsNotNull() {
            addCriterion("signing_center is not null");
            return (Criteria) this;
        }

        public Criteria andSigningCenterEqualTo(String value) {
            addCriterion("signing_center =", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterNotEqualTo(String value) {
            addCriterion("signing_center <>", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterGreaterThan(String value) {
            addCriterion("signing_center >", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterGreaterThanOrEqualTo(String value) {
            addCriterion("signing_center >=", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLessThan(String value) {
            addCriterion("signing_center <", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLessThanOrEqualTo(String value) {
            addCriterion("signing_center <=", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLike(String value) {
            addCriterion("signing_center like", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterNotLike(String value) {
            addCriterion("signing_center not like", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterIn(List<String> values) {
            addCriterion("signing_center in", values, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterNotIn(List<String> values) {
            addCriterion("signing_center not in", values, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterBetween(String value1, String value2) {
            addCriterion("signing_center between", value1, value2, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterNotBetween(String value1, String value2) {
            addCriterion("signing_center not between", value1, value2, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andCumulativeRevenueIsNull() {
            addCriterion("cumulative_revenue is null");
            return (Criteria) this;
        }

        public Criteria andCumulativeRevenueIsNotNull() {
            addCriterion("cumulative_revenue is not null");
            return (Criteria) this;
        }

        public Criteria andCumulativeRevenueEqualTo(BigDecimal value) {
            addCriterion("cumulative_revenue =", value, "cumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andCumulativeRevenueNotEqualTo(BigDecimal value) {
            addCriterion("cumulative_revenue <>", value, "cumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andCumulativeRevenueGreaterThan(BigDecimal value) {
            addCriterion("cumulative_revenue >", value, "cumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andCumulativeRevenueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cumulative_revenue >=", value, "cumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andCumulativeRevenueLessThan(BigDecimal value) {
            addCriterion("cumulative_revenue <", value, "cumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andCumulativeRevenueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cumulative_revenue <=", value, "cumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andCumulativeRevenueIn(List<BigDecimal> values) {
            addCriterion("cumulative_revenue in", values, "cumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andCumulativeRevenueNotIn(List<BigDecimal> values) {
            addCriterion("cumulative_revenue not in", values, "cumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andCumulativeRevenueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cumulative_revenue between", value1, value2, "cumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andCumulativeRevenueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cumulative_revenue not between", value1, value2, "cumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andCumulativeTaxInvoicingIsNull() {
            addCriterion("cumulative_tax_invoicing is null");
            return (Criteria) this;
        }

        public Criteria andCumulativeTaxInvoicingIsNotNull() {
            addCriterion("cumulative_tax_invoicing is not null");
            return (Criteria) this;
        }

        public Criteria andCumulativeTaxInvoicingEqualTo(BigDecimal value) {
            addCriterion("cumulative_tax_invoicing =", value, "cumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeTaxInvoicingNotEqualTo(BigDecimal value) {
            addCriterion("cumulative_tax_invoicing <>", value, "cumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeTaxInvoicingGreaterThan(BigDecimal value) {
            addCriterion("cumulative_tax_invoicing >", value, "cumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeTaxInvoicingGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cumulative_tax_invoicing >=", value, "cumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeTaxInvoicingLessThan(BigDecimal value) {
            addCriterion("cumulative_tax_invoicing <", value, "cumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeTaxInvoicingLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cumulative_tax_invoicing <=", value, "cumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeTaxInvoicingIn(List<BigDecimal> values) {
            addCriterion("cumulative_tax_invoicing in", values, "cumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeTaxInvoicingNotIn(List<BigDecimal> values) {
            addCriterion("cumulative_tax_invoicing not in", values, "cumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeTaxInvoicingBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cumulative_tax_invoicing between", value1, value2, "cumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeTaxInvoicingNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cumulative_tax_invoicing not between", value1, value2, "cumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeInvoicingIsNull() {
            addCriterion("cumulative_invoicing is null");
            return (Criteria) this;
        }

        public Criteria andCumulativeInvoicingIsNotNull() {
            addCriterion("cumulative_invoicing is not null");
            return (Criteria) this;
        }

        public Criteria andCumulativeInvoicingEqualTo(BigDecimal value) {
            addCriterion("cumulative_invoicing =", value, "cumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeInvoicingNotEqualTo(BigDecimal value) {
            addCriterion("cumulative_invoicing <>", value, "cumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeInvoicingGreaterThan(BigDecimal value) {
            addCriterion("cumulative_invoicing >", value, "cumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeInvoicingGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cumulative_invoicing >=", value, "cumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeInvoicingLessThan(BigDecimal value) {
            addCriterion("cumulative_invoicing <", value, "cumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeInvoicingLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cumulative_invoicing <=", value, "cumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeInvoicingIn(List<BigDecimal> values) {
            addCriterion("cumulative_invoicing in", values, "cumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeInvoicingNotIn(List<BigDecimal> values) {
            addCriterion("cumulative_invoicing not in", values, "cumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeInvoicingBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cumulative_invoicing between", value1, value2, "cumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeInvoicingNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cumulative_invoicing not between", value1, value2, "cumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andCumulativeRepaymentIsNull() {
            addCriterion("cumulative_repayment is null");
            return (Criteria) this;
        }

        public Criteria andCumulativeRepaymentIsNotNull() {
            addCriterion("cumulative_repayment is not null");
            return (Criteria) this;
        }

        public Criteria andCumulativeRepaymentEqualTo(BigDecimal value) {
            addCriterion("cumulative_repayment =", value, "cumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andCumulativeRepaymentNotEqualTo(BigDecimal value) {
            addCriterion("cumulative_repayment <>", value, "cumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andCumulativeRepaymentGreaterThan(BigDecimal value) {
            addCriterion("cumulative_repayment >", value, "cumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andCumulativeRepaymentGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cumulative_repayment >=", value, "cumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andCumulativeRepaymentLessThan(BigDecimal value) {
            addCriterion("cumulative_repayment <", value, "cumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andCumulativeRepaymentLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cumulative_repayment <=", value, "cumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andCumulativeRepaymentIn(List<BigDecimal> values) {
            addCriterion("cumulative_repayment in", values, "cumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andCumulativeRepaymentNotIn(List<BigDecimal> values) {
            addCriterion("cumulative_repayment not in", values, "cumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andCumulativeRepaymentBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cumulative_repayment between", value1, value2, "cumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andCumulativeRepaymentNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cumulative_repayment not between", value1, value2, "cumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andCumulativeRefundIsNull() {
            addCriterion("cumulative_refund is null");
            return (Criteria) this;
        }

        public Criteria andCumulativeRefundIsNotNull() {
            addCriterion("cumulative_refund is not null");
            return (Criteria) this;
        }

        public Criteria andCumulativeRefundEqualTo(BigDecimal value) {
            addCriterion("cumulative_refund =", value, "cumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andCumulativeRefundNotEqualTo(BigDecimal value) {
            addCriterion("cumulative_refund <>", value, "cumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andCumulativeRefundGreaterThan(BigDecimal value) {
            addCriterion("cumulative_refund >", value, "cumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andCumulativeRefundGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cumulative_refund >=", value, "cumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andCumulativeRefundLessThan(BigDecimal value) {
            addCriterion("cumulative_refund <", value, "cumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andCumulativeRefundLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cumulative_refund <=", value, "cumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andCumulativeRefundIn(List<BigDecimal> values) {
            addCriterion("cumulative_refund in", values, "cumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andCumulativeRefundNotIn(List<BigDecimal> values) {
            addCriterion("cumulative_refund not in", values, "cumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andCumulativeRefundBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cumulative_refund between", value1, value2, "cumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andCumulativeRefundNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cumulative_refund not between", value1, value2, "cumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andContractLiabilitiesIsNull() {
            addCriterion("contract_liabilities is null");
            return (Criteria) this;
        }

        public Criteria andContractLiabilitiesIsNotNull() {
            addCriterion("contract_liabilities is not null");
            return (Criteria) this;
        }

        public Criteria andContractLiabilitiesEqualTo(BigDecimal value) {
            addCriterion("contract_liabilities =", value, "contractLiabilities");
            return (Criteria) this;
        }

        public Criteria andContractLiabilitiesNotEqualTo(BigDecimal value) {
            addCriterion("contract_liabilities <>", value, "contractLiabilities");
            return (Criteria) this;
        }

        public Criteria andContractLiabilitiesGreaterThan(BigDecimal value) {
            addCriterion("contract_liabilities >", value, "contractLiabilities");
            return (Criteria) this;
        }

        public Criteria andContractLiabilitiesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_liabilities >=", value, "contractLiabilities");
            return (Criteria) this;
        }

        public Criteria andContractLiabilitiesLessThan(BigDecimal value) {
            addCriterion("contract_liabilities <", value, "contractLiabilities");
            return (Criteria) this;
        }

        public Criteria andContractLiabilitiesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_liabilities <=", value, "contractLiabilities");
            return (Criteria) this;
        }

        public Criteria andContractLiabilitiesIn(List<BigDecimal> values) {
            addCriterion("contract_liabilities in", values, "contractLiabilities");
            return (Criteria) this;
        }

        public Criteria andContractLiabilitiesNotIn(List<BigDecimal> values) {
            addCriterion("contract_liabilities not in", values, "contractLiabilities");
            return (Criteria) this;
        }

        public Criteria andContractLiabilitiesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_liabilities between", value1, value2, "contractLiabilities");
            return (Criteria) this;
        }

        public Criteria andContractLiabilitiesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_liabilities not between", value1, value2, "contractLiabilities");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsTaxReceivableIsNull() {
            addCriterion("project_accounts_tax_receivable is null");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsTaxReceivableIsNotNull() {
            addCriterion("project_accounts_tax_receivable is not null");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsTaxReceivableEqualTo(BigDecimal value) {
            addCriterion("project_accounts_tax_receivable =", value, "projectAccountsTaxReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsTaxReceivableNotEqualTo(BigDecimal value) {
            addCriterion("project_accounts_tax_receivable <>", value, "projectAccountsTaxReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsTaxReceivableGreaterThan(BigDecimal value) {
            addCriterion("project_accounts_tax_receivable >", value, "projectAccountsTaxReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsTaxReceivableGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_accounts_tax_receivable >=", value, "projectAccountsTaxReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsTaxReceivableLessThan(BigDecimal value) {
            addCriterion("project_accounts_tax_receivable <", value, "projectAccountsTaxReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsTaxReceivableLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_accounts_tax_receivable <=", value, "projectAccountsTaxReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsTaxReceivableIn(List<BigDecimal> values) {
            addCriterion("project_accounts_tax_receivable in", values, "projectAccountsTaxReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsTaxReceivableNotIn(List<BigDecimal> values) {
            addCriterion("project_accounts_tax_receivable not in", values, "projectAccountsTaxReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsTaxReceivableBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_accounts_tax_receivable between", value1, value2, "projectAccountsTaxReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsTaxReceivableNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_accounts_tax_receivable not between", value1, value2, "projectAccountsTaxReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsReceivableIsNull() {
            addCriterion("project_accounts_receivable is null");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsReceivableIsNotNull() {
            addCriterion("project_accounts_receivable is not null");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsReceivableEqualTo(BigDecimal value) {
            addCriterion("project_accounts_receivable =", value, "projectAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsReceivableNotEqualTo(BigDecimal value) {
            addCriterion("project_accounts_receivable <>", value, "projectAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsReceivableGreaterThan(BigDecimal value) {
            addCriterion("project_accounts_receivable >", value, "projectAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsReceivableGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_accounts_receivable >=", value, "projectAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsReceivableLessThan(BigDecimal value) {
            addCriterion("project_accounts_receivable <", value, "projectAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsReceivableLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_accounts_receivable <=", value, "projectAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsReceivableIn(List<BigDecimal> values) {
            addCriterion("project_accounts_receivable in", values, "projectAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsReceivableNotIn(List<BigDecimal> values) {
            addCriterion("project_accounts_receivable not in", values, "projectAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsReceivableBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_accounts_receivable between", value1, value2, "projectAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andProjectAccountsReceivableNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_accounts_receivable not between", value1, value2, "projectAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andBalanceIsNull() {
            addCriterion("balance is null");
            return (Criteria) this;
        }

        public Criteria andBalanceIsNotNull() {
            addCriterion("balance is not null");
            return (Criteria) this;
        }

        public Criteria andBalanceEqualTo(BigDecimal value) {
            addCriterion("balance =", value, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceNotEqualTo(BigDecimal value) {
            addCriterion("balance <>", value, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceGreaterThan(BigDecimal value) {
            addCriterion("balance >", value, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("balance >=", value, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceLessThan(BigDecimal value) {
            addCriterion("balance <", value, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("balance <=", value, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceIn(List<BigDecimal> values) {
            addCriterion("balance in", values, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceNotIn(List<BigDecimal> values) {
            addCriterion("balance not in", values, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("balance between", value1, value2, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("balance not between", value1, value2, "balance");
            return (Criteria) this;
        }

        public Criteria andRuleValueIsNull() {
            addCriterion("rule_value is null");
            return (Criteria) this;
        }

        public Criteria andRuleValueIsNotNull() {
            addCriterion("rule_value is not null");
            return (Criteria) this;
        }

        public Criteria andRuleValueEqualTo(String value) {
            addCriterion("rule_value =", value, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueNotEqualTo(String value) {
            addCriterion("rule_value <>", value, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueGreaterThan(String value) {
            addCriterion("rule_value >", value, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueGreaterThanOrEqualTo(String value) {
            addCriterion("rule_value >=", value, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueLessThan(String value) {
            addCriterion("rule_value <", value, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueLessThanOrEqualTo(String value) {
            addCriterion("rule_value <=", value, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueLike(String value) {
            addCriterion("rule_value like", value, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueNotLike(String value) {
            addCriterion("rule_value not like", value, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueIn(List<String> values) {
            addCriterion("rule_value in", values, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueNotIn(List<String> values) {
            addCriterion("rule_value not in", values, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueBetween(String value1, String value2) {
            addCriterion("rule_value between", value1, value2, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleValueNotBetween(String value1, String value2) {
            addCriterion("rule_value not between", value1, value2, "ruleValue");
            return (Criteria) this;
        }

        public Criteria andRuleScopeIsNull() {
            addCriterion("rule_scope is null");
            return (Criteria) this;
        }

        public Criteria andRuleScopeIsNotNull() {
            addCriterion("rule_scope is not null");
            return (Criteria) this;
        }

        public Criteria andRuleScopeEqualTo(String value) {
            addCriterion("rule_scope =", value, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeNotEqualTo(String value) {
            addCriterion("rule_scope <>", value, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeGreaterThan(String value) {
            addCriterion("rule_scope >", value, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeGreaterThanOrEqualTo(String value) {
            addCriterion("rule_scope >=", value, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeLessThan(String value) {
            addCriterion("rule_scope <", value, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeLessThanOrEqualTo(String value) {
            addCriterion("rule_scope <=", value, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeLike(String value) {
            addCriterion("rule_scope like", value, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeNotLike(String value) {
            addCriterion("rule_scope not like", value, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeIn(List<String> values) {
            addCriterion("rule_scope in", values, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeNotIn(List<String> values) {
            addCriterion("rule_scope not in", values, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeBetween(String value1, String value2) {
            addCriterion("rule_scope between", value1, value2, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleScopeNotBetween(String value1, String value2) {
            addCriterion("rule_scope not between", value1, value2, "ruleScope");
            return (Criteria) this;
        }

        public Criteria andRuleNameIsNull() {
            addCriterion("rule_name is null");
            return (Criteria) this;
        }

        public Criteria andRuleNameIsNotNull() {
            addCriterion("rule_name is not null");
            return (Criteria) this;
        }

        public Criteria andRuleNameEqualTo(String value) {
            addCriterion("rule_name =", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameNotEqualTo(String value) {
            addCriterion("rule_name <>", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameGreaterThan(String value) {
            addCriterion("rule_name >", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameGreaterThanOrEqualTo(String value) {
            addCriterion("rule_name >=", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameLessThan(String value) {
            addCriterion("rule_name <", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameLessThanOrEqualTo(String value) {
            addCriterion("rule_name <=", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameLike(String value) {
            addCriterion("rule_name like", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameNotLike(String value) {
            addCriterion("rule_name not like", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameIn(List<String> values) {
            addCriterion("rule_name in", values, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameNotIn(List<String> values) {
            addCriterion("rule_name not in", values, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameBetween(String value1, String value2) {
            addCriterion("rule_name between", value1, value2, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameNotBetween(String value1, String value2) {
            addCriterion("rule_name not between", value1, value2, "ruleName");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRevenueIsNull() {
            addCriterion("standard_cumulative_revenue is null");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRevenueIsNotNull() {
            addCriterion("standard_cumulative_revenue is not null");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRevenueEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_revenue =", value, "standardCumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRevenueNotEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_revenue <>", value, "standardCumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRevenueGreaterThan(BigDecimal value) {
            addCriterion("standard_cumulative_revenue >", value, "standardCumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRevenueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_revenue >=", value, "standardCumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRevenueLessThan(BigDecimal value) {
            addCriterion("standard_cumulative_revenue <", value, "standardCumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRevenueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_revenue <=", value, "standardCumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRevenueIn(List<BigDecimal> values) {
            addCriterion("standard_cumulative_revenue in", values, "standardCumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRevenueNotIn(List<BigDecimal> values) {
            addCriterion("standard_cumulative_revenue not in", values, "standardCumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRevenueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_cumulative_revenue between", value1, value2, "standardCumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRevenueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_cumulative_revenue not between", value1, value2, "standardCumulativeRevenue");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeTaxInvoicingIsNull() {
            addCriterion("standard_cumulative_tax_invoicing is null");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeTaxInvoicingIsNotNull() {
            addCriterion("standard_cumulative_tax_invoicing is not null");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeTaxInvoicingEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_tax_invoicing =", value, "standardCumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeTaxInvoicingNotEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_tax_invoicing <>", value, "standardCumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeTaxInvoicingGreaterThan(BigDecimal value) {
            addCriterion("standard_cumulative_tax_invoicing >", value, "standardCumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeTaxInvoicingGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_tax_invoicing >=", value, "standardCumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeTaxInvoicingLessThan(BigDecimal value) {
            addCriterion("standard_cumulative_tax_invoicing <", value, "standardCumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeTaxInvoicingLessThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_tax_invoicing <=", value, "standardCumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeTaxInvoicingIn(List<BigDecimal> values) {
            addCriterion("standard_cumulative_tax_invoicing in", values, "standardCumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeTaxInvoicingNotIn(List<BigDecimal> values) {
            addCriterion("standard_cumulative_tax_invoicing not in", values, "standardCumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeTaxInvoicingBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_cumulative_tax_invoicing between", value1, value2, "standardCumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeTaxInvoicingNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_cumulative_tax_invoicing not between", value1, value2, "standardCumulativeTaxInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeInvoicingIsNull() {
            addCriterion("standard_cumulative_invoicing is null");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeInvoicingIsNotNull() {
            addCriterion("standard_cumulative_invoicing is not null");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeInvoicingEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_invoicing =", value, "standardCumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeInvoicingNotEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_invoicing <>", value, "standardCumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeInvoicingGreaterThan(BigDecimal value) {
            addCriterion("standard_cumulative_invoicing >", value, "standardCumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeInvoicingGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_invoicing >=", value, "standardCumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeInvoicingLessThan(BigDecimal value) {
            addCriterion("standard_cumulative_invoicing <", value, "standardCumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeInvoicingLessThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_invoicing <=", value, "standardCumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeInvoicingIn(List<BigDecimal> values) {
            addCriterion("standard_cumulative_invoicing in", values, "standardCumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeInvoicingNotIn(List<BigDecimal> values) {
            addCriterion("standard_cumulative_invoicing not in", values, "standardCumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeInvoicingBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_cumulative_invoicing between", value1, value2, "standardCumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeInvoicingNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_cumulative_invoicing not between", value1, value2, "standardCumulativeInvoicing");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRepaymentIsNull() {
            addCriterion("standard_cumulative_repayment is null");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRepaymentIsNotNull() {
            addCriterion("standard_cumulative_repayment is not null");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRepaymentEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_repayment =", value, "standardCumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRepaymentNotEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_repayment <>", value, "standardCumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRepaymentGreaterThan(BigDecimal value) {
            addCriterion("standard_cumulative_repayment >", value, "standardCumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRepaymentGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_repayment >=", value, "standardCumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRepaymentLessThan(BigDecimal value) {
            addCriterion("standard_cumulative_repayment <", value, "standardCumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRepaymentLessThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_repayment <=", value, "standardCumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRepaymentIn(List<BigDecimal> values) {
            addCriterion("standard_cumulative_repayment in", values, "standardCumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRepaymentNotIn(List<BigDecimal> values) {
            addCriterion("standard_cumulative_repayment not in", values, "standardCumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRepaymentBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_cumulative_repayment between", value1, value2, "standardCumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRepaymentNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_cumulative_repayment not between", value1, value2, "standardCumulativeRepayment");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRefundIsNull() {
            addCriterion("standard_cumulative_refund is null");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRefundIsNotNull() {
            addCriterion("standard_cumulative_refund is not null");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRefundEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_refund =", value, "standardCumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRefundNotEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_refund <>", value, "standardCumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRefundGreaterThan(BigDecimal value) {
            addCriterion("standard_cumulative_refund >", value, "standardCumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRefundGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_refund >=", value, "standardCumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRefundLessThan(BigDecimal value) {
            addCriterion("standard_cumulative_refund <", value, "standardCumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRefundLessThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_cumulative_refund <=", value, "standardCumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRefundIn(List<BigDecimal> values) {
            addCriterion("standard_cumulative_refund in", values, "standardCumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRefundNotIn(List<BigDecimal> values) {
            addCriterion("standard_cumulative_refund not in", values, "standardCumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRefundBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_cumulative_refund between", value1, value2, "standardCumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andStandardCumulativeRefundNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_cumulative_refund not between", value1, value2, "standardCumulativeRefund");
            return (Criteria) this;
        }

        public Criteria andStandardBalanceIsNull() {
            addCriterion("standard_balance is null");
            return (Criteria) this;
        }

        public Criteria andStandardBalanceIsNotNull() {
            addCriterion("standard_balance is not null");
            return (Criteria) this;
        }

        public Criteria andStandardBalanceEqualTo(BigDecimal value) {
            addCriterion("standard_balance =", value, "standardBalance");
            return (Criteria) this;
        }

        public Criteria andStandardBalanceNotEqualTo(BigDecimal value) {
            addCriterion("standard_balance <>", value, "standardBalance");
            return (Criteria) this;
        }

        public Criteria andStandardBalanceGreaterThan(BigDecimal value) {
            addCriterion("standard_balance >", value, "standardBalance");
            return (Criteria) this;
        }

        public Criteria andStandardBalanceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_balance >=", value, "standardBalance");
            return (Criteria) this;
        }

        public Criteria andStandardBalanceLessThan(BigDecimal value) {
            addCriterion("standard_balance <", value, "standardBalance");
            return (Criteria) this;
        }

        public Criteria andStandardBalanceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("standard_balance <=", value, "standardBalance");
            return (Criteria) this;
        }

        public Criteria andStandardBalanceIn(List<BigDecimal> values) {
            addCriterion("standard_balance in", values, "standardBalance");
            return (Criteria) this;
        }

        public Criteria andStandardBalanceNotIn(List<BigDecimal> values) {
            addCriterion("standard_balance not in", values, "standardBalance");
            return (Criteria) this;
        }

        public Criteria andStandardBalanceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_balance between", value1, value2, "standardBalance");
            return (Criteria) this;
        }

        public Criteria andStandardBalanceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("standard_balance not between", value1, value2, "standardBalance");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}