package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.ReportProjectBudgetManager;
import io.swagger.annotations.ApiModel;

import java.util.List;


/**
 * 创建时间 2017-11-14 8:32
 *
 * <AUTHOR>
 */
@ApiModel(value = "ProjectBudgetManagerDto", description = "项目预算管理")
public class ProjectBudgetManagerDto extends ReportProjectBudgetManager {

    private String statusStr;

    private String codeOrName;

    private String ouIdStr;

    private String managerName;

    private List<Integer> statuses;

    private List<Long> ouIds;

    private List<Long> data;

    private Long personal;

    private Long companyId;

    private List<Long> projectIds;

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public String getCodeOrName() {
        return codeOrName;
    }

    public void setCodeOrName(String codeOrName) {
        this.codeOrName = codeOrName;
    }

    public String getOuIdStr() {
        return ouIdStr;
    }

    public void setOuIdStr(String ouIdStr) {
        this.ouIdStr = ouIdStr;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public List<Integer> getStatuses() {
        return statuses;
    }

    public void setStatuses(List<Integer> statuses) {
        this.statuses = statuses;
    }

    public List<Long> getOuIds() {
        return ouIds;
    }

    public void setOuIds(List<Long> ouIds) {
        this.ouIds = ouIds;
    }

    public List<Long> getData() {
        return data;
    }

    public void setData(List<Long> data) {
        this.data = data;
    }

    public Long getPersonal() {
        return personal;
    }

    public void setPersonal(Long personal) {
        this.personal = personal;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public List<Long> getProjectIds() {
        return projectIds;
    }

    public void setProjectIds(List<Long> projectIds) {
        this.projectIds = projectIds;
    }
}
