package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: common-module
 * @description: 工时成本统计导出明细实体类
 * @author:zhongpeng
 * @create:2020-03-20 17:28
 **/
@Getter
@Setter
public class LaborCostDetailExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer num;

    @Excel(name = "审批月份", width = 20)
    private String approveMonth;

    @Excel(name = "出勤月份", width = 20)
    private String applyMonth;

    @Excel(name = "项目编号", width = 30)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "类型", width = 20, replace = {"内部_1", "人力点工_2", "自招外包_3"})
    private Integer userType;

    @Excel(name = "姓名", width = 20)
    private String userName;

    @Excel(name = "MIP账号", width = 20)
    private String mipName;

    @Excel(name = "部门", width = 20)
    private String organizationName;

    @Excel(name = "项目角色", width = 30)
    private String projectRole;

    @Excel(name = "出勤日期", width = 30, format = "yyyy-MM-dd")
    private Date applyDate;

    @Excel(name = "填报日期", width = 30, format = "yyyy-MM-dd")
    private Date fillInDate;

    @Excel(name = "审批日期", width = 30, format = "yyyy-MM-dd")
    private Date systemApplyDate;

    @Excel(name = "工时数(H)", width = 30)
    private BigDecimal actualWorkingHours;

    @Excel(name = "实际费率(天)", width = 30)
    private BigDecimal costMoney;

    @Excel(name = "人力成本", width = 30)
    private BigDecimal costTotal;

    @Excel(name = "币种", width = 10)
    private String currency;

    @Excel(name = "业务分类", width = 30)
    private String typeName;

    @Excel(name = "业务实体", width = 30)
    private String ouName;

    @Excel(name = "工时结转科目", width = 40)
    private String hardWorking;

    private String projectType;

    private Boolean isCrossUnitLaborCost = false;

    @Excel(name = "是否跨单位工时", width = 30)
    private String isCrossUnitLaborCostStr;
}
