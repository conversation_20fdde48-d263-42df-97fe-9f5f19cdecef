package com.midea.pam.common.statistics.excelVo;

import com.midea.pam.common.util.BigDecimalUtils;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-05-10
 * 物料历史价格表ExcelVO
 */
@Getter
@Setter
public class MaterialHistoryPriceExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer number;
    
    @Excel(name = "PAM编码", width = 20)
    private String pamCode;

    @Excel(name = "ERP编码", width = 20)
    private String itemCode;

    @Excel(name = "物料描述", width = 30)
    private String itemInfo;

    @Excel(name = "基本计量单位", width = 10)
    private String unit;

    @Excel(name = "单价", width = 15)
    private String priceStr;

    private BigDecimal price;

    @Excel(name = "币种", width = 10)
    private String currency;

    @Excel(name = "下单日期", width = 15, format = "yyyy-MM-dd")
    private Date orderDate;

    @Excel(name = "单据号", width = 20)
    private String code;

    @Excel(name = "物料大类", width = 20)
    private String materialClassification;

    @Excel(name = "物料中类", width = 20)
    private String codingMiddleclass;

    @Excel(name = "物料小类", width = 20)
    private String materialType;

    @Excel(name = "物料名称", width = 20)
    private String name;

    @Excel(name = "型号/规格", width = 20)
    private String model;

    @Excel(name = "品牌", width = 20)
    private String brand;

    @Excel(name = "图号", width = 20)
    private String figureNumber;

    @Excel(name = "图纸版本号", width = 20)
    private String chartVersion;

    @Excel(name = "加工分类", width = 15)
    private String machiningPartType;

    @Excel(name = "材质", width = 15)
    private String material;

    @Excel(name = "单位重量(Kg)", width = 15)
    private BigDecimal unitWeight;

    @Excel(name = "表面处理", width = 20)
    private String materialProcessing;

    @Excel(name = "品牌商物料编码", width = 20)
    private String brandMaterialCode;


    public BigDecimal getPrice() {
        return BigDecimalUtils.stripTrailingZeros(price);
    }

    public BigDecimal getUnitWeight() {
        return BigDecimalUtils.scale(unitWeight);
    }

}