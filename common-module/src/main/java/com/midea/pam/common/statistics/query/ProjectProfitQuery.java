package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;


/**
 * 创建时间 2020-5-11
 *
 * <AUTHOR>
 */
@ApiModel(value = "ProjectProfitQuery", description = "项目盈利报表查询参数")
public class ProjectProfitQuery {

    private Long id;

    @ApiModelProperty(value = "项目状态，必填，不为空时，只查项目状态属于此列表中的项目")
    private List<Long> projectStatuss;

    @ApiModelProperty(value = "项目类型，不为空时，只查项目类型属于此列表中的项目")
    private List<Long> projectTypes;

    @ApiModelProperty(value = "项目属性，不为空时，只查项目属性属于此列表中的项目")
    private List<String> projectAttribute;

    @ApiModelProperty(value = "事业部ID列表，不为空时，只查事业部属于此列表中的项目")
    private List<Long> departmentIds;

    @ApiModelProperty(value = "业务部门ID列表，不为空时，只查业务部门属于此列表中的项目")
    private List<Long> unitIds;

    @ApiModelProperty(value = "项目经理ID列表，不为空时，只查项目经理属于此列表中的项目")
    private List<Long> managerIds; // "395623249611849728"

    @ApiModelProperty(value = "项目开始日期，不可空，只查找项目开始日期大于或等于此日期的项目")
    private Date projectDateBegin;

    @ApiModelProperty(value = "项目结束日期，不可空, 只查找项目结束日期小于或等于此日期的项目")
    private Date projectDateEnd;

    @ApiModelProperty(value = "业务实体ID列表，不为空时，只查项目实体属于此列表中的项目")
    private List<Long> ouIds; // "395623249611849728"

    private Long personal;
    private Long companyId;
    private Long reportId;
    private Long executeId;
    private Long createBy;

    // 费用类型code，fee_type表中的fee_type_code字段值
    private String feeTypeCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public List<Long> getProjectStatuss() {
        return projectStatuss;
    }

    public void setProjectStatuss(List<Long> projectStatuss) {
        this.projectStatuss = projectStatuss;
    }

    public List<Long> getProjectTypes() {
        return projectTypes;
    }

    public void setProjectTypes(List<Long> projectTypes) {
        this.projectTypes = projectTypes;
    }

    public List<String> getProjectAttribute() {
        return projectAttribute;
    }

    public void setProjectAttribute(List<String> projectAttribute) {
        this.projectAttribute = projectAttribute;
    }

    public List<Long> getDepartmentIds() {
        return departmentIds;
    }

    public void setDepartmentIds(List<Long> departmentIds) {
        this.departmentIds = departmentIds;
    }

    public List<Long> getUnitIds() {
        return unitIds;
    }

    public void setUnitIds(List<Long> unitIds) {
        this.unitIds = unitIds;
    }

    public List<Long> getManagerIds() {
        return managerIds;
    }

    public void setManagerIds(List<Long> managerIds) {
        this.managerIds = managerIds;
    }

    public Date getProjectDateBegin() {
        return projectDateBegin;
    }

    public void setProjectDateBegin(Date projectDateBegin) {
        this.projectDateBegin = projectDateBegin;
    }

    public Date getProjectDateEnd() {
        return projectDateEnd;
    }

    public void setProjectDateEnd(Date projectDateEnd) {
        this.projectDateEnd = projectDateEnd;
    }

    public List<Long> getOuIds() {
        return ouIds;
    }

    public void setOuIds(List<Long> ouIds) {
        this.ouIds = ouIds;
    }

    public Long getPersonal() {
        return personal;
    }

    public void setPersonal(Long personal) {
        this.personal = personal;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public String getFeeTypeCode() {
        return feeTypeCode;
    }

    public void setFeeTypeCode(String feeTypeCode) {
        this.feeTypeCode = feeTypeCode;
    }

    @Override
    public String toString() {
        return "ProjectProfitQuery{" +
                "id=" + id +
                ", projectStatuss=" + projectStatuss +
                ", projectTypes=" + projectTypes +
                ", projectAttribute=" + projectAttribute +
                ", departmentIds=" + departmentIds +
                ", unitIds=" + unitIds +
                ", managerIds=" + managerIds +
                ", projectDateBegin=" + projectDateBegin +
                ", projectDateEnd=" + projectDateEnd +
                ", ouIds=" + ouIds +
                ", personal=" + personal +
                ", companyId=" + companyId +
                ", reportId=" + reportId +
                ", executeId=" + executeId +
                ", createBy=" + createBy +
                ", feeTypeCode='" + feeTypeCode + '\'' +
                '}';
    }
}
