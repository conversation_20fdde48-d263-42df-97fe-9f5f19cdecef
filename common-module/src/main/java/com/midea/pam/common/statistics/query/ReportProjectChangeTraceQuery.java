package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

@ApiModel(value = "ReportProjectChangeTraceQuery", description = "项目变更跟踪汇总表")
public class ReportProjectChangeTraceQuery {

    private Long id;

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    @ApiModelProperty(value = "报表ID")
    private Long reportId;

    @ApiModelProperty(value = "项目审批通过截止时间")
    private Date expireDate;

    @ApiModelProperty(value = "业务实体id列表")
    private List<Long> ouIdList;

    @ApiModelProperty(value = "项目类型")
    private List<Long> projectTypeList;

    @ApiModelProperty(value = "项目状态")
    private List<Integer> projectStatusList;

    @ApiModelProperty(value = "销售部门id列表，业务分类")
    private List<Long> unitIdList;

    @ApiModelProperty(value = "项目经理")
    private String managerName;

    @ApiModelProperty(value = "项目经理")
    private Long managerId;

    private Long personal;
    private Long companyId;
    private Long createBy;

    private Long projectManager;

    public Long getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(Long projectManager) {
        this.projectManager = projectManager;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public List<Long> getOuIdList() {
        return ouIdList;
    }

    public void setOuIdList(List<Long> ouIdList) {
        this.ouIdList = ouIdList;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public List<Long> getProjectTypeList() {
        return projectTypeList;
    }

    public void setProjectTypeList(List<Long> projectTypeList) {
        this.projectTypeList = projectTypeList;
    }

    public List<Integer> getProjectStatusList() {
        return projectStatusList;
    }

    public void setProjectStatusList(List<Integer> projectStatusList) {
        this.projectStatusList = projectStatusList;
    }

    public List<Long> getUnitIdList() {
        return unitIdList;
    }

    public void setUnitIdList(List<Long> unitIdList) {
        this.unitIdList = unitIdList;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public Long getManagerId() {
        return managerId;
    }

    public void setManagerId(Long managerId) {
        this.managerId = managerId;
    }

    public Long getPersonal() {
        return personal;
    }

    public void setPersonal(Long personal) {
        this.personal = personal;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    @Override
    public String toString() {
        return "ReportProjectChangeTraceQuery{" +
                "id=" + id +
                ", executeId=" + executeId +
                ", reportId=" + reportId +
                ", expireDate=" + expireDate +
                ", ouIdList=" + ouIdList +
                ", projectTypeList=" + projectTypeList +
                ", projectStatusList=" + projectStatusList +
                ", unitIdList=" + unitIdList +
                ", managerName='" + managerName + '\'' +
                ", managerId=" + managerId +
                ", personal=" + personal +
                ", companyId=" + companyId +
                ", createBy=" + createBy +
                '}';
    }
}
