package com.midea.pam.common.statistics.dto;

/**
 * <AUTHOR>
 * @date 2020/9/18
 * @description 收入预测项目经理清单汇总
 */
public class IncomeCalculateProjectSummaryDTO {

    private Long departmentId;

    private String departmentName;

    private Long productOrgId;

    private String productOrgName;

    /**
     * 已提交
     */
    private int committedNum = 0;

    /**
     * 未提交
     */
    private int notCommittedNum = 0;

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Long getProductOrgId() {
        return productOrgId;
    }

    public void setProductOrgId(Long productOrgId) {
        this.productOrgId = productOrgId;
    }

    public String getProductOrgName() {
        return productOrgName;
    }

    public void setProductOrgName(String productOrgName) {
        this.productOrgName = productOrgName;
    }

    public int getCommittedNum() {
        return committedNum;
    }

    public void setCommittedNum(int committedNum) {
        this.committedNum = committedNum;
    }

    public int getNotCommittedNum() {
        return notCommittedNum;
    }

    public void setNotCommittedNum(int notCommittedNum) {
        this.notCommittedNum = notCommittedNum;
    }
}
