package com.midea.pam.common.statistics.query;

import com.midea.pam.common.statistics.entity.ReportContractProcess;

import java.util.Date;
import java.util.List;

public class ReportContractProcessQuery extends ReportContractProcess {

    private Date processDateBegin;

    private Date processDateEnd;

    private List<Long> saleDepartments;

    private Long personal;

    private Long companyId;

    public Date getProcessDateBegin() {
        return processDateBegin;
    }

    public void setProcessDateBegin(Date processDateBegin) {
        this.processDateBegin = processDateBegin;
    }

    public Date getProcessDateEnd() {
        return processDateEnd;
    }

    public void setProcessDateEnd(Date processDateEnd) {
        this.processDateEnd = processDateEnd;
    }

    public List<Long> getSaleDepartments() {
        return saleDepartments;
    }

    public void setSaleDepartments(List<Long> saleDepartments) {
        this.saleDepartments = saleDepartments;
    }

    public Long getPersonal() {
        return personal;
    }

    public void setPersonal(Long personal) {
        this.personal = personal;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}