package com.midea.pam.common.statistics.excelVo;

/**
 * Description 应收到期日期明细报表
 * Created by chenchong
 * Date 2023/05/06 15:52
 */

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * Description 应收到期日期明细报表（开票申请启用账期）
 * Created by chenchong
 * Date 2023/05/06 15:52
 */

@Getter
@Setter
public class FinanceAgesReceivableTermDetailExcelVO {

    @Excel(name = "子合同编号", width = 15)
    private String code;

    @Excel(name = "子合同名称", width = 30)
    private String name;

    @Excel(name = "主合同编号", width = 15)
    private String parentCode;

    @Excel(name = "主合同名称", width = 30)
    private String parentName;

    @Excel(name = "业务实体", width = 30)
    private String ouName;

    @Excel(name = "销售部门", width = 20)
    private String unitName;

    @Excel(name = "业务模式", width = 20)
    private String projectType;

    @Excel(name = "客户CRM编码", width = 30)
    private String customerCode;

    @Excel(name = "客户名称", width = 30)
    private String customerName;

    @Excel(name = "客户属性", width = 10, replace = {"外部客户_0", "内部客户_1", "_null"})
    private Integer customerType;

    @Excel(name = "项目编号", width = 15)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "项目经理", width = 15)
    private String projectManager;

    @Excel(name = "销售经理", width = 15)
    private String salesManager;

    @Excel(name = "币种", width = 10)
    private String currency;

    private BigDecimal amount;

    private BigDecimal excludingTaxAmount;

    private BigDecimal conversionRate;

    private BigDecimal standardAmount;

    private BigDecimal standardExcludingTaxAmount;

    private BigDecimal invoiceAmount;

    private BigDecimal receiptAmount;

    @Excel(name = "子合同含税金额（原币）", width = 20)
    private String amount_dt;

    @Excel(name = "子合同不含税金额（原币）", width = 20)
    private String excludingTaxAmount_dt;

    @Excel(name = "子合同汇率", width = 10)
    private String conversionRate_dt;

    @Excel(name = "子合同含税金额（本位币）", width = 20)
    private String standardAmount_dt;

    @Excel(name = "子合同不含税金额（本位币）", width = 20)
    private String standardExcludingTaxAmount_dt;

    @Excel(name = "子合同已开票金额（原币）", width = 20)
    private String invoiceAmount_dt;

    @Excel(name = "子合同已收款金额（原币）", width = 20)
    private String receiptAmount_dt;

    /*@Excel(name = "开票计划", width = 20)
    private String invoicePlanDetailCode;*/

    @Excel(name = "开票申请", width = 20)
    private String invoiceApplyCode;

    @Excel(name = "开票申请人", width = 20)
    private String invoiceApplyCreateUserName;

    @Excel(name = "开票申请审批通过时间", width = 20, format = "yyyy/MM/dd")
    private Date invoiceApplyApprovedAt;

    @Excel(name = "应收发票号", width = 15)
    private String invoiceCode;

    @Excel(name = "开票条件", width = 30)
    private String requirement;

    private BigDecimal receivableAmount;

    private BigDecimal claimMount;

    private BigDecimal overageAmount;

    @Excel(name = "发票金额(原币，含税金额)", width = 20)
    private String receivableAmount_dt;

    @Excel(name = "已核销金额（原币）", width = 20)
    private String claimMount_dt;

    @Excel(name = "应收发票余额（原币，含税金额)", width = 20)
    private String overageAmount_dt;

    @Excel(name = "应收到期日", width = 20, format = "yyyy/MM/dd")
    private Date expireDate;
    ;

    @Excel(name = "逾期账龄", width = 20)
    private String overdueAges;

    @Excel(name = "截止入账期间", width = 20)
    private String statisticDate;


    public String getAmount_dt() {
        if (Objects.nonNull(this.amount)) {
            return this.amount.stripTrailingZeros().toPlainString();
        } else {
            return this.amount_dt;
        }
    }

    public String getExcludingTaxAmount_dt() {
        if (Objects.nonNull(this.excludingTaxAmount)) {
            return this.excludingTaxAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.excludingTaxAmount_dt;
        }
    }

    public String getConversionRate_dt() {
        if (Objects.nonNull(this.conversionRate)) {
            return this.conversionRate.stripTrailingZeros().toPlainString();
        } else {
            return this.conversionRate_dt;
        }
    }

    public String getStandardAmount_dt() {
        if (Objects.nonNull(this.standardAmount)) {
            return this.standardAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.standardAmount_dt;
        }
    }

    public String getStandardExcludingTaxAmount_dt() {
        if (Objects.nonNull(this.standardExcludingTaxAmount)) {
            return this.standardExcludingTaxAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.standardExcludingTaxAmount_dt;
        }
    }

    public String getInvoiceAmount_dt() {
        if (Objects.nonNull(this.invoiceAmount)) {
            return this.invoiceAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.invoiceAmount_dt;
        }
    }

    public String getReceiptAmount_dt() {
        if (Objects.nonNull(this.receiptAmount)) {
            return this.receiptAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.receiptAmount_dt;
        }
    }

    public String getReceivableAmount_dt() {
        if (Objects.nonNull(this.receivableAmount)) {
            return this.receivableAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.receivableAmount_dt;
        }
    }

    public String getClaimMount_dt() {
        if (Objects.nonNull(this.claimMount)) {
            return this.claimMount.stripTrailingZeros().toPlainString();
        } else {
            return this.claimMount_dt;
        }
    }

    public String getOverageAmount_dt() {
        if (Objects.nonNull(this.overageAmount)) {
            return this.overageAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.overageAmount_dt;
        }
    }

}