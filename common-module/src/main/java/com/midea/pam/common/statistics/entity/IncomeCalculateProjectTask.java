package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class IncomeCalculateProjectTask extends LongIdEntity implements Serializable {
    private Long id;

    private Long calculateId;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private Long projectManager;

    private String projectManagerName;

    private Long projectType;

    private String projectTypeName;

    private String projectPriceType;

    private String currency;

    private Long unitId;

    private String unitName;

    private Long productOrgId;

    private String productOrgName;

    private Long departmentId;

    private String departmentName;

    private Long incomePointId;

    private String incomePointName;

    private Integer incomePointType;

    private Boolean helpFlag;

    private BigDecimal mainIncome;

    private BigDecimal helpIncome;

    private Boolean carryStatus;

    private BigDecimal cost;

    private BigDecimal income;

    private BigDecimal incomeRatio;

    private BigDecimal planIncomeRatio;

    private BigDecimal planIncome;

    private Date endTime;

    private Date actualEndTime;

    private String month;

    private String contractName;

    private String contractCode;

    private Integer status;

    private Boolean deletedFlag;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCalculateId() {
        return calculateId;
    }

    public void setCalculateId(Long calculateId) {
        this.calculateId = calculateId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public Long getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(Long projectManager) {
        this.projectManager = projectManager;
    }

    public String getProjectManagerName() {
        return projectManagerName;
    }

    public void setProjectManagerName(String projectManagerName) {
        this.projectManagerName = projectManagerName == null ? null : projectManagerName.trim();
    }

    public Long getProjectType() {
        return projectType;
    }

    public void setProjectType(Long projectType) {
        this.projectType = projectType;
    }

    public String getProjectTypeName() {
        return projectTypeName;
    }

    public void setProjectTypeName(String projectTypeName) {
        this.projectTypeName = projectTypeName == null ? null : projectTypeName.trim();
    }

    public String getProjectPriceType() {
        return projectPriceType;
    }

    public void setProjectPriceType(String projectPriceType) {
        this.projectPriceType = projectPriceType == null ? null : projectPriceType.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public Long getProductOrgId() {
        return productOrgId;
    }

    public void setProductOrgId(Long productOrgId) {
        this.productOrgId = productOrgId;
    }

    public String getProductOrgName() {
        return productOrgName;
    }

    public void setProductOrgName(String productOrgName) {
        this.productOrgName = productOrgName == null ? null : productOrgName.trim();
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName == null ? null : departmentName.trim();
    }

    public Long getIncomePointId() {
        return incomePointId;
    }

    public void setIncomePointId(Long incomePointId) {
        this.incomePointId = incomePointId;
    }

    public String getIncomePointName() {
        return incomePointName;
    }

    public void setIncomePointName(String incomePointName) {
        this.incomePointName = incomePointName == null ? null : incomePointName.trim();
    }

    public Integer getIncomePointType() {
        return incomePointType;
    }

    public void setIncomePointType(Integer incomePointType) {
        this.incomePointType = incomePointType;
    }

    public Boolean getHelpFlag() {
        return helpFlag;
    }

    public void setHelpFlag(Boolean helpFlag) {
        this.helpFlag = helpFlag;
    }

    public BigDecimal getMainIncome() {
        return mainIncome;
    }

    public void setMainIncome(BigDecimal mainIncome) {
        this.mainIncome = mainIncome;
    }

    public BigDecimal getHelpIncome() {
        return helpIncome;
    }

    public void setHelpIncome(BigDecimal helpIncome) {
        this.helpIncome = helpIncome;
    }

    public Boolean getCarryStatus() {
        return carryStatus;
    }

    public void setCarryStatus(Boolean carryStatus) {
        this.carryStatus = carryStatus;
    }

    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    public BigDecimal getIncome() {
        return income;
    }

    public void setIncome(BigDecimal income) {
        this.income = income;
    }

    public BigDecimal getIncomeRatio() {
        return incomeRatio;
    }

    public void setIncomeRatio(BigDecimal incomeRatio) {
        this.incomeRatio = incomeRatio;
    }

    public BigDecimal getPlanIncomeRatio() {
        return planIncomeRatio;
    }

    public void setPlanIncomeRatio(BigDecimal planIncomeRatio) {
        this.planIncomeRatio = planIncomeRatio;
    }

    public BigDecimal getPlanIncome() {
        return planIncome;
    }

    public void setPlanIncome(BigDecimal planIncome) {
        this.planIncome = planIncome;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getActualEndTime() {
        return actualEndTime;
    }

    public void setActualEndTime(Date actualEndTime) {
        this.actualEndTime = actualEndTime;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month == null ? null : month.trim();
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName == null ? null : contractName.trim();
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode == null ? null : contractCode.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", calculateId=").append(calculateId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", projectManagerName=").append(projectManagerName);
        sb.append(", projectType=").append(projectType);
        sb.append(", projectTypeName=").append(projectTypeName);
        sb.append(", projectPriceType=").append(projectPriceType);
        sb.append(", currency=").append(currency);
        sb.append(", unitId=").append(unitId);
        sb.append(", unitName=").append(unitName);
        sb.append(", productOrgId=").append(productOrgId);
        sb.append(", productOrgName=").append(productOrgName);
        sb.append(", departmentId=").append(departmentId);
        sb.append(", departmentName=").append(departmentName);
        sb.append(", incomePointId=").append(incomePointId);
        sb.append(", incomePointName=").append(incomePointName);
        sb.append(", incomePointType=").append(incomePointType);
        sb.append(", helpFlag=").append(helpFlag);
        sb.append(", mainIncome=").append(mainIncome);
        sb.append(", helpIncome=").append(helpIncome);
        sb.append(", carryStatus=").append(carryStatus);
        sb.append(", cost=").append(cost);
        sb.append(", income=").append(income);
        sb.append(", incomeRatio=").append(incomeRatio);
        sb.append(", planIncomeRatio=").append(planIncomeRatio);
        sb.append(", planIncome=").append(planIncome);
        sb.append(", endTime=").append(endTime);
        sb.append(", actualEndTime=").append(actualEndTime);
        sb.append(", month=").append(month);
        sb.append(", contractName=").append(contractName);
        sb.append(", contractCode=").append(contractCode);
        sb.append(", status=").append(status);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}