package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class WorkingHourExceptionRdm extends LongIdEntity implements Serializable {
    private Long id;

    private Long costCollectionWorkingHourId;

    private Long parentUnitId;

    private String erpMessages;

    private String userName;

    private String userMip;

    private BigDecimal attHour;

    private Date attDate;

    private BigDecimal reportHour;

    private Date reportDate;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private Long workingHourId;

    private String projectManager;

    private BigDecimal checkHour;

    private Date checkDate;

    private BigDecimal confirmHour;

    private Date confirmDate;

    private String docker;

    private BigDecimal dayPrice;

    private String unit;

    private String supplier;

    private String supplierCode;

    private Integer resourceFlag;

    private Long resourceId;

    private Boolean deletedFlag;

    private Date createAt;

    private Long createBy;

    private Date updateAt;

    private Long updateBy;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCostCollectionWorkingHourId() {
        return costCollectionWorkingHourId;
    }

    public void setCostCollectionWorkingHourId(Long costCollectionWorkingHourId) {
        this.costCollectionWorkingHourId = costCollectionWorkingHourId;
    }

    public Long getParentUnitId() {
        return parentUnitId;
    }

    public void setParentUnitId(Long parentUnitId) {
        this.parentUnitId = parentUnitId;
    }

    public String getErpMessages() {
        return erpMessages;
    }

    public void setErpMessages(String erpMessages) {
        this.erpMessages = erpMessages == null ? null : erpMessages.trim();
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public String getUserMip() {
        return userMip;
    }

    public void setUserMip(String userMip) {
        this.userMip = userMip == null ? null : userMip.trim();
    }

    public BigDecimal getAttHour() {
        return attHour;
    }

    public void setAttHour(BigDecimal attHour) {
        this.attHour = attHour;
    }

    public Date getAttDate() {
        return attDate;
    }

    public void setAttDate(Date attDate) {
        this.attDate = attDate;
    }

    public BigDecimal getReportHour() {
        return reportHour;
    }

    public void setReportHour(BigDecimal reportHour) {
        this.reportHour = reportHour;
    }

    public Date getReportDate() {
        return reportDate;
    }

    public void setReportDate(Date reportDate) {
        this.reportDate = reportDate;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public Long getWorkingHourId() {
        return workingHourId;
    }

    public void setWorkingHourId(Long workingHourId) {
        this.workingHourId = workingHourId;
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public BigDecimal getCheckHour() {
        return checkHour;
    }

    public void setCheckHour(BigDecimal checkHour) {
        this.checkHour = checkHour;
    }

    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    public BigDecimal getConfirmHour() {
        return confirmHour;
    }

    public void setConfirmHour(BigDecimal confirmHour) {
        this.confirmHour = confirmHour;
    }

    public Date getConfirmDate() {
        return confirmDate;
    }

    public void setConfirmDate(Date confirmDate) {
        this.confirmDate = confirmDate;
    }

    public String getDocker() {
        return docker;
    }

    public void setDocker(String docker) {
        this.docker = docker == null ? null : docker.trim();
    }

    public BigDecimal getDayPrice() {
        return dayPrice;
    }

    public void setDayPrice(BigDecimal dayPrice) {
        this.dayPrice = dayPrice;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier == null ? null : supplier.trim();
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode == null ? null : supplierCode.trim();
    }

    public Integer getResourceFlag() {
        return resourceFlag;
    }

    public void setResourceFlag(Integer resourceFlag) {
        this.resourceFlag = resourceFlag;
    }

    public Long getResourceId() {
        return resourceId;
    }

    public void setResourceId(Long resourceId) {
        this.resourceId = resourceId;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", costCollectionWorkingHourId=").append(costCollectionWorkingHourId);
        sb.append(", parentUnitId=").append(parentUnitId);
        sb.append(", erpMessages=").append(erpMessages);
        sb.append(", userName=").append(userName);
        sb.append(", userMip=").append(userMip);
        sb.append(", attHour=").append(attHour);
        sb.append(", attDate=").append(attDate);
        sb.append(", reportHour=").append(reportHour);
        sb.append(", reportDate=").append(reportDate);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", workingHourId=").append(workingHourId);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", checkHour=").append(checkHour);
        sb.append(", checkDate=").append(checkDate);
        sb.append(", confirmHour=").append(confirmHour);
        sb.append(", confirmDate=").append(confirmDate);
        sb.append(", docker=").append(docker);
        sb.append(", dayPrice=").append(dayPrice);
        sb.append(", unit=").append(unit);
        sb.append(", supplier=").append(supplier);
        sb.append(", supplierCode=").append(supplierCode);
        sb.append(", resourceFlag=").append(resourceFlag);
        sb.append(", resourceId=").append(resourceId);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", createAt=").append(createAt);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}