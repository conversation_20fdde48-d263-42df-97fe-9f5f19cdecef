package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/9/24
 * @description
 */
public class IncomeCalculateAutoCalculateExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer num;

    @Excel(name = "项目编号", width = 15)
    private String projectCode;

    @Excel(name = "项目名称", width = 15)
    private String projectName;

    @Excel(name = "业务分类", width = 15)
    private String unitName;

    @Excel(name = "项目属性", width = 15, replace = {"内部_1", "外部_2", "研发_3"})
    private Integer projectPriceType;

    @Excel(name = "项目类型", width = 15)
    private String projectTypeName;

    @Excel(name = "项目经理", width = 15)
    private String projectManagerName;

    @Excel(name = "收入节点", width = 15)
    private String incomePointName;

    @Excel(name = "计划收入比例", width = 15, suffix = "%")
    private BigDecimal planIncomeRatio;

    @Excel(name = "预测收入", width = 15)
    private BigDecimal planIncome;

    @Excel(name = "项目金额（不含税）", width = 15)
    private BigDecimal projectAmount;

    @Excel(name = "项目状态", width = 15, replace = {"审批驳回_-2", "财务驳回_-1", "草稿_0", "审批中_3", "项目进行中_4", "项目变更中_9", "结项_10",
            "预立项转正驳回_-3", "预立项审批驳回_-4", "审批撤回_11", "预立项审批撤回_13", "作废_12", "预立项转正审批中_7",
            "预立项转正作废_-13", "预立项转正删除_23", "项目终止审批中_15", "终止_16", "项目流程删除_17"})
    private Integer projectStatus;

    @Excel(name = "关联子合同", width = 15)
    private String contractName;

    @Excel(name = "子合同编号", width = 15)
    private String contractCode;

    @Excel(name = "合同开始日期", width = 15, format = "yyyy-MM-dd")
    private Date contractStartDate;

    @Excel(name = "合同结束日期", width = 15, format = "yyyy-MM-dd")
    private Date contractEndDate;

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectTypeName() {
        return projectTypeName;
    }

    public void setProjectTypeName(String projectTypeName) {
        this.projectTypeName = projectTypeName;
    }

    public String getProjectManagerName() {
        return projectManagerName;
    }

    public void setProjectManagerName(String projectManagerName) {
        this.projectManagerName = projectManagerName;
    }

    public String getIncomePointName() {
        return incomePointName;
    }

    public void setIncomePointName(String incomePointName) {
        this.incomePointName = incomePointName;
    }

    public BigDecimal getPlanIncomeRatio() {
        return planIncomeRatio;
    }

    public void setPlanIncomeRatio(BigDecimal planIncomeRatio) {
        this.planIncomeRatio = planIncomeRatio;
    }

    public BigDecimal getPlanIncome() {
        return planIncome;
    }

    public void setPlanIncome(BigDecimal planIncome) {
        this.planIncome = planIncome;
    }

    public BigDecimal getProjectAmount() {
        return projectAmount;
    }

    public void setProjectAmount(BigDecimal projectAmount) {
        this.projectAmount = projectAmount;
    }

    public Integer getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(Integer projectStatus) {
        this.projectStatus = projectStatus;
    }

    public Date getContractStartDate() {
        return contractStartDate;
    }

    public void setContractStartDate(Date contractStartDate) {
        this.contractStartDate = contractStartDate;
    }

    public Date getContractEndDate() {
        return contractEndDate;
    }

    public void setContractEndDate(Date contractEndDate) {
        this.contractEndDate = contractEndDate;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getProjectPriceType() {
        return projectPriceType;
    }

    public void setProjectPriceType(Integer projectPriceType) {
        this.projectPriceType = projectPriceType;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }
}
