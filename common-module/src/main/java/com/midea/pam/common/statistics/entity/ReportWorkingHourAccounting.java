package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ReportWorkingHourAccounting extends LongIdEntity implements Serializable {
    private Long id;

    private Long executeId;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private Integer projectStatus;

    private Integer projectPriceType;

    private Long projectTypeId;

    private String projectTypeName;

    private Long departmentId;

    private String departmentName;

    private String projectManagerId;

    private String projectManagerName;

    private Long applyUserId;

    private String applyUserName;

    private String applyUserMip;

    private String applyOrg;

    private Date applyDate;

    private Date createDate;

    private BigDecimal applyWorkingHours;

    private Date approveDate;

    private BigDecimal approveWorkingHours;

    private BigDecimal planCostMoney;

    private BigDecimal costMoney;

    private BigDecimal planCostTotal;

    private BigDecimal costTotal;

    private String accountingGlPeriod;

    private BigDecimal carryoverWorkingHours;

    private BigDecimal carryoverLaborCost;

    private String carryoverGlPeriod;

    private BigDecimal ihrAttendHours;

    private BigDecimal rdmWorkingHours;

    private BigDecimal rdmCostMoney;

    private Long unitId;

    private String unitName;

    private Long ouId;

    private String ouName;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public Integer getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(Integer projectStatus) {
        this.projectStatus = projectStatus;
    }

    public Integer getProjectPriceType() {
        return projectPriceType;
    }

    public void setProjectPriceType(Integer projectPriceType) {
        this.projectPriceType = projectPriceType;
    }

    public Long getProjectTypeId() {
        return projectTypeId;
    }

    public void setProjectTypeId(Long projectTypeId) {
        this.projectTypeId = projectTypeId;
    }

    public String getProjectTypeName() {
        return projectTypeName;
    }

    public void setProjectTypeName(String projectTypeName) {
        this.projectTypeName = projectTypeName == null ? null : projectTypeName.trim();
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName == null ? null : departmentName.trim();
    }

    public String getProjectManagerId() {
        return projectManagerId;
    }

    public void setProjectManagerId(String projectManagerId) {
        this.projectManagerId = projectManagerId == null ? null : projectManagerId.trim();
    }

    public String getProjectManagerName() {
        return projectManagerName;
    }

    public void setProjectManagerName(String projectManagerName) {
        this.projectManagerName = projectManagerName == null ? null : projectManagerName.trim();
    }

    public Long getApplyUserId() {
        return applyUserId;
    }

    public void setApplyUserId(Long applyUserId) {
        this.applyUserId = applyUserId;
    }

    public String getApplyUserName() {
        return applyUserName;
    }

    public void setApplyUserName(String applyUserName) {
        this.applyUserName = applyUserName == null ? null : applyUserName.trim();
    }

    public String getApplyUserMip() {
        return applyUserMip;
    }

    public void setApplyUserMip(String applyUserMip) {
        this.applyUserMip = applyUserMip == null ? null : applyUserMip.trim();
    }

    public String getApplyOrg() {
        return applyOrg;
    }

    public void setApplyOrg(String applyOrg) {
        this.applyOrg = applyOrg == null ? null : applyOrg.trim();
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getApplyWorkingHours() {
        return applyWorkingHours;
    }

    public void setApplyWorkingHours(BigDecimal applyWorkingHours) {
        this.applyWorkingHours = applyWorkingHours;
    }

    public Date getApproveDate() {
        return approveDate;
    }

    public void setApproveDate(Date approveDate) {
        this.approveDate = approveDate;
    }

    public BigDecimal getApproveWorkingHours() {
        return approveWorkingHours;
    }

    public void setApproveWorkingHours(BigDecimal approveWorkingHours) {
        this.approveWorkingHours = approveWorkingHours;
    }

    public BigDecimal getPlanCostMoney() {
        return planCostMoney;
    }

    public void setPlanCostMoney(BigDecimal planCostMoney) {
        this.planCostMoney = planCostMoney;
    }

    public BigDecimal getCostMoney() {
        return costMoney;
    }

    public void setCostMoney(BigDecimal costMoney) {
        this.costMoney = costMoney;
    }

    public BigDecimal getPlanCostTotal() {
        return planCostTotal;
    }

    public void setPlanCostTotal(BigDecimal planCostTotal) {
        this.planCostTotal = planCostTotal;
    }

    public BigDecimal getCostTotal() {
        return costTotal;
    }

    public void setCostTotal(BigDecimal costTotal) {
        this.costTotal = costTotal;
    }

    public String getAccountingGlPeriod() {
        return accountingGlPeriod;
    }

    public void setAccountingGlPeriod(String accountingGlPeriod) {
        this.accountingGlPeriod = accountingGlPeriod == null ? null : accountingGlPeriod.trim();
    }

    public BigDecimal getCarryoverWorkingHours() {
        return carryoverWorkingHours;
    }

    public void setCarryoverWorkingHours(BigDecimal carryoverWorkingHours) {
        this.carryoverWorkingHours = carryoverWorkingHours;
    }

    public BigDecimal getCarryoverLaborCost() {
        return carryoverLaborCost;
    }

    public void setCarryoverLaborCost(BigDecimal carryoverLaborCost) {
        this.carryoverLaborCost = carryoverLaborCost;
    }

    public String getCarryoverGlPeriod() {
        return carryoverGlPeriod;
    }

    public void setCarryoverGlPeriod(String carryoverGlPeriod) {
        this.carryoverGlPeriod = carryoverGlPeriod == null ? null : carryoverGlPeriod.trim();
    }

    public BigDecimal getIhrAttendHours() {
        return ihrAttendHours;
    }

    public void setIhrAttendHours(BigDecimal ihrAttendHours) {
        this.ihrAttendHours = ihrAttendHours;
    }

    public BigDecimal getRdmWorkingHours() {
        return rdmWorkingHours;
    }

    public void setRdmWorkingHours(BigDecimal rdmWorkingHours) {
        this.rdmWorkingHours = rdmWorkingHours;
    }

    public BigDecimal getRdmCostMoney() {
        return rdmCostMoney;
    }

    public void setRdmCostMoney(BigDecimal rdmCostMoney) {
        this.rdmCostMoney = rdmCostMoney;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectStatus=").append(projectStatus);
        sb.append(", projectPriceType=").append(projectPriceType);
        sb.append(", projectTypeId=").append(projectTypeId);
        sb.append(", projectTypeName=").append(projectTypeName);
        sb.append(", departmentId=").append(departmentId);
        sb.append(", departmentName=").append(departmentName);
        sb.append(", projectManagerId=").append(projectManagerId);
        sb.append(", projectManagerName=").append(projectManagerName);
        sb.append(", applyUserId=").append(applyUserId);
        sb.append(", applyUserName=").append(applyUserName);
        sb.append(", applyUserMip=").append(applyUserMip);
        sb.append(", applyOrg=").append(applyOrg);
        sb.append(", applyDate=").append(applyDate);
        sb.append(", createDate=").append(createDate);
        sb.append(", applyWorkingHours=").append(applyWorkingHours);
        sb.append(", approveDate=").append(approveDate);
        sb.append(", approveWorkingHours=").append(approveWorkingHours);
        sb.append(", planCostMoney=").append(planCostMoney);
        sb.append(", costMoney=").append(costMoney);
        sb.append(", planCostTotal=").append(planCostTotal);
        sb.append(", costTotal=").append(costTotal);
        sb.append(", accountingGlPeriod=").append(accountingGlPeriod);
        sb.append(", carryoverWorkingHours=").append(carryoverWorkingHours);
        sb.append(", carryoverLaborCost=").append(carryoverLaborCost);
        sb.append(", carryoverGlPeriod=").append(carryoverGlPeriod);
        sb.append(", ihrAttendHours=").append(ihrAttendHours);
        sb.append(", rdmWorkingHours=").append(rdmWorkingHours);
        sb.append(", rdmCostMoney=").append(rdmCostMoney);
        sb.append(", unitId=").append(unitId);
        sb.append(", unitName=").append(unitName);
        sb.append(", ouId=").append(ouId);
        sb.append(", ouName=").append(ouName);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}