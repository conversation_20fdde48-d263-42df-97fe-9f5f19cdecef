package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "采购进展报表")
public class ReportPurchaseProgress extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "项目业务分类")
    private String projectSort;

    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    @ApiModelProperty(value = "采购需求id")
    private Long materialPurchaseRequirementId;

    @ApiModelProperty(value = "需求类型:1：物料采购，2：物料外包(整包)")
    private Integer demandType;

    @ApiModelProperty(value = "需求发布单号")
    private String requirementCode;

    @ApiModelProperty(value = "需求发布审批通过日期")
    private Date approvedTime;

    @ApiModelProperty(value = "采购需求状态:0：待下达,1：已下达,2：已关闭")
    private Integer demandStatus;

    @ApiModelProperty(value = "wbs编码")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "物料编码-PAM编码")
    private String pamCode;

    @ApiModelProperty(value = "物料编码-ERP编码")
    private String erpCode;

    @ApiModelProperty(value = "物料描述")
    private String materielDescr;

    @ApiModelProperty(value = "物料品牌")
    private String brand;

    @ApiModelProperty(value = "图号/型号")
    private String model;

    @ApiModelProperty(value = "物料计量单位")
    private String unit;

    @ApiModelProperty(value = "物料需求总量")
    private BigDecimal needTotal;

    @ApiModelProperty(value = "待下达数量")
    private BigDecimal unreleasedQuantity;

    @ApiModelProperty(value = "已下单数量")
    private BigDecimal releasedQuantity;

    @ApiModelProperty(value = "已关闭需求数量")
    private BigDecimal closedQuantity;

    @ApiModelProperty(value = "需求日期（即计划交货日期）")
    private Date deliveryTime;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "详设发布批次号")
    private String designReleaseLotNumber;

    @ApiModelProperty(value = "设计人员")
    private String producerName;

    @ApiModelProperty(value = "物料类别-大类")
    private String materialClassification;

    @ApiModelProperty(value = "物料类别-中类")
    private String codingMiddleclass;

    @ApiModelProperty(value = "物料类别-小类")
    private String materialType;

    @ApiModelProperty(value = "订单号/合同号")
    private String codes;

    @ApiModelProperty(value = "采购员")
    private String buyer;

    @ApiModelProperty(value = "最迟PO承诺交期")
    private Date lastPoDelivery;

    @ApiModelProperty(value = "最新PO接收时间")
    private Date newestPoReceive;

    @ApiModelProperty(value = "最新PO入库时间")
    private Date newestPoWarehousing;

    @ApiModelProperty(value = "累计接收数量")
    private BigDecimal receiveQuantity;

    @ApiModelProperty(value = "累计入库数量")
    private BigDecimal warehousingQuantity;

    @ApiModelProperty(value = "未送货数量")
    private BigDecimal notDeliveryQuantity;

    @ApiModelProperty(value = "累计开票数量")
    private BigDecimal invoicingQuantity;

    @ApiModelProperty(value = "累计未开票数量")
    private BigDecimal notInvoicingQuantity;

    @ApiModelProperty(value = "是否删除")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType == null ? null : projectType.trim();
    }

    public String getProjectSort() {
        return projectSort;
    }

    public void setProjectSort(String projectSort) {
        this.projectSort = projectSort == null ? null : projectSort.trim();
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public Long getMaterialPurchaseRequirementId() {
        return materialPurchaseRequirementId;
    }

    public void setMaterialPurchaseRequirementId(Long materialPurchaseRequirementId) {
        this.materialPurchaseRequirementId = materialPurchaseRequirementId;
    }

    public Integer getDemandType() {
        return demandType;
    }

    public void setDemandType(Integer demandType) {
        this.demandType = demandType;
    }

    public String getRequirementCode() {
        return requirementCode;
    }

    public void setRequirementCode(String requirementCode) {
        this.requirementCode = requirementCode == null ? null : requirementCode.trim();
    }

    public Date getApprovedTime() {
        return approvedTime;
    }

    public void setApprovedTime(Date approvedTime) {
        this.approvedTime = approvedTime;
    }

    public Integer getDemandStatus() {
        return demandStatus;
    }

    public void setDemandStatus(Integer demandStatus) {
        this.demandStatus = demandStatus;
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode == null ? null : pamCode.trim();
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode == null ? null : erpCode.trim();
    }

    public String getMaterielDescr() {
        return materielDescr;
    }

    public void setMaterielDescr(String materielDescr) {
        this.materielDescr = materielDescr == null ? null : materielDescr.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public BigDecimal getNeedTotal() {
        return needTotal;
    }

    public void setNeedTotal(BigDecimal needTotal) {
        this.needTotal = needTotal;
    }

    public BigDecimal getUnreleasedQuantity() {
        return unreleasedQuantity;
    }

    public void setUnreleasedQuantity(BigDecimal unreleasedQuantity) {
        this.unreleasedQuantity = unreleasedQuantity;
    }

    public BigDecimal getReleasedQuantity() {
        return releasedQuantity;
    }

    public void setReleasedQuantity(BigDecimal releasedQuantity) {
        this.releasedQuantity = releasedQuantity;
    }

    public BigDecimal getClosedQuantity() {
        return closedQuantity;
    }

    public void setClosedQuantity(BigDecimal closedQuantity) {
        this.closedQuantity = closedQuantity;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public String getDesignReleaseLotNumber() {
        return designReleaseLotNumber;
    }

    public void setDesignReleaseLotNumber(String designReleaseLotNumber) {
        this.designReleaseLotNumber = designReleaseLotNumber == null ? null : designReleaseLotNumber.trim();
    }

    public String getProducerName() {
        return producerName;
    }

    public void setProducerName(String producerName) {
        this.producerName = producerName == null ? null : producerName.trim();
    }

    public String getMaterialClassification() {
        return materialClassification;
    }

    public void setMaterialClassification(String materialClassification) {
        this.materialClassification = materialClassification == null ? null : materialClassification.trim();
    }

    public String getCodingMiddleclass() {
        return codingMiddleclass;
    }

    public void setCodingMiddleclass(String codingMiddleclass) {
        this.codingMiddleclass = codingMiddleclass == null ? null : codingMiddleclass.trim();
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType == null ? null : materialType.trim();
    }

    public String getCodes() {
        return codes;
    }

    public void setCodes(String codes) {
        this.codes = codes == null ? null : codes.trim();
    }

    public String getBuyer() {
        return buyer;
    }

    public void setBuyer(String buyer) {
        this.buyer = buyer == null ? null : buyer.trim();
    }

    public Date getLastPoDelivery() {
        return lastPoDelivery;
    }

    public void setLastPoDelivery(Date lastPoDelivery) {
        this.lastPoDelivery = lastPoDelivery;
    }

    public Date getNewestPoReceive() {
        return newestPoReceive;
    }

    public void setNewestPoReceive(Date newestPoReceive) {
        this.newestPoReceive = newestPoReceive;
    }

    public Date getNewestPoWarehousing() {
        return newestPoWarehousing;
    }

    public void setNewestPoWarehousing(Date newestPoWarehousing) {
        this.newestPoWarehousing = newestPoWarehousing;
    }

    public BigDecimal getReceiveQuantity() {
        return receiveQuantity;
    }

    public void setReceiveQuantity(BigDecimal receiveQuantity) {
        this.receiveQuantity = receiveQuantity;
    }

    public BigDecimal getWarehousingQuantity() {
        return warehousingQuantity;
    }

    public void setWarehousingQuantity(BigDecimal warehousingQuantity) {
        this.warehousingQuantity = warehousingQuantity;
    }

    public BigDecimal getNotDeliveryQuantity() {
        return notDeliveryQuantity;
    }

    public void setNotDeliveryQuantity(BigDecimal notDeliveryQuantity) {
        this.notDeliveryQuantity = notDeliveryQuantity;
    }

    public BigDecimal getInvoicingQuantity() {
        return invoicingQuantity;
    }

    public void setInvoicingQuantity(BigDecimal invoicingQuantity) {
        this.invoicingQuantity = invoicingQuantity;
    }

    public BigDecimal getNotInvoicingQuantity() {
        return notInvoicingQuantity;
    }

    public void setNotInvoicingQuantity(BigDecimal notInvoicingQuantity) {
        this.notInvoicingQuantity = notInvoicingQuantity;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectType=").append(projectType);
        sb.append(", projectSort=").append(projectSort);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", materialPurchaseRequirementId=").append(materialPurchaseRequirementId);
        sb.append(", demandType=").append(demandType);
        sb.append(", requirementCode=").append(requirementCode);
        sb.append(", approvedTime=").append(approvedTime);
        sb.append(", demandStatus=").append(demandStatus);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", pamCode=").append(pamCode);
        sb.append(", erpCode=").append(erpCode);
        sb.append(", materielDescr=").append(materielDescr);
        sb.append(", brand=").append(brand);
        sb.append(", model=").append(model);
        sb.append(", unit=").append(unit);
        sb.append(", needTotal=").append(needTotal);
        sb.append(", unreleasedQuantity=").append(unreleasedQuantity);
        sb.append(", releasedQuantity=").append(releasedQuantity);
        sb.append(", closedQuantity=").append(closedQuantity);
        sb.append(", deliveryTime=").append(deliveryTime);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", designReleaseLotNumber=").append(designReleaseLotNumber);
        sb.append(", producerName=").append(producerName);
        sb.append(", materialClassification=").append(materialClassification);
        sb.append(", codingMiddleclass=").append(codingMiddleclass);
        sb.append(", materialType=").append(materialType);
        sb.append(", codes=").append(codes);
        sb.append(", buyer=").append(buyer);
        sb.append(", lastPoDelivery=").append(lastPoDelivery);
        sb.append(", newestPoReceive=").append(newestPoReceive);
        sb.append(", newestPoWarehousing=").append(newestPoWarehousing);
        sb.append(", receiveQuantity=").append(receiveQuantity);
        sb.append(", warehousingQuantity=").append(warehousingQuantity);
        sb.append(", notDeliveryQuantity=").append(notDeliveryQuantity);
        sb.append(", invoicingQuantity=").append(invoicingQuantity);
        sb.append(", notInvoicingQuantity=").append(notInvoicingQuantity);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}