package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/15
 */
@Getter
@Setter
@ToString
@ApiModel(value = "PurchaseContractDebtAgesQuery", description = "采购合同应计负债账龄明细报表")
public class PurchaseContractDebtAgesQuery {

    private Long id;

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    @ApiModelProperty(value = "报表ID")
    private Long reportId;

    @ApiModelProperty(value = "业务实体id列表")
    private List<Long> ouIdList;

    @ApiModelProperty(value = "截止入账日期")
    private Date expireDate;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "是否显示合同进展未开票金额（本位币）为0的数据：true/false")
    private Boolean displayZeroOrNot;

    @ApiModelProperty(value = "账龄规则id")
    private Long agingSegmentHeaderId;

    @ApiModelProperty(value = "重估汇率")
    private BigDecimal evaluateRate;

    @ApiModelProperty(value = "规则范围拼接")
    private String ruleScope;

    @ApiModelProperty(value = "规则名称拼接")
    private String ruleName;

    private Long personal;

    private Long companyId;

    private Long createBy;

}
