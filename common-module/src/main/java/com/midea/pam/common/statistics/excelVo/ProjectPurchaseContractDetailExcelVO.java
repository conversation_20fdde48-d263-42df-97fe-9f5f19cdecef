package com.midea.pam.common.statistics.excelVo;


import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-7-25
 * @description 在途成本-采购合同未进度执行金额
 */
@Getter
@Setter
public class ProjectPurchaseContractDetailExcelVO {

    @Excel(name = "序号", width = 5)
    private Integer number;

    @Excel(name = "WBS", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项", width = 15)
    private String activityCode;

    @Excel(name = "采购合同编号", width = 25)
    private String purchaseContractCode;

    @Excel(name = "物料PAM编码", width = 25)
    private String pamCode;

    @Excel(name = "总价", width = 15)
    private BigDecimal totalPrice;

    @Excel(name = "累计进度执行金额（不含税）", width = 15)
    private BigDecimal budgetExecuteAmountTotal;

    @Excel(name = "采购合同分配金额-进度执行金额", width = 15)
    private BigDecimal allocationExecuteAmount;

    @Excel(name = "合同创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date dataTime;

}
