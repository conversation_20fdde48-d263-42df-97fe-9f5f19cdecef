package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.IncomeCalculateProductTask;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/18
 * @description
 */
public class IncomeCalculateProductTaskDTO extends IncomeCalculateProductTask {
    private String statusesStr;

    private List<Integer> statuses;
    private String incomeCalculateName;

    private String timePeriod;


    public String getIncomeCalculateName() {
        return incomeCalculateName;
    }

    public void setIncomeCalculateName(String incomeCalculateName) {
        this.incomeCalculateName = incomeCalculateName;
    }

    public String getTimePeriod() {
        return timePeriod;
    }

    public void setTimePeriod(String timePeriod) {
        this.timePeriod = timePeriod;
    }

    public String getStatusesStr() {
        return statusesStr;
    }

    public void setStatusesStr(String statusesStr) {
        this.statusesStr = statusesStr;
    }

    public List<Integer> getStatuses() {
        return statuses;
    }

    public void setStatuses(List<Integer> statuses) {
        this.statuses = statuses;
    }
}
