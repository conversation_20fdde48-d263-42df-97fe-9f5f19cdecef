package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目变更基本信息汇总表")
public class ReportProjectChangeBaseInfo extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "变更发起人")
    private String changeSubmitBy;

    @ApiModelProperty(value = "变更日期")
    private Date changeTime;

    @ApiModelProperty(value = "变更原因")
    private String changeReason;

    @ApiModelProperty(value = "变更原因说明")
    private String changeReasonDescribe;

    @ApiModelProperty(value = "变更后名称")
    private String afterName;

    @ApiModelProperty(value = "变更后业务分类")
    private String afterBusinessType;

    @ApiModelProperty(value = "变更后项目开始时间")
    private Date afterStartTime;

    @ApiModelProperty(value = "变更后项目结束时间")
    private Date afterEndTime;

    @ApiModelProperty(value = "变更后项目经理")
    private Long afterManager;

    @ApiModelProperty(value = "变更后财务")
    private Long afterFinancial;

    @ApiModelProperty(value = "变更后销售经理")
    private Long afterSaleManager;

    @ApiModelProperty(value = "变更后方案设计人员")
    private Long afterDesigner;

    @ApiModelProperty(value = "变更后技术负责人员")
    private Long afterTechnology;

    @ApiModelProperty(value = "变更后项目描述")
    private String afterProjectDescribe;

    @ApiModelProperty(value = "变更前业务分类")
    private String beforeBusinessType;

    @ApiModelProperty(value = "变更前项目开始时间")
    private Date beforeStartTime;

    @ApiModelProperty(value = "变更前项目结束时间")
    private Date beforeEndTime;

    @ApiModelProperty(value = "变更前项目经理")
    private Long beforeManager;

    @ApiModelProperty(value = "变更前财务")
    private Long beforeFinancial;

    @ApiModelProperty(value = "变更前项目销售经理")
    private Long beforeSaleManager;

    @ApiModelProperty(value = "变更前方案设计人员")
    private Long beforeDesigner;

    @ApiModelProperty(value = "变更前技术负责人员")
    private Long beforeTechnology;

    @ApiModelProperty(value = "变更前项目描述")
    private String beforeProjectDescribe;

    @ApiModelProperty(value = "是否删除")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "")
    private String afterSaleManagerName;

    @ApiModelProperty(value = "变更后项目经理名称")
    private String afterManagerName;

    @ApiModelProperty(value = "变更后财务人员名称")
    private String afterFinancialName;

    @ApiModelProperty(value = "变更后技术负责人名称")
    private String afterTechnologyName;

    @ApiModelProperty(value = "变更后设计人员名称")
    private String afterDesignerName;

    @ApiModelProperty(value = "")
    private String beforeSaleManagerName;

    @ApiModelProperty(value = "")
    private String beforeManagerName;

    @ApiModelProperty(value = "")
    private String beforeFinancialName;

    @ApiModelProperty(value = "")
    private String beforeDesignerName;

    @ApiModelProperty(value = "")
    private String beforeTechnologyName;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getChangeSubmitBy() {
        return changeSubmitBy;
    }

    public void setChangeSubmitBy(String changeSubmitBy) {
        this.changeSubmitBy = changeSubmitBy == null ? null : changeSubmitBy.trim();
    }

    public Date getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(Date changeTime) {
        this.changeTime = changeTime;
    }

    public String getChangeReason() {
        return changeReason;
    }

    public void setChangeReason(String changeReason) {
        this.changeReason = changeReason == null ? null : changeReason.trim();
    }

    public String getChangeReasonDescribe() {
        return changeReasonDescribe;
    }

    public void setChangeReasonDescribe(String changeReasonDescribe) {
        this.changeReasonDescribe = changeReasonDescribe == null ? null : changeReasonDescribe.trim();
    }

    public String getAfterName() {
        return afterName;
    }

    public void setAfterName(String afterName) {
        this.afterName = afterName == null ? null : afterName.trim();
    }

    public String getAfterBusinessType() {
        return afterBusinessType;
    }

    public void setAfterBusinessType(String afterBusinessType) {
        this.afterBusinessType = afterBusinessType == null ? null : afterBusinessType.trim();
    }

    public Date getAfterStartTime() {
        return afterStartTime;
    }

    public void setAfterStartTime(Date afterStartTime) {
        this.afterStartTime = afterStartTime;
    }

    public Date getAfterEndTime() {
        return afterEndTime;
    }

    public void setAfterEndTime(Date afterEndTime) {
        this.afterEndTime = afterEndTime;
    }

    public Long getAfterManager() {
        return afterManager;
    }

    public void setAfterManager(Long afterManager) {
        this.afterManager = afterManager;
    }

    public Long getAfterFinancial() {
        return afterFinancial;
    }

    public void setAfterFinancial(Long afterFinancial) {
        this.afterFinancial = afterFinancial;
    }

    public Long getAfterSaleManager() {
        return afterSaleManager;
    }

    public void setAfterSaleManager(Long afterSaleManager) {
        this.afterSaleManager = afterSaleManager;
    }

    public Long getAfterDesigner() {
        return afterDesigner;
    }

    public void setAfterDesigner(Long afterDesigner) {
        this.afterDesigner = afterDesigner;
    }

    public Long getAfterTechnology() {
        return afterTechnology;
    }

    public void setAfterTechnology(Long afterTechnology) {
        this.afterTechnology = afterTechnology;
    }

    public String getAfterProjectDescribe() {
        return afterProjectDescribe;
    }

    public void setAfterProjectDescribe(String afterProjectDescribe) {
        this.afterProjectDescribe = afterProjectDescribe == null ? null : afterProjectDescribe.trim();
    }

    public String getBeforeBusinessType() {
        return beforeBusinessType;
    }

    public void setBeforeBusinessType(String beforeBusinessType) {
        this.beforeBusinessType = beforeBusinessType == null ? null : beforeBusinessType.trim();
    }

    public Date getBeforeStartTime() {
        return beforeStartTime;
    }

    public void setBeforeStartTime(Date beforeStartTime) {
        this.beforeStartTime = beforeStartTime;
    }

    public Date getBeforeEndTime() {
        return beforeEndTime;
    }

    public void setBeforeEndTime(Date beforeEndTime) {
        this.beforeEndTime = beforeEndTime;
    }

    public Long getBeforeManager() {
        return beforeManager;
    }

    public void setBeforeManager(Long beforeManager) {
        this.beforeManager = beforeManager;
    }

    public Long getBeforeFinancial() {
        return beforeFinancial;
    }

    public void setBeforeFinancial(Long beforeFinancial) {
        this.beforeFinancial = beforeFinancial;
    }

    public Long getBeforeSaleManager() {
        return beforeSaleManager;
    }

    public void setBeforeSaleManager(Long beforeSaleManager) {
        this.beforeSaleManager = beforeSaleManager;
    }

    public Long getBeforeDesigner() {
        return beforeDesigner;
    }

    public void setBeforeDesigner(Long beforeDesigner) {
        this.beforeDesigner = beforeDesigner;
    }

    public Long getBeforeTechnology() {
        return beforeTechnology;
    }

    public void setBeforeTechnology(Long beforeTechnology) {
        this.beforeTechnology = beforeTechnology;
    }

    public String getBeforeProjectDescribe() {
        return beforeProjectDescribe;
    }

    public void setBeforeProjectDescribe(String beforeProjectDescribe) {
        this.beforeProjectDescribe = beforeProjectDescribe == null ? null : beforeProjectDescribe.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public String getAfterSaleManagerName() {
        return afterSaleManagerName;
    }

    public void setAfterSaleManagerName(String afterSaleManagerName) {
        this.afterSaleManagerName = afterSaleManagerName == null ? null : afterSaleManagerName.trim();
    }

    public String getAfterManagerName() {
        return afterManagerName;
    }

    public void setAfterManagerName(String afterManagerName) {
        this.afterManagerName = afterManagerName == null ? null : afterManagerName.trim();
    }

    public String getAfterFinancialName() {
        return afterFinancialName;
    }

    public void setAfterFinancialName(String afterFinancialName) {
        this.afterFinancialName = afterFinancialName == null ? null : afterFinancialName.trim();
    }

    public String getAfterTechnologyName() {
        return afterTechnologyName;
    }

    public void setAfterTechnologyName(String afterTechnologyName) {
        this.afterTechnologyName = afterTechnologyName == null ? null : afterTechnologyName.trim();
    }

    public String getAfterDesignerName() {
        return afterDesignerName;
    }

    public void setAfterDesignerName(String afterDesignerName) {
        this.afterDesignerName = afterDesignerName == null ? null : afterDesignerName.trim();
    }

    public String getBeforeSaleManagerName() {
        return beforeSaleManagerName;
    }

    public void setBeforeSaleManagerName(String beforeSaleManagerName) {
        this.beforeSaleManagerName = beforeSaleManagerName == null ? null : beforeSaleManagerName.trim();
    }

    public String getBeforeManagerName() {
        return beforeManagerName;
    }

    public void setBeforeManagerName(String beforeManagerName) {
        this.beforeManagerName = beforeManagerName == null ? null : beforeManagerName.trim();
    }

    public String getBeforeFinancialName() {
        return beforeFinancialName;
    }

    public void setBeforeFinancialName(String beforeFinancialName) {
        this.beforeFinancialName = beforeFinancialName == null ? null : beforeFinancialName.trim();
    }

    public String getBeforeDesignerName() {
        return beforeDesignerName;
    }

    public void setBeforeDesignerName(String beforeDesignerName) {
        this.beforeDesignerName = beforeDesignerName == null ? null : beforeDesignerName.trim();
    }

    public String getBeforeTechnologyName() {
        return beforeTechnologyName;
    }

    public void setBeforeTechnologyName(String beforeTechnologyName) {
        this.beforeTechnologyName = beforeTechnologyName == null ? null : beforeTechnologyName.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", changeSubmitBy=").append(changeSubmitBy);
        sb.append(", changeTime=").append(changeTime);
        sb.append(", changeReason=").append(changeReason);
        sb.append(", changeReasonDescribe=").append(changeReasonDescribe);
        sb.append(", afterName=").append(afterName);
        sb.append(", afterBusinessType=").append(afterBusinessType);
        sb.append(", afterStartTime=").append(afterStartTime);
        sb.append(", afterEndTime=").append(afterEndTime);
        sb.append(", afterManager=").append(afterManager);
        sb.append(", afterFinancial=").append(afterFinancial);
        sb.append(", afterSaleManager=").append(afterSaleManager);
        sb.append(", afterDesigner=").append(afterDesigner);
        sb.append(", afterTechnology=").append(afterTechnology);
        sb.append(", afterProjectDescribe=").append(afterProjectDescribe);
        sb.append(", beforeBusinessType=").append(beforeBusinessType);
        sb.append(", beforeStartTime=").append(beforeStartTime);
        sb.append(", beforeEndTime=").append(beforeEndTime);
        sb.append(", beforeManager=").append(beforeManager);
        sb.append(", beforeFinancial=").append(beforeFinancial);
        sb.append(", beforeSaleManager=").append(beforeSaleManager);
        sb.append(", beforeDesigner=").append(beforeDesigner);
        sb.append(", beforeTechnology=").append(beforeTechnology);
        sb.append(", beforeProjectDescribe=").append(beforeProjectDescribe);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", afterSaleManagerName=").append(afterSaleManagerName);
        sb.append(", afterManagerName=").append(afterManagerName);
        sb.append(", afterFinancialName=").append(afterFinancialName);
        sb.append(", afterTechnologyName=").append(afterTechnologyName);
        sb.append(", afterDesignerName=").append(afterDesignerName);
        sb.append(", beforeSaleManagerName=").append(beforeSaleManagerName);
        sb.append(", beforeManagerName=").append(beforeManagerName);
        sb.append(", beforeFinancialName=").append(beforeFinancialName);
        sb.append(", beforeDesignerName=").append(beforeDesignerName);
        sb.append(", beforeTechnologyName=").append(beforeTechnologyName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}