package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportContractProcessExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportContractProcessExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProcessYearIsNull() {
            addCriterion("process_year is null");
            return (Criteria) this;
        }

        public Criteria andProcessYearIsNotNull() {
            addCriterion("process_year is not null");
            return (Criteria) this;
        }

        public Criteria andProcessYearEqualTo(String value) {
            addCriterion("process_year =", value, "processYear");
            return (Criteria) this;
        }

        public Criteria andProcessYearNotEqualTo(String value) {
            addCriterion("process_year <>", value, "processYear");
            return (Criteria) this;
        }

        public Criteria andProcessYearGreaterThan(String value) {
            addCriterion("process_year >", value, "processYear");
            return (Criteria) this;
        }

        public Criteria andProcessYearGreaterThanOrEqualTo(String value) {
            addCriterion("process_year >=", value, "processYear");
            return (Criteria) this;
        }

        public Criteria andProcessYearLessThan(String value) {
            addCriterion("process_year <", value, "processYear");
            return (Criteria) this;
        }

        public Criteria andProcessYearLessThanOrEqualTo(String value) {
            addCriterion("process_year <=", value, "processYear");
            return (Criteria) this;
        }

        public Criteria andProcessYearLike(String value) {
            addCriterion("process_year like", value, "processYear");
            return (Criteria) this;
        }

        public Criteria andProcessYearNotLike(String value) {
            addCriterion("process_year not like", value, "processYear");
            return (Criteria) this;
        }

        public Criteria andProcessYearIn(List<String> values) {
            addCriterion("process_year in", values, "processYear");
            return (Criteria) this;
        }

        public Criteria andProcessYearNotIn(List<String> values) {
            addCriterion("process_year not in", values, "processYear");
            return (Criteria) this;
        }

        public Criteria andProcessYearBetween(String value1, String value2) {
            addCriterion("process_year between", value1, value2, "processYear");
            return (Criteria) this;
        }

        public Criteria andProcessYearNotBetween(String value1, String value2) {
            addCriterion("process_year not between", value1, value2, "processYear");
            return (Criteria) this;
        }

        public Criteria andProcessMonthIsNull() {
            addCriterion("process_month is null");
            return (Criteria) this;
        }

        public Criteria andProcessMonthIsNotNull() {
            addCriterion("process_month is not null");
            return (Criteria) this;
        }

        public Criteria andProcessMonthEqualTo(String value) {
            addCriterion("process_month =", value, "processMonth");
            return (Criteria) this;
        }

        public Criteria andProcessMonthNotEqualTo(String value) {
            addCriterion("process_month <>", value, "processMonth");
            return (Criteria) this;
        }

        public Criteria andProcessMonthGreaterThan(String value) {
            addCriterion("process_month >", value, "processMonth");
            return (Criteria) this;
        }

        public Criteria andProcessMonthGreaterThanOrEqualTo(String value) {
            addCriterion("process_month >=", value, "processMonth");
            return (Criteria) this;
        }

        public Criteria andProcessMonthLessThan(String value) {
            addCriterion("process_month <", value, "processMonth");
            return (Criteria) this;
        }

        public Criteria andProcessMonthLessThanOrEqualTo(String value) {
            addCriterion("process_month <=", value, "processMonth");
            return (Criteria) this;
        }

        public Criteria andProcessMonthLike(String value) {
            addCriterion("process_month like", value, "processMonth");
            return (Criteria) this;
        }

        public Criteria andProcessMonthNotLike(String value) {
            addCriterion("process_month not like", value, "processMonth");
            return (Criteria) this;
        }

        public Criteria andProcessMonthIn(List<String> values) {
            addCriterion("process_month in", values, "processMonth");
            return (Criteria) this;
        }

        public Criteria andProcessMonthNotIn(List<String> values) {
            addCriterion("process_month not in", values, "processMonth");
            return (Criteria) this;
        }

        public Criteria andProcessMonthBetween(String value1, String value2) {
            addCriterion("process_month between", value1, value2, "processMonth");
            return (Criteria) this;
        }

        public Criteria andProcessMonthNotBetween(String value1, String value2) {
            addCriterion("process_month not between", value1, value2, "processMonth");
            return (Criteria) this;
        }

        public Criteria andProcessTypeIsNull() {
            addCriterion("process_type is null");
            return (Criteria) this;
        }

        public Criteria andProcessTypeIsNotNull() {
            addCriterion("process_type is not null");
            return (Criteria) this;
        }

        public Criteria andProcessTypeEqualTo(Integer value) {
            addCriterion("process_type =", value, "processType");
            return (Criteria) this;
        }

        public Criteria andProcessTypeNotEqualTo(Integer value) {
            addCriterion("process_type <>", value, "processType");
            return (Criteria) this;
        }

        public Criteria andProcessTypeGreaterThan(Integer value) {
            addCriterion("process_type >", value, "processType");
            return (Criteria) this;
        }

        public Criteria andProcessTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("process_type >=", value, "processType");
            return (Criteria) this;
        }

        public Criteria andProcessTypeLessThan(Integer value) {
            addCriterion("process_type <", value, "processType");
            return (Criteria) this;
        }

        public Criteria andProcessTypeLessThanOrEqualTo(Integer value) {
            addCriterion("process_type <=", value, "processType");
            return (Criteria) this;
        }

        public Criteria andProcessTypeIn(List<Integer> values) {
            addCriterion("process_type in", values, "processType");
            return (Criteria) this;
        }

        public Criteria andProcessTypeNotIn(List<Integer> values) {
            addCriterion("process_type not in", values, "processType");
            return (Criteria) this;
        }

        public Criteria andProcessTypeBetween(Integer value1, Integer value2) {
            addCriterion("process_type between", value1, value2, "processType");
            return (Criteria) this;
        }

        public Criteria andProcessTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("process_type not between", value1, value2, "processType");
            return (Criteria) this;
        }

        public Criteria andFrameFlagIsNull() {
            addCriterion("frame_flag is null");
            return (Criteria) this;
        }

        public Criteria andFrameFlagIsNotNull() {
            addCriterion("frame_flag is not null");
            return (Criteria) this;
        }

        public Criteria andFrameFlagEqualTo(Integer value) {
            addCriterion("frame_flag =", value, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagNotEqualTo(Integer value) {
            addCriterion("frame_flag <>", value, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagGreaterThan(Integer value) {
            addCriterion("frame_flag >", value, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("frame_flag >=", value, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagLessThan(Integer value) {
            addCriterion("frame_flag <", value, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagLessThanOrEqualTo(Integer value) {
            addCriterion("frame_flag <=", value, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagIn(List<Integer> values) {
            addCriterion("frame_flag in", values, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagNotIn(List<Integer> values) {
            addCriterion("frame_flag not in", values, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagBetween(Integer value1, Integer value2) {
            addCriterion("frame_flag between", value1, value2, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andFrameFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("frame_flag not between", value1, value2, "frameFlag");
            return (Criteria) this;
        }

        public Criteria andProcessTimeIsNull() {
            addCriterion("process_time is null");
            return (Criteria) this;
        }

        public Criteria andProcessTimeIsNotNull() {
            addCriterion("process_time is not null");
            return (Criteria) this;
        }

        public Criteria andProcessTimeEqualTo(Date value) {
            addCriterion("process_time =", value, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeNotEqualTo(Date value) {
            addCriterion("process_time <>", value, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeGreaterThan(Date value) {
            addCriterion("process_time >", value, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("process_time >=", value, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeLessThan(Date value) {
            addCriterion("process_time <", value, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeLessThanOrEqualTo(Date value) {
            addCriterion("process_time <=", value, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeIn(List<Date> values) {
            addCriterion("process_time in", values, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeNotIn(List<Date> values) {
            addCriterion("process_time not in", values, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeBetween(Date value1, Date value2) {
            addCriterion("process_time between", value1, value2, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeNotBetween(Date value1, Date value2) {
            addCriterion("process_time not between", value1, value2, "processTime");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumIsNull() {
            addCriterion("legal_contract_num is null");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumIsNotNull() {
            addCriterion("legal_contract_num is not null");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumEqualTo(String value) {
            addCriterion("legal_contract_num =", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumNotEqualTo(String value) {
            addCriterion("legal_contract_num <>", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumGreaterThan(String value) {
            addCriterion("legal_contract_num >", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumGreaterThanOrEqualTo(String value) {
            addCriterion("legal_contract_num >=", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumLessThan(String value) {
            addCriterion("legal_contract_num <", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumLessThanOrEqualTo(String value) {
            addCriterion("legal_contract_num <=", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumLike(String value) {
            addCriterion("legal_contract_num like", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumNotLike(String value) {
            addCriterion("legal_contract_num not like", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumIn(List<String> values) {
            addCriterion("legal_contract_num in", values, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumNotIn(List<String> values) {
            addCriterion("legal_contract_num not in", values, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumBetween(String value1, String value2) {
            addCriterion("legal_contract_num between", value1, value2, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumNotBetween(String value1, String value2) {
            addCriterion("legal_contract_num not between", value1, value2, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andFrameCodeIsNull() {
            addCriterion("frame_code is null");
            return (Criteria) this;
        }

        public Criteria andFrameCodeIsNotNull() {
            addCriterion("frame_code is not null");
            return (Criteria) this;
        }

        public Criteria andFrameCodeEqualTo(String value) {
            addCriterion("frame_code =", value, "frameCode");
            return (Criteria) this;
        }

        public Criteria andFrameCodeNotEqualTo(String value) {
            addCriterion("frame_code <>", value, "frameCode");
            return (Criteria) this;
        }

        public Criteria andFrameCodeGreaterThan(String value) {
            addCriterion("frame_code >", value, "frameCode");
            return (Criteria) this;
        }

        public Criteria andFrameCodeGreaterThanOrEqualTo(String value) {
            addCriterion("frame_code >=", value, "frameCode");
            return (Criteria) this;
        }

        public Criteria andFrameCodeLessThan(String value) {
            addCriterion("frame_code <", value, "frameCode");
            return (Criteria) this;
        }

        public Criteria andFrameCodeLessThanOrEqualTo(String value) {
            addCriterion("frame_code <=", value, "frameCode");
            return (Criteria) this;
        }

        public Criteria andFrameCodeLike(String value) {
            addCriterion("frame_code like", value, "frameCode");
            return (Criteria) this;
        }

        public Criteria andFrameCodeNotLike(String value) {
            addCriterion("frame_code not like", value, "frameCode");
            return (Criteria) this;
        }

        public Criteria andFrameCodeIn(List<String> values) {
            addCriterion("frame_code in", values, "frameCode");
            return (Criteria) this;
        }

        public Criteria andFrameCodeNotIn(List<String> values) {
            addCriterion("frame_code not in", values, "frameCode");
            return (Criteria) this;
        }

        public Criteria andFrameCodeBetween(String value1, String value2) {
            addCriterion("frame_code between", value1, value2, "frameCode");
            return (Criteria) this;
        }

        public Criteria andFrameCodeNotBetween(String value1, String value2) {
            addCriterion("frame_code not between", value1, value2, "frameCode");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNull() {
            addCriterion("customer_code is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNotNull() {
            addCriterion("customer_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeEqualTo(String value) {
            addCriterion("customer_code =", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotEqualTo(String value) {
            addCriterion("customer_code <>", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThan(String value) {
            addCriterion("customer_code >", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("customer_code >=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThan(String value) {
            addCriterion("customer_code <", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThanOrEqualTo(String value) {
            addCriterion("customer_code <=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLike(String value) {
            addCriterion("customer_code like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotLike(String value) {
            addCriterion("customer_code not like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIn(List<String> values) {
            addCriterion("customer_code in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotIn(List<String> values) {
            addCriterion("customer_code not in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBetween(String value1, String value2) {
            addCriterion("customer_code between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotBetween(String value1, String value2) {
            addCriterion("customer_code not between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeIsNull() {
            addCriterion("customer_type is null");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeIsNotNull() {
            addCriterion("customer_type is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeEqualTo(Integer value) {
            addCriterion("customer_type =", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeNotEqualTo(Integer value) {
            addCriterion("customer_type <>", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeGreaterThan(Integer value) {
            addCriterion("customer_type >", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_type >=", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeLessThan(Integer value) {
            addCriterion("customer_type <", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeLessThanOrEqualTo(Integer value) {
            addCriterion("customer_type <=", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeIn(List<Integer> values) {
            addCriterion("customer_type in", values, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeNotIn(List<Integer> values) {
            addCriterion("customer_type not in", values, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeBetween(Integer value1, Integer value2) {
            addCriterion("customer_type between", value1, value2, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_type not between", value1, value2, "customerType");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountIsNull() {
            addCriterion("excluding_tax_amount is null");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountIsNotNull() {
            addCriterion("excluding_tax_amount is not null");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountEqualTo(BigDecimal value) {
            addCriterion("excluding_tax_amount =", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountNotEqualTo(BigDecimal value) {
            addCriterion("excluding_tax_amount <>", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountGreaterThan(BigDecimal value) {
            addCriterion("excluding_tax_amount >", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("excluding_tax_amount >=", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountLessThan(BigDecimal value) {
            addCriterion("excluding_tax_amount <", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("excluding_tax_amount <=", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountIn(List<BigDecimal> values) {
            addCriterion("excluding_tax_amount in", values, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountNotIn(List<BigDecimal> values) {
            addCriterion("excluding_tax_amount not in", values, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("excluding_tax_amount between", value1, value2, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("excluding_tax_amount not between", value1, value2, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andConversionRateIsNull() {
            addCriterion("conversion_rate is null");
            return (Criteria) this;
        }

        public Criteria andConversionRateIsNotNull() {
            addCriterion("conversion_rate is not null");
            return (Criteria) this;
        }

        public Criteria andConversionRateEqualTo(BigDecimal value) {
            addCriterion("conversion_rate =", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotEqualTo(BigDecimal value) {
            addCriterion("conversion_rate <>", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateGreaterThan(BigDecimal value) {
            addCriterion("conversion_rate >", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("conversion_rate >=", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateLessThan(BigDecimal value) {
            addCriterion("conversion_rate <", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("conversion_rate <=", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateIn(List<BigDecimal> values) {
            addCriterion("conversion_rate in", values, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotIn(List<BigDecimal> values) {
            addCriterion("conversion_rate not in", values, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("conversion_rate between", value1, value2, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("conversion_rate not between", value1, value2, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNull() {
            addCriterion("unit_name is null");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNotNull() {
            addCriterion("unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andUnitNameEqualTo(String value) {
            addCriterion("unit_name =", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotEqualTo(String value) {
            addCriterion("unit_name <>", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThan(String value) {
            addCriterion("unit_name >", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("unit_name >=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThan(String value) {
            addCriterion("unit_name <", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThanOrEqualTo(String value) {
            addCriterion("unit_name <=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLike(String value) {
            addCriterion("unit_name like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotLike(String value) {
            addCriterion("unit_name not like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIn(List<String> values) {
            addCriterion("unit_name in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotIn(List<String> values) {
            addCriterion("unit_name not in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameBetween(String value1, String value2) {
            addCriterion("unit_name between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotBetween(String value1, String value2) {
            addCriterion("unit_name not between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andOldSalesManagerIsNull() {
            addCriterion("old_sales_manager is null");
            return (Criteria) this;
        }

        public Criteria andOldSalesManagerIsNotNull() {
            addCriterion("old_sales_manager is not null");
            return (Criteria) this;
        }

        public Criteria andOldSalesManagerEqualTo(String value) {
            addCriterion("old_sales_manager =", value, "oldSalesManager");
            return (Criteria) this;
        }

        public Criteria andOldSalesManagerNotEqualTo(String value) {
            addCriterion("old_sales_manager <>", value, "oldSalesManager");
            return (Criteria) this;
        }

        public Criteria andOldSalesManagerGreaterThan(String value) {
            addCriterion("old_sales_manager >", value, "oldSalesManager");
            return (Criteria) this;
        }

        public Criteria andOldSalesManagerGreaterThanOrEqualTo(String value) {
            addCriterion("old_sales_manager >=", value, "oldSalesManager");
            return (Criteria) this;
        }

        public Criteria andOldSalesManagerLessThan(String value) {
            addCriterion("old_sales_manager <", value, "oldSalesManager");
            return (Criteria) this;
        }

        public Criteria andOldSalesManagerLessThanOrEqualTo(String value) {
            addCriterion("old_sales_manager <=", value, "oldSalesManager");
            return (Criteria) this;
        }

        public Criteria andOldSalesManagerLike(String value) {
            addCriterion("old_sales_manager like", value, "oldSalesManager");
            return (Criteria) this;
        }

        public Criteria andOldSalesManagerNotLike(String value) {
            addCriterion("old_sales_manager not like", value, "oldSalesManager");
            return (Criteria) this;
        }

        public Criteria andOldSalesManagerIn(List<String> values) {
            addCriterion("old_sales_manager in", values, "oldSalesManager");
            return (Criteria) this;
        }

        public Criteria andOldSalesManagerNotIn(List<String> values) {
            addCriterion("old_sales_manager not in", values, "oldSalesManager");
            return (Criteria) this;
        }

        public Criteria andOldSalesManagerBetween(String value1, String value2) {
            addCriterion("old_sales_manager between", value1, value2, "oldSalesManager");
            return (Criteria) this;
        }

        public Criteria andOldSalesManagerNotBetween(String value1, String value2) {
            addCriterion("old_sales_manager not between", value1, value2, "oldSalesManager");
            return (Criteria) this;
        }

        public Criteria andNewSalesManagerIsNull() {
            addCriterion("new_sales_manager is null");
            return (Criteria) this;
        }

        public Criteria andNewSalesManagerIsNotNull() {
            addCriterion("new_sales_manager is not null");
            return (Criteria) this;
        }

        public Criteria andNewSalesManagerEqualTo(String value) {
            addCriterion("new_sales_manager =", value, "newSalesManager");
            return (Criteria) this;
        }

        public Criteria andNewSalesManagerNotEqualTo(String value) {
            addCriterion("new_sales_manager <>", value, "newSalesManager");
            return (Criteria) this;
        }

        public Criteria andNewSalesManagerGreaterThan(String value) {
            addCriterion("new_sales_manager >", value, "newSalesManager");
            return (Criteria) this;
        }

        public Criteria andNewSalesManagerGreaterThanOrEqualTo(String value) {
            addCriterion("new_sales_manager >=", value, "newSalesManager");
            return (Criteria) this;
        }

        public Criteria andNewSalesManagerLessThan(String value) {
            addCriterion("new_sales_manager <", value, "newSalesManager");
            return (Criteria) this;
        }

        public Criteria andNewSalesManagerLessThanOrEqualTo(String value) {
            addCriterion("new_sales_manager <=", value, "newSalesManager");
            return (Criteria) this;
        }

        public Criteria andNewSalesManagerLike(String value) {
            addCriterion("new_sales_manager like", value, "newSalesManager");
            return (Criteria) this;
        }

        public Criteria andNewSalesManagerNotLike(String value) {
            addCriterion("new_sales_manager not like", value, "newSalesManager");
            return (Criteria) this;
        }

        public Criteria andNewSalesManagerIn(List<String> values) {
            addCriterion("new_sales_manager in", values, "newSalesManager");
            return (Criteria) this;
        }

        public Criteria andNewSalesManagerNotIn(List<String> values) {
            addCriterion("new_sales_manager not in", values, "newSalesManager");
            return (Criteria) this;
        }

        public Criteria andNewSalesManagerBetween(String value1, String value2) {
            addCriterion("new_sales_manager between", value1, value2, "newSalesManager");
            return (Criteria) this;
        }

        public Criteria andNewSalesManagerNotBetween(String value1, String value2) {
            addCriterion("new_sales_manager not between", value1, value2, "newSalesManager");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNull() {
            addCriterion("project_manager is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNotNull() {
            addCriterion("project_manager is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerEqualTo(String value) {
            addCriterion("project_manager =", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotEqualTo(String value) {
            addCriterion("project_manager <>", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThan(String value) {
            addCriterion("project_manager >", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThanOrEqualTo(String value) {
            addCriterion("project_manager >=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThan(String value) {
            addCriterion("project_manager <", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThanOrEqualTo(String value) {
            addCriterion("project_manager <=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLike(String value) {
            addCriterion("project_manager like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotLike(String value) {
            addCriterion("project_manager not like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIn(List<String> values) {
            addCriterion("project_manager in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotIn(List<String> values) {
            addCriterion("project_manager not in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerBetween(String value1, String value2) {
            addCriterion("project_manager between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotBetween(String value1, String value2) {
            addCriterion("project_manager not between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNull() {
            addCriterion("ou_name is null");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNotNull() {
            addCriterion("ou_name is not null");
            return (Criteria) this;
        }

        public Criteria andOuNameEqualTo(String value) {
            addCriterion("ou_name =", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotEqualTo(String value) {
            addCriterion("ou_name <>", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThan(String value) {
            addCriterion("ou_name >", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThanOrEqualTo(String value) {
            addCriterion("ou_name >=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThan(String value) {
            addCriterion("ou_name <", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThanOrEqualTo(String value) {
            addCriterion("ou_name <=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLike(String value) {
            addCriterion("ou_name like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotLike(String value) {
            addCriterion("ou_name not like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameIn(List<String> values) {
            addCriterion("ou_name in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotIn(List<String> values) {
            addCriterion("ou_name not in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameBetween(String value1, String value2) {
            addCriterion("ou_name between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotBetween(String value1, String value2) {
            addCriterion("ou_name not between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameIsNull() {
            addCriterion("business_type_name is null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameIsNotNull() {
            addCriterion("business_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameEqualTo(String value) {
            addCriterion("business_type_name =", value, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameNotEqualTo(String value) {
            addCriterion("business_type_name <>", value, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameGreaterThan(String value) {
            addCriterion("business_type_name >", value, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("business_type_name >=", value, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameLessThan(String value) {
            addCriterion("business_type_name <", value, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameLessThanOrEqualTo(String value) {
            addCriterion("business_type_name <=", value, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameLike(String value) {
            addCriterion("business_type_name like", value, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameNotLike(String value) {
            addCriterion("business_type_name not like", value, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameIn(List<String> values) {
            addCriterion("business_type_name in", values, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameNotIn(List<String> values) {
            addCriterion("business_type_name not in", values, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameBetween(String value1, String value2) {
            addCriterion("business_type_name between", value1, value2, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNameNotBetween(String value1, String value2) {
            addCriterion("business_type_name not between", value1, value2, "businessTypeName");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}