package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportPurchaseProgressExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportPurchaseProgressExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNull() {
            addCriterion("project_type is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNotNull() {
            addCriterion("project_type is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeEqualTo(String value) {
            addCriterion("project_type =", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotEqualTo(String value) {
            addCriterion("project_type <>", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThan(String value) {
            addCriterion("project_type >", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThanOrEqualTo(String value) {
            addCriterion("project_type >=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThan(String value) {
            addCriterion("project_type <", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThanOrEqualTo(String value) {
            addCriterion("project_type <=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLike(String value) {
            addCriterion("project_type like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotLike(String value) {
            addCriterion("project_type not like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIn(List<String> values) {
            addCriterion("project_type in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotIn(List<String> values) {
            addCriterion("project_type not in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeBetween(String value1, String value2) {
            addCriterion("project_type between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotBetween(String value1, String value2) {
            addCriterion("project_type not between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectSortIsNull() {
            addCriterion("project_sort is null");
            return (Criteria) this;
        }

        public Criteria andProjectSortIsNotNull() {
            addCriterion("project_sort is not null");
            return (Criteria) this;
        }

        public Criteria andProjectSortEqualTo(String value) {
            addCriterion("project_sort =", value, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortNotEqualTo(String value) {
            addCriterion("project_sort <>", value, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortGreaterThan(String value) {
            addCriterion("project_sort >", value, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortGreaterThanOrEqualTo(String value) {
            addCriterion("project_sort >=", value, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortLessThan(String value) {
            addCriterion("project_sort <", value, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortLessThanOrEqualTo(String value) {
            addCriterion("project_sort <=", value, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortLike(String value) {
            addCriterion("project_sort like", value, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortNotLike(String value) {
            addCriterion("project_sort not like", value, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortIn(List<String> values) {
            addCriterion("project_sort in", values, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortNotIn(List<String> values) {
            addCriterion("project_sort not in", values, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortBetween(String value1, String value2) {
            addCriterion("project_sort between", value1, value2, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectSortNotBetween(String value1, String value2) {
            addCriterion("project_sort not between", value1, value2, "projectSort");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNull() {
            addCriterion("project_manager is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNotNull() {
            addCriterion("project_manager is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerEqualTo(String value) {
            addCriterion("project_manager =", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotEqualTo(String value) {
            addCriterion("project_manager <>", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThan(String value) {
            addCriterion("project_manager >", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThanOrEqualTo(String value) {
            addCriterion("project_manager >=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThan(String value) {
            addCriterion("project_manager <", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThanOrEqualTo(String value) {
            addCriterion("project_manager <=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLike(String value) {
            addCriterion("project_manager like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotLike(String value) {
            addCriterion("project_manager not like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIn(List<String> values) {
            addCriterion("project_manager in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotIn(List<String> values) {
            addCriterion("project_manager not in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerBetween(String value1, String value2) {
            addCriterion("project_manager between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotBetween(String value1, String value2) {
            addCriterion("project_manager not between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdIsNull() {
            addCriterion("material_purchase_requirement_id is null");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdIsNotNull() {
            addCriterion("material_purchase_requirement_id is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdEqualTo(Long value) {
            addCriterion("material_purchase_requirement_id =", value, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdNotEqualTo(Long value) {
            addCriterion("material_purchase_requirement_id <>", value, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdGreaterThan(Long value) {
            addCriterion("material_purchase_requirement_id >", value, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdGreaterThanOrEqualTo(Long value) {
            addCriterion("material_purchase_requirement_id >=", value, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdLessThan(Long value) {
            addCriterion("material_purchase_requirement_id <", value, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdLessThanOrEqualTo(Long value) {
            addCriterion("material_purchase_requirement_id <=", value, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdIn(List<Long> values) {
            addCriterion("material_purchase_requirement_id in", values, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdNotIn(List<Long> values) {
            addCriterion("material_purchase_requirement_id not in", values, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdBetween(Long value1, Long value2) {
            addCriterion("material_purchase_requirement_id between", value1, value2, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andMaterialPurchaseRequirementIdNotBetween(Long value1, Long value2) {
            addCriterion("material_purchase_requirement_id not between", value1, value2, "materialPurchaseRequirementId");
            return (Criteria) this;
        }

        public Criteria andDemandTypeIsNull() {
            addCriterion("demand_type is null");
            return (Criteria) this;
        }

        public Criteria andDemandTypeIsNotNull() {
            addCriterion("demand_type is not null");
            return (Criteria) this;
        }

        public Criteria andDemandTypeEqualTo(Integer value) {
            addCriterion("demand_type =", value, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeNotEqualTo(Integer value) {
            addCriterion("demand_type <>", value, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeGreaterThan(Integer value) {
            addCriterion("demand_type >", value, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("demand_type >=", value, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeLessThan(Integer value) {
            addCriterion("demand_type <", value, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeLessThanOrEqualTo(Integer value) {
            addCriterion("demand_type <=", value, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeIn(List<Integer> values) {
            addCriterion("demand_type in", values, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeNotIn(List<Integer> values) {
            addCriterion("demand_type not in", values, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeBetween(Integer value1, Integer value2) {
            addCriterion("demand_type between", value1, value2, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("demand_type not between", value1, value2, "demandType");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIsNull() {
            addCriterion("requirement_code is null");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIsNotNull() {
            addCriterion("requirement_code is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeEqualTo(String value) {
            addCriterion("requirement_code =", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotEqualTo(String value) {
            addCriterion("requirement_code <>", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeGreaterThan(String value) {
            addCriterion("requirement_code >", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeGreaterThanOrEqualTo(String value) {
            addCriterion("requirement_code >=", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLessThan(String value) {
            addCriterion("requirement_code <", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLessThanOrEqualTo(String value) {
            addCriterion("requirement_code <=", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLike(String value) {
            addCriterion("requirement_code like", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotLike(String value) {
            addCriterion("requirement_code not like", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIn(List<String> values) {
            addCriterion("requirement_code in", values, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotIn(List<String> values) {
            addCriterion("requirement_code not in", values, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeBetween(String value1, String value2) {
            addCriterion("requirement_code between", value1, value2, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotBetween(String value1, String value2) {
            addCriterion("requirement_code not between", value1, value2, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeIsNull() {
            addCriterion("approved_time is null");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeIsNotNull() {
            addCriterion("approved_time is not null");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeEqualTo(Date value) {
            addCriterion("approved_time =", value, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeNotEqualTo(Date value) {
            addCriterion("approved_time <>", value, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeGreaterThan(Date value) {
            addCriterion("approved_time >", value, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("approved_time >=", value, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeLessThan(Date value) {
            addCriterion("approved_time <", value, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeLessThanOrEqualTo(Date value) {
            addCriterion("approved_time <=", value, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeIn(List<Date> values) {
            addCriterion("approved_time in", values, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeNotIn(List<Date> values) {
            addCriterion("approved_time not in", values, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeBetween(Date value1, Date value2) {
            addCriterion("approved_time between", value1, value2, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeNotBetween(Date value1, Date value2) {
            addCriterion("approved_time not between", value1, value2, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andDemandStatusIsNull() {
            addCriterion("demand_status is null");
            return (Criteria) this;
        }

        public Criteria andDemandStatusIsNotNull() {
            addCriterion("demand_status is not null");
            return (Criteria) this;
        }

        public Criteria andDemandStatusEqualTo(Integer value) {
            addCriterion("demand_status =", value, "demandStatus");
            return (Criteria) this;
        }

        public Criteria andDemandStatusNotEqualTo(Integer value) {
            addCriterion("demand_status <>", value, "demandStatus");
            return (Criteria) this;
        }

        public Criteria andDemandStatusGreaterThan(Integer value) {
            addCriterion("demand_status >", value, "demandStatus");
            return (Criteria) this;
        }

        public Criteria andDemandStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("demand_status >=", value, "demandStatus");
            return (Criteria) this;
        }

        public Criteria andDemandStatusLessThan(Integer value) {
            addCriterion("demand_status <", value, "demandStatus");
            return (Criteria) this;
        }

        public Criteria andDemandStatusLessThanOrEqualTo(Integer value) {
            addCriterion("demand_status <=", value, "demandStatus");
            return (Criteria) this;
        }

        public Criteria andDemandStatusIn(List<Integer> values) {
            addCriterion("demand_status in", values, "demandStatus");
            return (Criteria) this;
        }

        public Criteria andDemandStatusNotIn(List<Integer> values) {
            addCriterion("demand_status not in", values, "demandStatus");
            return (Criteria) this;
        }

        public Criteria andDemandStatusBetween(Integer value1, Integer value2) {
            addCriterion("demand_status between", value1, value2, "demandStatus");
            return (Criteria) this;
        }

        public Criteria andDemandStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("demand_status not between", value1, value2, "demandStatus");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNull() {
            addCriterion("wbs_summary_code is null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNotNull() {
            addCriterion("wbs_summary_code is not null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeEqualTo(String value) {
            addCriterion("wbs_summary_code =", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotEqualTo(String value) {
            addCriterion("wbs_summary_code <>", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThan(String value) {
            addCriterion("wbs_summary_code >", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code >=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThan(String value) {
            addCriterion("wbs_summary_code <", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code <=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLike(String value) {
            addCriterion("wbs_summary_code like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotLike(String value) {
            addCriterion("wbs_summary_code not like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIn(List<String> values) {
            addCriterion("wbs_summary_code in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotIn(List<String> values) {
            addCriterion("wbs_summary_code not in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeBetween(String value1, String value2) {
            addCriterion("wbs_summary_code between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotBetween(String value1, String value2) {
            addCriterion("wbs_summary_code not between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNull() {
            addCriterion("pam_code is null");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNotNull() {
            addCriterion("pam_code is not null");
            return (Criteria) this;
        }

        public Criteria andPamCodeEqualTo(String value) {
            addCriterion("pam_code =", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotEqualTo(String value) {
            addCriterion("pam_code <>", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThan(String value) {
            addCriterion("pam_code >", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("pam_code >=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThan(String value) {
            addCriterion("pam_code <", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThanOrEqualTo(String value) {
            addCriterion("pam_code <=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLike(String value) {
            addCriterion("pam_code like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotLike(String value) {
            addCriterion("pam_code not like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeIn(List<String> values) {
            addCriterion("pam_code in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotIn(List<String> values) {
            addCriterion("pam_code not in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeBetween(String value1, String value2) {
            addCriterion("pam_code between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotBetween(String value1, String value2) {
            addCriterion("pam_code not between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNull() {
            addCriterion("erp_code is null");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNotNull() {
            addCriterion("erp_code is not null");
            return (Criteria) this;
        }

        public Criteria andErpCodeEqualTo(String value) {
            addCriterion("erp_code =", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotEqualTo(String value) {
            addCriterion("erp_code <>", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThan(String value) {
            addCriterion("erp_code >", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("erp_code >=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThan(String value) {
            addCriterion("erp_code <", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThanOrEqualTo(String value) {
            addCriterion("erp_code <=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLike(String value) {
            addCriterion("erp_code like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotLike(String value) {
            addCriterion("erp_code not like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIn(List<String> values) {
            addCriterion("erp_code in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotIn(List<String> values) {
            addCriterion("erp_code not in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeBetween(String value1, String value2) {
            addCriterion("erp_code between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotBetween(String value1, String value2) {
            addCriterion("erp_code not between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNull() {
            addCriterion("materiel_descr is null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNotNull() {
            addCriterion("materiel_descr is not null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrEqualTo(String value) {
            addCriterion("materiel_descr =", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotEqualTo(String value) {
            addCriterion("materiel_descr <>", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThan(String value) {
            addCriterion("materiel_descr >", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThanOrEqualTo(String value) {
            addCriterion("materiel_descr >=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThan(String value) {
            addCriterion("materiel_descr <", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThanOrEqualTo(String value) {
            addCriterion("materiel_descr <=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLike(String value) {
            addCriterion("materiel_descr like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotLike(String value) {
            addCriterion("materiel_descr not like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIn(List<String> values) {
            addCriterion("materiel_descr in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotIn(List<String> values) {
            addCriterion("materiel_descr not in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrBetween(String value1, String value2) {
            addCriterion("materiel_descr between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotBetween(String value1, String value2) {
            addCriterion("materiel_descr not between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andNeedTotalIsNull() {
            addCriterion("need_total is null");
            return (Criteria) this;
        }

        public Criteria andNeedTotalIsNotNull() {
            addCriterion("need_total is not null");
            return (Criteria) this;
        }

        public Criteria andNeedTotalEqualTo(BigDecimal value) {
            addCriterion("need_total =", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalNotEqualTo(BigDecimal value) {
            addCriterion("need_total <>", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalGreaterThan(BigDecimal value) {
            addCriterion("need_total >", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("need_total >=", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalLessThan(BigDecimal value) {
            addCriterion("need_total <", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("need_total <=", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalIn(List<BigDecimal> values) {
            addCriterion("need_total in", values, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalNotIn(List<BigDecimal> values) {
            addCriterion("need_total not in", values, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("need_total between", value1, value2, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("need_total not between", value1, value2, "needTotal");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityIsNull() {
            addCriterion("unreleased_quantity is null");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityIsNotNull() {
            addCriterion("unreleased_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityEqualTo(BigDecimal value) {
            addCriterion("unreleased_quantity =", value, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityNotEqualTo(BigDecimal value) {
            addCriterion("unreleased_quantity <>", value, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityGreaterThan(BigDecimal value) {
            addCriterion("unreleased_quantity >", value, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("unreleased_quantity >=", value, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityLessThan(BigDecimal value) {
            addCriterion("unreleased_quantity <", value, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("unreleased_quantity <=", value, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityIn(List<BigDecimal> values) {
            addCriterion("unreleased_quantity in", values, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityNotIn(List<BigDecimal> values) {
            addCriterion("unreleased_quantity not in", values, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unreleased_quantity between", value1, value2, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andUnreleasedQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unreleased_quantity not between", value1, value2, "unreleasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityIsNull() {
            addCriterion("released_quantity is null");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityIsNotNull() {
            addCriterion("released_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityEqualTo(BigDecimal value) {
            addCriterion("released_quantity =", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityNotEqualTo(BigDecimal value) {
            addCriterion("released_quantity <>", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityGreaterThan(BigDecimal value) {
            addCriterion("released_quantity >", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("released_quantity >=", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityLessThan(BigDecimal value) {
            addCriterion("released_quantity <", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("released_quantity <=", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityIn(List<BigDecimal> values) {
            addCriterion("released_quantity in", values, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityNotIn(List<BigDecimal> values) {
            addCriterion("released_quantity not in", values, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("released_quantity between", value1, value2, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("released_quantity not between", value1, value2, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityIsNull() {
            addCriterion("closed_quantity is null");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityIsNotNull() {
            addCriterion("closed_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityEqualTo(BigDecimal value) {
            addCriterion("closed_quantity =", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityNotEqualTo(BigDecimal value) {
            addCriterion("closed_quantity <>", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityGreaterThan(BigDecimal value) {
            addCriterion("closed_quantity >", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("closed_quantity >=", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityLessThan(BigDecimal value) {
            addCriterion("closed_quantity <", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("closed_quantity <=", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityIn(List<BigDecimal> values) {
            addCriterion("closed_quantity in", values, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityNotIn(List<BigDecimal> values) {
            addCriterion("closed_quantity not in", values, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("closed_quantity between", value1, value2, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("closed_quantity not between", value1, value2, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNull() {
            addCriterion("delivery_time is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNotNull() {
            addCriterion("delivery_time is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeEqualTo(Date value) {
            addCriterion("delivery_time =", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotEqualTo(Date value) {
            addCriterion("delivery_time <>", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThan(Date value) {
            addCriterion("delivery_time >", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("delivery_time >=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThan(Date value) {
            addCriterion("delivery_time <", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThanOrEqualTo(Date value) {
            addCriterion("delivery_time <=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIn(List<Date> values) {
            addCriterion("delivery_time in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotIn(List<Date> values) {
            addCriterion("delivery_time not in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeBetween(Date value1, Date value2) {
            addCriterion("delivery_time between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotBetween(Date value1, Date value2) {
            addCriterion("delivery_time not between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNull() {
            addCriterion("activity_code is null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNotNull() {
            addCriterion("activity_code is not null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeEqualTo(String value) {
            addCriterion("activity_code =", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotEqualTo(String value) {
            addCriterion("activity_code <>", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThan(String value) {
            addCriterion("activity_code >", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("activity_code >=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThan(String value) {
            addCriterion("activity_code <", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThanOrEqualTo(String value) {
            addCriterion("activity_code <=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLike(String value) {
            addCriterion("activity_code like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotLike(String value) {
            addCriterion("activity_code not like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIn(List<String> values) {
            addCriterion("activity_code in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotIn(List<String> values) {
            addCriterion("activity_code not in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeBetween(String value1, String value2) {
            addCriterion("activity_code between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotBetween(String value1, String value2) {
            addCriterion("activity_code not between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberIsNull() {
            addCriterion("design_release_lot_number is null");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberIsNotNull() {
            addCriterion("design_release_lot_number is not null");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberEqualTo(String value) {
            addCriterion("design_release_lot_number =", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberNotEqualTo(String value) {
            addCriterion("design_release_lot_number <>", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberGreaterThan(String value) {
            addCriterion("design_release_lot_number >", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberGreaterThanOrEqualTo(String value) {
            addCriterion("design_release_lot_number >=", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberLessThan(String value) {
            addCriterion("design_release_lot_number <", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberLessThanOrEqualTo(String value) {
            addCriterion("design_release_lot_number <=", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberLike(String value) {
            addCriterion("design_release_lot_number like", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberNotLike(String value) {
            addCriterion("design_release_lot_number not like", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberIn(List<String> values) {
            addCriterion("design_release_lot_number in", values, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberNotIn(List<String> values) {
            addCriterion("design_release_lot_number not in", values, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberBetween(String value1, String value2) {
            addCriterion("design_release_lot_number between", value1, value2, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberNotBetween(String value1, String value2) {
            addCriterion("design_release_lot_number not between", value1, value2, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andProducerNameIsNull() {
            addCriterion("producer_name is null");
            return (Criteria) this;
        }

        public Criteria andProducerNameIsNotNull() {
            addCriterion("producer_name is not null");
            return (Criteria) this;
        }

        public Criteria andProducerNameEqualTo(String value) {
            addCriterion("producer_name =", value, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameNotEqualTo(String value) {
            addCriterion("producer_name <>", value, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameGreaterThan(String value) {
            addCriterion("producer_name >", value, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameGreaterThanOrEqualTo(String value) {
            addCriterion("producer_name >=", value, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameLessThan(String value) {
            addCriterion("producer_name <", value, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameLessThanOrEqualTo(String value) {
            addCriterion("producer_name <=", value, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameLike(String value) {
            addCriterion("producer_name like", value, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameNotLike(String value) {
            addCriterion("producer_name not like", value, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameIn(List<String> values) {
            addCriterion("producer_name in", values, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameNotIn(List<String> values) {
            addCriterion("producer_name not in", values, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameBetween(String value1, String value2) {
            addCriterion("producer_name between", value1, value2, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameNotBetween(String value1, String value2) {
            addCriterion("producer_name not between", value1, value2, "producerName");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationIsNull() {
            addCriterion("material_classification is null");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationIsNotNull() {
            addCriterion("material_classification is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationEqualTo(String value) {
            addCriterion("material_classification =", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotEqualTo(String value) {
            addCriterion("material_classification <>", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationGreaterThan(String value) {
            addCriterion("material_classification >", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationGreaterThanOrEqualTo(String value) {
            addCriterion("material_classification >=", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationLessThan(String value) {
            addCriterion("material_classification <", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationLessThanOrEqualTo(String value) {
            addCriterion("material_classification <=", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationLike(String value) {
            addCriterion("material_classification like", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotLike(String value) {
            addCriterion("material_classification not like", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationIn(List<String> values) {
            addCriterion("material_classification in", values, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotIn(List<String> values) {
            addCriterion("material_classification not in", values, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationBetween(String value1, String value2) {
            addCriterion("material_classification between", value1, value2, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotBetween(String value1, String value2) {
            addCriterion("material_classification not between", value1, value2, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassIsNull() {
            addCriterion("coding_middleclass is null");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassIsNotNull() {
            addCriterion("coding_middleclass is not null");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassEqualTo(String value) {
            addCriterion("coding_middleclass =", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassNotEqualTo(String value) {
            addCriterion("coding_middleclass <>", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassGreaterThan(String value) {
            addCriterion("coding_middleclass >", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassGreaterThanOrEqualTo(String value) {
            addCriterion("coding_middleclass >=", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassLessThan(String value) {
            addCriterion("coding_middleclass <", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassLessThanOrEqualTo(String value) {
            addCriterion("coding_middleclass <=", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassLike(String value) {
            addCriterion("coding_middleclass like", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassNotLike(String value) {
            addCriterion("coding_middleclass not like", value, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassIn(List<String> values) {
            addCriterion("coding_middleclass in", values, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassNotIn(List<String> values) {
            addCriterion("coding_middleclass not in", values, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassBetween(String value1, String value2) {
            addCriterion("coding_middleclass between", value1, value2, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleclassNotBetween(String value1, String value2) {
            addCriterion("coding_middleclass not between", value1, value2, "codingMiddleclass");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIsNull() {
            addCriterion("material_type is null");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIsNotNull() {
            addCriterion("material_type is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeEqualTo(String value) {
            addCriterion("material_type =", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotEqualTo(String value) {
            addCriterion("material_type <>", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeGreaterThan(String value) {
            addCriterion("material_type >", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeGreaterThanOrEqualTo(String value) {
            addCriterion("material_type >=", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLessThan(String value) {
            addCriterion("material_type <", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLessThanOrEqualTo(String value) {
            addCriterion("material_type <=", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLike(String value) {
            addCriterion("material_type like", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotLike(String value) {
            addCriterion("material_type not like", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIn(List<String> values) {
            addCriterion("material_type in", values, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotIn(List<String> values) {
            addCriterion("material_type not in", values, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeBetween(String value1, String value2) {
            addCriterion("material_type between", value1, value2, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotBetween(String value1, String value2) {
            addCriterion("material_type not between", value1, value2, "materialType");
            return (Criteria) this;
        }

        public Criteria andCodesIsNull() {
            addCriterion("codes is null");
            return (Criteria) this;
        }

        public Criteria andCodesIsNotNull() {
            addCriterion("codes is not null");
            return (Criteria) this;
        }

        public Criteria andCodesEqualTo(String value) {
            addCriterion("codes =", value, "codes");
            return (Criteria) this;
        }

        public Criteria andCodesNotEqualTo(String value) {
            addCriterion("codes <>", value, "codes");
            return (Criteria) this;
        }

        public Criteria andCodesGreaterThan(String value) {
            addCriterion("codes >", value, "codes");
            return (Criteria) this;
        }

        public Criteria andCodesGreaterThanOrEqualTo(String value) {
            addCriterion("codes >=", value, "codes");
            return (Criteria) this;
        }

        public Criteria andCodesLessThan(String value) {
            addCriterion("codes <", value, "codes");
            return (Criteria) this;
        }

        public Criteria andCodesLessThanOrEqualTo(String value) {
            addCriterion("codes <=", value, "codes");
            return (Criteria) this;
        }

        public Criteria andCodesLike(String value) {
            addCriterion("codes like", value, "codes");
            return (Criteria) this;
        }

        public Criteria andCodesNotLike(String value) {
            addCriterion("codes not like", value, "codes");
            return (Criteria) this;
        }

        public Criteria andCodesIn(List<String> values) {
            addCriterion("codes in", values, "codes");
            return (Criteria) this;
        }

        public Criteria andCodesNotIn(List<String> values) {
            addCriterion("codes not in", values, "codes");
            return (Criteria) this;
        }

        public Criteria andCodesBetween(String value1, String value2) {
            addCriterion("codes between", value1, value2, "codes");
            return (Criteria) this;
        }

        public Criteria andCodesNotBetween(String value1, String value2) {
            addCriterion("codes not between", value1, value2, "codes");
            return (Criteria) this;
        }

        public Criteria andBuyerIsNull() {
            addCriterion("buyer is null");
            return (Criteria) this;
        }

        public Criteria andBuyerIsNotNull() {
            addCriterion("buyer is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerEqualTo(String value) {
            addCriterion("buyer =", value, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerNotEqualTo(String value) {
            addCriterion("buyer <>", value, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerGreaterThan(String value) {
            addCriterion("buyer >", value, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerGreaterThanOrEqualTo(String value) {
            addCriterion("buyer >=", value, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerLessThan(String value) {
            addCriterion("buyer <", value, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerLessThanOrEqualTo(String value) {
            addCriterion("buyer <=", value, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerLike(String value) {
            addCriterion("buyer like", value, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerNotLike(String value) {
            addCriterion("buyer not like", value, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerIn(List<String> values) {
            addCriterion("buyer in", values, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerNotIn(List<String> values) {
            addCriterion("buyer not in", values, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerBetween(String value1, String value2) {
            addCriterion("buyer between", value1, value2, "buyer");
            return (Criteria) this;
        }

        public Criteria andBuyerNotBetween(String value1, String value2) {
            addCriterion("buyer not between", value1, value2, "buyer");
            return (Criteria) this;
        }

        public Criteria andLastPoDeliveryIsNull() {
            addCriterion("last_po_delivery is null");
            return (Criteria) this;
        }

        public Criteria andLastPoDeliveryIsNotNull() {
            addCriterion("last_po_delivery is not null");
            return (Criteria) this;
        }

        public Criteria andLastPoDeliveryEqualTo(Date value) {
            addCriterion("last_po_delivery =", value, "lastPoDelivery");
            return (Criteria) this;
        }

        public Criteria andLastPoDeliveryNotEqualTo(Date value) {
            addCriterion("last_po_delivery <>", value, "lastPoDelivery");
            return (Criteria) this;
        }

        public Criteria andLastPoDeliveryGreaterThan(Date value) {
            addCriterion("last_po_delivery >", value, "lastPoDelivery");
            return (Criteria) this;
        }

        public Criteria andLastPoDeliveryGreaterThanOrEqualTo(Date value) {
            addCriterion("last_po_delivery >=", value, "lastPoDelivery");
            return (Criteria) this;
        }

        public Criteria andLastPoDeliveryLessThan(Date value) {
            addCriterion("last_po_delivery <", value, "lastPoDelivery");
            return (Criteria) this;
        }

        public Criteria andLastPoDeliveryLessThanOrEqualTo(Date value) {
            addCriterion("last_po_delivery <=", value, "lastPoDelivery");
            return (Criteria) this;
        }

        public Criteria andLastPoDeliveryIn(List<Date> values) {
            addCriterion("last_po_delivery in", values, "lastPoDelivery");
            return (Criteria) this;
        }

        public Criteria andLastPoDeliveryNotIn(List<Date> values) {
            addCriterion("last_po_delivery not in", values, "lastPoDelivery");
            return (Criteria) this;
        }

        public Criteria andLastPoDeliveryBetween(Date value1, Date value2) {
            addCriterion("last_po_delivery between", value1, value2, "lastPoDelivery");
            return (Criteria) this;
        }

        public Criteria andLastPoDeliveryNotBetween(Date value1, Date value2) {
            addCriterion("last_po_delivery not between", value1, value2, "lastPoDelivery");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveIsNull() {
            addCriterion("newest_po_receive is null");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveIsNotNull() {
            addCriterion("newest_po_receive is not null");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveEqualTo(Date value) {
            addCriterion("newest_po_receive =", value, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveNotEqualTo(Date value) {
            addCriterion("newest_po_receive <>", value, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveGreaterThan(Date value) {
            addCriterion("newest_po_receive >", value, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveGreaterThanOrEqualTo(Date value) {
            addCriterion("newest_po_receive >=", value, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveLessThan(Date value) {
            addCriterion("newest_po_receive <", value, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveLessThanOrEqualTo(Date value) {
            addCriterion("newest_po_receive <=", value, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveIn(List<Date> values) {
            addCriterion("newest_po_receive in", values, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveNotIn(List<Date> values) {
            addCriterion("newest_po_receive not in", values, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveBetween(Date value1, Date value2) {
            addCriterion("newest_po_receive between", value1, value2, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoReceiveNotBetween(Date value1, Date value2) {
            addCriterion("newest_po_receive not between", value1, value2, "newestPoReceive");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingIsNull() {
            addCriterion("newest_po_warehousing is null");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingIsNotNull() {
            addCriterion("newest_po_warehousing is not null");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingEqualTo(Date value) {
            addCriterion("newest_po_warehousing =", value, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingNotEqualTo(Date value) {
            addCriterion("newest_po_warehousing <>", value, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingGreaterThan(Date value) {
            addCriterion("newest_po_warehousing >", value, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingGreaterThanOrEqualTo(Date value) {
            addCriterion("newest_po_warehousing >=", value, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingLessThan(Date value) {
            addCriterion("newest_po_warehousing <", value, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingLessThanOrEqualTo(Date value) {
            addCriterion("newest_po_warehousing <=", value, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingIn(List<Date> values) {
            addCriterion("newest_po_warehousing in", values, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingNotIn(List<Date> values) {
            addCriterion("newest_po_warehousing not in", values, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingBetween(Date value1, Date value2) {
            addCriterion("newest_po_warehousing between", value1, value2, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andNewestPoWarehousingNotBetween(Date value1, Date value2) {
            addCriterion("newest_po_warehousing not between", value1, value2, "newestPoWarehousing");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityIsNull() {
            addCriterion("receive_quantity is null");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityIsNotNull() {
            addCriterion("receive_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityEqualTo(BigDecimal value) {
            addCriterion("receive_quantity =", value, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityNotEqualTo(BigDecimal value) {
            addCriterion("receive_quantity <>", value, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityGreaterThan(BigDecimal value) {
            addCriterion("receive_quantity >", value, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("receive_quantity >=", value, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityLessThan(BigDecimal value) {
            addCriterion("receive_quantity <", value, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("receive_quantity <=", value, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityIn(List<BigDecimal> values) {
            addCriterion("receive_quantity in", values, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityNotIn(List<BigDecimal> values) {
            addCriterion("receive_quantity not in", values, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("receive_quantity between", value1, value2, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiveQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("receive_quantity not between", value1, value2, "receiveQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityIsNull() {
            addCriterion("warehousing_quantity is null");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityIsNotNull() {
            addCriterion("warehousing_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityEqualTo(BigDecimal value) {
            addCriterion("warehousing_quantity =", value, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityNotEqualTo(BigDecimal value) {
            addCriterion("warehousing_quantity <>", value, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityGreaterThan(BigDecimal value) {
            addCriterion("warehousing_quantity >", value, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("warehousing_quantity >=", value, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityLessThan(BigDecimal value) {
            addCriterion("warehousing_quantity <", value, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("warehousing_quantity <=", value, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityIn(List<BigDecimal> values) {
            addCriterion("warehousing_quantity in", values, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityNotIn(List<BigDecimal> values) {
            addCriterion("warehousing_quantity not in", values, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("warehousing_quantity between", value1, value2, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehousingQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("warehousing_quantity not between", value1, value2, "warehousingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityIsNull() {
            addCriterion("not_delivery_quantity is null");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityIsNotNull() {
            addCriterion("not_delivery_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityEqualTo(BigDecimal value) {
            addCriterion("not_delivery_quantity =", value, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityNotEqualTo(BigDecimal value) {
            addCriterion("not_delivery_quantity <>", value, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityGreaterThan(BigDecimal value) {
            addCriterion("not_delivery_quantity >", value, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("not_delivery_quantity >=", value, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityLessThan(BigDecimal value) {
            addCriterion("not_delivery_quantity <", value, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("not_delivery_quantity <=", value, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityIn(List<BigDecimal> values) {
            addCriterion("not_delivery_quantity in", values, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityNotIn(List<BigDecimal> values) {
            addCriterion("not_delivery_quantity not in", values, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("not_delivery_quantity between", value1, value2, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andNotDeliveryQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("not_delivery_quantity not between", value1, value2, "notDeliveryQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityIsNull() {
            addCriterion("invoicing_quantity is null");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityIsNotNull() {
            addCriterion("invoicing_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityEqualTo(BigDecimal value) {
            addCriterion("invoicing_quantity =", value, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityNotEqualTo(BigDecimal value) {
            addCriterion("invoicing_quantity <>", value, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityGreaterThan(BigDecimal value) {
            addCriterion("invoicing_quantity >", value, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("invoicing_quantity >=", value, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityLessThan(BigDecimal value) {
            addCriterion("invoicing_quantity <", value, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("invoicing_quantity <=", value, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityIn(List<BigDecimal> values) {
            addCriterion("invoicing_quantity in", values, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityNotIn(List<BigDecimal> values) {
            addCriterion("invoicing_quantity not in", values, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invoicing_quantity between", value1, value2, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andInvoicingQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invoicing_quantity not between", value1, value2, "invoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityIsNull() {
            addCriterion("not_invoicing_quantity is null");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityIsNotNull() {
            addCriterion("not_invoicing_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityEqualTo(BigDecimal value) {
            addCriterion("not_invoicing_quantity =", value, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityNotEqualTo(BigDecimal value) {
            addCriterion("not_invoicing_quantity <>", value, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityGreaterThan(BigDecimal value) {
            addCriterion("not_invoicing_quantity >", value, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("not_invoicing_quantity >=", value, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityLessThan(BigDecimal value) {
            addCriterion("not_invoicing_quantity <", value, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("not_invoicing_quantity <=", value, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityIn(List<BigDecimal> values) {
            addCriterion("not_invoicing_quantity in", values, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityNotIn(List<BigDecimal> values) {
            addCriterion("not_invoicing_quantity not in", values, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("not_invoicing_quantity between", value1, value2, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andNotInvoicingQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("not_invoicing_quantity not between", value1, value2, "notInvoicingQuantity");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}