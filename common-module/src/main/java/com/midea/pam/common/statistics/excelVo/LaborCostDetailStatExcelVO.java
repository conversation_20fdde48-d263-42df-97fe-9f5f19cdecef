package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: common-module
 * @description: 实际成本归集-人力成本明细导出实体
 * @author:zhongpeng
 * @create:2020-03-24 16:41
 **/
@Setter
@Getter
public class LaborCostDetailStatExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer num;

    @Excel(name = "归集日期", width = 20, format = "yyyy-MM-dd")
    private Date collectionDate;

    @Excel(name = "成本发生日期", width = 20, format = "yyyy-MM-dd")
    private Date costDate;

    @Excel(name = "结转状态", width = 10, replace = {"未结转_0", "已结转_1", "无需入账_2", "未结转_null"})
    private Integer carryStatus;

    @Excel(name = "结转期间", width = 20)
    private String glPeriod;

    @Excel(name = "项目编号", width = 30)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "类型", width = 20, replace = {"内部_1", "人力点工_2", "自招外包_3"})
    private Integer userType;

    @Excel(name = "姓名", width = 30)
    private String userName;

    @Excel(name = "MIP账号", width = 30)
    private String mipName;

    @Excel(name = "项目角色", width = 30)
    private String projectRole;

    @Excel(name = "出勤日期", width = 30, format = "yyyy-MM-dd")
    private Date applyDate;

    @Excel(name = "填报日期", width = 30, format = "yyyy-MM-dd")
    private Date fillInDate;

    @Excel(name = "审批日期", width = 30, format = "yyyy-MM-dd")
    private Date sytemApplyDate;

    @Excel(name = "工时数(H)", width = 30)
    private BigDecimal actualWorkingHours;

    @Excel(name = "实际费率(天)", width = 30)
    private BigDecimal costMoney;

    @Excel(name = "人力成本", width = 30)
    private BigDecimal costTotal;

    @Excel(name = "币种", width = 30)
    private String currency;

    @Excel(name = "业务分类", width = 30)
    private String typeName;

    @Excel(name = "业务实体", width = 30)
    private String ouName;

    @Excel(name = "WBS号", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项编码", width = 20)
    private String activityCode;

}
