package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目变更预算汇总表")
public class ReportProjectChangeBudget extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    @ApiModelProperty(value = "变更发起人")
    private String changeSubmitBy;

    @ApiModelProperty(value = "变更时间")
    private Date changeTime;

    @ApiModelProperty(value = "变更原因")
    private String changeReason;

    @ApiModelProperty(value = "变更原因说明")
    private String changeReasonDescribe;

    @ApiModelProperty(value = "变更物料成本")
    private BigDecimal afterMaterialCost;

    @ApiModelProperty(value = "变更后人力成本")
    private BigDecimal afterHumanCost;

    @ApiModelProperty(value = "变更后差旅成本")
    private BigDecimal afterTravelCost;

    @ApiModelProperty(value = "变更后非差旅成本")
    private BigDecimal afterNotTravelCost;

    @ApiModelProperty(value = "物料成本")
    private BigDecimal materialCost;

    @ApiModelProperty(value = "人力成本")
    private BigDecimal humanCost;

    @ApiModelProperty(value = "差旅成本")
    private BigDecimal travelCost;

    @ApiModelProperty(value = "非差旅成本")
    private BigDecimal notTravelCost;

    @ApiModelProperty(value = "是否删除")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public String getChangeSubmitBy() {
        return changeSubmitBy;
    }

    public void setChangeSubmitBy(String changeSubmitBy) {
        this.changeSubmitBy = changeSubmitBy == null ? null : changeSubmitBy.trim();
    }

    public Date getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(Date changeTime) {
        this.changeTime = changeTime;
    }

    public String getChangeReason() {
        return changeReason;
    }

    public void setChangeReason(String changeReason) {
        this.changeReason = changeReason == null ? null : changeReason.trim();
    }

    public String getChangeReasonDescribe() {
        return changeReasonDescribe;
    }

    public void setChangeReasonDescribe(String changeReasonDescribe) {
        this.changeReasonDescribe = changeReasonDescribe == null ? null : changeReasonDescribe.trim();
    }

    public BigDecimal getAfterMaterialCost() {
        return afterMaterialCost;
    }

    public void setAfterMaterialCost(BigDecimal afterMaterialCost) {
        this.afterMaterialCost = afterMaterialCost;
    }

    public BigDecimal getAfterHumanCost() {
        return afterHumanCost;
    }

    public void setAfterHumanCost(BigDecimal afterHumanCost) {
        this.afterHumanCost = afterHumanCost;
    }

    public BigDecimal getAfterTravelCost() {
        return afterTravelCost;
    }

    public void setAfterTravelCost(BigDecimal afterTravelCost) {
        this.afterTravelCost = afterTravelCost;
    }

    public BigDecimal getAfterNotTravelCost() {
        return afterNotTravelCost;
    }

    public void setAfterNotTravelCost(BigDecimal afterNotTravelCost) {
        this.afterNotTravelCost = afterNotTravelCost;
    }

    public BigDecimal getMaterialCost() {
        return materialCost;
    }

    public void setMaterialCost(BigDecimal materialCost) {
        this.materialCost = materialCost;
    }

    public BigDecimal getHumanCost() {
        return humanCost;
    }

    public void setHumanCost(BigDecimal humanCost) {
        this.humanCost = humanCost;
    }

    public BigDecimal getTravelCost() {
        return travelCost;
    }

    public void setTravelCost(BigDecimal travelCost) {
        this.travelCost = travelCost;
    }

    public BigDecimal getNotTravelCost() {
        return notTravelCost;
    }

    public void setNotTravelCost(BigDecimal notTravelCost) {
        this.notTravelCost = notTravelCost;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", changeSubmitBy=").append(changeSubmitBy);
        sb.append(", changeTime=").append(changeTime);
        sb.append(", changeReason=").append(changeReason);
        sb.append(", changeReasonDescribe=").append(changeReasonDescribe);
        sb.append(", afterMaterialCost=").append(afterMaterialCost);
        sb.append(", afterHumanCost=").append(afterHumanCost);
        sb.append(", afterTravelCost=").append(afterTravelCost);
        sb.append(", afterNotTravelCost=").append(afterNotTravelCost);
        sb.append(", materialCost=").append(materialCost);
        sb.append(", humanCost=").append(humanCost);
        sb.append(", travelCost=").append(travelCost);
        sb.append(", notTravelCost=").append(notTravelCost);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}