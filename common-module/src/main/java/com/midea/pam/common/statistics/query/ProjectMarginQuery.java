package com.midea.pam.common.statistics.query;

import com.midea.pam.common.statistics.entity.ProjectWbsExecuteDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString
@ApiModel(value = "ProjectMarginQuery", description = "项目利润报表")
public class ProjectMarginQuery {

    private Long id;

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    @ApiModelProperty(value = "报表ID")
    private Long reportId;

    @ApiModelProperty(value = "数据截止期间")
    private String deadlinePeriod;

    private Date deadlinePeriodBeginOfMonth;
    private Date deadlinePeriodEndOfMonth;
    private String deadlinePeriodEndOfMonthStr;
    private Date deadlinePeriodBeginOfYear;

    @ApiModelProperty(value = "业务实体id列表")
    private List<Long> ouIdList;

    @ApiModelProperty(value = "业务分类id列表")
    private List<Long> unitIdList;

    @ApiModelProperty(value = "项目类型id列表")
    private List<Long> projectTypeList;

    @ApiModelProperty(value = "项目状态列表")
    private List<Integer> projectStatusList;

    @ApiModelProperty(value = "立项审批通过日期-开始时间")
    private String approvalDateStart;

    @ApiModelProperty(value = "立项审批通过日期-结束时间")
    private String approvalDateEnd;

    @ApiModelProperty(value = "项目号")
    private String projectCode;

    @ApiModelProperty(value = "project_id+execute_id过滤条件")
    private List<ProjectWbsExecuteDetail> executeList;

    @ApiModelProperty(value = "虚拟部门列表")
    private List<Long> unitIds;

    private Date expireDate;

    private Long personal;

    private Long companyId;

    private Long createBy;

}
