package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class ProjectCostAssetDetailRecordExcelVO {

    @Excel(name = "序号")
    private int num;

    @Excel(name = "项目编号", width = 20)
    private String projectCode;

    @Excel(name = "项目名称", width = 20)
    private String projectName;

    @Excel(name = "资产编号", width = 20)
    private String assetNumber;

    @Excel(name = "资产说明", width = 50)
    private String description;

    @Excel(name = "启用日期", width = 20, format = "yyyy-MM-dd")
    private Date datePlacedInService;

    @Excel(name = "折旧月数")
    private Integer lifeInMonths;

    @Excel(name = "折旧期间", width = 15)
    private String periodName;

    @Excel(name = "当月折旧金额", width = 15)
    private BigDecimal deprnAmount;

    @Excel(name = "调整金额", width = 15)
    private BigDecimal adjustmentAmount;

    @Excel(name = "创建时间", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    private Date deprnCreateAt;
}
