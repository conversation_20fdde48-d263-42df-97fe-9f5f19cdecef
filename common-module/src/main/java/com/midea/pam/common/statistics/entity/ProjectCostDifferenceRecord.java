package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProjectCostDifferenceRecord extends LongIdEntity implements Serializable {
    private Long id;

    private Long executeId;

    private Long summaryId;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private String differenceType;

    private BigDecimal shareRadio;

    private BigDecimal amount;

    private Date costDate;

    private String differenceBigtype;

    private Boolean deletedFlag;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private BigDecimal materialDifferenceCost;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getSummaryId() {
        return summaryId;
    }

    public void setSummaryId(Long summaryId) {
        this.summaryId = summaryId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getDifferenceType() {
        return differenceType;
    }

    public void setDifferenceType(String differenceType) {
        this.differenceType = differenceType == null ? null : differenceType.trim();
    }

    public BigDecimal getShareRadio() {
        return shareRadio;
    }

    public void setShareRadio(BigDecimal shareRadio) {
        this.shareRadio = shareRadio;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Date getCostDate() {
        return costDate;
    }

    public void setCostDate(Date costDate) {
        this.costDate = costDate;
    }

    public String getDifferenceBigtype() {
        return differenceBigtype;
    }

    public void setDifferenceBigtype(String differenceBigtype) {
        this.differenceBigtype = differenceBigtype == null ? null : differenceBigtype.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    public BigDecimal getMaterialDifferenceCost() {
        return materialDifferenceCost;
    }

    public void setMaterialDifferenceCost(BigDecimal materialDifferenceCost) {
        this.materialDifferenceCost = materialDifferenceCost;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", executeId=").append(executeId);
        sb.append(", summaryId=").append(summaryId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", differenceType=").append(differenceType);
        sb.append(", shareRadio=").append(shareRadio);
        sb.append(", amount=").append(amount);
        sb.append(", costDate=").append(costDate);
        sb.append(", differenceBigtype=").append(differenceBigtype);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", materialDifferenceCost=").append(materialDifferenceCost);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}