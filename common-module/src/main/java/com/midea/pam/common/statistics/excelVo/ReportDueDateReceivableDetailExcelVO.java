package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

/**
 * Description 应收到期日期明细报表
 * Created by <PERSON><PERSON><PERSON>
 * Date 2021/11/17 9:52
 */
public class ReportDueDateReceivableDetailExcelVO {

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");

    @Excel(name = "子合同编号", width = 15)
    private String code;

    @Excel(name = "子合同名称", width = 30)
    private String name;

    @Excel(name = "主合同编号", width = 15)
    private String parentCode;

    @Excel(name = "主合同名称", width = 30)
    private String parentName;

    @Excel(name = "业务实体", width = 30)
    private String ouName;

    @Excel(name = "销售部门", width = 20)
    private String unitName;

    @Excel(name = "业务模式", width = 20)
    private String projectType;

    @Excel(name = "客户CRM编码", width = 30)
    private String customerCode;

    @Excel(name = "客户名称", width = 30)
    private String customerName;

    @Excel(name = "客户属性", width = 10, replace = {"外部客户_0", "内部客户_1", "_null"})
    private Integer customerType;

    @Excel(name = "项目编号", width = 15)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "项目经理", width = 15)
    private String projectManager;

    @Excel(name = "销售经理", width = 15)
    private String salesManager;

    @Excel(name = "币种", width = 10)
    private String currency;

    private BigDecimal amount;

    private BigDecimal excludingTaxAmount;

    private BigDecimal conversionRate;

    private BigDecimal standardAmount;

    private BigDecimal standardExcludingTaxAmount;

    private BigDecimal invoiceAmount;

    private BigDecimal receiptAmount;

    @Excel(name = "子合同含税金额（原币）", width = 20)
    private String amount_dt;

    @Excel(name = "子合同不含税金额（原币）", width = 20)
    private String excludingTaxAmount_dt;

    @Excel(name = "子合同汇率", width = 10)
    private String conversionRate_dt;

    @Excel(name = "子合同含税金额（本位币）", width = 20)
    private String standardAmount_dt;

    @Excel(name = "子合同不含税金额（本位币）", width = 20)
    private String standardExcludingTaxAmount_dt;

    @Excel(name = "子合同已开票金额（原币）", width = 20)
    private String invoiceAmount_dt;

    @Excel(name = "子合同已收款金额（原币）", width = 20)
    private String receiptAmount_dt;

    /*@Excel(name = "开票计划", width = 20)
    private String invoicePlanDetailCode;*/

    @Excel(name = "开票申请", width = 20)
    private String invoiceApplyCode;

    @Excel(name = "开票申请人", width = 20)
    private String invoiceApplyCreateUserName;

    private BigDecimal receivableAmount;

    private BigDecimal claimMount;

    private BigDecimal overageAmount;

    private Date expireDate;

    @Excel(name = "应收金额", width = 20)
    private String receivableAmount_dt;

    @Excel(name = "已回款金额", width = 20)
    private String claimMount_dt;

    @Excel(name = "应收余额", width = 20)
    private String overageAmount_dt;

    @Excel(name = "应收到期日", width = 20)
    private String expireDate_dt;

    @Excel(name = "逾期账龄", width = 20)
    private String overdueAges;

    @Excel(name = "截止入账期间", width = 20)
    private String statisticDate;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager;
    }

    public String getSalesManager() {
        return salesManager;
    }

    public void setSalesManager(String salesManager) {
        this.salesManager = salesManager;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getExcludingTaxAmount() {
        return excludingTaxAmount;
    }

    public void setExcludingTaxAmount(BigDecimal excludingTaxAmount) {
        this.excludingTaxAmount = excludingTaxAmount;
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public BigDecimal getStandardAmount() {
        return standardAmount;
    }

    public void setStandardAmount(BigDecimal standardAmount) {
        this.standardAmount = standardAmount;
    }

    public BigDecimal getStandardExcludingTaxAmount() {
        return standardExcludingTaxAmount;
    }

    public void setStandardExcludingTaxAmount(BigDecimal standardExcludingTaxAmount) {
        this.standardExcludingTaxAmount = standardExcludingTaxAmount;
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public BigDecimal getReceiptAmount() {
        return receiptAmount;
    }

    public void setReceiptAmount(BigDecimal receiptAmount) {
        this.receiptAmount = receiptAmount;
    }

    public String getAmount_dt() {
        if (Objects.nonNull(this.amount)) {
            return this.amount.stripTrailingZeros().toPlainString();
        } else {
            return this.amount_dt;
        }
    }

    public void setAmount_dt(String amount_dt) {
        this.amount_dt = amount_dt;
    }

    public String getExcludingTaxAmount_dt() {
        if (Objects.nonNull(this.excludingTaxAmount)) {
            return this.excludingTaxAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.excludingTaxAmount_dt;
        }
    }

    public void setExcludingTaxAmount_dt(String excludingTaxAmount_dt) {
        this.excludingTaxAmount_dt = excludingTaxAmount_dt;
    }

    public String getConversionRate_dt() {
        if (Objects.nonNull(this.conversionRate)) {
            return this.conversionRate.stripTrailingZeros().toPlainString();
        } else {
            return this.conversionRate_dt;
        }
    }

    public void setConversionRate_dt(String conversionRate_dt) {
        this.conversionRate_dt = conversionRate_dt;
    }

    public String getStandardAmount_dt() {
        if (Objects.nonNull(this.standardAmount)) {
            return this.standardAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.standardAmount_dt;
        }
    }

    public void setStandardAmount_dt(String standardAmount_dt) {
        this.standardAmount_dt = standardAmount_dt;
    }

    public String getStandardExcludingTaxAmount_dt() {
        if (Objects.nonNull(this.standardExcludingTaxAmount)) {
            return this.standardExcludingTaxAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.standardExcludingTaxAmount_dt;
        }
    }

    public void setStandardExcludingTaxAmount_dt(String standardExcludingTaxAmount_dt) {
        this.standardExcludingTaxAmount_dt = standardExcludingTaxAmount_dt;
    }

    public String getInvoiceAmount_dt() {
        if (Objects.nonNull(this.invoiceAmount)) {
            return this.invoiceAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.invoiceAmount_dt;
        }
    }

    public void setInvoiceAmount_dt(String invoiceAmount_dt) {
        this.invoiceAmount_dt = invoiceAmount_dt;
    }

    public String getReceiptAmount_dt() {
        if (Objects.nonNull(this.receiptAmount)) {
            return this.receiptAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.receiptAmount_dt;
        }
    }

    public void setReceiptAmount_dt(String receiptAmount_dt) {
        this.receiptAmount_dt = receiptAmount_dt;
    }

    /*public String getInvoicePlanDetailCode() {
        return invoicePlanDetailCode;
    }

    public void setInvoicePlanDetailCode(String invoicePlanDetailCode) {
        this.invoicePlanDetailCode = invoicePlanDetailCode;
    }*/

    public String getInvoiceApplyCode() {
        return invoiceApplyCode;
    }

    public void setInvoiceApplyCode(String invoiceApplyCode) {
        this.invoiceApplyCode = invoiceApplyCode;
    }

    public String getInvoiceApplyCreateUserName() {
        return invoiceApplyCreateUserName;
    }

    public void setInvoiceApplyCreateUserName(String invoiceApplyCreateUserName) {
        this.invoiceApplyCreateUserName = invoiceApplyCreateUserName;
    }

    public BigDecimal getReceivableAmount() {
        return receivableAmount;
    }

    public void setReceivableAmount(BigDecimal receivableAmount) {
        this.receivableAmount = receivableAmount;
    }

    public BigDecimal getClaimMount() {
        return claimMount;
    }

    public void setClaimMount(BigDecimal claimMount) {
        this.claimMount = claimMount;
    }

    public BigDecimal getOverageAmount() {
        return overageAmount;
    }

    public void setOverageAmount(BigDecimal overageAmount) {
        this.overageAmount = overageAmount;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public String getReceivableAmount_dt() {
        if (Objects.nonNull(this.receivableAmount)) {
            return this.receivableAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.receivableAmount_dt;
        }
    }

    public void setReceivableAmount_dt(String receivableAmount_dt) {
        this.receivableAmount_dt = receivableAmount_dt;
    }

    public String getClaimMount_dt() {
        if (Objects.nonNull(this.claimMount)) {
            return this.claimMount.stripTrailingZeros().toPlainString();
        } else {
            return this.claimMount_dt;
        }
    }

    public void setClaimMount_dt(String claimMount_dt) {
        this.claimMount_dt = claimMount_dt;
    }

    public String getOverageAmount_dt() {
        if (Objects.nonNull(this.overageAmount)) {
            return this.overageAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.overageAmount_dt;
        }
    }

    public void setOverageAmount_dt(String overageAmount_dt) {
        this.overageAmount_dt = overageAmount_dt;
    }

    public String getExpireDate_dt() {
        if (Objects.nonNull(this.expireDate)) {
            return sdf.format(this.expireDate);
        } else {
            return this.expireDate_dt;
        }
    }

    public void setExpireDate_dt(String expireDate_dt) {
        this.expireDate_dt = expireDate_dt;
    }

    public String getOverdueAges() {
        return overdueAges;
    }

    public void setOverdueAges(String overdueAges) {
        this.overdueAges = overdueAges;
    }

    public String getStatisticDate() {
        return statisticDate;
    }

    public void setStatisticDate(String statisticDate) {
        this.statisticDate = statisticDate;
    }
}
