package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/19
 */
@Getter
@Setter
@ToString
@ApiModel(value = "WorkingHourIhrAttendQuery", description = "考勤工时与项目工时汇总表")
public class WorkingHourIhrAttendQuery {

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    @ApiModelProperty(value = "报表ID")
    private Long reportId;

    @ApiModelProperty(value = "出勤日期开始时间")
    private String attendanceStartDate;

    @ApiModelProperty(value = "出勤日期结束时间")
    private String attendanceEndDate;

    @ApiModelProperty(value = "部门id")
    private List<Long> orgIds;

    @ApiModelProperty(value = "部门名称")
    private List<String> orgNames;

    private Long personal;

    private Long companyId;
}
