package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.PortalTarget;

import java.util.List;

public class PortalTodoBusinessDto {

    private String targetName;
    private String targetCode;
    private Long count;
    private List<Long> ids;

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }

    public String getTargetCode() {
        return targetCode;
    }

    public void setTargetCode(String targetCode) {
        this.targetCode = targetCode;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }
}