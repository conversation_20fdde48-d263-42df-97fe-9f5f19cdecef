package com.midea.pam.common.statistics.excelVo;


import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022-11-24
 * @description 需求预算-人力点工需求预算
 */
@Getter
@Setter
public class HroRequirementBudgetExcelVO {

    @Excel(name = "序号", width = 5)
    private Integer number;

    @Excel(name = "WBS", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项", width = 15)
    private String activityCode;

    @Excel(name = "人力点工需求单据编号", width = 25)
    private String requirementCode;

    @Excel(name = "角色", width = 25)
    private String roleName;

    @Excel(name = "需求情况", width = 25)
    private String receiptsType;

    @Excel(name = "总预算（需求预算）", width = 15)
    private BigDecimal budgetOccupiedAmount;

    @Excel(name = "累计采购合同占用金额", width = 15)
    private BigDecimal downAmount;

    @Excel(name = "当前人力点工需求预算金额", width = 15)
    private BigDecimal remainingCostAmount;

}
