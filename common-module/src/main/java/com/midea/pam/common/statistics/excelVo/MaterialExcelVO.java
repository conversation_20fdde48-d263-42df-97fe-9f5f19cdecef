package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/5/21
 * @description 物料导出模版
 */
@Getter
@Setter
public class MaterialExcelVO {
    private Long id;

    @Excel(name = "序号", width = 10)
    private int num;

    @Excel(name = "PAM编码", width = 15, replace = {"-_null"})
    private String pamCode;

    @Excel(name = "ERP编码", width = 15, replace = {"-_null"})
    private String itemCode;

    @Excel(name = "物料描述", width = 15, replace = {"-_null"})
    private String itemInfo;

    @Excel(name = "基本计量单位", width = 15, replace = {"-_null"})
    private String unit;

    @Excel(name = "物料状态", width = 15, replace = {"-_null"})
    private String itemStatusName;

    private String itemStatus;

    @Excel(name = "是否待退市物料", width = 20, replace = {"否_false", "是_true", "_null"})
    private Boolean delistFlag;

    @Excel(name = "物料大类", width = 15, replace = {"-_null"})
    private String materialClassification;

    @Excel(name = "物料中类", width = 15, replace = {"-_null"})
    private String codingMiddleclass;

    @Excel(name = "物料小类", width = 15, replace = {"-_null"})
    private String materialType;

    @Excel(name = "名称", width = 15, replace = {"-_null"})
    private String name;

    @Excel(name = "型号/规格/图号", width = 15, replace = {"-_null"})
    private String model;

    @Excel(name = "品牌", width = 15, replace = {"-_null"})
    private String brand;

    @Excel(name = "加工分类", width = 15, replace = {"-_null"})
    private String machiningPartType;

    @Excel(name = "材质", width = 15, replace = {"-_null"})
    private String material;

    @Excel(name = "单位重量（Kg）", width = 15, replace = {"-_null"})
    private String unitWeight;

    @Excel(name = "材质处理", width = 15, replace = {"-_null"})
    private String materialProcessing;

    @Excel(name = "最小订货量", width = 15, replace = {"-_null"})
    private Long minimumOrderQuantity;

    @Excel(name = "接受子库存", width = 15, replace = {"-_null"})
    private String recevingSubinventory;

    @Excel(name = "货架", width = 15, replace = {"-_null"})
    private String shelves;

    @Excel(name = "配套人员", width = 15, replace = {"-_null"})
    private String sourcer;

    @Excel(name = "安全库存", width = 15, replace = {"-_null"})
    private Long safetyStockQuantity;

    @Excel(name = "用户物料类型", width = 15, replace = {"-_null"})
    private String itemTypeName;

    private String itemType;

    @Excel(name = "采购员", width = 15, replace = {"-_null"})
    private String buyerNumber;

    @Excel(name = "采购周期", width = 15, replace = {"-_null"})
    private String buyerRound;

    @Excel(name = "库存分类", width = 15, replace = {"-_null"})
    private String inventoryType;

    @Excel(name = "采购分类", width = 15, replace = {"-_null"})
    private String purType;

    @Excel(name = "库存组织名称", width = 15, replace = {"-_null"})
    private String organizationName;

    @Excel(name = "创建日期", width = 15, format = "yyyy-MM-dd", replace = {"-_null"})
    private Date create_at;

    @Excel(name = "创建人", width = 15, replace = {"-_null"})
    private String creatorName;

    @Excel(name = "最后更新日期", width = 15, format = "yyyy-MM-dd", replace = {"-_null"})
    private Date update_at;

    @Excel(name = "最后更新人", width = 15, replace = {"-_null"})
    private String updaterName;

    @Excel(name = "图号", width = 15, replace = {"-_null"})
    private String figureNumber;

    @Excel(name = "图纸版本号", width = 15, replace = {"-_null"})
    private String chartVersion;

    @Excel(name = "品牌商物料编码", width = 15, replace = {"-_null"})
    private String brandMaterialCode;

    @Excel(name = "备注", width = 20)
    private String remark;
}
