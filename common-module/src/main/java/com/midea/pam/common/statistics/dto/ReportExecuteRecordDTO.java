package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.ReportExecuteParameter;
import com.midea.pam.common.statistics.entity.ReportExecuteRecord;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/23
 * @description 报表执行
 */
@Getter
@Setter
public class ReportExecuteRecordDTO extends ReportExecuteRecord {

    /**
     * 报表名称
     */
    private String reportName;

    /**
     * 报表类型
     */
    private String reportType;

    /**
     * 报表编号
     */
    private String reportCode;

    /**
     * 提交人
     */
    private String userName;

    /**
     * 提交人MIP
     */
    private String userMip;

    /**
     * 本人使用次数
     */
    private Integer useNum;

    /**
     * 总使用次数
     */
    private Integer totalUseNum;

    /**
     * 报表类型列表
     */
    private List<String> reportTypes;

    /**
     * 使用单位名称
     */
    private String companyName;

    /**
     * 状态列表，多个用,隔开
     */
    private String statusesStr;

    /**
     * 状态列表
     */
    private List<Integer> statuses;

    /**
     * 执行参数
     */
    private List<ReportExecuteParameter> executeParameters;

    /**
     * 报表组id
     */
    private Long reportGroupId;

    /**
     * 报表组名称
     */
    private String reportGroupName;

}
