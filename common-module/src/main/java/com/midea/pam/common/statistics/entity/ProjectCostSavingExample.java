package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectCostSavingExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectCostSavingExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIsNull() {
            addCriterion("requirement_code is null");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIsNotNull() {
            addCriterion("requirement_code is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeEqualTo(String value) {
            addCriterion("requirement_code =", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotEqualTo(String value) {
            addCriterion("requirement_code <>", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeGreaterThan(String value) {
            addCriterion("requirement_code >", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeGreaterThanOrEqualTo(String value) {
            addCriterion("requirement_code >=", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLessThan(String value) {
            addCriterion("requirement_code <", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLessThanOrEqualTo(String value) {
            addCriterion("requirement_code <=", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLike(String value) {
            addCriterion("requirement_code like", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotLike(String value) {
            addCriterion("requirement_code not like", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIn(List<String> values) {
            addCriterion("requirement_code in", values, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotIn(List<String> values) {
            addCriterion("requirement_code not in", values, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeBetween(String value1, String value2) {
            addCriterion("requirement_code between", value1, value2, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotBetween(String value1, String value2) {
            addCriterion("requirement_code not between", value1, value2, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNull() {
            addCriterion("wbs_summary_code is null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNotNull() {
            addCriterion("wbs_summary_code is not null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeEqualTo(String value) {
            addCriterion("wbs_summary_code =", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotEqualTo(String value) {
            addCriterion("wbs_summary_code <>", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThan(String value) {
            addCriterion("wbs_summary_code >", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code >=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThan(String value) {
            addCriterion("wbs_summary_code <", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code <=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLike(String value) {
            addCriterion("wbs_summary_code like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotLike(String value) {
            addCriterion("wbs_summary_code not like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIn(List<String> values) {
            addCriterion("wbs_summary_code in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotIn(List<String> values) {
            addCriterion("wbs_summary_code not in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeBetween(String value1, String value2) {
            addCriterion("wbs_summary_code between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotBetween(String value1, String value2) {
            addCriterion("wbs_summary_code not between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNull() {
            addCriterion("activity_code is null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNotNull() {
            addCriterion("activity_code is not null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeEqualTo(String value) {
            addCriterion("activity_code =", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotEqualTo(String value) {
            addCriterion("activity_code <>", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThan(String value) {
            addCriterion("activity_code >", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("activity_code >=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThan(String value) {
            addCriterion("activity_code <", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThanOrEqualTo(String value) {
            addCriterion("activity_code <=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLike(String value) {
            addCriterion("activity_code like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotLike(String value) {
            addCriterion("activity_code not like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIn(List<String> values) {
            addCriterion("activity_code in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotIn(List<String> values) {
            addCriterion("activity_code not in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeBetween(String value1, String value2) {
            addCriterion("activity_code between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotBetween(String value1, String value2) {
            addCriterion("activity_code not between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusIsNull() {
            addCriterion("requirement_status is null");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusIsNotNull() {
            addCriterion("requirement_status is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusEqualTo(Integer value) {
            addCriterion("requirement_status =", value, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusNotEqualTo(Integer value) {
            addCriterion("requirement_status <>", value, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusGreaterThan(Integer value) {
            addCriterion("requirement_status >", value, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("requirement_status >=", value, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusLessThan(Integer value) {
            addCriterion("requirement_status <", value, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusLessThanOrEqualTo(Integer value) {
            addCriterion("requirement_status <=", value, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusIn(List<Integer> values) {
            addCriterion("requirement_status in", values, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusNotIn(List<Integer> values) {
            addCriterion("requirement_status not in", values, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusBetween(Integer value1, Integer value2) {
            addCriterion("requirement_status between", value1, value2, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("requirement_status not between", value1, value2, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andDemandTypeIsNull() {
            addCriterion("demand_type is null");
            return (Criteria) this;
        }

        public Criteria andDemandTypeIsNotNull() {
            addCriterion("demand_type is not null");
            return (Criteria) this;
        }

        public Criteria andDemandTypeEqualTo(Integer value) {
            addCriterion("demand_type =", value, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeNotEqualTo(Integer value) {
            addCriterion("demand_type <>", value, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeGreaterThan(Integer value) {
            addCriterion("demand_type >", value, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("demand_type >=", value, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeLessThan(Integer value) {
            addCriterion("demand_type <", value, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeLessThanOrEqualTo(Integer value) {
            addCriterion("demand_type <=", value, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeIn(List<Integer> values) {
            addCriterion("demand_type in", values, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeNotIn(List<Integer> values) {
            addCriterion("demand_type not in", values, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeBetween(Integer value1, Integer value2) {
            addCriterion("demand_type between", value1, value2, "demandType");
            return (Criteria) this;
        }

        public Criteria andDemandTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("demand_type not between", value1, value2, "demandType");
            return (Criteria) this;
        }

        public Criteria andReceiptsStatusIsNull() {
            addCriterion("receipts_status is null");
            return (Criteria) this;
        }

        public Criteria andReceiptsStatusIsNotNull() {
            addCriterion("receipts_status is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptsStatusEqualTo(Byte value) {
            addCriterion("receipts_status =", value, "receiptsStatus");
            return (Criteria) this;
        }

        public Criteria andReceiptsStatusNotEqualTo(Byte value) {
            addCriterion("receipts_status <>", value, "receiptsStatus");
            return (Criteria) this;
        }

        public Criteria andReceiptsStatusGreaterThan(Byte value) {
            addCriterion("receipts_status >", value, "receiptsStatus");
            return (Criteria) this;
        }

        public Criteria andReceiptsStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("receipts_status >=", value, "receiptsStatus");
            return (Criteria) this;
        }

        public Criteria andReceiptsStatusLessThan(Byte value) {
            addCriterion("receipts_status <", value, "receiptsStatus");
            return (Criteria) this;
        }

        public Criteria andReceiptsStatusLessThanOrEqualTo(Byte value) {
            addCriterion("receipts_status <=", value, "receiptsStatus");
            return (Criteria) this;
        }

        public Criteria andReceiptsStatusIn(List<Byte> values) {
            addCriterion("receipts_status in", values, "receiptsStatus");
            return (Criteria) this;
        }

        public Criteria andReceiptsStatusNotIn(List<Byte> values) {
            addCriterion("receipts_status not in", values, "receiptsStatus");
            return (Criteria) this;
        }

        public Criteria andReceiptsStatusBetween(Byte value1, Byte value2) {
            addCriterion("receipts_status between", value1, value2, "receiptsStatus");
            return (Criteria) this;
        }

        public Criteria andReceiptsStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("receipts_status not between", value1, value2, "receiptsStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusIsNull() {
            addCriterion("purchase_status is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusIsNotNull() {
            addCriterion("purchase_status is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusEqualTo(Integer value) {
            addCriterion("purchase_status =", value, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusNotEqualTo(Integer value) {
            addCriterion("purchase_status <>", value, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusGreaterThan(Integer value) {
            addCriterion("purchase_status >", value, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("purchase_status >=", value, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusLessThan(Integer value) {
            addCriterion("purchase_status <", value, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusLessThanOrEqualTo(Integer value) {
            addCriterion("purchase_status <=", value, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusIn(List<Integer> values) {
            addCriterion("purchase_status in", values, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusNotIn(List<Integer> values) {
            addCriterion("purchase_status not in", values, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusBetween(Integer value1, Integer value2) {
            addCriterion("purchase_status between", value1, value2, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("purchase_status not between", value1, value2, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andDemandApprovalTimeIsNull() {
            addCriterion("demand_approval_time is null");
            return (Criteria) this;
        }

        public Criteria andDemandApprovalTimeIsNotNull() {
            addCriterion("demand_approval_time is not null");
            return (Criteria) this;
        }

        public Criteria andDemandApprovalTimeEqualTo(Date value) {
            addCriterion("demand_approval_time =", value, "demandApprovalTime");
            return (Criteria) this;
        }

        public Criteria andDemandApprovalTimeNotEqualTo(Date value) {
            addCriterion("demand_approval_time <>", value, "demandApprovalTime");
            return (Criteria) this;
        }

        public Criteria andDemandApprovalTimeGreaterThan(Date value) {
            addCriterion("demand_approval_time >", value, "demandApprovalTime");
            return (Criteria) this;
        }

        public Criteria andDemandApprovalTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("demand_approval_time >=", value, "demandApprovalTime");
            return (Criteria) this;
        }

        public Criteria andDemandApprovalTimeLessThan(Date value) {
            addCriterion("demand_approval_time <", value, "demandApprovalTime");
            return (Criteria) this;
        }

        public Criteria andDemandApprovalTimeLessThanOrEqualTo(Date value) {
            addCriterion("demand_approval_time <=", value, "demandApprovalTime");
            return (Criteria) this;
        }

        public Criteria andDemandApprovalTimeIn(List<Date> values) {
            addCriterion("demand_approval_time in", values, "demandApprovalTime");
            return (Criteria) this;
        }

        public Criteria andDemandApprovalTimeNotIn(List<Date> values) {
            addCriterion("demand_approval_time not in", values, "demandApprovalTime");
            return (Criteria) this;
        }

        public Criteria andDemandApprovalTimeBetween(Date value1, Date value2) {
            addCriterion("demand_approval_time between", value1, value2, "demandApprovalTime");
            return (Criteria) this;
        }

        public Criteria andDemandApprovalTimeNotBetween(Date value1, Date value2) {
            addCriterion("demand_approval_time not between", value1, value2, "demandApprovalTime");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountIsNull() {
            addCriterion("budget_occupied_amount is null");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountIsNotNull() {
            addCriterion("budget_occupied_amount is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountEqualTo(BigDecimal value) {
            addCriterion("budget_occupied_amount =", value, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountNotEqualTo(BigDecimal value) {
            addCriterion("budget_occupied_amount <>", value, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountGreaterThan(BigDecimal value) {
            addCriterion("budget_occupied_amount >", value, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_occupied_amount >=", value, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountLessThan(BigDecimal value) {
            addCriterion("budget_occupied_amount <", value, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_occupied_amount <=", value, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountIn(List<BigDecimal> values) {
            addCriterion("budget_occupied_amount in", values, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountNotIn(List<BigDecimal> values) {
            addCriterion("budget_occupied_amount not in", values, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_occupied_amount between", value1, value2, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andBudgetOccupiedAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_occupied_amount not between", value1, value2, "budgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityIsNull() {
            addCriterion("released_quantity is null");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityIsNotNull() {
            addCriterion("released_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityEqualTo(BigDecimal value) {
            addCriterion("released_quantity =", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityNotEqualTo(BigDecimal value) {
            addCriterion("released_quantity <>", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityGreaterThan(BigDecimal value) {
            addCriterion("released_quantity >", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("released_quantity >=", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityLessThan(BigDecimal value) {
            addCriterion("released_quantity <", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("released_quantity <=", value, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityIn(List<BigDecimal> values) {
            addCriterion("released_quantity in", values, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityNotIn(List<BigDecimal> values) {
            addCriterion("released_quantity not in", values, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("released_quantity between", value1, value2, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andReleasedQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("released_quantity not between", value1, value2, "releasedQuantity");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIsNull() {
            addCriterion("total_amount is null");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIsNotNull() {
            addCriterion("total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTotalAmountEqualTo(BigDecimal value) {
            addCriterion("total_amount =", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("total_amount <>", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("total_amount >", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_amount >=", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLessThan(BigDecimal value) {
            addCriterion("total_amount <", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_amount <=", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIn(List<BigDecimal> values) {
            addCriterion("total_amount in", values, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("total_amount not in", values, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_amount between", value1, value2, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_amount not between", value1, value2, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andSavingIsNull() {
            addCriterion("saving is null");
            return (Criteria) this;
        }

        public Criteria andSavingIsNotNull() {
            addCriterion("saving is not null");
            return (Criteria) this;
        }

        public Criteria andSavingEqualTo(BigDecimal value) {
            addCriterion("saving =", value, "saving");
            return (Criteria) this;
        }

        public Criteria andSavingNotEqualTo(BigDecimal value) {
            addCriterion("saving <>", value, "saving");
            return (Criteria) this;
        }

        public Criteria andSavingGreaterThan(BigDecimal value) {
            addCriterion("saving >", value, "saving");
            return (Criteria) this;
        }

        public Criteria andSavingGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("saving >=", value, "saving");
            return (Criteria) this;
        }

        public Criteria andSavingLessThan(BigDecimal value) {
            addCriterion("saving <", value, "saving");
            return (Criteria) this;
        }

        public Criteria andSavingLessThanOrEqualTo(BigDecimal value) {
            addCriterion("saving <=", value, "saving");
            return (Criteria) this;
        }

        public Criteria andSavingIn(List<BigDecimal> values) {
            addCriterion("saving in", values, "saving");
            return (Criteria) this;
        }

        public Criteria andSavingNotIn(List<BigDecimal> values) {
            addCriterion("saving not in", values, "saving");
            return (Criteria) this;
        }

        public Criteria andSavingBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("saving between", value1, value2, "saving");
            return (Criteria) this;
        }

        public Criteria andSavingNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("saving not between", value1, value2, "saving");
            return (Criteria) this;
        }

        public Criteria andReceiptQuantityIsNull() {
            addCriterion("receipt_quantity is null");
            return (Criteria) this;
        }

        public Criteria andReceiptQuantityIsNotNull() {
            addCriterion("receipt_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptQuantityEqualTo(BigDecimal value) {
            addCriterion("receipt_quantity =", value, "receiptQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiptQuantityNotEqualTo(BigDecimal value) {
            addCriterion("receipt_quantity <>", value, "receiptQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiptQuantityGreaterThan(BigDecimal value) {
            addCriterion("receipt_quantity >", value, "receiptQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiptQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("receipt_quantity >=", value, "receiptQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiptQuantityLessThan(BigDecimal value) {
            addCriterion("receipt_quantity <", value, "receiptQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiptQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("receipt_quantity <=", value, "receiptQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiptQuantityIn(List<BigDecimal> values) {
            addCriterion("receipt_quantity in", values, "receiptQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiptQuantityNotIn(List<BigDecimal> values) {
            addCriterion("receipt_quantity not in", values, "receiptQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiptQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("receipt_quantity between", value1, value2, "receiptQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiptQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("receipt_quantity not between", value1, value2, "receiptQuantity");
            return (Criteria) this;
        }

        public Criteria andReceiptAmountIsNull() {
            addCriterion("receipt_amount is null");
            return (Criteria) this;
        }

        public Criteria andReceiptAmountIsNotNull() {
            addCriterion("receipt_amount is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptAmountEqualTo(BigDecimal value) {
            addCriterion("receipt_amount =", value, "receiptAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptAmountNotEqualTo(BigDecimal value) {
            addCriterion("receipt_amount <>", value, "receiptAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptAmountGreaterThan(BigDecimal value) {
            addCriterion("receipt_amount >", value, "receiptAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("receipt_amount >=", value, "receiptAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptAmountLessThan(BigDecimal value) {
            addCriterion("receipt_amount <", value, "receiptAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("receipt_amount <=", value, "receiptAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptAmountIn(List<BigDecimal> values) {
            addCriterion("receipt_amount in", values, "receiptAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptAmountNotIn(List<BigDecimal> values) {
            addCriterion("receipt_amount not in", values, "receiptAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("receipt_amount between", value1, value2, "receiptAmount");
            return (Criteria) this;
        }

        public Criteria andReceiptAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("receipt_amount not between", value1, value2, "receiptAmount");
            return (Criteria) this;
        }

        public Criteria andPurchaseCodesIsNull() {
            addCriterion("purchase_codes is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseCodesIsNotNull() {
            addCriterion("purchase_codes is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseCodesEqualTo(String value) {
            addCriterion("purchase_codes =", value, "purchaseCodes");
            return (Criteria) this;
        }

        public Criteria andPurchaseCodesNotEqualTo(String value) {
            addCriterion("purchase_codes <>", value, "purchaseCodes");
            return (Criteria) this;
        }

        public Criteria andPurchaseCodesGreaterThan(String value) {
            addCriterion("purchase_codes >", value, "purchaseCodes");
            return (Criteria) this;
        }

        public Criteria andPurchaseCodesGreaterThanOrEqualTo(String value) {
            addCriterion("purchase_codes >=", value, "purchaseCodes");
            return (Criteria) this;
        }

        public Criteria andPurchaseCodesLessThan(String value) {
            addCriterion("purchase_codes <", value, "purchaseCodes");
            return (Criteria) this;
        }

        public Criteria andPurchaseCodesLessThanOrEqualTo(String value) {
            addCriterion("purchase_codes <=", value, "purchaseCodes");
            return (Criteria) this;
        }

        public Criteria andPurchaseCodesLike(String value) {
            addCriterion("purchase_codes like", value, "purchaseCodes");
            return (Criteria) this;
        }

        public Criteria andPurchaseCodesNotLike(String value) {
            addCriterion("purchase_codes not like", value, "purchaseCodes");
            return (Criteria) this;
        }

        public Criteria andPurchaseCodesIn(List<String> values) {
            addCriterion("purchase_codes in", values, "purchaseCodes");
            return (Criteria) this;
        }

        public Criteria andPurchaseCodesNotIn(List<String> values) {
            addCriterion("purchase_codes not in", values, "purchaseCodes");
            return (Criteria) this;
        }

        public Criteria andPurchaseCodesBetween(String value1, String value2) {
            addCriterion("purchase_codes between", value1, value2, "purchaseCodes");
            return (Criteria) this;
        }

        public Criteria andPurchaseCodesNotBetween(String value1, String value2) {
            addCriterion("purchase_codes not between", value1, value2, "purchaseCodes");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIsNull() {
            addCriterion("vendor_code is null");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIsNotNull() {
            addCriterion("vendor_code is not null");
            return (Criteria) this;
        }

        public Criteria andVendorCodeEqualTo(String value) {
            addCriterion("vendor_code =", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotEqualTo(String value) {
            addCriterion("vendor_code <>", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThan(String value) {
            addCriterion("vendor_code >", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_code >=", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThan(String value) {
            addCriterion("vendor_code <", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThanOrEqualTo(String value) {
            addCriterion("vendor_code <=", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLike(String value) {
            addCriterion("vendor_code like", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotLike(String value) {
            addCriterion("vendor_code not like", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIn(List<String> values) {
            addCriterion("vendor_code in", values, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotIn(List<String> values) {
            addCriterion("vendor_code not in", values, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeBetween(String value1, String value2) {
            addCriterion("vendor_code between", value1, value2, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotBetween(String value1, String value2) {
            addCriterion("vendor_code not between", value1, value2, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNull() {
            addCriterion("vendor_name is null");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNotNull() {
            addCriterion("vendor_name is not null");
            return (Criteria) this;
        }

        public Criteria andVendorNameEqualTo(String value) {
            addCriterion("vendor_name =", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotEqualTo(String value) {
            addCriterion("vendor_name <>", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThan(String value) {
            addCriterion("vendor_name >", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_name >=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThan(String value) {
            addCriterion("vendor_name <", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThanOrEqualTo(String value) {
            addCriterion("vendor_name <=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLike(String value) {
            addCriterion("vendor_name like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotLike(String value) {
            addCriterion("vendor_name not like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameIn(List<String> values) {
            addCriterion("vendor_name in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotIn(List<String> values) {
            addCriterion("vendor_name not in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameBetween(String value1, String value2) {
            addCriterion("vendor_name between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotBetween(String value1, String value2) {
            addCriterion("vendor_name not between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeIsNull() {
            addCriterion("vendor_site_code is null");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeIsNotNull() {
            addCriterion("vendor_site_code is not null");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeEqualTo(String value) {
            addCriterion("vendor_site_code =", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotEqualTo(String value) {
            addCriterion("vendor_site_code <>", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeGreaterThan(String value) {
            addCriterion("vendor_site_code >", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_site_code >=", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeLessThan(String value) {
            addCriterion("vendor_site_code <", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeLessThanOrEqualTo(String value) {
            addCriterion("vendor_site_code <=", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeLike(String value) {
            addCriterion("vendor_site_code like", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotLike(String value) {
            addCriterion("vendor_site_code not like", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeIn(List<String> values) {
            addCriterion("vendor_site_code in", values, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotIn(List<String> values) {
            addCriterion("vendor_site_code not in", values, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeBetween(String value1, String value2) {
            addCriterion("vendor_site_code between", value1, value2, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotBetween(String value1, String value2) {
            addCriterion("vendor_site_code not between", value1, value2, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andBuyerNamesIsNull() {
            addCriterion("buyer_names is null");
            return (Criteria) this;
        }

        public Criteria andBuyerNamesIsNotNull() {
            addCriterion("buyer_names is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerNamesEqualTo(String value) {
            addCriterion("buyer_names =", value, "buyerNames");
            return (Criteria) this;
        }

        public Criteria andBuyerNamesNotEqualTo(String value) {
            addCriterion("buyer_names <>", value, "buyerNames");
            return (Criteria) this;
        }

        public Criteria andBuyerNamesGreaterThan(String value) {
            addCriterion("buyer_names >", value, "buyerNames");
            return (Criteria) this;
        }

        public Criteria andBuyerNamesGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_names >=", value, "buyerNames");
            return (Criteria) this;
        }

        public Criteria andBuyerNamesLessThan(String value) {
            addCriterion("buyer_names <", value, "buyerNames");
            return (Criteria) this;
        }

        public Criteria andBuyerNamesLessThanOrEqualTo(String value) {
            addCriterion("buyer_names <=", value, "buyerNames");
            return (Criteria) this;
        }

        public Criteria andBuyerNamesLike(String value) {
            addCriterion("buyer_names like", value, "buyerNames");
            return (Criteria) this;
        }

        public Criteria andBuyerNamesNotLike(String value) {
            addCriterion("buyer_names not like", value, "buyerNames");
            return (Criteria) this;
        }

        public Criteria andBuyerNamesIn(List<String> values) {
            addCriterion("buyer_names in", values, "buyerNames");
            return (Criteria) this;
        }

        public Criteria andBuyerNamesNotIn(List<String> values) {
            addCriterion("buyer_names not in", values, "buyerNames");
            return (Criteria) this;
        }

        public Criteria andBuyerNamesBetween(String value1, String value2) {
            addCriterion("buyer_names between", value1, value2, "buyerNames");
            return (Criteria) this;
        }

        public Criteria andBuyerNamesNotBetween(String value1, String value2) {
            addCriterion("buyer_names not between", value1, value2, "buyerNames");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNull() {
            addCriterion("pam_code is null");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNotNull() {
            addCriterion("pam_code is not null");
            return (Criteria) this;
        }

        public Criteria andPamCodeEqualTo(String value) {
            addCriterion("pam_code =", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotEqualTo(String value) {
            addCriterion("pam_code <>", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThan(String value) {
            addCriterion("pam_code >", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("pam_code >=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThan(String value) {
            addCriterion("pam_code <", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThanOrEqualTo(String value) {
            addCriterion("pam_code <=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLike(String value) {
            addCriterion("pam_code like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotLike(String value) {
            addCriterion("pam_code not like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeIn(List<String> values) {
            addCriterion("pam_code in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotIn(List<String> values) {
            addCriterion("pam_code not in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeBetween(String value1, String value2) {
            addCriterion("pam_code between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotBetween(String value1, String value2) {
            addCriterion("pam_code not between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNull() {
            addCriterion("erp_code is null");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNotNull() {
            addCriterion("erp_code is not null");
            return (Criteria) this;
        }

        public Criteria andErpCodeEqualTo(String value) {
            addCriterion("erp_code =", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotEqualTo(String value) {
            addCriterion("erp_code <>", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThan(String value) {
            addCriterion("erp_code >", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("erp_code >=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThan(String value) {
            addCriterion("erp_code <", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThanOrEqualTo(String value) {
            addCriterion("erp_code <=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLike(String value) {
            addCriterion("erp_code like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotLike(String value) {
            addCriterion("erp_code not like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIn(List<String> values) {
            addCriterion("erp_code in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotIn(List<String> values) {
            addCriterion("erp_code not in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeBetween(String value1, String value2) {
            addCriterion("erp_code between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotBetween(String value1, String value2) {
            addCriterion("erp_code not between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNull() {
            addCriterion("materiel_descr is null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNotNull() {
            addCriterion("materiel_descr is not null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrEqualTo(String value) {
            addCriterion("materiel_descr =", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotEqualTo(String value) {
            addCriterion("materiel_descr <>", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThan(String value) {
            addCriterion("materiel_descr >", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThanOrEqualTo(String value) {
            addCriterion("materiel_descr >=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThan(String value) {
            addCriterion("materiel_descr <", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThanOrEqualTo(String value) {
            addCriterion("materiel_descr <=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLike(String value) {
            addCriterion("materiel_descr like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotLike(String value) {
            addCriterion("materiel_descr not like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIn(List<String> values) {
            addCriterion("materiel_descr in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotIn(List<String> values) {
            addCriterion("materiel_descr not in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrBetween(String value1, String value2) {
            addCriterion("materiel_descr between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotBetween(String value1, String value2) {
            addCriterion("materiel_descr not between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}