package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目成本人力点工合同占用明细表")
public class HroRequirementContractDetail extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "人力点工需求id")
    private Long hroRequirementId;

    @ApiModelProperty(value = "人力点工需求单据编号")
    private String requirementCode;

    @ApiModelProperty(value = "人力点工角色名称")
    private String roleName;

    @ApiModelProperty(value = "采购合同编号")
    private String purchaseContractCode;

    @ApiModelProperty(value = "待下达数量")
    private BigDecimal unreleasedQuantity;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "总价（不含税）-本位币")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "类型-默认0(备用)")
    private Integer budgetType;

    @ApiModelProperty(value = "wbs编码")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "合同创建时间")
    private Date contractCreateTime;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public Long getHroRequirementId() {
        return hroRequirementId;
    }

    public void setHroRequirementId(Long hroRequirementId) {
        this.hroRequirementId = hroRequirementId;
    }

    public String getRequirementCode() {
        return requirementCode;
    }

    public void setRequirementCode(String requirementCode) {
        this.requirementCode = requirementCode == null ? null : requirementCode.trim();
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName == null ? null : roleName.trim();
    }

    public String getPurchaseContractCode() {
        return purchaseContractCode;
    }

    public void setPurchaseContractCode(String purchaseContractCode) {
        this.purchaseContractCode = purchaseContractCode == null ? null : purchaseContractCode.trim();
    }

    public BigDecimal getUnreleasedQuantity() {
        return unreleasedQuantity;
    }

    public void setUnreleasedQuantity(BigDecimal unreleasedQuantity) {
        this.unreleasedQuantity = unreleasedQuantity;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getBudgetType() {
        return budgetType;
    }

    public void setBudgetType(Integer budgetType) {
        this.budgetType = budgetType;
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public Date getContractCreateTime() {
        return contractCreateTime;
    }

    public void setContractCreateTime(Date contractCreateTime) {
        this.contractCreateTime = contractCreateTime;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", hroRequirementId=").append(hroRequirementId);
        sb.append(", requirementCode=").append(requirementCode);
        sb.append(", roleName=").append(roleName);
        sb.append(", purchaseContractCode=").append(purchaseContractCode);
        sb.append(", unreleasedQuantity=").append(unreleasedQuantity);
        sb.append(", status=").append(status);
        sb.append(", totalAmount=").append(totalAmount);
        sb.append(", budgetType=").append(budgetType);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", contractCreateTime=").append(contractCreateTime);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}