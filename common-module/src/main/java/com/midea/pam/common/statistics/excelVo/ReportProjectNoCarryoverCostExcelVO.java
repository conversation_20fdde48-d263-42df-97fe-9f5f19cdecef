package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020-09-09 10:48
 * 项目未结转成本汇总报表ExcelVO
 */
public class ReportProjectNoCarryoverCostExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer number;

    @Excel(name = "项目号", width = 10)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "项目经理", width = 10)
    private String projectManager;

    @Excel(name = "项目状态", width = 15, replace = {"预立项审批驳回_-4", "预立项转正驳回_-3", "审批驳回_-2",
            "财务驳回_-1", "草稿_0", "审批中_3",
            "项目进行中_4", "项目变更中_9", "结项_10",
            "审批撤回_11", "预立项审批撤回_13", "作废_12",
            "预立项转正作废_-13", "预立项转正删除_23", "预立项转正审批中_7",
            "项目终止审批中_15", "项目终止_16", "项目流程删除_17","_null"})
    private Integer projectStatus;

    @Excel(name = "项目类型", width = 20)
    private String projectType;

    @Excel(name = "客户编号", width = 15)
    private String customerCode;

    @Excel(name = "客户名称", width = 30)
    private String customerName;

    @Excel(name = "业务分类", width = 20)
    private String unitName;

    @Excel(name = "子合同编号", width = 15)
    private String code;

    @Excel(name = "子合同名称", width = 30)
    private String name;

    private BigDecimal materialActualCostCollect;

    private BigDecimal materialActualCostCarryover;

    private BigDecimal materialActualCostNoCarryover;

    private BigDecimal materialOutsourceCostCollect;

    private BigDecimal materialOutsourceCostCarryover;

    private BigDecimal materialOutsourceCostNoCarryover;

    private BigDecimal materialDifferenceCostCollect;

    private BigDecimal materialDifferenceCostCarryover;

    private BigDecimal materialDifferenceCostNoCarryover;

    private BigDecimal innerLaborCostCollect;

    private BigDecimal innerLaborCostCarryover;

    private BigDecimal innerLaborCostNoAccount;

    private BigDecimal innerLaborCostNoCarryover;

    private BigDecimal feeCostCollect;

    private BigDecimal feeCostCarryover;

    private BigDecimal feeCostNoCarryover;

    private BigDecimal totalCostNoCarryover;

    @Excel(name = "物料（直接）已归集成本", width = 24)
    private String materialActualCostCollect_dt;

    @Excel(name = "物料（直接）已结转成本", width = 24)
    private String materialActualCostCarryover_dt;

    @Excel(name = "物料（直接）未结转成本", width = 24)
    private String materialActualCostNoCarryover_dt;

    @Excel(name = "物料（外包）已归集成本", width = 24)
    private String materialOutsourceCostCollect_dt;

    @Excel(name = "物料（外包）已结转成本", width = 24)
    private String materialOutsourceCostCarryover_dt;

    @Excel(name = "物料（外包）未结转成本", width = 24)
    private String materialOutsourceCostNoCarryover_dt;

    @Excel(name = "物料（差异）已归集成本", width = 24)
    private String materialDifferenceCostCollect_dt;

    @Excel(name = "物料（差异）已结转成本", width = 24)
    private String materialDifferenceCostCarryover_dt;

    @Excel(name = "物料（差异）未结转成本", width = 24)
    private String materialDifferenceCostNoCarryover_dt;

    @Excel(name = "人力（内部）已归集成本", width = 24)
    private String innerLaborCostCollect_dt;

    @Excel(name = "人力（内部）已结转成本", width = 24)
    private String innerLaborCostCarryover_dt;

    @Excel(name = "人力（内部）未入账成本", width = 24)
    private String innerLaborCostNoAccount_dt;

    @Excel(name = "人力（内部）未结转成本", width = 24)
    private String innerLaborCostNoCarryover_dt;

    @Excel(name = "费用已归集成本", width = 20)
    private String feeCostCollect_dt;

    @Excel(name = "费用已结转成本", width = 20)
    private String feeCostCarryover_dt;

    @Excel(name = "费用未结转成本", width = 20)
    private String feeCostNoCarryover_dt;

    @Excel(name = "差额汇总", width = 20)
    private String totalCostNoCarryover_dt;

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager;
    }

    public Integer getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(Integer projectStatus) {
        this.projectStatus = projectStatus;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getMaterialActualCostCollect() {
        return materialActualCostCollect;
    }

    public void setMaterialActualCostCollect(BigDecimal materialActualCostCollect) {
        this.materialActualCostCollect = materialActualCostCollect;
    }

    public BigDecimal getMaterialActualCostCarryover() {
        return materialActualCostCarryover;
    }

    public void setMaterialActualCostCarryover(BigDecimal materialActualCostCarryover) {
        this.materialActualCostCarryover = materialActualCostCarryover;
    }

    public BigDecimal getMaterialActualCostNoCarryover() {
        return materialActualCostNoCarryover;
    }

    public void setMaterialActualCostNoCarryover(BigDecimal materialActualCostNoCarryover) {
        this.materialActualCostNoCarryover = materialActualCostNoCarryover;
    }

    public BigDecimal getMaterialOutsourceCostCollect() {
        return materialOutsourceCostCollect;
    }

    public void setMaterialOutsourceCostCollect(BigDecimal materialOutsourceCostCollect) {
        this.materialOutsourceCostCollect = materialOutsourceCostCollect;
    }

    public BigDecimal getMaterialOutsourceCostCarryover() {
        return materialOutsourceCostCarryover;
    }

    public void setMaterialOutsourceCostCarryover(BigDecimal materialOutsourceCostCarryover) {
        this.materialOutsourceCostCarryover = materialOutsourceCostCarryover;
    }

    public BigDecimal getMaterialOutsourceCostNoCarryover() {
        return materialOutsourceCostNoCarryover;
    }

    public void setMaterialOutsourceCostNoCarryover(BigDecimal materialOutsourceCostNoCarryover) {
        this.materialOutsourceCostNoCarryover = materialOutsourceCostNoCarryover;
    }

    public BigDecimal getInnerLaborCostCollect() {
        return innerLaborCostCollect;
    }

    public void setInnerLaborCostCollect(BigDecimal innerLaborCostCollect) {
        this.innerLaborCostCollect = innerLaborCostCollect;
    }

    public BigDecimal getInnerLaborCostCarryover() {
        return innerLaborCostCarryover;
    }

    public void setInnerLaborCostCarryover(BigDecimal innerLaborCostCarryover) {
        this.innerLaborCostCarryover = innerLaborCostCarryover;
    }

    public BigDecimal getInnerLaborCostNoCarryover() {
        return innerLaborCostNoCarryover;
    }

    public void setInnerLaborCostNoCarryover(BigDecimal innerLaborCostNoCarryover) {
        this.innerLaborCostNoCarryover = innerLaborCostNoCarryover;
    }

    public BigDecimal getFeeCostCollect() {
        return feeCostCollect;
    }

    public void setFeeCostCollect(BigDecimal feeCostCollect) {
        this.feeCostCollect = feeCostCollect;
    }

    public BigDecimal getFeeCostCarryover() {
        return feeCostCarryover;
    }

    public void setFeeCostCarryover(BigDecimal feeCostCarryover) {
        this.feeCostCarryover = feeCostCarryover;
    }

    public BigDecimal getFeeCostNoCarryover() {
        return feeCostNoCarryover;
    }

    public void setFeeCostNoCarryover(BigDecimal feeCostNoCarryover) {
        this.feeCostNoCarryover = feeCostNoCarryover;
    }

    public BigDecimal getTotalCostNoCarryover() {
        return totalCostNoCarryover;
    }

    public void setTotalCostNoCarryover(BigDecimal totalCostNoCarryover) {
        this.totalCostNoCarryover = totalCostNoCarryover;
    }

    public String getMaterialActualCostCollect_dt() {
        if (Objects.nonNull(this.getMaterialActualCostCollect())) {
            return this.materialActualCostCollect.stripTrailingZeros().toPlainString();
        } else {
            return this.materialActualCostCollect_dt;
        }
    }

    public void setMaterialActualCostCollect_dt(String materialActualCostCollect_dt) {
        this.materialActualCostCollect_dt = materialActualCostCollect_dt;
    }

    public String getMaterialActualCostCarryover_dt() {
        if (Objects.nonNull(this.getMaterialActualCostCarryover())) {
            return this.materialActualCostCarryover.stripTrailingZeros().toPlainString();
        } else {
            return this.materialActualCostCarryover_dt;
        }
    }

    public void setMaterialActualCostCarryover_dt(String materialActualCostCarryover_dt) {
        this.materialActualCostCarryover_dt = materialActualCostCarryover_dt;
    }

    public String getMaterialActualCostNoCarryover_dt() {
        if (Objects.nonNull(this.getMaterialActualCostNoCarryover())) {
            return this.materialActualCostNoCarryover.stripTrailingZeros().toPlainString();
        } else {
            return this.materialActualCostNoCarryover_dt;
        }
    }

    public void setMaterialActualCostNoCarryover_dt(String materialActualCostNoCarryover_dt) {
        this.materialActualCostNoCarryover_dt = materialActualCostNoCarryover_dt;
    }

    public String getMaterialOutsourceCostCollect_dt() {
        if (Objects.nonNull(this.getMaterialOutsourceCostCollect())) {
            return this.materialOutsourceCostCollect.stripTrailingZeros().toPlainString();
        } else {
            return this.materialOutsourceCostCollect_dt;
        }
    }

    public void setMaterialOutsourceCostCollect_dt(String materialOutsourceCostCollect_dt) {
        this.materialOutsourceCostCollect_dt = materialOutsourceCostCollect_dt;
    }

    public String getMaterialOutsourceCostCarryover_dt() {
        if (Objects.nonNull(this.getMaterialOutsourceCostCarryover())) {
            return this.materialOutsourceCostCarryover.stripTrailingZeros().toPlainString();
        } else {
            return this.materialOutsourceCostCarryover_dt;
        }
    }

    public void setMaterialOutsourceCostCarryover_dt(String materialOutsourceCostCarryover_dt) {
        this.materialOutsourceCostCarryover_dt = materialOutsourceCostCarryover_dt;
    }

    public String getMaterialOutsourceCostNoCarryover_dt() {
        if (Objects.nonNull(this.getMaterialOutsourceCostNoCarryover())) {
            return this.materialOutsourceCostNoCarryover.stripTrailingZeros().toPlainString();
        } else {
            return this.materialOutsourceCostNoCarryover_dt;
        }
    }

    public void setMaterialOutsourceCostNoCarryover_dt(String materialOutsourceCostNoCarryover_dt) {
        this.materialOutsourceCostNoCarryover_dt = materialOutsourceCostNoCarryover_dt;
    }

    public String getInnerLaborCostCollect_dt() {
        if (Objects.nonNull(this.getInnerLaborCostCollect())) {
            return this.innerLaborCostCollect.stripTrailingZeros().toPlainString();
        } else {
            return this.innerLaborCostCollect_dt;
        }
    }

    public void setInnerLaborCostCollect_dt(String innerLaborCostCollect_dt) {
        this.innerLaborCostCollect_dt = innerLaborCostCollect_dt;
    }

    public String getInnerLaborCostCarryover_dt() {
        if (Objects.nonNull(this.getInnerLaborCostCarryover())) {
            return this.innerLaborCostCarryover.stripTrailingZeros().toPlainString();
        } else {
            return this.innerLaborCostCarryover_dt;
        }
    }

    public void setInnerLaborCostCarryover_dt(String innerLaborCostCarryover_dt) {
        this.innerLaborCostCarryover_dt = innerLaborCostCarryover_dt;
    }

    public String getInnerLaborCostNoCarryover_dt() {
        if (Objects.nonNull(this.getInnerLaborCostNoCarryover())) {
            return this.innerLaborCostNoCarryover.stripTrailingZeros().toPlainString();
        } else {
            return this.innerLaborCostNoCarryover_dt;
        }
    }

    public void setInnerLaborCostNoCarryover_dt(String innerLaborCostNoCarryover_dt) {
        this.innerLaborCostNoCarryover_dt = innerLaborCostNoCarryover_dt;
    }

    public BigDecimal getInnerLaborCostNoAccount() {
        return innerLaborCostNoAccount;
    }

    public void setInnerLaborCostNoAccount(BigDecimal innerLaborCostNoAccount) {
        this.innerLaborCostNoAccount = innerLaborCostNoAccount;
    }

    public String getInnerLaborCostNoAccount_dt() {
        if (Objects.nonNull(this.getInnerLaborCostNoAccount())) {
            return this.innerLaborCostNoAccount.stripTrailingZeros().toPlainString();
        } else {
            return this.innerLaborCostNoAccount_dt;
        }
    }

    public void setInnerLaborCostNoAccount_dt(String innerLaborCostNoAccount_dt) {
        this.innerLaborCostNoAccount_dt = innerLaborCostNoAccount_dt;
    }

    public String getFeeCostCollect_dt() {
        if (Objects.nonNull(this.getFeeCostCollect())) {
            return this.feeCostCollect.stripTrailingZeros().toPlainString();
        } else {
            return this.feeCostCollect_dt;
        }
    }

    public void setFeeCostCollect_dt(String feeCostCollect_dt) {
        this.feeCostCollect_dt = feeCostCollect_dt;
    }

    public String getFeeCostCarryover_dt() {
        if (Objects.nonNull(this.getFeeCostCarryover())) {
            return this.feeCostCarryover.stripTrailingZeros().toPlainString();
        } else {
            return this.feeCostCarryover_dt;
        }
    }

    public void setFeeCostCarryover_dt(String feeCostCarryover_dt) {
        this.feeCostCarryover_dt = feeCostCarryover_dt;
    }

    public String getFeeCostNoCarryover_dt() {
        if (Objects.nonNull(this.getFeeCostNoCarryover())) {
            return this.feeCostNoCarryover.stripTrailingZeros().toPlainString();
        } else {
            return this.feeCostNoCarryover_dt;
        }
    }

    public void setFeeCostNoCarryover_dt(String feeCostNoCarryover_dt) {
        this.feeCostNoCarryover_dt = feeCostNoCarryover_dt;
    }

    public String getTotalCostNoCarryover_dt() {
        if (Objects.nonNull(this.getTotalCostNoCarryover())) {
            return this.totalCostNoCarryover.stripTrailingZeros().toPlainString();
        } else {
            return this.totalCostNoCarryover_dt;
        }
    }

    public void setTotalCostNoCarryover_dt(String totalCostNoCarryover_dt) {
        this.totalCostNoCarryover_dt = totalCostNoCarryover_dt;
    }

    public BigDecimal getMaterialDifferenceCostCollect() {
        return materialDifferenceCostCollect;
    }

    public void setMaterialDifferenceCostCollect(BigDecimal materialDifferenceCostCollect) {
        this.materialDifferenceCostCollect = materialDifferenceCostCollect;
    }

    public BigDecimal getMaterialDifferenceCostCarryover() {
        return materialDifferenceCostCarryover;
    }

    public void setMaterialDifferenceCostCarryover(BigDecimal materialDifferenceCostCarryover) {
        this.materialDifferenceCostCarryover = materialDifferenceCostCarryover;
    }

    public BigDecimal getMaterialDifferenceCostNoCarryover() {
        return materialDifferenceCostNoCarryover;
    }

    public void setMaterialDifferenceCostNoCarryover(BigDecimal materialDifferenceCostNoCarryover) {
        this.materialDifferenceCostNoCarryover = materialDifferenceCostNoCarryover;
    }

    public String getMaterialDifferenceCostCollect_dt() {
        if (Objects.nonNull(this.getMaterialDifferenceCostCollect())) {
            return this.materialDifferenceCostCollect.stripTrailingZeros().toPlainString();
        } else {
            return this.materialDifferenceCostCollect_dt;
        }
    }

    public void setMaterialDifferenceCostCollect_dt(String materialDifferenceCostCollect_dt) {
        this.materialDifferenceCostCollect_dt = materialDifferenceCostCollect_dt;
    }

    public String getMaterialDifferenceCostCarryover_dt() {
        if (Objects.nonNull(this.getMaterialDifferenceCostCarryover())) {
            return this.materialDifferenceCostCarryover.stripTrailingZeros().toPlainString();
        } else {
            return this.materialDifferenceCostCarryover_dt;
        }
    }

    public void setMaterialDifferenceCostCarryover_dt(String materialDifferenceCostCarryover_dt) {
        this.materialDifferenceCostCarryover_dt = materialDifferenceCostCarryover_dt;
    }

    public String getMaterialDifferenceCostNoCarryover_dt() {
        if (Objects.nonNull(this.getMaterialDifferenceCostNoCarryover())) {
            return this.materialDifferenceCostNoCarryover.stripTrailingZeros().toPlainString();
        } else {
            return this.materialDifferenceCostNoCarryover_dt;
        }
    }

    public void setMaterialDifferenceCostNoCarryover_dt(String materialDifferenceCostNoCarryover_dt) {
        this.materialDifferenceCostNoCarryover_dt = materialDifferenceCostNoCarryover_dt;
    }
}