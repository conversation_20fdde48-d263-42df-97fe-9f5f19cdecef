package com.midea.pam.common.statistics.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/5/21
 * @description 物料导出模版
 */
@Getter
@Setter
public class MaterialPriceReceiptStatisticsExcelVO {
    private Long id;

    @Excel(name = "单据码", width = 15, replace = {"-_null"})
    private String receiptCode;

    @Excel(name = "状态", width = 15, replace = {"草稿_0","审批中_1","驳回_2","撤回_3","通过_4","废弃_5", "-_null"})
    private String status;

    @Excel(name = "申请人", width = 15, replace = {"-_null"})
    private String createByName;

    @Excel(name = "创建日期", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    private Date createAt;

    @Excel(name = "更新日期", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    private Date updateAt;

    @Excel(name = "库存组织", width = 20, replace = {"_null"})
    private String organizationName;

}
