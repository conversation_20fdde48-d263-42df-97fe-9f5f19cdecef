package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.IncomeCalculateProjectTask;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/18
 * @description
 */
public class IncomeCalculateProjectTaskDTO extends IncomeCalculateProjectTask {

    private String statusesStr;

    private List<Integer> statuses;

    private List<Long> unitIds;

    private String incomeCalculateName;

    private String timePeriod;

    public String getStatusesStr() {
        return statusesStr;
    }

    public void setStatusesStr(String statusesStr) {
        this.statusesStr = statusesStr;
    }

    public List<Integer> getStatuses() {
        return statuses;
    }

    public void setStatuses(List<Integer> statuses) {
        this.statuses = statuses;
    }

    public String getIncomeCalculateName() {
        return incomeCalculateName;
    }

    public void setIncomeCalculateName(String incomeCalculateName) {
        this.incomeCalculateName = incomeCalculateName;
    }

    public String getTimePeriod() {
        return timePeriod;
    }

    public void setTimePeriod(String timePeriod) {
        this.timePeriod = timePeriod;
    }

    public List<Long> getUnitIds() {
        return unitIds;
    }

    public void setUnitIds(List<Long> unitIds) {
        this.unitIds = unitIds;
    }
}
