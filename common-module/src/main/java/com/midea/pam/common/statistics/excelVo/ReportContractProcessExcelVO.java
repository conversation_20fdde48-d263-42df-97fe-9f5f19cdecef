package com.midea.pam.common.statistics.excelVo;

import com.midea.pam.common.util.BigDecimalUtils;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/6/17
 * 销售合同接单报表ExcelVO
 */
public class ReportContractProcessExcelVO {

    @Excel(name = "接单年度", width = 15)
    private String processYear;

    @Excel(name = "接单月份", width = 15)
    private String processMonth;

    @Excel(name = "类型", width = 15, replace = {"新增_1", "变更_2", "终止_3", "_null"})
    private Integer processType;

    @Excel(name = "合同类型", width = 15, replace = {"普通合同_0", "框架合同_1", "_null"})
    private Integer frameFlag;

    @Excel(name = "订单日期", width = 25, format = "yyyy-MM-dd")
    private Date processTime;

    @Excel(name = "PAM合同编号", width = 20)
    private String code;

    @Excel(name = "法务合同号", width = 20)
    private String legalContractNum;

    @Excel(name = "框架合同号", width = 20)
    private String frameCode;

    @Excel(name = "合同名称", width = 30)
    private String name;

    @Excel(name = "客户编码", width = 15)
    private String customerCode;

    @Excel(name = "客户名称", width = 30)
    private String customerName;

    @Excel(name = "客户属性", width = 15, replace = {"外部客户_0", "内部客户_1", "_null"})
    private Integer customerType;

    private BigDecimal excludingTaxAmount;

    @Excel(name = "订单金额（不含税）", width = 20)
    private String excludingTaxAmount_dt;

    @Excel(name = "币种", width = 15)
    private String currency;

    @Excel(name = "汇率", width = 10)
    private BigDecimal conversionRate;

    @Excel(name = "业务分类（销售部门）", width = 20)
    private String unitName;

    @Excel(name = "销售经理-初始", width = 15)
    private String oldSalesManager;

    @Excel(name = "销售经理-当前", width = 15)
    private String newSalesManager;

    @Excel(name = "项目号", width = 25)
    private String projectCode;

    @Excel(name = "项目经理", width = 25)
    private String projectManager;

    @Excel(name = "业务实体", width = 30)
    private String ouName;

    @Excel(name = "业务模式", width = 30)
    private String businessTypeName;

    public String getProcessYear() {
        return processYear;
    }

    public void setProcessYear(String processYear) {
        this.processYear = processYear;
    }

    public String getProcessMonth() {
        return processMonth;
    }

    public void setProcessMonth(String processMonth) {
        this.processMonth = processMonth;
    }

    public Integer getProcessType() {
        return processType;
    }

    public void setProcessType(Integer processType) {
        this.processType = processType;
    }

    public Integer getFrameFlag() {
        return frameFlag;
    }

    public void setFrameFlag(Integer frameFlag) {
        this.frameFlag = frameFlag;
    }

    public Date getProcessTime() {
        return processTime;
    }

    public void setProcessTime(Date processTime) {
        this.processTime = processTime;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getLegalContractNum() {
        return legalContractNum;
    }

    public void setLegalContractNum(String legalContractNum) {
        this.legalContractNum = legalContractNum;
    }

    public String getFrameCode() {
        return frameCode;
    }

    public void setFrameCode(String frameCode) {
        this.frameCode = frameCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public BigDecimal getExcludingTaxAmount() {
        return excludingTaxAmount;
    }

    public void setExcludingTaxAmount(BigDecimal excludingTaxAmount) {
        this.excludingTaxAmount = excludingTaxAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getOldSalesManager() {
        return oldSalesManager;
    }

    public void setOldSalesManager(String oldSalesManager) {
        this.oldSalesManager = oldSalesManager;
    }

    public String getNewSalesManager() {
        return newSalesManager;
    }

    public void setNewSalesManager(String newSalesManager) {
        this.newSalesManager = newSalesManager;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName;
    }

    public String getExcludingTaxAmount_dt() {
        if (Objects.nonNull(this.excludingTaxAmount)) {
            return this.excludingTaxAmount.stripTrailingZeros().toPlainString();
        } else {
            return this.excludingTaxAmount_dt;
        }
    }

    public void setExcludingTaxAmount_dt(String excludingTaxAmount_dt) {
        this.excludingTaxAmount_dt = excludingTaxAmount_dt;
    }

    public String getBusinessTypeName() {
        return businessTypeName;
    }

    public void setBusinessTypeName(String businessTypeName) {
        this.businessTypeName = businessTypeName;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public BigDecimal getConversionRate() {
        return BigDecimalUtils.stripTrailingZeros(conversionRate);
    }
}
