package com.midea.pam.common.statistics.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportProjectChangeTraceSummaryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportProjectChangeTraceSummaryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNull() {
            addCriterion("project_manager is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNotNull() {
            addCriterion("project_manager is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerEqualTo(Long value) {
            addCriterion("project_manager =", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotEqualTo(Long value) {
            addCriterion("project_manager <>", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThan(Long value) {
            addCriterion("project_manager >", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThanOrEqualTo(Long value) {
            addCriterion("project_manager >=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThan(Long value) {
            addCriterion("project_manager <", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThanOrEqualTo(Long value) {
            addCriterion("project_manager <=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIn(List<Long> values) {
            addCriterion("project_manager in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotIn(List<Long> values) {
            addCriterion("project_manager not in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerBetween(Long value1, Long value2) {
            addCriterion("project_manager between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotBetween(Long value1, Long value2) {
            addCriterion("project_manager not between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andBaseInfoCountIsNull() {
            addCriterion("base_info_count is null");
            return (Criteria) this;
        }

        public Criteria andBaseInfoCountIsNotNull() {
            addCriterion("base_info_count is not null");
            return (Criteria) this;
        }

        public Criteria andBaseInfoCountEqualTo(Integer value) {
            addCriterion("base_info_count =", value, "baseInfoCount");
            return (Criteria) this;
        }

        public Criteria andBaseInfoCountNotEqualTo(Integer value) {
            addCriterion("base_info_count <>", value, "baseInfoCount");
            return (Criteria) this;
        }

        public Criteria andBaseInfoCountGreaterThan(Integer value) {
            addCriterion("base_info_count >", value, "baseInfoCount");
            return (Criteria) this;
        }

        public Criteria andBaseInfoCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("base_info_count >=", value, "baseInfoCount");
            return (Criteria) this;
        }

        public Criteria andBaseInfoCountLessThan(Integer value) {
            addCriterion("base_info_count <", value, "baseInfoCount");
            return (Criteria) this;
        }

        public Criteria andBaseInfoCountLessThanOrEqualTo(Integer value) {
            addCriterion("base_info_count <=", value, "baseInfoCount");
            return (Criteria) this;
        }

        public Criteria andBaseInfoCountIn(List<Integer> values) {
            addCriterion("base_info_count in", values, "baseInfoCount");
            return (Criteria) this;
        }

        public Criteria andBaseInfoCountNotIn(List<Integer> values) {
            addCriterion("base_info_count not in", values, "baseInfoCount");
            return (Criteria) this;
        }

        public Criteria andBaseInfoCountBetween(Integer value1, Integer value2) {
            addCriterion("base_info_count between", value1, value2, "baseInfoCount");
            return (Criteria) this;
        }

        public Criteria andBaseInfoCountNotBetween(Integer value1, Integer value2) {
            addCriterion("base_info_count not between", value1, value2, "baseInfoCount");
            return (Criteria) this;
        }

        public Criteria andBaseInfoTypeIsNull() {
            addCriterion("base_info_type is null");
            return (Criteria) this;
        }

        public Criteria andBaseInfoTypeIsNotNull() {
            addCriterion("base_info_type is not null");
            return (Criteria) this;
        }

        public Criteria andBaseInfoTypeEqualTo(String value) {
            addCriterion("base_info_type =", value, "baseInfoType");
            return (Criteria) this;
        }

        public Criteria andBaseInfoTypeNotEqualTo(String value) {
            addCriterion("base_info_type <>", value, "baseInfoType");
            return (Criteria) this;
        }

        public Criteria andBaseInfoTypeGreaterThan(String value) {
            addCriterion("base_info_type >", value, "baseInfoType");
            return (Criteria) this;
        }

        public Criteria andBaseInfoTypeGreaterThanOrEqualTo(String value) {
            addCriterion("base_info_type >=", value, "baseInfoType");
            return (Criteria) this;
        }

        public Criteria andBaseInfoTypeLessThan(String value) {
            addCriterion("base_info_type <", value, "baseInfoType");
            return (Criteria) this;
        }

        public Criteria andBaseInfoTypeLessThanOrEqualTo(String value) {
            addCriterion("base_info_type <=", value, "baseInfoType");
            return (Criteria) this;
        }

        public Criteria andBaseInfoTypeLike(String value) {
            addCriterion("base_info_type like", value, "baseInfoType");
            return (Criteria) this;
        }

        public Criteria andBaseInfoTypeNotLike(String value) {
            addCriterion("base_info_type not like", value, "baseInfoType");
            return (Criteria) this;
        }

        public Criteria andBaseInfoTypeIn(List<String> values) {
            addCriterion("base_info_type in", values, "baseInfoType");
            return (Criteria) this;
        }

        public Criteria andBaseInfoTypeNotIn(List<String> values) {
            addCriterion("base_info_type not in", values, "baseInfoType");
            return (Criteria) this;
        }

        public Criteria andBaseInfoTypeBetween(String value1, String value2) {
            addCriterion("base_info_type between", value1, value2, "baseInfoType");
            return (Criteria) this;
        }

        public Criteria andBaseInfoTypeNotBetween(String value1, String value2) {
            addCriterion("base_info_type not between", value1, value2, "baseInfoType");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastUpdaterIsNull() {
            addCriterion("base_info_last_updater is null");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastUpdaterIsNotNull() {
            addCriterion("base_info_last_updater is not null");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastUpdaterEqualTo(Long value) {
            addCriterion("base_info_last_updater =", value, "baseInfoLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastUpdaterNotEqualTo(Long value) {
            addCriterion("base_info_last_updater <>", value, "baseInfoLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastUpdaterGreaterThan(Long value) {
            addCriterion("base_info_last_updater >", value, "baseInfoLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastUpdaterGreaterThanOrEqualTo(Long value) {
            addCriterion("base_info_last_updater >=", value, "baseInfoLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastUpdaterLessThan(Long value) {
            addCriterion("base_info_last_updater <", value, "baseInfoLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastUpdaterLessThanOrEqualTo(Long value) {
            addCriterion("base_info_last_updater <=", value, "baseInfoLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastUpdaterIn(List<Long> values) {
            addCriterion("base_info_last_updater in", values, "baseInfoLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastUpdaterNotIn(List<Long> values) {
            addCriterion("base_info_last_updater not in", values, "baseInfoLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastUpdaterBetween(Long value1, Long value2) {
            addCriterion("base_info_last_updater between", value1, value2, "baseInfoLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastUpdaterNotBetween(Long value1, Long value2) {
            addCriterion("base_info_last_updater not between", value1, value2, "baseInfoLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastApprovedTimeIsNull() {
            addCriterion("base_info_last_approved_time is null");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastApprovedTimeIsNotNull() {
            addCriterion("base_info_last_approved_time is not null");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastApprovedTimeEqualTo(Date value) {
            addCriterion("base_info_last_approved_time =", value, "baseInfoLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastApprovedTimeNotEqualTo(Date value) {
            addCriterion("base_info_last_approved_time <>", value, "baseInfoLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastApprovedTimeGreaterThan(Date value) {
            addCriterion("base_info_last_approved_time >", value, "baseInfoLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastApprovedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("base_info_last_approved_time >=", value, "baseInfoLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastApprovedTimeLessThan(Date value) {
            addCriterion("base_info_last_approved_time <", value, "baseInfoLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastApprovedTimeLessThanOrEqualTo(Date value) {
            addCriterion("base_info_last_approved_time <=", value, "baseInfoLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastApprovedTimeIn(List<Date> values) {
            addCriterion("base_info_last_approved_time in", values, "baseInfoLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastApprovedTimeNotIn(List<Date> values) {
            addCriterion("base_info_last_approved_time not in", values, "baseInfoLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastApprovedTimeBetween(Date value1, Date value2) {
            addCriterion("base_info_last_approved_time between", value1, value2, "baseInfoLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastApprovedTimeNotBetween(Date value1, Date value2) {
            addCriterion("base_info_last_approved_time not between", value1, value2, "baseInfoLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andMilepostCountIsNull() {
            addCriterion("milepost_count is null");
            return (Criteria) this;
        }

        public Criteria andMilepostCountIsNotNull() {
            addCriterion("milepost_count is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostCountEqualTo(Integer value) {
            addCriterion("milepost_count =", value, "milepostCount");
            return (Criteria) this;
        }

        public Criteria andMilepostCountNotEqualTo(Integer value) {
            addCriterion("milepost_count <>", value, "milepostCount");
            return (Criteria) this;
        }

        public Criteria andMilepostCountGreaterThan(Integer value) {
            addCriterion("milepost_count >", value, "milepostCount");
            return (Criteria) this;
        }

        public Criteria andMilepostCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("milepost_count >=", value, "milepostCount");
            return (Criteria) this;
        }

        public Criteria andMilepostCountLessThan(Integer value) {
            addCriterion("milepost_count <", value, "milepostCount");
            return (Criteria) this;
        }

        public Criteria andMilepostCountLessThanOrEqualTo(Integer value) {
            addCriterion("milepost_count <=", value, "milepostCount");
            return (Criteria) this;
        }

        public Criteria andMilepostCountIn(List<Integer> values) {
            addCriterion("milepost_count in", values, "milepostCount");
            return (Criteria) this;
        }

        public Criteria andMilepostCountNotIn(List<Integer> values) {
            addCriterion("milepost_count not in", values, "milepostCount");
            return (Criteria) this;
        }

        public Criteria andMilepostCountBetween(Integer value1, Integer value2) {
            addCriterion("milepost_count between", value1, value2, "milepostCount");
            return (Criteria) this;
        }

        public Criteria andMilepostCountNotBetween(Integer value1, Integer value2) {
            addCriterion("milepost_count not between", value1, value2, "milepostCount");
            return (Criteria) this;
        }

        public Criteria andMilepostTypeIsNull() {
            addCriterion("milepost_type is null");
            return (Criteria) this;
        }

        public Criteria andMilepostTypeIsNotNull() {
            addCriterion("milepost_type is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostTypeEqualTo(String value) {
            addCriterion("milepost_type =", value, "milepostType");
            return (Criteria) this;
        }

        public Criteria andMilepostTypeNotEqualTo(String value) {
            addCriterion("milepost_type <>", value, "milepostType");
            return (Criteria) this;
        }

        public Criteria andMilepostTypeGreaterThan(String value) {
            addCriterion("milepost_type >", value, "milepostType");
            return (Criteria) this;
        }

        public Criteria andMilepostTypeGreaterThanOrEqualTo(String value) {
            addCriterion("milepost_type >=", value, "milepostType");
            return (Criteria) this;
        }

        public Criteria andMilepostTypeLessThan(String value) {
            addCriterion("milepost_type <", value, "milepostType");
            return (Criteria) this;
        }

        public Criteria andMilepostTypeLessThanOrEqualTo(String value) {
            addCriterion("milepost_type <=", value, "milepostType");
            return (Criteria) this;
        }

        public Criteria andMilepostTypeLike(String value) {
            addCriterion("milepost_type like", value, "milepostType");
            return (Criteria) this;
        }

        public Criteria andMilepostTypeNotLike(String value) {
            addCriterion("milepost_type not like", value, "milepostType");
            return (Criteria) this;
        }

        public Criteria andMilepostTypeIn(List<String> values) {
            addCriterion("milepost_type in", values, "milepostType");
            return (Criteria) this;
        }

        public Criteria andMilepostTypeNotIn(List<String> values) {
            addCriterion("milepost_type not in", values, "milepostType");
            return (Criteria) this;
        }

        public Criteria andMilepostTypeBetween(String value1, String value2) {
            addCriterion("milepost_type between", value1, value2, "milepostType");
            return (Criteria) this;
        }

        public Criteria andMilepostTypeNotBetween(String value1, String value2) {
            addCriterion("milepost_type not between", value1, value2, "milepostType");
            return (Criteria) this;
        }

        public Criteria andMilepostLastUpdaterIsNull() {
            addCriterion("milepost_last_updater is null");
            return (Criteria) this;
        }

        public Criteria andMilepostLastUpdaterIsNotNull() {
            addCriterion("milepost_last_updater is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostLastUpdaterEqualTo(Long value) {
            addCriterion("milepost_last_updater =", value, "milepostLastUpdater");
            return (Criteria) this;
        }

        public Criteria andMilepostLastUpdaterNotEqualTo(Long value) {
            addCriterion("milepost_last_updater <>", value, "milepostLastUpdater");
            return (Criteria) this;
        }

        public Criteria andMilepostLastUpdaterGreaterThan(Long value) {
            addCriterion("milepost_last_updater >", value, "milepostLastUpdater");
            return (Criteria) this;
        }

        public Criteria andMilepostLastUpdaterGreaterThanOrEqualTo(Long value) {
            addCriterion("milepost_last_updater >=", value, "milepostLastUpdater");
            return (Criteria) this;
        }

        public Criteria andMilepostLastUpdaterLessThan(Long value) {
            addCriterion("milepost_last_updater <", value, "milepostLastUpdater");
            return (Criteria) this;
        }

        public Criteria andMilepostLastUpdaterLessThanOrEqualTo(Long value) {
            addCriterion("milepost_last_updater <=", value, "milepostLastUpdater");
            return (Criteria) this;
        }

        public Criteria andMilepostLastUpdaterIn(List<Long> values) {
            addCriterion("milepost_last_updater in", values, "milepostLastUpdater");
            return (Criteria) this;
        }

        public Criteria andMilepostLastUpdaterNotIn(List<Long> values) {
            addCriterion("milepost_last_updater not in", values, "milepostLastUpdater");
            return (Criteria) this;
        }

        public Criteria andMilepostLastUpdaterBetween(Long value1, Long value2) {
            addCriterion("milepost_last_updater between", value1, value2, "milepostLastUpdater");
            return (Criteria) this;
        }

        public Criteria andMilepostLastUpdaterNotBetween(Long value1, Long value2) {
            addCriterion("milepost_last_updater not between", value1, value2, "milepostLastUpdater");
            return (Criteria) this;
        }

        public Criteria andMilepostLastApprovedTimeIsNull() {
            addCriterion("milepost_last_approved_time is null");
            return (Criteria) this;
        }

        public Criteria andMilepostLastApprovedTimeIsNotNull() {
            addCriterion("milepost_last_approved_time is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostLastApprovedTimeEqualTo(Date value) {
            addCriterion("milepost_last_approved_time =", value, "milepostLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andMilepostLastApprovedTimeNotEqualTo(Date value) {
            addCriterion("milepost_last_approved_time <>", value, "milepostLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andMilepostLastApprovedTimeGreaterThan(Date value) {
            addCriterion("milepost_last_approved_time >", value, "milepostLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andMilepostLastApprovedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("milepost_last_approved_time >=", value, "milepostLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andMilepostLastApprovedTimeLessThan(Date value) {
            addCriterion("milepost_last_approved_time <", value, "milepostLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andMilepostLastApprovedTimeLessThanOrEqualTo(Date value) {
            addCriterion("milepost_last_approved_time <=", value, "milepostLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andMilepostLastApprovedTimeIn(List<Date> values) {
            addCriterion("milepost_last_approved_time in", values, "milepostLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andMilepostLastApprovedTimeNotIn(List<Date> values) {
            addCriterion("milepost_last_approved_time not in", values, "milepostLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andMilepostLastApprovedTimeBetween(Date value1, Date value2) {
            addCriterion("milepost_last_approved_time between", value1, value2, "milepostLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andMilepostLastApprovedTimeNotBetween(Date value1, Date value2) {
            addCriterion("milepost_last_approved_time not between", value1, value2, "milepostLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBudgetCountIsNull() {
            addCriterion("budget_count is null");
            return (Criteria) this;
        }

        public Criteria andBudgetCountIsNotNull() {
            addCriterion("budget_count is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetCountEqualTo(Integer value) {
            addCriterion("budget_count =", value, "budgetCount");
            return (Criteria) this;
        }

        public Criteria andBudgetCountNotEqualTo(Integer value) {
            addCriterion("budget_count <>", value, "budgetCount");
            return (Criteria) this;
        }

        public Criteria andBudgetCountGreaterThan(Integer value) {
            addCriterion("budget_count >", value, "budgetCount");
            return (Criteria) this;
        }

        public Criteria andBudgetCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("budget_count >=", value, "budgetCount");
            return (Criteria) this;
        }

        public Criteria andBudgetCountLessThan(Integer value) {
            addCriterion("budget_count <", value, "budgetCount");
            return (Criteria) this;
        }

        public Criteria andBudgetCountLessThanOrEqualTo(Integer value) {
            addCriterion("budget_count <=", value, "budgetCount");
            return (Criteria) this;
        }

        public Criteria andBudgetCountIn(List<Integer> values) {
            addCriterion("budget_count in", values, "budgetCount");
            return (Criteria) this;
        }

        public Criteria andBudgetCountNotIn(List<Integer> values) {
            addCriterion("budget_count not in", values, "budgetCount");
            return (Criteria) this;
        }

        public Criteria andBudgetCountBetween(Integer value1, Integer value2) {
            addCriterion("budget_count between", value1, value2, "budgetCount");
            return (Criteria) this;
        }

        public Criteria andBudgetCountNotBetween(Integer value1, Integer value2) {
            addCriterion("budget_count not between", value1, value2, "budgetCount");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeIsNull() {
            addCriterion("budget_type is null");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeIsNotNull() {
            addCriterion("budget_type is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeEqualTo(String value) {
            addCriterion("budget_type =", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeNotEqualTo(String value) {
            addCriterion("budget_type <>", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeGreaterThan(String value) {
            addCriterion("budget_type >", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeGreaterThanOrEqualTo(String value) {
            addCriterion("budget_type >=", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeLessThan(String value) {
            addCriterion("budget_type <", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeLessThanOrEqualTo(String value) {
            addCriterion("budget_type <=", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeLike(String value) {
            addCriterion("budget_type like", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeNotLike(String value) {
            addCriterion("budget_type not like", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeIn(List<String> values) {
            addCriterion("budget_type in", values, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeNotIn(List<String> values) {
            addCriterion("budget_type not in", values, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeBetween(String value1, String value2) {
            addCriterion("budget_type between", value1, value2, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeNotBetween(String value1, String value2) {
            addCriterion("budget_type not between", value1, value2, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetLastUpdaterIsNull() {
            addCriterion("budget_last_updater is null");
            return (Criteria) this;
        }

        public Criteria andBudgetLastUpdaterIsNotNull() {
            addCriterion("budget_last_updater is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetLastUpdaterEqualTo(Long value) {
            addCriterion("budget_last_updater =", value, "budgetLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBudgetLastUpdaterNotEqualTo(Long value) {
            addCriterion("budget_last_updater <>", value, "budgetLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBudgetLastUpdaterGreaterThan(Long value) {
            addCriterion("budget_last_updater >", value, "budgetLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBudgetLastUpdaterGreaterThanOrEqualTo(Long value) {
            addCriterion("budget_last_updater >=", value, "budgetLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBudgetLastUpdaterLessThan(Long value) {
            addCriterion("budget_last_updater <", value, "budgetLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBudgetLastUpdaterLessThanOrEqualTo(Long value) {
            addCriterion("budget_last_updater <=", value, "budgetLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBudgetLastUpdaterIn(List<Long> values) {
            addCriterion("budget_last_updater in", values, "budgetLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBudgetLastUpdaterNotIn(List<Long> values) {
            addCriterion("budget_last_updater not in", values, "budgetLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBudgetLastUpdaterBetween(Long value1, Long value2) {
            addCriterion("budget_last_updater between", value1, value2, "budgetLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBudgetLastUpdaterNotBetween(Long value1, Long value2) {
            addCriterion("budget_last_updater not between", value1, value2, "budgetLastUpdater");
            return (Criteria) this;
        }

        public Criteria andBudgetLastApprovedTimeIsNull() {
            addCriterion("budget_last_approved_time is null");
            return (Criteria) this;
        }

        public Criteria andBudgetLastApprovedTimeIsNotNull() {
            addCriterion("budget_last_approved_time is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetLastApprovedTimeEqualTo(Date value) {
            addCriterion("budget_last_approved_time =", value, "budgetLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBudgetLastApprovedTimeNotEqualTo(Date value) {
            addCriterion("budget_last_approved_time <>", value, "budgetLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBudgetLastApprovedTimeGreaterThan(Date value) {
            addCriterion("budget_last_approved_time >", value, "budgetLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBudgetLastApprovedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("budget_last_approved_time >=", value, "budgetLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBudgetLastApprovedTimeLessThan(Date value) {
            addCriterion("budget_last_approved_time <", value, "budgetLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBudgetLastApprovedTimeLessThanOrEqualTo(Date value) {
            addCriterion("budget_last_approved_time <=", value, "budgetLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBudgetLastApprovedTimeIn(List<Date> values) {
            addCriterion("budget_last_approved_time in", values, "budgetLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBudgetLastApprovedTimeNotIn(List<Date> values) {
            addCriterion("budget_last_approved_time not in", values, "budgetLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBudgetLastApprovedTimeBetween(Date value1, Date value2) {
            addCriterion("budget_last_approved_time between", value1, value2, "budgetLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andBudgetLastApprovedTimeNotBetween(Date value1, Date value2) {
            addCriterion("budget_last_approved_time not between", value1, value2, "budgetLastApprovedTime");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameIsNull() {
            addCriterion("project_manager_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameIsNotNull() {
            addCriterion("project_manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameEqualTo(String value) {
            addCriterion("project_manager_name =", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameNotEqualTo(String value) {
            addCriterion("project_manager_name <>", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameGreaterThan(String value) {
            addCriterion("project_manager_name >", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_manager_name >=", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameLessThan(String value) {
            addCriterion("project_manager_name <", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameLessThanOrEqualTo(String value) {
            addCriterion("project_manager_name <=", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameLike(String value) {
            addCriterion("project_manager_name like", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameNotLike(String value) {
            addCriterion("project_manager_name not like", value, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameIn(List<String> values) {
            addCriterion("project_manager_name in", values, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameNotIn(List<String> values) {
            addCriterion("project_manager_name not in", values, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameBetween(String value1, String value2) {
            addCriterion("project_manager_name between", value1, value2, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNameNotBetween(String value1, String value2) {
            addCriterion("project_manager_name not between", value1, value2, "projectManagerName");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastNameIsNull() {
            addCriterion("base_info_last_name is null");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastNameIsNotNull() {
            addCriterion("base_info_last_name is not null");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastNameEqualTo(String value) {
            addCriterion("base_info_last_name =", value, "baseInfoLastName");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastNameNotEqualTo(String value) {
            addCriterion("base_info_last_name <>", value, "baseInfoLastName");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastNameGreaterThan(String value) {
            addCriterion("base_info_last_name >", value, "baseInfoLastName");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastNameGreaterThanOrEqualTo(String value) {
            addCriterion("base_info_last_name >=", value, "baseInfoLastName");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastNameLessThan(String value) {
            addCriterion("base_info_last_name <", value, "baseInfoLastName");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastNameLessThanOrEqualTo(String value) {
            addCriterion("base_info_last_name <=", value, "baseInfoLastName");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastNameLike(String value) {
            addCriterion("base_info_last_name like", value, "baseInfoLastName");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastNameNotLike(String value) {
            addCriterion("base_info_last_name not like", value, "baseInfoLastName");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastNameIn(List<String> values) {
            addCriterion("base_info_last_name in", values, "baseInfoLastName");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastNameNotIn(List<String> values) {
            addCriterion("base_info_last_name not in", values, "baseInfoLastName");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastNameBetween(String value1, String value2) {
            addCriterion("base_info_last_name between", value1, value2, "baseInfoLastName");
            return (Criteria) this;
        }

        public Criteria andBaseInfoLastNameNotBetween(String value1, String value2) {
            addCriterion("base_info_last_name not between", value1, value2, "baseInfoLastName");
            return (Criteria) this;
        }

        public Criteria andMilepostLastNameIsNull() {
            addCriterion("milepost_last_name is null");
            return (Criteria) this;
        }

        public Criteria andMilepostLastNameIsNotNull() {
            addCriterion("milepost_last_name is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostLastNameEqualTo(String value) {
            addCriterion("milepost_last_name =", value, "milepostLastName");
            return (Criteria) this;
        }

        public Criteria andMilepostLastNameNotEqualTo(String value) {
            addCriterion("milepost_last_name <>", value, "milepostLastName");
            return (Criteria) this;
        }

        public Criteria andMilepostLastNameGreaterThan(String value) {
            addCriterion("milepost_last_name >", value, "milepostLastName");
            return (Criteria) this;
        }

        public Criteria andMilepostLastNameGreaterThanOrEqualTo(String value) {
            addCriterion("milepost_last_name >=", value, "milepostLastName");
            return (Criteria) this;
        }

        public Criteria andMilepostLastNameLessThan(String value) {
            addCriterion("milepost_last_name <", value, "milepostLastName");
            return (Criteria) this;
        }

        public Criteria andMilepostLastNameLessThanOrEqualTo(String value) {
            addCriterion("milepost_last_name <=", value, "milepostLastName");
            return (Criteria) this;
        }

        public Criteria andMilepostLastNameLike(String value) {
            addCriterion("milepost_last_name like", value, "milepostLastName");
            return (Criteria) this;
        }

        public Criteria andMilepostLastNameNotLike(String value) {
            addCriterion("milepost_last_name not like", value, "milepostLastName");
            return (Criteria) this;
        }

        public Criteria andMilepostLastNameIn(List<String> values) {
            addCriterion("milepost_last_name in", values, "milepostLastName");
            return (Criteria) this;
        }

        public Criteria andMilepostLastNameNotIn(List<String> values) {
            addCriterion("milepost_last_name not in", values, "milepostLastName");
            return (Criteria) this;
        }

        public Criteria andMilepostLastNameBetween(String value1, String value2) {
            addCriterion("milepost_last_name between", value1, value2, "milepostLastName");
            return (Criteria) this;
        }

        public Criteria andMilepostLastNameNotBetween(String value1, String value2) {
            addCriterion("milepost_last_name not between", value1, value2, "milepostLastName");
            return (Criteria) this;
        }

        public Criteria andBudgetLastNameIsNull() {
            addCriterion("budget_last_name is null");
            return (Criteria) this;
        }

        public Criteria andBudgetLastNameIsNotNull() {
            addCriterion("budget_last_name is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetLastNameEqualTo(String value) {
            addCriterion("budget_last_name =", value, "budgetLastName");
            return (Criteria) this;
        }

        public Criteria andBudgetLastNameNotEqualTo(String value) {
            addCriterion("budget_last_name <>", value, "budgetLastName");
            return (Criteria) this;
        }

        public Criteria andBudgetLastNameGreaterThan(String value) {
            addCriterion("budget_last_name >", value, "budgetLastName");
            return (Criteria) this;
        }

        public Criteria andBudgetLastNameGreaterThanOrEqualTo(String value) {
            addCriterion("budget_last_name >=", value, "budgetLastName");
            return (Criteria) this;
        }

        public Criteria andBudgetLastNameLessThan(String value) {
            addCriterion("budget_last_name <", value, "budgetLastName");
            return (Criteria) this;
        }

        public Criteria andBudgetLastNameLessThanOrEqualTo(String value) {
            addCriterion("budget_last_name <=", value, "budgetLastName");
            return (Criteria) this;
        }

        public Criteria andBudgetLastNameLike(String value) {
            addCriterion("budget_last_name like", value, "budgetLastName");
            return (Criteria) this;
        }

        public Criteria andBudgetLastNameNotLike(String value) {
            addCriterion("budget_last_name not like", value, "budgetLastName");
            return (Criteria) this;
        }

        public Criteria andBudgetLastNameIn(List<String> values) {
            addCriterion("budget_last_name in", values, "budgetLastName");
            return (Criteria) this;
        }

        public Criteria andBudgetLastNameNotIn(List<String> values) {
            addCriterion("budget_last_name not in", values, "budgetLastName");
            return (Criteria) this;
        }

        public Criteria andBudgetLastNameBetween(String value1, String value2) {
            addCriterion("budget_last_name between", value1, value2, "budgetLastName");
            return (Criteria) this;
        }

        public Criteria andBudgetLastNameNotBetween(String value1, String value2) {
            addCriterion("budget_last_name not between", value1, value2, "budgetLastName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}