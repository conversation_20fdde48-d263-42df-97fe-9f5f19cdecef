package com.midea.pam.common.statistics.excelVo;

import com.midea.pam.common.util.DateUtils;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 **/
public class ReportProjectProfitIncurredCostExcelVO {
    // 已发生成本
    @Excel(name = "物料成本",width = 20)
    private BigDecimal incurredCostHardware;

    @Excel(name = "人力成本",width = 20)
    private BigDecimal incurredCostLabour;

    @Excel(name = "差旅成本",width = 20)
    private BigDecimal incurredCostTravel;

    @Excel(name = "非差旅成本",width = 20)
    private BigDecimal incurredCostOther;

    public BigDecimal getIncurredCostHardware() {
        return incurredCostHardware;
    }

    public void setIncurredCostHardware(BigDecimal incurredCostHardware) {
        this.incurredCostHardware = incurredCostHardware;
    }

    public BigDecimal getIncurredCostLabour() {
        return incurredCostLabour;
    }

    public void setIncurredCostLabour(BigDecimal incurredCostLabour) {
        this.incurredCostLabour = incurredCostLabour;
    }

    public BigDecimal getIncurredCostTravel() {
        return incurredCostTravel;
    }

    public void setIncurredCostTravel(BigDecimal incurredCostTravel) {
        this.incurredCostTravel = incurredCostTravel;
    }

    public BigDecimal getIncurredCostOther() {
        return incurredCostOther;
    }

    public void setIncurredCostOther(BigDecimal incurredCostOther) {
        this.incurredCostOther = incurredCostOther;
    }
}