package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(value = "财务口径应收报表")
public class ReportInvoiceApplyReceivableAges extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "子合同编号")
    private String code;

    @ApiModelProperty(value = "子合同名称")
    private String name;

    @ApiModelProperty(value = "主合同编号")
    private String parentCode;

    @ApiModelProperty(value = "主合同名称")
    private String parentName;

    @ApiModelProperty(value = "业务实体")
    private String ouName;

    @ApiModelProperty(value = "销售部门")
    private String unitName;

    @ApiModelProperty(value = "业务模式")
    private String projectType;

    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "是否内部客户:1-内部客户;0-外部客户")
    private Integer customerType;

    @ApiModelProperty(value = "项目号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    @ApiModelProperty(value = "销售人员")
    private String salesManager;

    @ApiModelProperty(value = "币种编码")
    private String currency;

    @ApiModelProperty(value = "子合同含税金额（原币）")
    private BigDecimal amount;

    @ApiModelProperty(value = "子合同不含税金额（原币）")
    private BigDecimal excludingTaxAmount;

    @ApiModelProperty(value = "子合同汇率")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "子合同含税金额（本位币）")
    private BigDecimal standardAmount;

    @ApiModelProperty(value = "子合同不含税金额（本位币）")
    private BigDecimal standardExcludingTaxAmount;

    @ApiModelProperty(value = "子合同已开票金额（原币）")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "子合同已收款金额（原币）")
    private BigDecimal receiptAmount;

    @ApiModelProperty(value = "应收余额（原币）")
    private BigDecimal receivableAmount;

    @ApiModelProperty(value = "<0月账龄金额（原币）")
    private BigDecimal ageAmount0;

    @ApiModelProperty(value = "0-1个月账龄金额（原币）")
    private BigDecimal ageAmount1;

    @ApiModelProperty(value = "1-2个月账龄金额（原币）")
    private BigDecimal ageAmount2;

    @ApiModelProperty(value = "2-3月账龄金额（原币）")
    private BigDecimal ageAmount3;

    @ApiModelProperty(value = "4-6个月账龄金额（原币）")
    private BigDecimal ageAmount4;

    @ApiModelProperty(value = "7-12个月账龄金额（原币）")
    private BigDecimal ageAmount5;

    @ApiModelProperty(value = ">1年账龄金额（原币）")
    private BigDecimal ageAmount6;

    @ApiModelProperty(value = "1-3个月账龄金额（原币）")
    private BigDecimal ageAmount7;

    @ApiModelProperty(value = "规则值拼接")
    private String ruleValue;

    @ApiModelProperty(value = "规则范围拼接")
    private String ruleScope;

    @ApiModelProperty(value = "规则名称拼接")
    private String ruleName;

    @ApiModelProperty(value = "是否删除")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode == null ? null : parentCode.trim();
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName == null ? null : parentName.trim();
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType == null ? null : projectType.trim();
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode == null ? null : customerCode.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public String getSalesManager() {
        return salesManager;
    }

    public void setSalesManager(String salesManager) {
        this.salesManager = salesManager == null ? null : salesManager.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getExcludingTaxAmount() {
        return excludingTaxAmount;
    }

    public void setExcludingTaxAmount(BigDecimal excludingTaxAmount) {
        this.excludingTaxAmount = excludingTaxAmount;
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public BigDecimal getStandardAmount() {
        return standardAmount;
    }

    public void setStandardAmount(BigDecimal standardAmount) {
        this.standardAmount = standardAmount;
    }

    public BigDecimal getStandardExcludingTaxAmount() {
        return standardExcludingTaxAmount;
    }

    public void setStandardExcludingTaxAmount(BigDecimal standardExcludingTaxAmount) {
        this.standardExcludingTaxAmount = standardExcludingTaxAmount;
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public BigDecimal getReceiptAmount() {
        return receiptAmount;
    }

    public void setReceiptAmount(BigDecimal receiptAmount) {
        this.receiptAmount = receiptAmount;
    }

    public BigDecimal getReceivableAmount() {
        return receivableAmount;
    }

    public void setReceivableAmount(BigDecimal receivableAmount) {
        this.receivableAmount = receivableAmount;
    }

    public BigDecimal getAgeAmount0() {
        return ageAmount0;
    }

    public void setAgeAmount0(BigDecimal ageAmount0) {
        this.ageAmount0 = ageAmount0;
    }

    public BigDecimal getAgeAmount1() {
        return ageAmount1;
    }

    public void setAgeAmount1(BigDecimal ageAmount1) {
        this.ageAmount1 = ageAmount1;
    }

    public BigDecimal getAgeAmount2() {
        return ageAmount2;
    }

    public void setAgeAmount2(BigDecimal ageAmount2) {
        this.ageAmount2 = ageAmount2;
    }

    public BigDecimal getAgeAmount3() {
        return ageAmount3;
    }

    public void setAgeAmount3(BigDecimal ageAmount3) {
        this.ageAmount3 = ageAmount3;
    }

    public BigDecimal getAgeAmount4() {
        return ageAmount4;
    }

    public void setAgeAmount4(BigDecimal ageAmount4) {
        this.ageAmount4 = ageAmount4;
    }

    public BigDecimal getAgeAmount5() {
        return ageAmount5;
    }

    public void setAgeAmount5(BigDecimal ageAmount5) {
        this.ageAmount5 = ageAmount5;
    }

    public BigDecimal getAgeAmount6() {
        return ageAmount6;
    }

    public void setAgeAmount6(BigDecimal ageAmount6) {
        this.ageAmount6 = ageAmount6;
    }

    public BigDecimal getAgeAmount7() {
        return ageAmount7;
    }

    public void setAgeAmount7(BigDecimal ageAmount7) {
        this.ageAmount7 = ageAmount7;
    }

    public String getRuleValue() {
        return ruleValue;
    }

    public void setRuleValue(String ruleValue) {
        this.ruleValue = ruleValue == null ? null : ruleValue.trim();
    }

    public String getRuleScope() {
        return ruleScope;
    }

    public void setRuleScope(String ruleScope) {
        this.ruleScope = ruleScope == null ? null : ruleScope.trim();
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName == null ? null : ruleName.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", code=").append(code);
        sb.append(", name=").append(name);
        sb.append(", parentCode=").append(parentCode);
        sb.append(", parentName=").append(parentName);
        sb.append(", ouName=").append(ouName);
        sb.append(", unitName=").append(unitName);
        sb.append(", projectType=").append(projectType);
        sb.append(", customerCode=").append(customerCode);
        sb.append(", customerName=").append(customerName);
        sb.append(", customerType=").append(customerType);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", salesManager=").append(salesManager);
        sb.append(", currency=").append(currency);
        sb.append(", amount=").append(amount);
        sb.append(", excludingTaxAmount=").append(excludingTaxAmount);
        sb.append(", conversionRate=").append(conversionRate);
        sb.append(", standardAmount=").append(standardAmount);
        sb.append(", standardExcludingTaxAmount=").append(standardExcludingTaxAmount);
        sb.append(", invoiceAmount=").append(invoiceAmount);
        sb.append(", receiptAmount=").append(receiptAmount);
        sb.append(", receivableAmount=").append(receivableAmount);
        sb.append(", ageAmount0=").append(ageAmount0);
        sb.append(", ageAmount1=").append(ageAmount1);
        sb.append(", ageAmount2=").append(ageAmount2);
        sb.append(", ageAmount3=").append(ageAmount3);
        sb.append(", ageAmount4=").append(ageAmount4);
        sb.append(", ageAmount5=").append(ageAmount5);
        sb.append(", ageAmount6=").append(ageAmount6);
        sb.append(", ageAmount7=").append(ageAmount7);
        sb.append(", ruleValue=").append(ruleValue);
        sb.append(", ruleScope=").append(ruleScope);
        sb.append(", ruleName=").append(ruleName);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}