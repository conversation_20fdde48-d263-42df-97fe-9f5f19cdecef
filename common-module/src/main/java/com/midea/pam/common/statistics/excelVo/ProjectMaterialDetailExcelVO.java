package com.midea.pam.common.statistics.excelVo;


import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-7-25
 * @description 已发生成本-领退料成本
 */
@Getter
@Setter
public class ProjectMaterialDetailExcelVO {

    @Excel(name = "序号", width = 5)
    private Integer number;

    @Excel(name = "WBS", width = 30)
    private String wbsSummaryCode;

    @Excel(name = "活动事项", width = 15)
    private String activityCode;

    @Excel(name = "领退料单编号", width = 25)
    private String billCode;

    @Excel(name = "物料ERP编码", width = 25)
    private String materialCode;

    @Excel(name = "实际领退料数", width = 15)
    private BigDecimal actualAmount;

    @Excel(name = "成本单价", width = 15)
    private BigDecimal actualCost;

    @Excel(name = "领退料单*单位成本", width = 15)
    private BigDecimal totalAmount;

    @Excel(name = "实际发料日期", width = 20, format = "yyyy-MM-dd")
    private Date dataTime;
}
