package com.midea.pam.common.statistics.excelVo;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020-08-09 10:48
 * 项目PO成本明细报表ExcelVO
 */
public class ReportProjectPurchaseOrderExcelVO {

    @Excel(name = "序号", width = 10)
    private Integer number;

    @Excel(name = "项目编号", width = 15)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "采购单号/合同号", width = 30)
    private String code;

    @Excel(name = "供应商名称", width = 30)
    private String vendorName;

    @Excel(name = "供应商编码", width = 15)
    private String vendorNum;

    @Excel(name = "ERP物料编码", width = 30)
    private String erpCode;

    @Excel(name = "物料描述", width = 30)
    private String materielDescr;

    @Excel(name = "品牌", width = 20)
    private String brand;

    @Excel(name = "型号", width = 20)
    private String model;

    @Excel(name = "名称", width = 20)
    private String name;

    @Excel(name = "下单数量", width = 10)
    private String orderNum_dt;

    @Excel(name = "采购单价/合同总额", width = 20)
    private String cost_dt;

    @Excel(name = "采购总额/合同总额", width = 20)
    private String totalCost_dt;

    private BigDecimal orderNum;

    private BigDecimal cost;

    private BigDecimal totalCost;

    @Excel(name = "ERP同步状态", width = 15, replace = {"3_同步中", "2_同步失败", "已同步_1","未同步_0","_null"})
    private Integer syncStatus;

    @Excel(name = "是否外包", width = 10, replace = {"是_1","否_0","_null"})
    private Integer contractFlag;

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(Integer syncStatus) {
        this.syncStatus = syncStatus;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getVendorNum() {
        return vendorNum;
    }

    public void setVendorNum(String vendorNum) {
        this.vendorNum = vendorNum;
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode;
    }

    public String getMaterielDescr() {
        return materielDescr;
    }

    public void setMaterielDescr(String materielDescr) {
        this.materielDescr = materielDescr;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOrderNum_dt() {
        if (Objects.nonNull(this.getOrderNum())) {
            return this.orderNum.stripTrailingZeros().toPlainString();
        } else {
            return this.orderNum_dt;
        }
    }

    public void setOrderNum_dt(String orderNum_dt) {
        this.orderNum_dt = orderNum_dt;
    }

    public String getCost_dt() {
        if (Objects.nonNull(this.getCost())) {
            return this.cost.stripTrailingZeros().toPlainString();
        } else {
            return this.cost_dt;
        }
    }

    public void setCost_dt(String cost_dt) {
        this.cost_dt = cost_dt;
    }

    public String getTotalCost_dt() {
        if (Objects.nonNull(this.getTotalCost())) {
            return this.totalCost.stripTrailingZeros().toPlainString();
        } else {
            return this.totalCost_dt;
        }
    }

    public void setTotalCost_dt(String totalCost_dt) {
        this.totalCost_dt = totalCost_dt;
    }

    public BigDecimal getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(BigDecimal orderNum) {
        this.orderNum = orderNum;
    }

    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    public Integer getContractFlag() {
        return contractFlag;
    }

    public void setContractFlag(Integer contractFlag) {
        this.contractFlag = contractFlag;
    }
}