package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "wbs预算快照头表")
public class ProjectWbsBudgetVersion extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "项目历史变更头表id(定时null)")
    private Long projectHistoryHeaderId;

    @ApiModelProperty(value = "流程app")
    private String formUrl;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "业务模式:1-定时生成、2-预算变更、3-预立项转正、4-关联合同变更")
    private Integer businessType;

    @ApiModelProperty(value = "版本时间")
    private Date versionTime;

    @ApiModelProperty(value = "版本号")
    private String versionCode;

    @ApiModelProperty(value = "创建人MIP")
    private String createByMip;

    @ApiModelProperty(value = "创建人名称")
    private String createByName;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getProjectHistoryHeaderId() {
        return projectHistoryHeaderId;
    }

    public void setProjectHistoryHeaderId(Long projectHistoryHeaderId) {
        this.projectHistoryHeaderId = projectHistoryHeaderId;
    }

    public String getFormUrl() {
        return formUrl;
    }

    public void setFormUrl(String formUrl) {
        this.formUrl = formUrl == null ? null : formUrl.trim();
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Date getVersionTime() {
        return versionTime;
    }

    public void setVersionTime(Date versionTime) {
        this.versionTime = versionTime;
    }

    public String getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode == null ? null : versionCode.trim();
    }

    public String getCreateByMip() {
        return createByMip;
    }

    public void setCreateByMip(String createByMip) {
        this.createByMip = createByMip == null ? null : createByMip.trim();
    }

    public String getCreateByName() {
        return createByName;
    }

    public void setCreateByName(String createByName) {
        this.createByName = createByName == null ? null : createByName.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", projectHistoryHeaderId=").append(projectHistoryHeaderId);
        sb.append(", formUrl=").append(formUrl);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", businessType=").append(businessType);
        sb.append(", versionTime=").append(versionTime);
        sb.append(", versionCode=").append(versionCode);
        sb.append(", createByMip=").append(createByMip);
        sb.append(", createByName=").append(createByName);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}