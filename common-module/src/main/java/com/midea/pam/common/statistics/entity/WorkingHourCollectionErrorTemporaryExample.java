package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WorkingHourCollectionErrorTemporaryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WorkingHourCollectionErrorTemporaryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCostCollectionIdIsNull() {
            addCriterion("cost_collection_id is null");
            return (Criteria) this;
        }

        public Criteria andCostCollectionIdIsNotNull() {
            addCriterion("cost_collection_id is not null");
            return (Criteria) this;
        }

        public Criteria andCostCollectionIdEqualTo(Long value) {
            addCriterion("cost_collection_id =", value, "costCollectionId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionIdNotEqualTo(Long value) {
            addCriterion("cost_collection_id <>", value, "costCollectionId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionIdGreaterThan(Long value) {
            addCriterion("cost_collection_id >", value, "costCollectionId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionIdGreaterThanOrEqualTo(Long value) {
            addCriterion("cost_collection_id >=", value, "costCollectionId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionIdLessThan(Long value) {
            addCriterion("cost_collection_id <", value, "costCollectionId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionIdLessThanOrEqualTo(Long value) {
            addCriterion("cost_collection_id <=", value, "costCollectionId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionIdIn(List<Long> values) {
            addCriterion("cost_collection_id in", values, "costCollectionId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionIdNotIn(List<Long> values) {
            addCriterion("cost_collection_id not in", values, "costCollectionId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionIdBetween(Long value1, Long value2) {
            addCriterion("cost_collection_id between", value1, value2, "costCollectionId");
            return (Criteria) this;
        }

        public Criteria andCostCollectionIdNotBetween(Long value1, Long value2) {
            addCriterion("cost_collection_id not between", value1, value2, "costCollectionId");
            return (Criteria) this;
        }

        public Criteria andCollectionDateIsNull() {
            addCriterion("collection_date is null");
            return (Criteria) this;
        }

        public Criteria andCollectionDateIsNotNull() {
            addCriterion("collection_date is not null");
            return (Criteria) this;
        }

        public Criteria andCollectionDateEqualTo(Date value) {
            addCriterion("collection_date =", value, "collectionDate");
            return (Criteria) this;
        }

        public Criteria andCollectionDateNotEqualTo(Date value) {
            addCriterion("collection_date <>", value, "collectionDate");
            return (Criteria) this;
        }

        public Criteria andCollectionDateGreaterThan(Date value) {
            addCriterion("collection_date >", value, "collectionDate");
            return (Criteria) this;
        }

        public Criteria andCollectionDateGreaterThanOrEqualTo(Date value) {
            addCriterion("collection_date >=", value, "collectionDate");
            return (Criteria) this;
        }

        public Criteria andCollectionDateLessThan(Date value) {
            addCriterion("collection_date <", value, "collectionDate");
            return (Criteria) this;
        }

        public Criteria andCollectionDateLessThanOrEqualTo(Date value) {
            addCriterion("collection_date <=", value, "collectionDate");
            return (Criteria) this;
        }

        public Criteria andCollectionDateIn(List<Date> values) {
            addCriterion("collection_date in", values, "collectionDate");
            return (Criteria) this;
        }

        public Criteria andCollectionDateNotIn(List<Date> values) {
            addCriterion("collection_date not in", values, "collectionDate");
            return (Criteria) this;
        }

        public Criteria andCollectionDateBetween(Date value1, Date value2) {
            addCriterion("collection_date between", value1, value2, "collectionDate");
            return (Criteria) this;
        }

        public Criteria andCollectionDateNotBetween(Date value1, Date value2) {
            addCriterion("collection_date not between", value1, value2, "collectionDate");
            return (Criteria) this;
        }

        public Criteria andCostDateIsNull() {
            addCriterion("cost_date is null");
            return (Criteria) this;
        }

        public Criteria andCostDateIsNotNull() {
            addCriterion("cost_date is not null");
            return (Criteria) this;
        }

        public Criteria andCostDateEqualTo(Date value) {
            addCriterion("cost_date =", value, "costDate");
            return (Criteria) this;
        }

        public Criteria andCostDateNotEqualTo(Date value) {
            addCriterion("cost_date <>", value, "costDate");
            return (Criteria) this;
        }

        public Criteria andCostDateGreaterThan(Date value) {
            addCriterion("cost_date >", value, "costDate");
            return (Criteria) this;
        }

        public Criteria andCostDateGreaterThanOrEqualTo(Date value) {
            addCriterion("cost_date >=", value, "costDate");
            return (Criteria) this;
        }

        public Criteria andCostDateLessThan(Date value) {
            addCriterion("cost_date <", value, "costDate");
            return (Criteria) this;
        }

        public Criteria andCostDateLessThanOrEqualTo(Date value) {
            addCriterion("cost_date <=", value, "costDate");
            return (Criteria) this;
        }

        public Criteria andCostDateIn(List<Date> values) {
            addCriterion("cost_date in", values, "costDate");
            return (Criteria) this;
        }

        public Criteria andCostDateNotIn(List<Date> values) {
            addCriterion("cost_date not in", values, "costDate");
            return (Criteria) this;
        }

        public Criteria andCostDateBetween(Date value1, Date value2) {
            addCriterion("cost_date between", value1, value2, "costDate");
            return (Criteria) this;
        }

        public Criteria andCostDateNotBetween(Date value1, Date value2) {
            addCriterion("cost_date not between", value1, value2, "costDate");
            return (Criteria) this;
        }

        public Criteria andCarryStatusIsNull() {
            addCriterion("carry_status is null");
            return (Criteria) this;
        }

        public Criteria andCarryStatusIsNotNull() {
            addCriterion("carry_status is not null");
            return (Criteria) this;
        }

        public Criteria andCarryStatusEqualTo(Integer value) {
            addCriterion("carry_status =", value, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusNotEqualTo(Integer value) {
            addCriterion("carry_status <>", value, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusGreaterThan(Integer value) {
            addCriterion("carry_status >", value, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("carry_status >=", value, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusLessThan(Integer value) {
            addCriterion("carry_status <", value, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusLessThanOrEqualTo(Integer value) {
            addCriterion("carry_status <=", value, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusIn(List<Integer> values) {
            addCriterion("carry_status in", values, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusNotIn(List<Integer> values) {
            addCriterion("carry_status not in", values, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusBetween(Integer value1, Integer value2) {
            addCriterion("carry_status between", value1, value2, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andCarryStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("carry_status not between", value1, value2, "carryStatus");
            return (Criteria) this;
        }

        public Criteria andGlPeriodIsNull() {
            addCriterion("gl_period is null");
            return (Criteria) this;
        }

        public Criteria andGlPeriodIsNotNull() {
            addCriterion("gl_period is not null");
            return (Criteria) this;
        }

        public Criteria andGlPeriodEqualTo(String value) {
            addCriterion("gl_period =", value, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodNotEqualTo(String value) {
            addCriterion("gl_period <>", value, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodGreaterThan(String value) {
            addCriterion("gl_period >", value, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodGreaterThanOrEqualTo(String value) {
            addCriterion("gl_period >=", value, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodLessThan(String value) {
            addCriterion("gl_period <", value, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodLessThanOrEqualTo(String value) {
            addCriterion("gl_period <=", value, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodLike(String value) {
            addCriterion("gl_period like", value, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodNotLike(String value) {
            addCriterion("gl_period not like", value, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodIn(List<String> values) {
            addCriterion("gl_period in", values, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodNotIn(List<String> values) {
            addCriterion("gl_period not in", values, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodBetween(String value1, String value2) {
            addCriterion("gl_period between", value1, value2, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodNotBetween(String value1, String value2) {
            addCriterion("gl_period not between", value1, value2, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNull() {
            addCriterion("project_type is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNotNull() {
            addCriterion("project_type is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeEqualTo(Long value) {
            addCriterion("project_type =", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotEqualTo(Long value) {
            addCriterion("project_type <>", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThan(Long value) {
            addCriterion("project_type >", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThanOrEqualTo(Long value) {
            addCriterion("project_type >=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThan(Long value) {
            addCriterion("project_type <", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThanOrEqualTo(Long value) {
            addCriterion("project_type <=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIn(List<Long> values) {
            addCriterion("project_type in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotIn(List<Long> values) {
            addCriterion("project_type not in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeBetween(Long value1, Long value2) {
            addCriterion("project_type between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotBetween(Long value1, Long value2) {
            addCriterion("project_type not between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostIsNull() {
            addCriterion("material_actual_cost is null");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostIsNotNull() {
            addCriterion("material_actual_cost is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostEqualTo(BigDecimal value) {
            addCriterion("material_actual_cost =", value, "materialActualCost");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostNotEqualTo(BigDecimal value) {
            addCriterion("material_actual_cost <>", value, "materialActualCost");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostGreaterThan(BigDecimal value) {
            addCriterion("material_actual_cost >", value, "materialActualCost");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_actual_cost >=", value, "materialActualCost");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostLessThan(BigDecimal value) {
            addCriterion("material_actual_cost <", value, "materialActualCost");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_actual_cost <=", value, "materialActualCost");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostIn(List<BigDecimal> values) {
            addCriterion("material_actual_cost in", values, "materialActualCost");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostNotIn(List<BigDecimal> values) {
            addCriterion("material_actual_cost not in", values, "materialActualCost");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_actual_cost between", value1, value2, "materialActualCost");
            return (Criteria) this;
        }

        public Criteria andMaterialActualCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_actual_cost not between", value1, value2, "materialActualCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostIsNull() {
            addCriterion("material_outsource_cost is null");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostIsNotNull() {
            addCriterion("material_outsource_cost is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost =", value, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNotEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost <>", value, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostGreaterThan(BigDecimal value) {
            addCriterion("material_outsource_cost >", value, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost >=", value, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostLessThan(BigDecimal value) {
            addCriterion("material_outsource_cost <", value, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost <=", value, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostIn(List<BigDecimal> values) {
            addCriterion("material_outsource_cost in", values, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNotIn(List<BigDecimal> values) {
            addCriterion("material_outsource_cost not in", values, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_outsource_cost between", value1, value2, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_outsource_cost not between", value1, value2, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostIsNull() {
            addCriterion("inner_labor_cost is null");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostIsNotNull() {
            addCriterion("inner_labor_cost is not null");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost =", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNotEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost <>", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostGreaterThan(BigDecimal value) {
            addCriterion("inner_labor_cost >", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost >=", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostLessThan(BigDecimal value) {
            addCriterion("inner_labor_cost <", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost <=", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostIn(List<BigDecimal> values) {
            addCriterion("inner_labor_cost in", values, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNotIn(List<BigDecimal> values) {
            addCriterion("inner_labor_cost not in", values, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_labor_cost between", value1, value2, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_labor_cost not between", value1, value2, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostIsNull() {
            addCriterion("outer_labor_cost is null");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostIsNotNull() {
            addCriterion("outer_labor_cost is not null");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostEqualTo(BigDecimal value) {
            addCriterion("outer_labor_cost =", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostNotEqualTo(BigDecimal value) {
            addCriterion("outer_labor_cost <>", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostGreaterThan(BigDecimal value) {
            addCriterion("outer_labor_cost >", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("outer_labor_cost >=", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostLessThan(BigDecimal value) {
            addCriterion("outer_labor_cost <", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("outer_labor_cost <=", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostIn(List<BigDecimal> values) {
            addCriterion("outer_labor_cost in", values, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostNotIn(List<BigDecimal> values) {
            addCriterion("outer_labor_cost not in", values, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outer_labor_cost between", value1, value2, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outer_labor_cost not between", value1, value2, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostIsNull() {
            addCriterion("material_difference_cost is null");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostIsNotNull() {
            addCriterion("material_difference_cost is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostEqualTo(BigDecimal value) {
            addCriterion("material_difference_cost =", value, "materialDifferenceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostNotEqualTo(BigDecimal value) {
            addCriterion("material_difference_cost <>", value, "materialDifferenceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostGreaterThan(BigDecimal value) {
            addCriterion("material_difference_cost >", value, "materialDifferenceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_difference_cost >=", value, "materialDifferenceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostLessThan(BigDecimal value) {
            addCriterion("material_difference_cost <", value, "materialDifferenceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_difference_cost <=", value, "materialDifferenceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostIn(List<BigDecimal> values) {
            addCriterion("material_difference_cost in", values, "materialDifferenceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostNotIn(List<BigDecimal> values) {
            addCriterion("material_difference_cost not in", values, "materialDifferenceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_difference_cost between", value1, value2, "materialDifferenceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialDifferenceCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_difference_cost not between", value1, value2, "materialDifferenceCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostIsNull() {
            addCriterion("fee_cost is null");
            return (Criteria) this;
        }

        public Criteria andFeeCostIsNotNull() {
            addCriterion("fee_cost is not null");
            return (Criteria) this;
        }

        public Criteria andFeeCostEqualTo(BigDecimal value) {
            addCriterion("fee_cost =", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostNotEqualTo(BigDecimal value) {
            addCriterion("fee_cost <>", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostGreaterThan(BigDecimal value) {
            addCriterion("fee_cost >", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("fee_cost >=", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostLessThan(BigDecimal value) {
            addCriterion("fee_cost <", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("fee_cost <=", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostIn(List<BigDecimal> values) {
            addCriterion("fee_cost in", values, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostNotIn(List<BigDecimal> values) {
            addCriterion("fee_cost not in", values, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee_cost between", value1, value2, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee_cost not between", value1, value2, "feeCost");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdIsNull() {
            addCriterion("biz_unit_id is null");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdIsNotNull() {
            addCriterion("biz_unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdEqualTo(Long value) {
            addCriterion("biz_unit_id =", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdNotEqualTo(Long value) {
            addCriterion("biz_unit_id <>", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdGreaterThan(Long value) {
            addCriterion("biz_unit_id >", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("biz_unit_id >=", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdLessThan(Long value) {
            addCriterion("biz_unit_id <", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("biz_unit_id <=", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdIn(List<Long> values) {
            addCriterion("biz_unit_id in", values, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdNotIn(List<Long> values) {
            addCriterion("biz_unit_id not in", values, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdBetween(Long value1, Long value2) {
            addCriterion("biz_unit_id between", value1, value2, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("biz_unit_id not between", value1, value2, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNull() {
            addCriterion("ou_name is null");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNotNull() {
            addCriterion("ou_name is not null");
            return (Criteria) this;
        }

        public Criteria andOuNameEqualTo(String value) {
            addCriterion("ou_name =", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotEqualTo(String value) {
            addCriterion("ou_name <>", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThan(String value) {
            addCriterion("ou_name >", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThanOrEqualTo(String value) {
            addCriterion("ou_name >=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThan(String value) {
            addCriterion("ou_name <", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThanOrEqualTo(String value) {
            addCriterion("ou_name <=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLike(String value) {
            addCriterion("ou_name like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotLike(String value) {
            addCriterion("ou_name not like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameIn(List<String> values) {
            addCriterion("ou_name in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotIn(List<String> values) {
            addCriterion("ou_name not in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameBetween(String value1, String value2) {
            addCriterion("ou_name between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotBetween(String value1, String value2) {
            addCriterion("ou_name not between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andErpMessagesIsNull() {
            addCriterion("erp_messages is null");
            return (Criteria) this;
        }

        public Criteria andErpMessagesIsNotNull() {
            addCriterion("erp_messages is not null");
            return (Criteria) this;
        }

        public Criteria andErpMessagesEqualTo(String value) {
            addCriterion("erp_messages =", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesNotEqualTo(String value) {
            addCriterion("erp_messages <>", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesGreaterThan(String value) {
            addCriterion("erp_messages >", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesGreaterThanOrEqualTo(String value) {
            addCriterion("erp_messages >=", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesLessThan(String value) {
            addCriterion("erp_messages <", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesLessThanOrEqualTo(String value) {
            addCriterion("erp_messages <=", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesLike(String value) {
            addCriterion("erp_messages like", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesNotLike(String value) {
            addCriterion("erp_messages not like", value, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesIn(List<String> values) {
            addCriterion("erp_messages in", values, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesNotIn(List<String> values) {
            addCriterion("erp_messages not in", values, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesBetween(String value1, String value2) {
            addCriterion("erp_messages between", value1, value2, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andErpMessagesNotBetween(String value1, String value2) {
            addCriterion("erp_messages not between", value1, value2, "erpMessages");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}