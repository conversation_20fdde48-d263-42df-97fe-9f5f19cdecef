package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class ProjectCostRevenueOrderRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectCostRevenueOrderRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdIsNull() {
            addCriterion("summary_id is null");
            return (Criteria) this;
        }

        public Criteria andSummaryIdIsNotNull() {
            addCriterion("summary_id is not null");
            return (Criteria) this;
        }

        public Criteria andSummaryIdEqualTo(Long value) {
            addCriterion("summary_id =", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdNotEqualTo(Long value) {
            addCriterion("summary_id <>", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdGreaterThan(Long value) {
            addCriterion("summary_id >", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("summary_id >=", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdLessThan(Long value) {
            addCriterion("summary_id <", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdLessThanOrEqualTo(Long value) {
            addCriterion("summary_id <=", value, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdIn(List<Long> values) {
            addCriterion("summary_id in", values, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdNotIn(List<Long> values) {
            addCriterion("summary_id not in", values, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdBetween(Long value1, Long value2) {
            addCriterion("summary_id between", value1, value2, "summaryId");
            return (Criteria) this;
        }

        public Criteria andSummaryIdNotBetween(Long value1, Long value2) {
            addCriterion("summary_id not between", value1, value2, "summaryId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andOrderCodeIsNull() {
            addCriterion("order_code is null");
            return (Criteria) this;
        }

        public Criteria andOrderCodeIsNotNull() {
            addCriterion("order_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCodeEqualTo(String value) {
            addCriterion("order_code =", value, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeNotEqualTo(String value) {
            addCriterion("order_code <>", value, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeGreaterThan(String value) {
            addCriterion("order_code >", value, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeGreaterThanOrEqualTo(String value) {
            addCriterion("order_code >=", value, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeLessThan(String value) {
            addCriterion("order_code <", value, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeLessThanOrEqualTo(String value) {
            addCriterion("order_code <=", value, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeLike(String value) {
            addCriterion("order_code like", value, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeNotLike(String value) {
            addCriterion("order_code not like", value, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeIn(List<String> values) {
            addCriterion("order_code in", values, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeNotIn(List<String> values) {
            addCriterion("order_code not in", values, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeBetween(String value1, String value2) {
            addCriterion("order_code between", value1, value2, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeNotBetween(String value1, String value2) {
            addCriterion("order_code not between", value1, value2, "orderCode");
            return (Criteria) this;
        }

        public Criteria andInputDateIsNull() {
            addCriterion("input_date is null");
            return (Criteria) this;
        }

        public Criteria andInputDateIsNotNull() {
            addCriterion("input_date is not null");
            return (Criteria) this;
        }

        public Criteria andInputDateEqualTo(Date value) {
            addCriterionForJDBCDate("input_date =", value, "inputDate");
            return (Criteria) this;
        }

        public Criteria andInputDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("input_date <>", value, "inputDate");
            return (Criteria) this;
        }

        public Criteria andInputDateGreaterThan(Date value) {
            addCriterionForJDBCDate("input_date >", value, "inputDate");
            return (Criteria) this;
        }

        public Criteria andInputDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("input_date >=", value, "inputDate");
            return (Criteria) this;
        }

        public Criteria andInputDateLessThan(Date value) {
            addCriterionForJDBCDate("input_date <", value, "inputDate");
            return (Criteria) this;
        }

        public Criteria andInputDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("input_date <=", value, "inputDate");
            return (Criteria) this;
        }

        public Criteria andInputDateIn(List<Date> values) {
            addCriterionForJDBCDate("input_date in", values, "inputDate");
            return (Criteria) this;
        }

        public Criteria andInputDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("input_date not in", values, "inputDate");
            return (Criteria) this;
        }

        public Criteria andInputDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("input_date between", value1, value2, "inputDate");
            return (Criteria) this;
        }

        public Criteria andInputDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("input_date not between", value1, value2, "inputDate");
            return (Criteria) this;
        }

        public Criteria andMilepostIdIsNull() {
            addCriterion("milepost_id is null");
            return (Criteria) this;
        }

        public Criteria andMilepostIdIsNotNull() {
            addCriterion("milepost_id is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostIdEqualTo(Long value) {
            addCriterion("milepost_id =", value, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdNotEqualTo(Long value) {
            addCriterion("milepost_id <>", value, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdGreaterThan(Long value) {
            addCriterion("milepost_id >", value, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdGreaterThanOrEqualTo(Long value) {
            addCriterion("milepost_id >=", value, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdLessThan(Long value) {
            addCriterion("milepost_id <", value, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdLessThanOrEqualTo(Long value) {
            addCriterion("milepost_id <=", value, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdIn(List<Long> values) {
            addCriterion("milepost_id in", values, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdNotIn(List<Long> values) {
            addCriterion("milepost_id not in", values, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdBetween(Long value1, Long value2) {
            addCriterion("milepost_id between", value1, value2, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdNotBetween(Long value1, Long value2) {
            addCriterion("milepost_id not between", value1, value2, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostNameIsNull() {
            addCriterion("milepost_name is null");
            return (Criteria) this;
        }

        public Criteria andMilepostNameIsNotNull() {
            addCriterion("milepost_name is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostNameEqualTo(String value) {
            addCriterion("milepost_name =", value, "milepostName");
            return (Criteria) this;
        }

        public Criteria andMilepostNameNotEqualTo(String value) {
            addCriterion("milepost_name <>", value, "milepostName");
            return (Criteria) this;
        }

        public Criteria andMilepostNameGreaterThan(String value) {
            addCriterion("milepost_name >", value, "milepostName");
            return (Criteria) this;
        }

        public Criteria andMilepostNameGreaterThanOrEqualTo(String value) {
            addCriterion("milepost_name >=", value, "milepostName");
            return (Criteria) this;
        }

        public Criteria andMilepostNameLessThan(String value) {
            addCriterion("milepost_name <", value, "milepostName");
            return (Criteria) this;
        }

        public Criteria andMilepostNameLessThanOrEqualTo(String value) {
            addCriterion("milepost_name <=", value, "milepostName");
            return (Criteria) this;
        }

        public Criteria andMilepostNameLike(String value) {
            addCriterion("milepost_name like", value, "milepostName");
            return (Criteria) this;
        }

        public Criteria andMilepostNameNotLike(String value) {
            addCriterion("milepost_name not like", value, "milepostName");
            return (Criteria) this;
        }

        public Criteria andMilepostNameIn(List<String> values) {
            addCriterion("milepost_name in", values, "milepostName");
            return (Criteria) this;
        }

        public Criteria andMilepostNameNotIn(List<String> values) {
            addCriterion("milepost_name not in", values, "milepostName");
            return (Criteria) this;
        }

        public Criteria andMilepostNameBetween(String value1, String value2) {
            addCriterion("milepost_name between", value1, value2, "milepostName");
            return (Criteria) this;
        }

        public Criteria andMilepostNameNotBetween(String value1, String value2) {
            addCriterion("milepost_name not between", value1, value2, "milepostName");
            return (Criteria) this;
        }

        public Criteria andMaterialCostIsNull() {
            addCriterion("material_cost is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCostIsNotNull() {
            addCriterion("material_cost is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCostEqualTo(BigDecimal value) {
            addCriterion("material_cost =", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostNotEqualTo(BigDecimal value) {
            addCriterion("material_cost <>", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostGreaterThan(BigDecimal value) {
            addCriterion("material_cost >", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_cost >=", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostLessThan(BigDecimal value) {
            addCriterion("material_cost <", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_cost <=", value, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostIn(List<BigDecimal> values) {
            addCriterion("material_cost in", values, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostNotIn(List<BigDecimal> values) {
            addCriterion("material_cost not in", values, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_cost between", value1, value2, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_cost not between", value1, value2, "materialCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostIsNull() {
            addCriterion("material_outsource_cost is null");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostIsNotNull() {
            addCriterion("material_outsource_cost is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost =", value, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNotEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost <>", value, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostGreaterThan(BigDecimal value) {
            addCriterion("material_outsource_cost >", value, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost >=", value, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostLessThan(BigDecimal value) {
            addCriterion("material_outsource_cost <", value, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_outsource_cost <=", value, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostIn(List<BigDecimal> values) {
            addCriterion("material_outsource_cost in", values, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNotIn(List<BigDecimal> values) {
            addCriterion("material_outsource_cost not in", values, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_outsource_cost between", value1, value2, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andMaterialOutsourceCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_outsource_cost not between", value1, value2, "materialOutsourceCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostIsNull() {
            addCriterion("inner_labor_cost is null");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostIsNotNull() {
            addCriterion("inner_labor_cost is not null");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost =", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNotEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost <>", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostGreaterThan(BigDecimal value) {
            addCriterion("inner_labor_cost >", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost >=", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostLessThan(BigDecimal value) {
            addCriterion("inner_labor_cost <", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_labor_cost <=", value, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostIn(List<BigDecimal> values) {
            addCriterion("inner_labor_cost in", values, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNotIn(List<BigDecimal> values) {
            addCriterion("inner_labor_cost not in", values, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_labor_cost between", value1, value2, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andInnerLaborCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_labor_cost not between", value1, value2, "innerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostIsNull() {
            addCriterion("outer_labor_cost is null");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostIsNotNull() {
            addCriterion("outer_labor_cost is not null");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostEqualTo(BigDecimal value) {
            addCriterion("outer_labor_cost =", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostNotEqualTo(BigDecimal value) {
            addCriterion("outer_labor_cost <>", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostGreaterThan(BigDecimal value) {
            addCriterion("outer_labor_cost >", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("outer_labor_cost >=", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostLessThan(BigDecimal value) {
            addCriterion("outer_labor_cost <", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("outer_labor_cost <=", value, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostIn(List<BigDecimal> values) {
            addCriterion("outer_labor_cost in", values, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostNotIn(List<BigDecimal> values) {
            addCriterion("outer_labor_cost not in", values, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outer_labor_cost between", value1, value2, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andOuterLaborCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("outer_labor_cost not between", value1, value2, "outerLaborCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostIsNull() {
            addCriterion("fee_cost is null");
            return (Criteria) this;
        }

        public Criteria andFeeCostIsNotNull() {
            addCriterion("fee_cost is not null");
            return (Criteria) this;
        }

        public Criteria andFeeCostEqualTo(BigDecimal value) {
            addCriterion("fee_cost =", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostNotEqualTo(BigDecimal value) {
            addCriterion("fee_cost <>", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostGreaterThan(BigDecimal value) {
            addCriterion("fee_cost >", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("fee_cost >=", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostLessThan(BigDecimal value) {
            addCriterion("fee_cost <", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("fee_cost <=", value, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostIn(List<BigDecimal> values) {
            addCriterion("fee_cost in", values, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostNotIn(List<BigDecimal> values) {
            addCriterion("fee_cost not in", values, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee_cost between", value1, value2, "feeCost");
            return (Criteria) this;
        }

        public Criteria andFeeCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee_cost not between", value1, value2, "feeCost");
            return (Criteria) this;
        }

        public Criteria andOtherCostIsNull() {
            addCriterion("other_cost is null");
            return (Criteria) this;
        }

        public Criteria andOtherCostIsNotNull() {
            addCriterion("other_cost is not null");
            return (Criteria) this;
        }

        public Criteria andOtherCostEqualTo(BigDecimal value) {
            addCriterion("other_cost =", value, "otherCost");
            return (Criteria) this;
        }

        public Criteria andOtherCostNotEqualTo(BigDecimal value) {
            addCriterion("other_cost <>", value, "otherCost");
            return (Criteria) this;
        }

        public Criteria andOtherCostGreaterThan(BigDecimal value) {
            addCriterion("other_cost >", value, "otherCost");
            return (Criteria) this;
        }

        public Criteria andOtherCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("other_cost >=", value, "otherCost");
            return (Criteria) this;
        }

        public Criteria andOtherCostLessThan(BigDecimal value) {
            addCriterion("other_cost <", value, "otherCost");
            return (Criteria) this;
        }

        public Criteria andOtherCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("other_cost <=", value, "otherCost");
            return (Criteria) this;
        }

        public Criteria andOtherCostIn(List<BigDecimal> values) {
            addCriterion("other_cost in", values, "otherCost");
            return (Criteria) this;
        }

        public Criteria andOtherCostNotIn(List<BigDecimal> values) {
            addCriterion("other_cost not in", values, "otherCost");
            return (Criteria) this;
        }

        public Criteria andOtherCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_cost between", value1, value2, "otherCost");
            return (Criteria) this;
        }

        public Criteria andOtherCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_cost not between", value1, value2, "otherCost");
            return (Criteria) this;
        }

        public Criteria andSubTotalIsNull() {
            addCriterion("sub_total is null");
            return (Criteria) this;
        }

        public Criteria andSubTotalIsNotNull() {
            addCriterion("sub_total is not null");
            return (Criteria) this;
        }

        public Criteria andSubTotalEqualTo(BigDecimal value) {
            addCriterion("sub_total =", value, "subTotal");
            return (Criteria) this;
        }

        public Criteria andSubTotalNotEqualTo(BigDecimal value) {
            addCriterion("sub_total <>", value, "subTotal");
            return (Criteria) this;
        }

        public Criteria andSubTotalGreaterThan(BigDecimal value) {
            addCriterion("sub_total >", value, "subTotal");
            return (Criteria) this;
        }

        public Criteria andSubTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sub_total >=", value, "subTotal");
            return (Criteria) this;
        }

        public Criteria andSubTotalLessThan(BigDecimal value) {
            addCriterion("sub_total <", value, "subTotal");
            return (Criteria) this;
        }

        public Criteria andSubTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sub_total <=", value, "subTotal");
            return (Criteria) this;
        }

        public Criteria andSubTotalIn(List<BigDecimal> values) {
            addCriterion("sub_total in", values, "subTotal");
            return (Criteria) this;
        }

        public Criteria andSubTotalNotIn(List<BigDecimal> values) {
            addCriterion("sub_total not in", values, "subTotal");
            return (Criteria) this;
        }

        public Criteria andSubTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sub_total between", value1, value2, "subTotal");
            return (Criteria) this;
        }

        public Criteria andSubTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sub_total not between", value1, value2, "subTotal");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}