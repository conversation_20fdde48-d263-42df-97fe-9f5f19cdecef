package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.ReportDueDateReceivableDetail;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * Description
 * Created by liuqing
 * Date 2021/11/17 15:11
 */
public class ReportDueDateReceivableDetailDTO extends ReportDueDateReceivableDetail {

    private Long contractId;

    @ApiModelProperty(value = "冲销单原开票申请ID")
    private Long oldHeaderId;

    @ApiModelProperty(value = "开票申请含税金额")
    private BigDecimal invoiceApplyHeaderPrice;

    public BigDecimal getInvoiceApplyHeaderPrice() {
        return invoiceApplyHeaderPrice;
    }

    public void setInvoiceApplyHeaderPrice(BigDecimal invoiceApplyHeaderPrice) {
        this.invoiceApplyHeaderPrice = invoiceApplyHeaderPrice;
    }

    public Long getOldHeaderId() {
        return oldHeaderId;
    }

    public void setOldHeaderId(Long oldHeaderId) {
        this.oldHeaderId = oldHeaderId;
    }

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }
}
