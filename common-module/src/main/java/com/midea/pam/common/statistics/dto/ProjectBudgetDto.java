package com.midea.pam.common.statistics.dto;

import java.math.BigDecimal;

/**
 * @Author: <EMAIL>
 * @Description: 项目总预算结果Dto
 * @Date: 2020-12-17 15:50
 * @Version: 1.0
 */
public class ProjectBudgetDto {
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 项目总预算
     */
    private BigDecimal projectBudget;

    /**
     * 项目已发生的成本
     */
    private BigDecimal allHappenedCost;

    /**
     * 物料成本
     */
    private BigDecimal materialCost;
    /**
     * 人力成本
     */
    private BigDecimal laborCost;

    /**
     * 费用成本
     */
    private BigDecimal feeCost;


    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public BigDecimal getProjectBudget() {
        return projectBudget;
    }

    public void setProjectBudget(BigDecimal projectBudget) {
        this.projectBudget = projectBudget;
    }

    public BigDecimal getAllHappenedCost() {
        return allHappenedCost;
    }

    public void setAllHappenedCost(BigDecimal allHappenedCost) {
        this.allHappenedCost = allHappenedCost;
    }

    public BigDecimal getMaterialCost() {
        return materialCost;
    }

    public void setMaterialCost(BigDecimal materialCost) {
        this.materialCost = materialCost;
    }

    public BigDecimal getLaborCost() {
        return laborCost;
    }

    public void setLaborCost(BigDecimal laborCost) {
        this.laborCost = laborCost;
    }

    public BigDecimal getFeeCost() {
        return feeCost;
    }

    public void setFeeCost(BigDecimal feeCost) {
        this.feeCost = feeCost;
    }
}
