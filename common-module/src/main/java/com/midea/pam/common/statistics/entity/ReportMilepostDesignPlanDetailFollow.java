package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "详设跟踪报表")
public class ReportMilepostDesignPlanDetailFollow extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "详设id")
    private Long milepostDesignDetailId;

    @ApiModelProperty(value = "wbs编码（拼接）")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "物料类型")
    private String materialCategory;

    @ApiModelProperty(value = "序号")
    private String serialNumber;

    @ApiModelProperty(value = "物料描述")
    private String materielDescr;

    @ApiModelProperty(value = "PAM编码")
    private String pamCode;

    @ApiModelProperty(value = "ERP物料编码")
    private String erpCode;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "采购需求总数")
    private BigDecimal totalNum;

    @ApiModelProperty(value = "单套数量")
    private BigDecimal number;

    @ApiModelProperty(value = "已生成采购需求数量")
    private BigDecimal requirementNum;

    @ApiModelProperty(value = "需求日期")
    private Date deliveryTime;

    @ApiModelProperty(value = "详设发布数量")
    private BigDecimal milepostDesignPlanDetailTotalNum;

    @ApiModelProperty(value = "总需求数量")
    private BigDecimal needTotal;

    @ApiModelProperty(value = "待下达数量")
    private BigDecimal unreleasedAmount;

    @ApiModelProperty(value = "已下达数量")
    private BigDecimal releasedQuantity;

    @ApiModelProperty(value = "已关闭需求数量")
    private BigDecimal closedQuantity;

    @ApiModelProperty(value = "订单号 用“/”拼接")
    private String purchaseOrderNum;

    @ApiModelProperty(value = "订单取消数量")
    private BigDecimal purchaseOrderCancelNumber;

    @ApiModelProperty(value = "入库数量")
    private BigDecimal purchaseOrderTransactionQuantity;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "规格/型号")
    private String model;

    @ApiModelProperty(value = "图号")
    private String figureNumber;

    @ApiModelProperty(value = "物料类别-大类")
    private String materialClassification;

    @ApiModelProperty(value = "物料类别-中类")
    private String codingMiddleClass;

    @ApiModelProperty(value = "物料类别-小类")
    private String materielType;

    @ApiModelProperty(value = "是否删除")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getMilepostDesignDetailId() {
        return milepostDesignDetailId;
    }

    public void setMilepostDesignDetailId(Long milepostDesignDetailId) {
        this.milepostDesignDetailId = milepostDesignDetailId;
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public String getMaterialCategory() {
        return materialCategory;
    }

    public void setMaterialCategory(String materialCategory) {
        this.materialCategory = materialCategory == null ? null : materialCategory.trim();
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber == null ? null : serialNumber.trim();
    }

    public String getMaterielDescr() {
        return materielDescr;
    }

    public void setMaterielDescr(String materielDescr) {
        this.materielDescr = materielDescr == null ? null : materielDescr.trim();
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode == null ? null : pamCode.trim();
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode == null ? null : erpCode.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public BigDecimal getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(BigDecimal totalNum) {
        this.totalNum = totalNum;
    }

    public BigDecimal getNumber() {
        return number;
    }

    public void setNumber(BigDecimal number) {
        this.number = number;
    }

    public BigDecimal getRequirementNum() {
        return requirementNum;
    }

    public void setRequirementNum(BigDecimal requirementNum) {
        this.requirementNum = requirementNum;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public BigDecimal getMilepostDesignPlanDetailTotalNum() {
        return milepostDesignPlanDetailTotalNum;
    }

    public void setMilepostDesignPlanDetailTotalNum(BigDecimal milepostDesignPlanDetailTotalNum) {
        this.milepostDesignPlanDetailTotalNum = milepostDesignPlanDetailTotalNum;
    }

    public BigDecimal getNeedTotal() {
        return needTotal;
    }

    public void setNeedTotal(BigDecimal needTotal) {
        this.needTotal = needTotal;
    }

    public BigDecimal getUnreleasedAmount() {
        return unreleasedAmount;
    }

    public void setUnreleasedAmount(BigDecimal unreleasedAmount) {
        this.unreleasedAmount = unreleasedAmount;
    }

    public BigDecimal getReleasedQuantity() {
        return releasedQuantity;
    }

    public void setReleasedQuantity(BigDecimal releasedQuantity) {
        this.releasedQuantity = releasedQuantity;
    }

    public BigDecimal getClosedQuantity() {
        return closedQuantity;
    }

    public void setClosedQuantity(BigDecimal closedQuantity) {
        this.closedQuantity = closedQuantity;
    }

    public String getPurchaseOrderNum() {
        return purchaseOrderNum;
    }

    public void setPurchaseOrderNum(String purchaseOrderNum) {
        this.purchaseOrderNum = purchaseOrderNum == null ? null : purchaseOrderNum.trim();
    }

    public BigDecimal getPurchaseOrderCancelNumber() {
        return purchaseOrderCancelNumber;
    }

    public void setPurchaseOrderCancelNumber(BigDecimal purchaseOrderCancelNumber) {
        this.purchaseOrderCancelNumber = purchaseOrderCancelNumber;
    }

    public BigDecimal getPurchaseOrderTransactionQuantity() {
        return purchaseOrderTransactionQuantity;
    }

    public void setPurchaseOrderTransactionQuantity(BigDecimal purchaseOrderTransactionQuantity) {
        this.purchaseOrderTransactionQuantity = purchaseOrderTransactionQuantity;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getFigureNumber() {
        return figureNumber;
    }

    public void setFigureNumber(String figureNumber) {
        this.figureNumber = figureNumber == null ? null : figureNumber.trim();
    }

    public String getMaterialClassification() {
        return materialClassification;
    }

    public void setMaterialClassification(String materialClassification) {
        this.materialClassification = materialClassification == null ? null : materialClassification.trim();
    }

    public String getCodingMiddleClass() {
        return codingMiddleClass;
    }

    public void setCodingMiddleClass(String codingMiddleClass) {
        this.codingMiddleClass = codingMiddleClass == null ? null : codingMiddleClass.trim();
    }

    public String getMaterielType() {
        return materielType;
    }

    public void setMaterielType(String materielType) {
        this.materielType = materielType == null ? null : materielType.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", milepostDesignDetailId=").append(milepostDesignDetailId);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", materialCategory=").append(materialCategory);
        sb.append(", serialNumber=").append(serialNumber);
        sb.append(", materielDescr=").append(materielDescr);
        sb.append(", pamCode=").append(pamCode);
        sb.append(", erpCode=").append(erpCode);
        sb.append(", unit=").append(unit);
        sb.append(", totalNum=").append(totalNum);
        sb.append(", number=").append(number);
        sb.append(", requirementNum=").append(requirementNum);
        sb.append(", deliveryTime=").append(deliveryTime);
        sb.append(", milepostDesignPlanDetailTotalNum=").append(milepostDesignPlanDetailTotalNum);
        sb.append(", needTotal=").append(needTotal);
        sb.append(", unreleasedAmount=").append(unreleasedAmount);
        sb.append(", releasedQuantity=").append(releasedQuantity);
        sb.append(", closedQuantity=").append(closedQuantity);
        sb.append(", purchaseOrderNum=").append(purchaseOrderNum);
        sb.append(", purchaseOrderCancelNumber=").append(purchaseOrderCancelNumber);
        sb.append(", purchaseOrderTransactionQuantity=").append(purchaseOrderTransactionQuantity);
        sb.append(", name=").append(name);
        sb.append(", brand=").append(brand);
        sb.append(", model=").append(model);
        sb.append(", figureNumber=").append(figureNumber);
        sb.append(", materialClassification=").append(materialClassification);
        sb.append(", codingMiddleClass=").append(codingMiddleClass);
        sb.append(", materielType=").append(materielType);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}