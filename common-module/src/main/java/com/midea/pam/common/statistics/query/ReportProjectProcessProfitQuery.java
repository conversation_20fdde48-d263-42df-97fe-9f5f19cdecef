package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/28
 */
@Getter
@Setter
@ApiModel(value = "ReportProjectProcessProfitQuery", description = "项目进度毛利跟踪表")
public class ReportProjectProcessProfitQuery {

    private Long id;

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    @ApiModelProperty(value = "报表ID")
    private Long reportId;

    @ApiModelProperty(value = "销售部门id列表")
    private List<Long> unitIdList;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "客户名称或CRM编码")
    private String customerName;

    @ApiModelProperty(value = "业务实体id列表")
    private List<Long> ouIdList;

    @ApiModelProperty(value = "业务模式id列表")
    private List<Long> projectTypeList;

    @ApiModelProperty(value = "项目状态")
    private List<Integer> projectStatusList;

    @ApiModelProperty(value = "项目里程碑")
    private List<String> projectMilepostList;

    private Date expireDate;

    private Long personal;

    private Long companyId;

    private String projectMilepostStr;

    private Long createBy;

    @ApiModelProperty(value = "项目计划结束时间开始")
    private Date endTimeStart;

    @ApiModelProperty(value = "项目计划结束时间结束")
    private Date endTimeEnd;

    @ApiModelProperty(value = "项目经理")
    private List<String> projectManageMipList;

    @Override
    public String toString() {
        return "UnconfirmedIncomeQuery{" +
                "id=" + id +
                ", executeId=" + executeId +
                ", unitIdList=" + unitIdList +
                ", customerId=" + customerId +
                ", customerName='" + customerName + '\'' +
                ", ouIdList=" + ouIdList +
                ", endTimeStart=" + endTimeStart +
                ", endTimeEnd=" + endTimeEnd +
                '}';
    }
}
