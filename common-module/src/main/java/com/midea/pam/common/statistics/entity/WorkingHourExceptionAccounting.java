package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class WorkingHourExceptionAccounting extends LongIdEntity implements Serializable {
    private Long id;

    private Long ouId;

    private Long parentUnitId;

    private String ouName;

    private String accountingCode;

    private BigDecimal totalWorkingHour;

    private BigDecimal totalLaborCost;

    private String glPeriod;

    private Date glDate;

    private String applyMonth;

    private String currency;

    private String dailyBatchNum;

    private String dailyBatchName;

    private String debitSubject;

    private String creditSubject;

    private String remark;

    private Integer erpStatus;

    private String erpMessages;

    private String createByName;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private Boolean deletedFlag;

    private String writeOffStatus;

    private String writeOffReason;

    private Long writeOffUser;

    private Date writeOffTime;

    private String writeOffFile;

    private Integer accountingStatus;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public Long getParentUnitId() {
        return parentUnitId;
    }

    public void setParentUnitId(Long parentUnitId) {
        this.parentUnitId = parentUnitId;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public String getAccountingCode() {
        return accountingCode;
    }

    public void setAccountingCode(String accountingCode) {
        this.accountingCode = accountingCode == null ? null : accountingCode.trim();
    }

    public BigDecimal getTotalWorkingHour() {
        return totalWorkingHour;
    }

    public void setTotalWorkingHour(BigDecimal totalWorkingHour) {
        this.totalWorkingHour = totalWorkingHour;
    }

    public BigDecimal getTotalLaborCost() {
        return totalLaborCost;
    }

    public void setTotalLaborCost(BigDecimal totalLaborCost) {
        this.totalLaborCost = totalLaborCost;
    }

    public String getGlPeriod() {
        return glPeriod;
    }

    public void setGlPeriod(String glPeriod) {
        this.glPeriod = glPeriod == null ? null : glPeriod.trim();
    }

    public Date getGlDate() {
        return glDate;
    }

    public void setGlDate(Date glDate) {
        this.glDate = glDate;
    }

    public String getApplyMonth() {
        return applyMonth;
    }

    public void setApplyMonth(String applyMonth) {
        this.applyMonth = applyMonth == null ? null : applyMonth.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public String getDailyBatchNum() {
        return dailyBatchNum;
    }

    public void setDailyBatchNum(String dailyBatchNum) {
        this.dailyBatchNum = dailyBatchNum == null ? null : dailyBatchNum.trim();
    }

    public String getDailyBatchName() {
        return dailyBatchName;
    }

    public void setDailyBatchName(String dailyBatchName) {
        this.dailyBatchName = dailyBatchName == null ? null : dailyBatchName.trim();
    }

    public String getDebitSubject() {
        return debitSubject;
    }

    public void setDebitSubject(String debitSubject) {
        this.debitSubject = debitSubject == null ? null : debitSubject.trim();
    }

    public String getCreditSubject() {
        return creditSubject;
    }

    public void setCreditSubject(String creditSubject) {
        this.creditSubject = creditSubject == null ? null : creditSubject.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Integer getErpStatus() {
        return erpStatus;
    }

    public void setErpStatus(Integer erpStatus) {
        this.erpStatus = erpStatus;
    }

    public String getErpMessages() {
        return erpMessages;
    }

    public void setErpMessages(String erpMessages) {
        this.erpMessages = erpMessages == null ? null : erpMessages.trim();
    }

    public String getCreateByName() {
        return createByName;
    }

    public void setCreateByName(String createByName) {
        this.createByName = createByName == null ? null : createByName.trim();
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public String getWriteOffStatus() {
        return writeOffStatus;
    }

    public void setWriteOffStatus(String writeOffStatus) {
        this.writeOffStatus = writeOffStatus == null ? null : writeOffStatus.trim();
    }

    public String getWriteOffReason() {
        return writeOffReason;
    }

    public void setWriteOffReason(String writeOffReason) {
        this.writeOffReason = writeOffReason == null ? null : writeOffReason.trim();
    }

    public Long getWriteOffUser() {
        return writeOffUser;
    }

    public void setWriteOffUser(Long writeOffUser) {
        this.writeOffUser = writeOffUser;
    }

    public Date getWriteOffTime() {
        return writeOffTime;
    }

    public void setWriteOffTime(Date writeOffTime) {
        this.writeOffTime = writeOffTime;
    }

    public String getWriteOffFile() {
        return writeOffFile;
    }

    public void setWriteOffFile(String writeOffFile) {
        this.writeOffFile = writeOffFile == null ? null : writeOffFile.trim();
    }

    public Integer getAccountingStatus() {
        return accountingStatus;
    }

    public void setAccountingStatus(Integer accountingStatus) {
        this.accountingStatus = accountingStatus;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", ouId=").append(ouId);
        sb.append(", parentUnitId=").append(parentUnitId);
        sb.append(", ouName=").append(ouName);
        sb.append(", accountingCode=").append(accountingCode);
        sb.append(", totalWorkingHour=").append(totalWorkingHour);
        sb.append(", totalLaborCost=").append(totalLaborCost);
        sb.append(", glPeriod=").append(glPeriod);
        sb.append(", glDate=").append(glDate);
        sb.append(", applyMonth=").append(applyMonth);
        sb.append(", currency=").append(currency);
        sb.append(", dailyBatchNum=").append(dailyBatchNum);
        sb.append(", dailyBatchName=").append(dailyBatchName);
        sb.append(", debitSubject=").append(debitSubject);
        sb.append(", creditSubject=").append(creditSubject);
        sb.append(", remark=").append(remark);
        sb.append(", erpStatus=").append(erpStatus);
        sb.append(", erpMessages=").append(erpMessages);
        sb.append(", createByName=").append(createByName);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", writeOffStatus=").append(writeOffStatus);
        sb.append(", writeOffReason=").append(writeOffReason);
        sb.append(", writeOffUser=").append(writeOffUser);
        sb.append(", writeOffTime=").append(writeOffTime);
        sb.append(", writeOffFile=").append(writeOffFile);
        sb.append(", accountingStatus=").append(accountingStatus);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}