package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportProjectMonthlyExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportProjectMonthlyExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andTheYearMonthIsNull() {
            addCriterion("the_year_month is null");
            return (Criteria) this;
        }

        public Criteria andTheYearMonthIsNotNull() {
            addCriterion("the_year_month is not null");
            return (Criteria) this;
        }

        public Criteria andTheYearMonthEqualTo(String value) {
            addCriterion("the_year_month =", value, "theYearMonth");
            return (Criteria) this;
        }

        public Criteria andTheYearMonthNotEqualTo(String value) {
            addCriterion("the_year_month <>", value, "theYearMonth");
            return (Criteria) this;
        }

        public Criteria andTheYearMonthGreaterThan(String value) {
            addCriterion("the_year_month >", value, "theYearMonth");
            return (Criteria) this;
        }

        public Criteria andTheYearMonthGreaterThanOrEqualTo(String value) {
            addCriterion("the_year_month >=", value, "theYearMonth");
            return (Criteria) this;
        }

        public Criteria andTheYearMonthLessThan(String value) {
            addCriterion("the_year_month <", value, "theYearMonth");
            return (Criteria) this;
        }

        public Criteria andTheYearMonthLessThanOrEqualTo(String value) {
            addCriterion("the_year_month <=", value, "theYearMonth");
            return (Criteria) this;
        }

        public Criteria andTheYearMonthLike(String value) {
            addCriterion("the_year_month like", value, "theYearMonth");
            return (Criteria) this;
        }

        public Criteria andTheYearMonthNotLike(String value) {
            addCriterion("the_year_month not like", value, "theYearMonth");
            return (Criteria) this;
        }

        public Criteria andTheYearMonthIn(List<String> values) {
            addCriterion("the_year_month in", values, "theYearMonth");
            return (Criteria) this;
        }

        public Criteria andTheYearMonthNotIn(List<String> values) {
            addCriterion("the_year_month not in", values, "theYearMonth");
            return (Criteria) this;
        }

        public Criteria andTheYearMonthBetween(String value1, String value2) {
            addCriterion("the_year_month between", value1, value2, "theYearMonth");
            return (Criteria) this;
        }

        public Criteria andTheYearMonthNotBetween(String value1, String value2) {
            addCriterion("the_year_month not between", value1, value2, "theYearMonth");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNull() {
            addCriterion("ou_name is null");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNotNull() {
            addCriterion("ou_name is not null");
            return (Criteria) this;
        }

        public Criteria andOuNameEqualTo(String value) {
            addCriterion("ou_name =", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotEqualTo(String value) {
            addCriterion("ou_name <>", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThan(String value) {
            addCriterion("ou_name >", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThanOrEqualTo(String value) {
            addCriterion("ou_name >=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThan(String value) {
            addCriterion("ou_name <", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThanOrEqualTo(String value) {
            addCriterion("ou_name <=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLike(String value) {
            addCriterion("ou_name like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotLike(String value) {
            addCriterion("ou_name not like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameIn(List<String> values) {
            addCriterion("ou_name in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotIn(List<String> values) {
            addCriterion("ou_name not in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameBetween(String value1, String value2) {
            addCriterion("ou_name between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotBetween(String value1, String value2) {
            addCriterion("ou_name not between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNull() {
            addCriterion("contract_code is null");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNotNull() {
            addCriterion("contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andContractCodeEqualTo(String value) {
            addCriterion("contract_code =", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotEqualTo(String value) {
            addCriterion("contract_code <>", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThan(String value) {
            addCriterion("contract_code >", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_code >=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThan(String value) {
            addCriterion("contract_code <", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThanOrEqualTo(String value) {
            addCriterion("contract_code <=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLike(String value) {
            addCriterion("contract_code like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotLike(String value) {
            addCriterion("contract_code not like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeIn(List<String> values) {
            addCriterion("contract_code in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotIn(List<String> values) {
            addCriterion("contract_code not in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeBetween(String value1, String value2) {
            addCriterion("contract_code between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotBetween(String value1, String value2) {
            addCriterion("contract_code not between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNull() {
            addCriterion("project_manager is null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIsNotNull() {
            addCriterion("project_manager is not null");
            return (Criteria) this;
        }

        public Criteria andProjectManagerEqualTo(String value) {
            addCriterion("project_manager =", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotEqualTo(String value) {
            addCriterion("project_manager <>", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThan(String value) {
            addCriterion("project_manager >", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerGreaterThanOrEqualTo(String value) {
            addCriterion("project_manager >=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThan(String value) {
            addCriterion("project_manager <", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLessThanOrEqualTo(String value) {
            addCriterion("project_manager <=", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerLike(String value) {
            addCriterion("project_manager like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotLike(String value) {
            addCriterion("project_manager not like", value, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerIn(List<String> values) {
            addCriterion("project_manager in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotIn(List<String> values) {
            addCriterion("project_manager not in", values, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerBetween(String value1, String value2) {
            addCriterion("project_manager between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andProjectManagerNotBetween(String value1, String value2) {
            addCriterion("project_manager not between", value1, value2, "projectManager");
            return (Criteria) this;
        }

        public Criteria andRegionIsNull() {
            addCriterion("region is null");
            return (Criteria) this;
        }

        public Criteria andRegionIsNotNull() {
            addCriterion("region is not null");
            return (Criteria) this;
        }

        public Criteria andRegionEqualTo(String value) {
            addCriterion("region =", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotEqualTo(String value) {
            addCriterion("region <>", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionGreaterThan(String value) {
            addCriterion("region >", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionGreaterThanOrEqualTo(String value) {
            addCriterion("region >=", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionLessThan(String value) {
            addCriterion("region <", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionLessThanOrEqualTo(String value) {
            addCriterion("region <=", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionLike(String value) {
            addCriterion("region like", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotLike(String value) {
            addCriterion("region not like", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionIn(List<String> values) {
            addCriterion("region in", values, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotIn(List<String> values) {
            addCriterion("region not in", values, "region");
            return (Criteria) this;
        }

        public Criteria andRegionBetween(String value1, String value2) {
            addCriterion("region between", value1, value2, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotBetween(String value1, String value2) {
            addCriterion("region not between", value1, value2, "region");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNull() {
            addCriterion("project_type is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNotNull() {
            addCriterion("project_type is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeEqualTo(String value) {
            addCriterion("project_type =", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotEqualTo(String value) {
            addCriterion("project_type <>", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThan(String value) {
            addCriterion("project_type >", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThanOrEqualTo(String value) {
            addCriterion("project_type >=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThan(String value) {
            addCriterion("project_type <", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThanOrEqualTo(String value) {
            addCriterion("project_type <=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLike(String value) {
            addCriterion("project_type like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotLike(String value) {
            addCriterion("project_type not like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIn(List<String> values) {
            addCriterion("project_type in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotIn(List<String> values) {
            addCriterion("project_type not in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeBetween(String value1, String value2) {
            addCriterion("project_type between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotBetween(String value1, String value2) {
            addCriterion("project_type not between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNull() {
            addCriterion("project_status is null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIsNotNull() {
            addCriterion("project_status is not null");
            return (Criteria) this;
        }

        public Criteria andProjectStatusEqualTo(Integer value) {
            addCriterion("project_status =", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotEqualTo(Integer value) {
            addCriterion("project_status <>", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThan(Integer value) {
            addCriterion("project_status >", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("project_status >=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThan(Integer value) {
            addCriterion("project_status <", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusLessThanOrEqualTo(Integer value) {
            addCriterion("project_status <=", value, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusIn(List<Integer> values) {
            addCriterion("project_status in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotIn(List<Integer> values) {
            addCriterion("project_status not in", values, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusBetween(Integer value1, Integer value2) {
            addCriterion("project_status between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andProjectStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("project_status not between", value1, value2, "projectStatus");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Date value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Date value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Date value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Date> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Date> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andContractAmountIsNull() {
            addCriterion("contract_amount is null");
            return (Criteria) this;
        }

        public Criteria andContractAmountIsNotNull() {
            addCriterion("contract_amount is not null");
            return (Criteria) this;
        }

        public Criteria andContractAmountEqualTo(BigDecimal value) {
            addCriterion("contract_amount =", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountNotEqualTo(BigDecimal value) {
            addCriterion("contract_amount <>", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountGreaterThan(BigDecimal value) {
            addCriterion("contract_amount >", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_amount >=", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountLessThan(BigDecimal value) {
            addCriterion("contract_amount <", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_amount <=", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountIn(List<BigDecimal> values) {
            addCriterion("contract_amount in", values, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountNotIn(List<BigDecimal> values) {
            addCriterion("contract_amount not in", values, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_amount between", value1, value2, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_amount not between", value1, value2, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andInitialProfitIsNull() {
            addCriterion("initial_profit is null");
            return (Criteria) this;
        }

        public Criteria andInitialProfitIsNotNull() {
            addCriterion("initial_profit is not null");
            return (Criteria) this;
        }

        public Criteria andInitialProfitEqualTo(BigDecimal value) {
            addCriterion("initial_profit =", value, "initialProfit");
            return (Criteria) this;
        }

        public Criteria andInitialProfitNotEqualTo(BigDecimal value) {
            addCriterion("initial_profit <>", value, "initialProfit");
            return (Criteria) this;
        }

        public Criteria andInitialProfitGreaterThan(BigDecimal value) {
            addCriterion("initial_profit >", value, "initialProfit");
            return (Criteria) this;
        }

        public Criteria andInitialProfitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("initial_profit >=", value, "initialProfit");
            return (Criteria) this;
        }

        public Criteria andInitialProfitLessThan(BigDecimal value) {
            addCriterion("initial_profit <", value, "initialProfit");
            return (Criteria) this;
        }

        public Criteria andInitialProfitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("initial_profit <=", value, "initialProfit");
            return (Criteria) this;
        }

        public Criteria andInitialProfitIn(List<BigDecimal> values) {
            addCriterion("initial_profit in", values, "initialProfit");
            return (Criteria) this;
        }

        public Criteria andInitialProfitNotIn(List<BigDecimal> values) {
            addCriterion("initial_profit not in", values, "initialProfit");
            return (Criteria) this;
        }

        public Criteria andInitialProfitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("initial_profit between", value1, value2, "initialProfit");
            return (Criteria) this;
        }

        public Criteria andInitialProfitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("initial_profit not between", value1, value2, "initialProfit");
            return (Criteria) this;
        }

        public Criteria andTotalIncomeIsNull() {
            addCriterion("total_income is null");
            return (Criteria) this;
        }

        public Criteria andTotalIncomeIsNotNull() {
            addCriterion("total_income is not null");
            return (Criteria) this;
        }

        public Criteria andTotalIncomeEqualTo(BigDecimal value) {
            addCriterion("total_income =", value, "totalIncome");
            return (Criteria) this;
        }

        public Criteria andTotalIncomeNotEqualTo(BigDecimal value) {
            addCriterion("total_income <>", value, "totalIncome");
            return (Criteria) this;
        }

        public Criteria andTotalIncomeGreaterThan(BigDecimal value) {
            addCriterion("total_income >", value, "totalIncome");
            return (Criteria) this;
        }

        public Criteria andTotalIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_income >=", value, "totalIncome");
            return (Criteria) this;
        }

        public Criteria andTotalIncomeLessThan(BigDecimal value) {
            addCriterion("total_income <", value, "totalIncome");
            return (Criteria) this;
        }

        public Criteria andTotalIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_income <=", value, "totalIncome");
            return (Criteria) this;
        }

        public Criteria andTotalIncomeIn(List<BigDecimal> values) {
            addCriterion("total_income in", values, "totalIncome");
            return (Criteria) this;
        }

        public Criteria andTotalIncomeNotIn(List<BigDecimal> values) {
            addCriterion("total_income not in", values, "totalIncome");
            return (Criteria) this;
        }

        public Criteria andTotalIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_income between", value1, value2, "totalIncome");
            return (Criteria) this;
        }

        public Criteria andTotalIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_income not between", value1, value2, "totalIncome");
            return (Criteria) this;
        }

        public Criteria andInitialIncomeIsNull() {
            addCriterion("initial_income is null");
            return (Criteria) this;
        }

        public Criteria andInitialIncomeIsNotNull() {
            addCriterion("initial_income is not null");
            return (Criteria) this;
        }

        public Criteria andInitialIncomeEqualTo(BigDecimal value) {
            addCriterion("initial_income =", value, "initialIncome");
            return (Criteria) this;
        }

        public Criteria andInitialIncomeNotEqualTo(BigDecimal value) {
            addCriterion("initial_income <>", value, "initialIncome");
            return (Criteria) this;
        }

        public Criteria andInitialIncomeGreaterThan(BigDecimal value) {
            addCriterion("initial_income >", value, "initialIncome");
            return (Criteria) this;
        }

        public Criteria andInitialIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("initial_income >=", value, "initialIncome");
            return (Criteria) this;
        }

        public Criteria andInitialIncomeLessThan(BigDecimal value) {
            addCriterion("initial_income <", value, "initialIncome");
            return (Criteria) this;
        }

        public Criteria andInitialIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("initial_income <=", value, "initialIncome");
            return (Criteria) this;
        }

        public Criteria andInitialIncomeIn(List<BigDecimal> values) {
            addCriterion("initial_income in", values, "initialIncome");
            return (Criteria) this;
        }

        public Criteria andInitialIncomeNotIn(List<BigDecimal> values) {
            addCriterion("initial_income not in", values, "initialIncome");
            return (Criteria) this;
        }

        public Criteria andInitialIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("initial_income between", value1, value2, "initialIncome");
            return (Criteria) this;
        }

        public Criteria andInitialIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("initial_income not between", value1, value2, "initialIncome");
            return (Criteria) this;
        }

        public Criteria andAppendIncomeIsNull() {
            addCriterion("append_income is null");
            return (Criteria) this;
        }

        public Criteria andAppendIncomeIsNotNull() {
            addCriterion("append_income is not null");
            return (Criteria) this;
        }

        public Criteria andAppendIncomeEqualTo(BigDecimal value) {
            addCriterion("append_income =", value, "appendIncome");
            return (Criteria) this;
        }

        public Criteria andAppendIncomeNotEqualTo(BigDecimal value) {
            addCriterion("append_income <>", value, "appendIncome");
            return (Criteria) this;
        }

        public Criteria andAppendIncomeGreaterThan(BigDecimal value) {
            addCriterion("append_income >", value, "appendIncome");
            return (Criteria) this;
        }

        public Criteria andAppendIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("append_income >=", value, "appendIncome");
            return (Criteria) this;
        }

        public Criteria andAppendIncomeLessThan(BigDecimal value) {
            addCriterion("append_income <", value, "appendIncome");
            return (Criteria) this;
        }

        public Criteria andAppendIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("append_income <=", value, "appendIncome");
            return (Criteria) this;
        }

        public Criteria andAppendIncomeIn(List<BigDecimal> values) {
            addCriterion("append_income in", values, "appendIncome");
            return (Criteria) this;
        }

        public Criteria andAppendIncomeNotIn(List<BigDecimal> values) {
            addCriterion("append_income not in", values, "appendIncome");
            return (Criteria) this;
        }

        public Criteria andAppendIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("append_income between", value1, value2, "appendIncome");
            return (Criteria) this;
        }

        public Criteria andAppendIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("append_income not between", value1, value2, "appendIncome");
            return (Criteria) this;
        }

        public Criteria andProjectTotalBudgetIsNull() {
            addCriterion("project_total_budget is null");
            return (Criteria) this;
        }

        public Criteria andProjectTotalBudgetIsNotNull() {
            addCriterion("project_total_budget is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTotalBudgetEqualTo(BigDecimal value) {
            addCriterion("project_total_budget =", value, "projectTotalBudget");
            return (Criteria) this;
        }

        public Criteria andProjectTotalBudgetNotEqualTo(BigDecimal value) {
            addCriterion("project_total_budget <>", value, "projectTotalBudget");
            return (Criteria) this;
        }

        public Criteria andProjectTotalBudgetGreaterThan(BigDecimal value) {
            addCriterion("project_total_budget >", value, "projectTotalBudget");
            return (Criteria) this;
        }

        public Criteria andProjectTotalBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_total_budget >=", value, "projectTotalBudget");
            return (Criteria) this;
        }

        public Criteria andProjectTotalBudgetLessThan(BigDecimal value) {
            addCriterion("project_total_budget <", value, "projectTotalBudget");
            return (Criteria) this;
        }

        public Criteria andProjectTotalBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_total_budget <=", value, "projectTotalBudget");
            return (Criteria) this;
        }

        public Criteria andProjectTotalBudgetIn(List<BigDecimal> values) {
            addCriterion("project_total_budget in", values, "projectTotalBudget");
            return (Criteria) this;
        }

        public Criteria andProjectTotalBudgetNotIn(List<BigDecimal> values) {
            addCriterion("project_total_budget not in", values, "projectTotalBudget");
            return (Criteria) this;
        }

        public Criteria andProjectTotalBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_total_budget between", value1, value2, "projectTotalBudget");
            return (Criteria) this;
        }

        public Criteria andProjectTotalBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_total_budget not between", value1, value2, "projectTotalBudget");
            return (Criteria) this;
        }

        public Criteria andProjectInitialBudgetIsNull() {
            addCriterion("project_initial_budget is null");
            return (Criteria) this;
        }

        public Criteria andProjectInitialBudgetIsNotNull() {
            addCriterion("project_initial_budget is not null");
            return (Criteria) this;
        }

        public Criteria andProjectInitialBudgetEqualTo(BigDecimal value) {
            addCriterion("project_initial_budget =", value, "projectInitialBudget");
            return (Criteria) this;
        }

        public Criteria andProjectInitialBudgetNotEqualTo(BigDecimal value) {
            addCriterion("project_initial_budget <>", value, "projectInitialBudget");
            return (Criteria) this;
        }

        public Criteria andProjectInitialBudgetGreaterThan(BigDecimal value) {
            addCriterion("project_initial_budget >", value, "projectInitialBudget");
            return (Criteria) this;
        }

        public Criteria andProjectInitialBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_initial_budget >=", value, "projectInitialBudget");
            return (Criteria) this;
        }

        public Criteria andProjectInitialBudgetLessThan(BigDecimal value) {
            addCriterion("project_initial_budget <", value, "projectInitialBudget");
            return (Criteria) this;
        }

        public Criteria andProjectInitialBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_initial_budget <=", value, "projectInitialBudget");
            return (Criteria) this;
        }

        public Criteria andProjectInitialBudgetIn(List<BigDecimal> values) {
            addCriterion("project_initial_budget in", values, "projectInitialBudget");
            return (Criteria) this;
        }

        public Criteria andProjectInitialBudgetNotIn(List<BigDecimal> values) {
            addCriterion("project_initial_budget not in", values, "projectInitialBudget");
            return (Criteria) this;
        }

        public Criteria andProjectInitialBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_initial_budget between", value1, value2, "projectInitialBudget");
            return (Criteria) this;
        }

        public Criteria andProjectInitialBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_initial_budget not between", value1, value2, "projectInitialBudget");
            return (Criteria) this;
        }

        public Criteria andProjectChangeBudgetIsNull() {
            addCriterion("project_change_budget is null");
            return (Criteria) this;
        }

        public Criteria andProjectChangeBudgetIsNotNull() {
            addCriterion("project_change_budget is not null");
            return (Criteria) this;
        }

        public Criteria andProjectChangeBudgetEqualTo(BigDecimal value) {
            addCriterion("project_change_budget =", value, "projectChangeBudget");
            return (Criteria) this;
        }

        public Criteria andProjectChangeBudgetNotEqualTo(BigDecimal value) {
            addCriterion("project_change_budget <>", value, "projectChangeBudget");
            return (Criteria) this;
        }

        public Criteria andProjectChangeBudgetGreaterThan(BigDecimal value) {
            addCriterion("project_change_budget >", value, "projectChangeBudget");
            return (Criteria) this;
        }

        public Criteria andProjectChangeBudgetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_change_budget >=", value, "projectChangeBudget");
            return (Criteria) this;
        }

        public Criteria andProjectChangeBudgetLessThan(BigDecimal value) {
            addCriterion("project_change_budget <", value, "projectChangeBudget");
            return (Criteria) this;
        }

        public Criteria andProjectChangeBudgetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_change_budget <=", value, "projectChangeBudget");
            return (Criteria) this;
        }

        public Criteria andProjectChangeBudgetIn(List<BigDecimal> values) {
            addCriterion("project_change_budget in", values, "projectChangeBudget");
            return (Criteria) this;
        }

        public Criteria andProjectChangeBudgetNotIn(List<BigDecimal> values) {
            addCriterion("project_change_budget not in", values, "projectChangeBudget");
            return (Criteria) this;
        }

        public Criteria andProjectChangeBudgetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_change_budget between", value1, value2, "projectChangeBudget");
            return (Criteria) this;
        }

        public Criteria andProjectChangeBudgetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_change_budget not between", value1, value2, "projectChangeBudget");
            return (Criteria) this;
        }

        public Criteria andActualOccurCompletePercentIsNull() {
            addCriterion("actual_occur_complete_percent is null");
            return (Criteria) this;
        }

        public Criteria andActualOccurCompletePercentIsNotNull() {
            addCriterion("actual_occur_complete_percent is not null");
            return (Criteria) this;
        }

        public Criteria andActualOccurCompletePercentEqualTo(String value) {
            addCriterion("actual_occur_complete_percent =", value, "actualOccurCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualOccurCompletePercentNotEqualTo(String value) {
            addCriterion("actual_occur_complete_percent <>", value, "actualOccurCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualOccurCompletePercentGreaterThan(String value) {
            addCriterion("actual_occur_complete_percent >", value, "actualOccurCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualOccurCompletePercentGreaterThanOrEqualTo(String value) {
            addCriterion("actual_occur_complete_percent >=", value, "actualOccurCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualOccurCompletePercentLessThan(String value) {
            addCriterion("actual_occur_complete_percent <", value, "actualOccurCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualOccurCompletePercentLessThanOrEqualTo(String value) {
            addCriterion("actual_occur_complete_percent <=", value, "actualOccurCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualOccurCompletePercentLike(String value) {
            addCriterion("actual_occur_complete_percent like", value, "actualOccurCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualOccurCompletePercentNotLike(String value) {
            addCriterion("actual_occur_complete_percent not like", value, "actualOccurCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualOccurCompletePercentIn(List<String> values) {
            addCriterion("actual_occur_complete_percent in", values, "actualOccurCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualOccurCompletePercentNotIn(List<String> values) {
            addCriterion("actual_occur_complete_percent not in", values, "actualOccurCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualOccurCompletePercentBetween(String value1, String value2) {
            addCriterion("actual_occur_complete_percent between", value1, value2, "actualOccurCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualOccurCompletePercentNotBetween(String value1, String value2) {
            addCriterion("actual_occur_complete_percent not between", value1, value2, "actualOccurCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualCarryCompletePercentIsNull() {
            addCriterion("actual_carry_complete_percent is null");
            return (Criteria) this;
        }

        public Criteria andActualCarryCompletePercentIsNotNull() {
            addCriterion("actual_carry_complete_percent is not null");
            return (Criteria) this;
        }

        public Criteria andActualCarryCompletePercentEqualTo(String value) {
            addCriterion("actual_carry_complete_percent =", value, "actualCarryCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualCarryCompletePercentNotEqualTo(String value) {
            addCriterion("actual_carry_complete_percent <>", value, "actualCarryCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualCarryCompletePercentGreaterThan(String value) {
            addCriterion("actual_carry_complete_percent >", value, "actualCarryCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualCarryCompletePercentGreaterThanOrEqualTo(String value) {
            addCriterion("actual_carry_complete_percent >=", value, "actualCarryCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualCarryCompletePercentLessThan(String value) {
            addCriterion("actual_carry_complete_percent <", value, "actualCarryCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualCarryCompletePercentLessThanOrEqualTo(String value) {
            addCriterion("actual_carry_complete_percent <=", value, "actualCarryCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualCarryCompletePercentLike(String value) {
            addCriterion("actual_carry_complete_percent like", value, "actualCarryCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualCarryCompletePercentNotLike(String value) {
            addCriterion("actual_carry_complete_percent not like", value, "actualCarryCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualCarryCompletePercentIn(List<String> values) {
            addCriterion("actual_carry_complete_percent in", values, "actualCarryCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualCarryCompletePercentNotIn(List<String> values) {
            addCriterion("actual_carry_complete_percent not in", values, "actualCarryCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualCarryCompletePercentBetween(String value1, String value2) {
            addCriterion("actual_carry_complete_percent between", value1, value2, "actualCarryCompletePercent");
            return (Criteria) this;
        }

        public Criteria andActualCarryCompletePercentNotBetween(String value1, String value2) {
            addCriterion("actual_carry_complete_percent not between", value1, value2, "actualCarryCompletePercent");
            return (Criteria) this;
        }

        public Criteria andCollectProjectCostIsNull() {
            addCriterion("collect_project_cost is null");
            return (Criteria) this;
        }

        public Criteria andCollectProjectCostIsNotNull() {
            addCriterion("collect_project_cost is not null");
            return (Criteria) this;
        }

        public Criteria andCollectProjectCostEqualTo(BigDecimal value) {
            addCriterion("collect_project_cost =", value, "collectProjectCost");
            return (Criteria) this;
        }

        public Criteria andCollectProjectCostNotEqualTo(BigDecimal value) {
            addCriterion("collect_project_cost <>", value, "collectProjectCost");
            return (Criteria) this;
        }

        public Criteria andCollectProjectCostGreaterThan(BigDecimal value) {
            addCriterion("collect_project_cost >", value, "collectProjectCost");
            return (Criteria) this;
        }

        public Criteria andCollectProjectCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("collect_project_cost >=", value, "collectProjectCost");
            return (Criteria) this;
        }

        public Criteria andCollectProjectCostLessThan(BigDecimal value) {
            addCriterion("collect_project_cost <", value, "collectProjectCost");
            return (Criteria) this;
        }

        public Criteria andCollectProjectCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("collect_project_cost <=", value, "collectProjectCost");
            return (Criteria) this;
        }

        public Criteria andCollectProjectCostIn(List<BigDecimal> values) {
            addCriterion("collect_project_cost in", values, "collectProjectCost");
            return (Criteria) this;
        }

        public Criteria andCollectProjectCostNotIn(List<BigDecimal> values) {
            addCriterion("collect_project_cost not in", values, "collectProjectCost");
            return (Criteria) this;
        }

        public Criteria andCollectProjectCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("collect_project_cost between", value1, value2, "collectProjectCost");
            return (Criteria) this;
        }

        public Criteria andCollectProjectCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("collect_project_cost not between", value1, value2, "collectProjectCost");
            return (Criteria) this;
        }

        public Criteria andProjectCarryCostIsNull() {
            addCriterion("project_carry_cost is null");
            return (Criteria) this;
        }

        public Criteria andProjectCarryCostIsNotNull() {
            addCriterion("project_carry_cost is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCarryCostEqualTo(BigDecimal value) {
            addCriterion("project_carry_cost =", value, "projectCarryCost");
            return (Criteria) this;
        }

        public Criteria andProjectCarryCostNotEqualTo(BigDecimal value) {
            addCriterion("project_carry_cost <>", value, "projectCarryCost");
            return (Criteria) this;
        }

        public Criteria andProjectCarryCostGreaterThan(BigDecimal value) {
            addCriterion("project_carry_cost >", value, "projectCarryCost");
            return (Criteria) this;
        }

        public Criteria andProjectCarryCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_carry_cost >=", value, "projectCarryCost");
            return (Criteria) this;
        }

        public Criteria andProjectCarryCostLessThan(BigDecimal value) {
            addCriterion("project_carry_cost <", value, "projectCarryCost");
            return (Criteria) this;
        }

        public Criteria andProjectCarryCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_carry_cost <=", value, "projectCarryCost");
            return (Criteria) this;
        }

        public Criteria andProjectCarryCostIn(List<BigDecimal> values) {
            addCriterion("project_carry_cost in", values, "projectCarryCost");
            return (Criteria) this;
        }

        public Criteria andProjectCarryCostNotIn(List<BigDecimal> values) {
            addCriterion("project_carry_cost not in", values, "projectCarryCost");
            return (Criteria) this;
        }

        public Criteria andProjectCarryCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_carry_cost between", value1, value2, "projectCarryCost");
            return (Criteria) this;
        }

        public Criteria andProjectCarryCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_carry_cost not between", value1, value2, "projectCarryCost");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyIncomeIsNull() {
            addCriterion("month_early_income is null");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyIncomeIsNotNull() {
            addCriterion("month_early_income is not null");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyIncomeEqualTo(BigDecimal value) {
            addCriterion("month_early_income =", value, "monthEarlyIncome");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyIncomeNotEqualTo(BigDecimal value) {
            addCriterion("month_early_income <>", value, "monthEarlyIncome");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyIncomeGreaterThan(BigDecimal value) {
            addCriterion("month_early_income >", value, "monthEarlyIncome");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("month_early_income >=", value, "monthEarlyIncome");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyIncomeLessThan(BigDecimal value) {
            addCriterion("month_early_income <", value, "monthEarlyIncome");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("month_early_income <=", value, "monthEarlyIncome");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyIncomeIn(List<BigDecimal> values) {
            addCriterion("month_early_income in", values, "monthEarlyIncome");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyIncomeNotIn(List<BigDecimal> values) {
            addCriterion("month_early_income not in", values, "monthEarlyIncome");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_early_income between", value1, value2, "monthEarlyIncome");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_early_income not between", value1, value2, "monthEarlyIncome");
            return (Criteria) this;
        }

        public Criteria andMonthIncomeIsNull() {
            addCriterion("month_income is null");
            return (Criteria) this;
        }

        public Criteria andMonthIncomeIsNotNull() {
            addCriterion("month_income is not null");
            return (Criteria) this;
        }

        public Criteria andMonthIncomeEqualTo(BigDecimal value) {
            addCriterion("month_income =", value, "monthIncome");
            return (Criteria) this;
        }

        public Criteria andMonthIncomeNotEqualTo(BigDecimal value) {
            addCriterion("month_income <>", value, "monthIncome");
            return (Criteria) this;
        }

        public Criteria andMonthIncomeGreaterThan(BigDecimal value) {
            addCriterion("month_income >", value, "monthIncome");
            return (Criteria) this;
        }

        public Criteria andMonthIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("month_income >=", value, "monthIncome");
            return (Criteria) this;
        }

        public Criteria andMonthIncomeLessThan(BigDecimal value) {
            addCriterion("month_income <", value, "monthIncome");
            return (Criteria) this;
        }

        public Criteria andMonthIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("month_income <=", value, "monthIncome");
            return (Criteria) this;
        }

        public Criteria andMonthIncomeIn(List<BigDecimal> values) {
            addCriterion("month_income in", values, "monthIncome");
            return (Criteria) this;
        }

        public Criteria andMonthIncomeNotIn(List<BigDecimal> values) {
            addCriterion("month_income not in", values, "monthIncome");
            return (Criteria) this;
        }

        public Criteria andMonthIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_income between", value1, value2, "monthIncome");
            return (Criteria) this;
        }

        public Criteria andMonthIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_income not between", value1, value2, "monthIncome");
            return (Criteria) this;
        }

        public Criteria andCollectIncomeIsNull() {
            addCriterion("collect_income is null");
            return (Criteria) this;
        }

        public Criteria andCollectIncomeIsNotNull() {
            addCriterion("collect_income is not null");
            return (Criteria) this;
        }

        public Criteria andCollectIncomeEqualTo(BigDecimal value) {
            addCriterion("collect_income =", value, "collectIncome");
            return (Criteria) this;
        }

        public Criteria andCollectIncomeNotEqualTo(BigDecimal value) {
            addCriterion("collect_income <>", value, "collectIncome");
            return (Criteria) this;
        }

        public Criteria andCollectIncomeGreaterThan(BigDecimal value) {
            addCriterion("collect_income >", value, "collectIncome");
            return (Criteria) this;
        }

        public Criteria andCollectIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("collect_income >=", value, "collectIncome");
            return (Criteria) this;
        }

        public Criteria andCollectIncomeLessThan(BigDecimal value) {
            addCriterion("collect_income <", value, "collectIncome");
            return (Criteria) this;
        }

        public Criteria andCollectIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("collect_income <=", value, "collectIncome");
            return (Criteria) this;
        }

        public Criteria andCollectIncomeIn(List<BigDecimal> values) {
            addCriterion("collect_income in", values, "collectIncome");
            return (Criteria) this;
        }

        public Criteria andCollectIncomeNotIn(List<BigDecimal> values) {
            addCriterion("collect_income not in", values, "collectIncome");
            return (Criteria) this;
        }

        public Criteria andCollectIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("collect_income between", value1, value2, "collectIncome");
            return (Criteria) this;
        }

        public Criteria andCollectIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("collect_income not between", value1, value2, "collectIncome");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyCostIsNull() {
            addCriterion("month_early_cost is null");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyCostIsNotNull() {
            addCriterion("month_early_cost is not null");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyCostEqualTo(BigDecimal value) {
            addCriterion("month_early_cost =", value, "monthEarlyCost");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyCostNotEqualTo(BigDecimal value) {
            addCriterion("month_early_cost <>", value, "monthEarlyCost");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyCostGreaterThan(BigDecimal value) {
            addCriterion("month_early_cost >", value, "monthEarlyCost");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("month_early_cost >=", value, "monthEarlyCost");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyCostLessThan(BigDecimal value) {
            addCriterion("month_early_cost <", value, "monthEarlyCost");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("month_early_cost <=", value, "monthEarlyCost");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyCostIn(List<BigDecimal> values) {
            addCriterion("month_early_cost in", values, "monthEarlyCost");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyCostNotIn(List<BigDecimal> values) {
            addCriterion("month_early_cost not in", values, "monthEarlyCost");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_early_cost between", value1, value2, "monthEarlyCost");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_early_cost not between", value1, value2, "monthEarlyCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostIsNull() {
            addCriterion("month_cost is null");
            return (Criteria) this;
        }

        public Criteria andMonthCostIsNotNull() {
            addCriterion("month_cost is not null");
            return (Criteria) this;
        }

        public Criteria andMonthCostEqualTo(BigDecimal value) {
            addCriterion("month_cost =", value, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostNotEqualTo(BigDecimal value) {
            addCriterion("month_cost <>", value, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostGreaterThan(BigDecimal value) {
            addCriterion("month_cost >", value, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("month_cost >=", value, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostLessThan(BigDecimal value) {
            addCriterion("month_cost <", value, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("month_cost <=", value, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostIn(List<BigDecimal> values) {
            addCriterion("month_cost in", values, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostNotIn(List<BigDecimal> values) {
            addCriterion("month_cost not in", values, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_cost between", value1, value2, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_cost not between", value1, value2, "monthCost");
            return (Criteria) this;
        }

        public Criteria andCollectCostIsNull() {
            addCriterion("collect_cost is null");
            return (Criteria) this;
        }

        public Criteria andCollectCostIsNotNull() {
            addCriterion("collect_cost is not null");
            return (Criteria) this;
        }

        public Criteria andCollectCostEqualTo(BigDecimal value) {
            addCriterion("collect_cost =", value, "collectCost");
            return (Criteria) this;
        }

        public Criteria andCollectCostNotEqualTo(BigDecimal value) {
            addCriterion("collect_cost <>", value, "collectCost");
            return (Criteria) this;
        }

        public Criteria andCollectCostGreaterThan(BigDecimal value) {
            addCriterion("collect_cost >", value, "collectCost");
            return (Criteria) this;
        }

        public Criteria andCollectCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("collect_cost >=", value, "collectCost");
            return (Criteria) this;
        }

        public Criteria andCollectCostLessThan(BigDecimal value) {
            addCriterion("collect_cost <", value, "collectCost");
            return (Criteria) this;
        }

        public Criteria andCollectCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("collect_cost <=", value, "collectCost");
            return (Criteria) this;
        }

        public Criteria andCollectCostIn(List<BigDecimal> values) {
            addCriterion("collect_cost in", values, "collectCost");
            return (Criteria) this;
        }

        public Criteria andCollectCostNotIn(List<BigDecimal> values) {
            addCriterion("collect_cost not in", values, "collectCost");
            return (Criteria) this;
        }

        public Criteria andCollectCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("collect_cost between", value1, value2, "collectCost");
            return (Criteria) this;
        }

        public Criteria andCollectCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("collect_cost not between", value1, value2, "collectCost");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyGrossProfitIsNull() {
            addCriterion("month_early_gross_profit is null");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyGrossProfitIsNotNull() {
            addCriterion("month_early_gross_profit is not null");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyGrossProfitEqualTo(BigDecimal value) {
            addCriterion("month_early_gross_profit =", value, "monthEarlyGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyGrossProfitNotEqualTo(BigDecimal value) {
            addCriterion("month_early_gross_profit <>", value, "monthEarlyGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyGrossProfitGreaterThan(BigDecimal value) {
            addCriterion("month_early_gross_profit >", value, "monthEarlyGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyGrossProfitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("month_early_gross_profit >=", value, "monthEarlyGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyGrossProfitLessThan(BigDecimal value) {
            addCriterion("month_early_gross_profit <", value, "monthEarlyGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyGrossProfitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("month_early_gross_profit <=", value, "monthEarlyGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyGrossProfitIn(List<BigDecimal> values) {
            addCriterion("month_early_gross_profit in", values, "monthEarlyGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyGrossProfitNotIn(List<BigDecimal> values) {
            addCriterion("month_early_gross_profit not in", values, "monthEarlyGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyGrossProfitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_early_gross_profit between", value1, value2, "monthEarlyGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyGrossProfitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_early_gross_profit not between", value1, value2, "monthEarlyGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthGrossProfitIsNull() {
            addCriterion("month_gross_profit is null");
            return (Criteria) this;
        }

        public Criteria andMonthGrossProfitIsNotNull() {
            addCriterion("month_gross_profit is not null");
            return (Criteria) this;
        }

        public Criteria andMonthGrossProfitEqualTo(BigDecimal value) {
            addCriterion("month_gross_profit =", value, "monthGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthGrossProfitNotEqualTo(BigDecimal value) {
            addCriterion("month_gross_profit <>", value, "monthGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthGrossProfitGreaterThan(BigDecimal value) {
            addCriterion("month_gross_profit >", value, "monthGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthGrossProfitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("month_gross_profit >=", value, "monthGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthGrossProfitLessThan(BigDecimal value) {
            addCriterion("month_gross_profit <", value, "monthGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthGrossProfitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("month_gross_profit <=", value, "monthGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthGrossProfitIn(List<BigDecimal> values) {
            addCriterion("month_gross_profit in", values, "monthGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthGrossProfitNotIn(List<BigDecimal> values) {
            addCriterion("month_gross_profit not in", values, "monthGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthGrossProfitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_gross_profit between", value1, value2, "monthGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthGrossProfitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_gross_profit not between", value1, value2, "monthGrossProfit");
            return (Criteria) this;
        }

        public Criteria andCollectGrossProfitIsNull() {
            addCriterion("collect_gross_profit is null");
            return (Criteria) this;
        }

        public Criteria andCollectGrossProfitIsNotNull() {
            addCriterion("collect_gross_profit is not null");
            return (Criteria) this;
        }

        public Criteria andCollectGrossProfitEqualTo(BigDecimal value) {
            addCriterion("collect_gross_profit =", value, "collectGrossProfit");
            return (Criteria) this;
        }

        public Criteria andCollectGrossProfitNotEqualTo(BigDecimal value) {
            addCriterion("collect_gross_profit <>", value, "collectGrossProfit");
            return (Criteria) this;
        }

        public Criteria andCollectGrossProfitGreaterThan(BigDecimal value) {
            addCriterion("collect_gross_profit >", value, "collectGrossProfit");
            return (Criteria) this;
        }

        public Criteria andCollectGrossProfitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("collect_gross_profit >=", value, "collectGrossProfit");
            return (Criteria) this;
        }

        public Criteria andCollectGrossProfitLessThan(BigDecimal value) {
            addCriterion("collect_gross_profit <", value, "collectGrossProfit");
            return (Criteria) this;
        }

        public Criteria andCollectGrossProfitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("collect_gross_profit <=", value, "collectGrossProfit");
            return (Criteria) this;
        }

        public Criteria andCollectGrossProfitIn(List<BigDecimal> values) {
            addCriterion("collect_gross_profit in", values, "collectGrossProfit");
            return (Criteria) this;
        }

        public Criteria andCollectGrossProfitNotIn(List<BigDecimal> values) {
            addCriterion("collect_gross_profit not in", values, "collectGrossProfit");
            return (Criteria) this;
        }

        public Criteria andCollectGrossProfitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("collect_gross_profit between", value1, value2, "collectGrossProfit");
            return (Criteria) this;
        }

        public Criteria andCollectGrossProfitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("collect_gross_profit not between", value1, value2, "collectGrossProfit");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyInvoiceIsNull() {
            addCriterion("month_early_invoice is null");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyInvoiceIsNotNull() {
            addCriterion("month_early_invoice is not null");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyInvoiceEqualTo(BigDecimal value) {
            addCriterion("month_early_invoice =", value, "monthEarlyInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyInvoiceNotEqualTo(BigDecimal value) {
            addCriterion("month_early_invoice <>", value, "monthEarlyInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyInvoiceGreaterThan(BigDecimal value) {
            addCriterion("month_early_invoice >", value, "monthEarlyInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyInvoiceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("month_early_invoice >=", value, "monthEarlyInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyInvoiceLessThan(BigDecimal value) {
            addCriterion("month_early_invoice <", value, "monthEarlyInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyInvoiceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("month_early_invoice <=", value, "monthEarlyInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyInvoiceIn(List<BigDecimal> values) {
            addCriterion("month_early_invoice in", values, "monthEarlyInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyInvoiceNotIn(List<BigDecimal> values) {
            addCriterion("month_early_invoice not in", values, "monthEarlyInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyInvoiceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_early_invoice between", value1, value2, "monthEarlyInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyInvoiceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_early_invoice not between", value1, value2, "monthEarlyInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthInvoiceIsNull() {
            addCriterion("month_invoice is null");
            return (Criteria) this;
        }

        public Criteria andMonthInvoiceIsNotNull() {
            addCriterion("month_invoice is not null");
            return (Criteria) this;
        }

        public Criteria andMonthInvoiceEqualTo(BigDecimal value) {
            addCriterion("month_invoice =", value, "monthInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthInvoiceNotEqualTo(BigDecimal value) {
            addCriterion("month_invoice <>", value, "monthInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthInvoiceGreaterThan(BigDecimal value) {
            addCriterion("month_invoice >", value, "monthInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthInvoiceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("month_invoice >=", value, "monthInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthInvoiceLessThan(BigDecimal value) {
            addCriterion("month_invoice <", value, "monthInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthInvoiceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("month_invoice <=", value, "monthInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthInvoiceIn(List<BigDecimal> values) {
            addCriterion("month_invoice in", values, "monthInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthInvoiceNotIn(List<BigDecimal> values) {
            addCriterion("month_invoice not in", values, "monthInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthInvoiceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_invoice between", value1, value2, "monthInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthInvoiceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_invoice not between", value1, value2, "monthInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectInvoiceIsNull() {
            addCriterion("collect_invoice is null");
            return (Criteria) this;
        }

        public Criteria andCollectInvoiceIsNotNull() {
            addCriterion("collect_invoice is not null");
            return (Criteria) this;
        }

        public Criteria andCollectInvoiceEqualTo(BigDecimal value) {
            addCriterion("collect_invoice =", value, "collectInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectInvoiceNotEqualTo(BigDecimal value) {
            addCriterion("collect_invoice <>", value, "collectInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectInvoiceGreaterThan(BigDecimal value) {
            addCriterion("collect_invoice >", value, "collectInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectInvoiceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("collect_invoice >=", value, "collectInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectInvoiceLessThan(BigDecimal value) {
            addCriterion("collect_invoice <", value, "collectInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectInvoiceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("collect_invoice <=", value, "collectInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectInvoiceIn(List<BigDecimal> values) {
            addCriterion("collect_invoice in", values, "collectInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectInvoiceNotIn(List<BigDecimal> values) {
            addCriterion("collect_invoice not in", values, "collectInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectInvoiceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("collect_invoice between", value1, value2, "collectInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectInvoiceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("collect_invoice not between", value1, value2, "collectInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectInvoicePercentIsNull() {
            addCriterion("collect_invoice_percent is null");
            return (Criteria) this;
        }

        public Criteria andCollectInvoicePercentIsNotNull() {
            addCriterion("collect_invoice_percent is not null");
            return (Criteria) this;
        }

        public Criteria andCollectInvoicePercentEqualTo(String value) {
            addCriterion("collect_invoice_percent =", value, "collectInvoicePercent");
            return (Criteria) this;
        }

        public Criteria andCollectInvoicePercentNotEqualTo(String value) {
            addCriterion("collect_invoice_percent <>", value, "collectInvoicePercent");
            return (Criteria) this;
        }

        public Criteria andCollectInvoicePercentGreaterThan(String value) {
            addCriterion("collect_invoice_percent >", value, "collectInvoicePercent");
            return (Criteria) this;
        }

        public Criteria andCollectInvoicePercentGreaterThanOrEqualTo(String value) {
            addCriterion("collect_invoice_percent >=", value, "collectInvoicePercent");
            return (Criteria) this;
        }

        public Criteria andCollectInvoicePercentLessThan(String value) {
            addCriterion("collect_invoice_percent <", value, "collectInvoicePercent");
            return (Criteria) this;
        }

        public Criteria andCollectInvoicePercentLessThanOrEqualTo(String value) {
            addCriterion("collect_invoice_percent <=", value, "collectInvoicePercent");
            return (Criteria) this;
        }

        public Criteria andCollectInvoicePercentLike(String value) {
            addCriterion("collect_invoice_percent like", value, "collectInvoicePercent");
            return (Criteria) this;
        }

        public Criteria andCollectInvoicePercentNotLike(String value) {
            addCriterion("collect_invoice_percent not like", value, "collectInvoicePercent");
            return (Criteria) this;
        }

        public Criteria andCollectInvoicePercentIn(List<String> values) {
            addCriterion("collect_invoice_percent in", values, "collectInvoicePercent");
            return (Criteria) this;
        }

        public Criteria andCollectInvoicePercentNotIn(List<String> values) {
            addCriterion("collect_invoice_percent not in", values, "collectInvoicePercent");
            return (Criteria) this;
        }

        public Criteria andCollectInvoicePercentBetween(String value1, String value2) {
            addCriterion("collect_invoice_percent between", value1, value2, "collectInvoicePercent");
            return (Criteria) this;
        }

        public Criteria andCollectInvoicePercentNotBetween(String value1, String value2) {
            addCriterion("collect_invoice_percent not between", value1, value2, "collectInvoicePercent");
            return (Criteria) this;
        }

        public Criteria andNotTaxNotInvoiceReceivableIsNull() {
            addCriterion("not_tax_not_invoice_receivable is null");
            return (Criteria) this;
        }

        public Criteria andNotTaxNotInvoiceReceivableIsNotNull() {
            addCriterion("not_tax_not_invoice_receivable is not null");
            return (Criteria) this;
        }

        public Criteria andNotTaxNotInvoiceReceivableEqualTo(BigDecimal value) {
            addCriterion("not_tax_not_invoice_receivable =", value, "notTaxNotInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxNotInvoiceReceivableNotEqualTo(BigDecimal value) {
            addCriterion("not_tax_not_invoice_receivable <>", value, "notTaxNotInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxNotInvoiceReceivableGreaterThan(BigDecimal value) {
            addCriterion("not_tax_not_invoice_receivable >", value, "notTaxNotInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxNotInvoiceReceivableGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("not_tax_not_invoice_receivable >=", value, "notTaxNotInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxNotInvoiceReceivableLessThan(BigDecimal value) {
            addCriterion("not_tax_not_invoice_receivable <", value, "notTaxNotInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxNotInvoiceReceivableLessThanOrEqualTo(BigDecimal value) {
            addCriterion("not_tax_not_invoice_receivable <=", value, "notTaxNotInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxNotInvoiceReceivableIn(List<BigDecimal> values) {
            addCriterion("not_tax_not_invoice_receivable in", values, "notTaxNotInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxNotInvoiceReceivableNotIn(List<BigDecimal> values) {
            addCriterion("not_tax_not_invoice_receivable not in", values, "notTaxNotInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxNotInvoiceReceivableBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("not_tax_not_invoice_receivable between", value1, value2, "notTaxNotInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxNotInvoiceReceivableNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("not_tax_not_invoice_receivable not between", value1, value2, "notTaxNotInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxInvoiceReceivableIsNull() {
            addCriterion("not_tax_invoice_receivable is null");
            return (Criteria) this;
        }

        public Criteria andNotTaxInvoiceReceivableIsNotNull() {
            addCriterion("not_tax_invoice_receivable is not null");
            return (Criteria) this;
        }

        public Criteria andNotTaxInvoiceReceivableEqualTo(BigDecimal value) {
            addCriterion("not_tax_invoice_receivable =", value, "notTaxInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxInvoiceReceivableNotEqualTo(BigDecimal value) {
            addCriterion("not_tax_invoice_receivable <>", value, "notTaxInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxInvoiceReceivableGreaterThan(BigDecimal value) {
            addCriterion("not_tax_invoice_receivable >", value, "notTaxInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxInvoiceReceivableGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("not_tax_invoice_receivable >=", value, "notTaxInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxInvoiceReceivableLessThan(BigDecimal value) {
            addCriterion("not_tax_invoice_receivable <", value, "notTaxInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxInvoiceReceivableLessThanOrEqualTo(BigDecimal value) {
            addCriterion("not_tax_invoice_receivable <=", value, "notTaxInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxInvoiceReceivableIn(List<BigDecimal> values) {
            addCriterion("not_tax_invoice_receivable in", values, "notTaxInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxInvoiceReceivableNotIn(List<BigDecimal> values) {
            addCriterion("not_tax_invoice_receivable not in", values, "notTaxInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxInvoiceReceivableBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("not_tax_invoice_receivable between", value1, value2, "notTaxInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andNotTaxInvoiceReceivableNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("not_tax_invoice_receivable not between", value1, value2, "notTaxInvoiceReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyNotInvoiceIsNull() {
            addCriterion("month_early_not_invoice is null");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyNotInvoiceIsNotNull() {
            addCriterion("month_early_not_invoice is not null");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyNotInvoiceEqualTo(BigDecimal value) {
            addCriterion("month_early_not_invoice =", value, "monthEarlyNotInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyNotInvoiceNotEqualTo(BigDecimal value) {
            addCriterion("month_early_not_invoice <>", value, "monthEarlyNotInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyNotInvoiceGreaterThan(BigDecimal value) {
            addCriterion("month_early_not_invoice >", value, "monthEarlyNotInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyNotInvoiceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("month_early_not_invoice >=", value, "monthEarlyNotInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyNotInvoiceLessThan(BigDecimal value) {
            addCriterion("month_early_not_invoice <", value, "monthEarlyNotInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyNotInvoiceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("month_early_not_invoice <=", value, "monthEarlyNotInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyNotInvoiceIn(List<BigDecimal> values) {
            addCriterion("month_early_not_invoice in", values, "monthEarlyNotInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyNotInvoiceNotIn(List<BigDecimal> values) {
            addCriterion("month_early_not_invoice not in", values, "monthEarlyNotInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyNotInvoiceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_early_not_invoice between", value1, value2, "monthEarlyNotInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyNotInvoiceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_early_not_invoice not between", value1, value2, "monthEarlyNotInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectNotInvoiceIsNull() {
            addCriterion("collect_not_invoice is null");
            return (Criteria) this;
        }

        public Criteria andCollectNotInvoiceIsNotNull() {
            addCriterion("collect_not_invoice is not null");
            return (Criteria) this;
        }

        public Criteria andCollectNotInvoiceEqualTo(BigDecimal value) {
            addCriterion("collect_not_invoice =", value, "collectNotInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectNotInvoiceNotEqualTo(BigDecimal value) {
            addCriterion("collect_not_invoice <>", value, "collectNotInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectNotInvoiceGreaterThan(BigDecimal value) {
            addCriterion("collect_not_invoice >", value, "collectNotInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectNotInvoiceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("collect_not_invoice >=", value, "collectNotInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectNotInvoiceLessThan(BigDecimal value) {
            addCriterion("collect_not_invoice <", value, "collectNotInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectNotInvoiceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("collect_not_invoice <=", value, "collectNotInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectNotInvoiceIn(List<BigDecimal> values) {
            addCriterion("collect_not_invoice in", values, "collectNotInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectNotInvoiceNotIn(List<BigDecimal> values) {
            addCriterion("collect_not_invoice not in", values, "collectNotInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectNotInvoiceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("collect_not_invoice between", value1, value2, "collectNotInvoice");
            return (Criteria) this;
        }

        public Criteria andCollectNotInvoiceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("collect_not_invoice not between", value1, value2, "collectNotInvoice");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceiptIsNull() {
            addCriterion("month_early_receipt is null");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceiptIsNotNull() {
            addCriterion("month_early_receipt is not null");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceiptEqualTo(BigDecimal value) {
            addCriterion("month_early_receipt =", value, "monthEarlyReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceiptNotEqualTo(BigDecimal value) {
            addCriterion("month_early_receipt <>", value, "monthEarlyReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceiptGreaterThan(BigDecimal value) {
            addCriterion("month_early_receipt >", value, "monthEarlyReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceiptGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("month_early_receipt >=", value, "monthEarlyReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceiptLessThan(BigDecimal value) {
            addCriterion("month_early_receipt <", value, "monthEarlyReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceiptLessThanOrEqualTo(BigDecimal value) {
            addCriterion("month_early_receipt <=", value, "monthEarlyReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceiptIn(List<BigDecimal> values) {
            addCriterion("month_early_receipt in", values, "monthEarlyReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceiptNotIn(List<BigDecimal> values) {
            addCriterion("month_early_receipt not in", values, "monthEarlyReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceiptBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_early_receipt between", value1, value2, "monthEarlyReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceiptNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_early_receipt not between", value1, value2, "monthEarlyReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthReceiptIsNull() {
            addCriterion("month_receipt is null");
            return (Criteria) this;
        }

        public Criteria andMonthReceiptIsNotNull() {
            addCriterion("month_receipt is not null");
            return (Criteria) this;
        }

        public Criteria andMonthReceiptEqualTo(BigDecimal value) {
            addCriterion("month_receipt =", value, "monthReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthReceiptNotEqualTo(BigDecimal value) {
            addCriterion("month_receipt <>", value, "monthReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthReceiptGreaterThan(BigDecimal value) {
            addCriterion("month_receipt >", value, "monthReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthReceiptGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("month_receipt >=", value, "monthReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthReceiptLessThan(BigDecimal value) {
            addCriterion("month_receipt <", value, "monthReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthReceiptLessThanOrEqualTo(BigDecimal value) {
            addCriterion("month_receipt <=", value, "monthReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthReceiptIn(List<BigDecimal> values) {
            addCriterion("month_receipt in", values, "monthReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthReceiptNotIn(List<BigDecimal> values) {
            addCriterion("month_receipt not in", values, "monthReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthReceiptBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_receipt between", value1, value2, "monthReceipt");
            return (Criteria) this;
        }

        public Criteria andMonthReceiptNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_receipt not between", value1, value2, "monthReceipt");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptIsNull() {
            addCriterion("collect_receipt is null");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptIsNotNull() {
            addCriterion("collect_receipt is not null");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptEqualTo(BigDecimal value) {
            addCriterion("collect_receipt =", value, "collectReceipt");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptNotEqualTo(BigDecimal value) {
            addCriterion("collect_receipt <>", value, "collectReceipt");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptGreaterThan(BigDecimal value) {
            addCriterion("collect_receipt >", value, "collectReceipt");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("collect_receipt >=", value, "collectReceipt");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptLessThan(BigDecimal value) {
            addCriterion("collect_receipt <", value, "collectReceipt");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptLessThanOrEqualTo(BigDecimal value) {
            addCriterion("collect_receipt <=", value, "collectReceipt");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptIn(List<BigDecimal> values) {
            addCriterion("collect_receipt in", values, "collectReceipt");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptNotIn(List<BigDecimal> values) {
            addCriterion("collect_receipt not in", values, "collectReceipt");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("collect_receipt between", value1, value2, "collectReceipt");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("collect_receipt not between", value1, value2, "collectReceipt");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptPercentIsNull() {
            addCriterion("collect_receipt_percent is null");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptPercentIsNotNull() {
            addCriterion("collect_receipt_percent is not null");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptPercentEqualTo(String value) {
            addCriterion("collect_receipt_percent =", value, "collectReceiptPercent");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptPercentNotEqualTo(String value) {
            addCriterion("collect_receipt_percent <>", value, "collectReceiptPercent");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptPercentGreaterThan(String value) {
            addCriterion("collect_receipt_percent >", value, "collectReceiptPercent");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptPercentGreaterThanOrEqualTo(String value) {
            addCriterion("collect_receipt_percent >=", value, "collectReceiptPercent");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptPercentLessThan(String value) {
            addCriterion("collect_receipt_percent <", value, "collectReceiptPercent");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptPercentLessThanOrEqualTo(String value) {
            addCriterion("collect_receipt_percent <=", value, "collectReceiptPercent");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptPercentLike(String value) {
            addCriterion("collect_receipt_percent like", value, "collectReceiptPercent");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptPercentNotLike(String value) {
            addCriterion("collect_receipt_percent not like", value, "collectReceiptPercent");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptPercentIn(List<String> values) {
            addCriterion("collect_receipt_percent in", values, "collectReceiptPercent");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptPercentNotIn(List<String> values) {
            addCriterion("collect_receipt_percent not in", values, "collectReceiptPercent");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptPercentBetween(String value1, String value2) {
            addCriterion("collect_receipt_percent between", value1, value2, "collectReceiptPercent");
            return (Criteria) this;
        }

        public Criteria andCollectReceiptPercentNotBetween(String value1, String value2) {
            addCriterion("collect_receipt_percent not between", value1, value2, "collectReceiptPercent");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceivableIsNull() {
            addCriterion("month_early_receivable is null");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceivableIsNotNull() {
            addCriterion("month_early_receivable is not null");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceivableEqualTo(BigDecimal value) {
            addCriterion("month_early_receivable =", value, "monthEarlyReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceivableNotEqualTo(BigDecimal value) {
            addCriterion("month_early_receivable <>", value, "monthEarlyReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceivableGreaterThan(BigDecimal value) {
            addCriterion("month_early_receivable >", value, "monthEarlyReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceivableGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("month_early_receivable >=", value, "monthEarlyReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceivableLessThan(BigDecimal value) {
            addCriterion("month_early_receivable <", value, "monthEarlyReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceivableLessThanOrEqualTo(BigDecimal value) {
            addCriterion("month_early_receivable <=", value, "monthEarlyReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceivableIn(List<BigDecimal> values) {
            addCriterion("month_early_receivable in", values, "monthEarlyReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceivableNotIn(List<BigDecimal> values) {
            addCriterion("month_early_receivable not in", values, "monthEarlyReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceivableBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_early_receivable between", value1, value2, "monthEarlyReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyReceivableNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_early_receivable not between", value1, value2, "monthEarlyReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEndReceivableIsNull() {
            addCriterion("month_end_receivable is null");
            return (Criteria) this;
        }

        public Criteria andMonthEndReceivableIsNotNull() {
            addCriterion("month_end_receivable is not null");
            return (Criteria) this;
        }

        public Criteria andMonthEndReceivableEqualTo(BigDecimal value) {
            addCriterion("month_end_receivable =", value, "monthEndReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEndReceivableNotEqualTo(BigDecimal value) {
            addCriterion("month_end_receivable <>", value, "monthEndReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEndReceivableGreaterThan(BigDecimal value) {
            addCriterion("month_end_receivable >", value, "monthEndReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEndReceivableGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("month_end_receivable >=", value, "monthEndReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEndReceivableLessThan(BigDecimal value) {
            addCriterion("month_end_receivable <", value, "monthEndReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEndReceivableLessThanOrEqualTo(BigDecimal value) {
            addCriterion("month_end_receivable <=", value, "monthEndReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEndReceivableIn(List<BigDecimal> values) {
            addCriterion("month_end_receivable in", values, "monthEndReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEndReceivableNotIn(List<BigDecimal> values) {
            addCriterion("month_end_receivable not in", values, "monthEndReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEndReceivableBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_end_receivable between", value1, value2, "monthEndReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEndReceivableNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_end_receivable not between", value1, value2, "monthEndReceivable");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyPaymentIsNull() {
            addCriterion("month_early_payment is null");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyPaymentIsNotNull() {
            addCriterion("month_early_payment is not null");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyPaymentEqualTo(BigDecimal value) {
            addCriterion("month_early_payment =", value, "monthEarlyPayment");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyPaymentNotEqualTo(BigDecimal value) {
            addCriterion("month_early_payment <>", value, "monthEarlyPayment");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyPaymentGreaterThan(BigDecimal value) {
            addCriterion("month_early_payment >", value, "monthEarlyPayment");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyPaymentGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("month_early_payment >=", value, "monthEarlyPayment");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyPaymentLessThan(BigDecimal value) {
            addCriterion("month_early_payment <", value, "monthEarlyPayment");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyPaymentLessThanOrEqualTo(BigDecimal value) {
            addCriterion("month_early_payment <=", value, "monthEarlyPayment");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyPaymentIn(List<BigDecimal> values) {
            addCriterion("month_early_payment in", values, "monthEarlyPayment");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyPaymentNotIn(List<BigDecimal> values) {
            addCriterion("month_early_payment not in", values, "monthEarlyPayment");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyPaymentBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_early_payment between", value1, value2, "monthEarlyPayment");
            return (Criteria) this;
        }

        public Criteria andMonthEarlyPaymentNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_early_payment not between", value1, value2, "monthEarlyPayment");
            return (Criteria) this;
        }

        public Criteria andMonthPaymentIsNull() {
            addCriterion("month_payment is null");
            return (Criteria) this;
        }

        public Criteria andMonthPaymentIsNotNull() {
            addCriterion("month_payment is not null");
            return (Criteria) this;
        }

        public Criteria andMonthPaymentEqualTo(BigDecimal value) {
            addCriterion("month_payment =", value, "monthPayment");
            return (Criteria) this;
        }

        public Criteria andMonthPaymentNotEqualTo(BigDecimal value) {
            addCriterion("month_payment <>", value, "monthPayment");
            return (Criteria) this;
        }

        public Criteria andMonthPaymentGreaterThan(BigDecimal value) {
            addCriterion("month_payment >", value, "monthPayment");
            return (Criteria) this;
        }

        public Criteria andMonthPaymentGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("month_payment >=", value, "monthPayment");
            return (Criteria) this;
        }

        public Criteria andMonthPaymentLessThan(BigDecimal value) {
            addCriterion("month_payment <", value, "monthPayment");
            return (Criteria) this;
        }

        public Criteria andMonthPaymentLessThanOrEqualTo(BigDecimal value) {
            addCriterion("month_payment <=", value, "monthPayment");
            return (Criteria) this;
        }

        public Criteria andMonthPaymentIn(List<BigDecimal> values) {
            addCriterion("month_payment in", values, "monthPayment");
            return (Criteria) this;
        }

        public Criteria andMonthPaymentNotIn(List<BigDecimal> values) {
            addCriterion("month_payment not in", values, "monthPayment");
            return (Criteria) this;
        }

        public Criteria andMonthPaymentBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_payment between", value1, value2, "monthPayment");
            return (Criteria) this;
        }

        public Criteria andMonthPaymentNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_payment not between", value1, value2, "monthPayment");
            return (Criteria) this;
        }

        public Criteria andCollectPaymentIsNull() {
            addCriterion("collect_payment is null");
            return (Criteria) this;
        }

        public Criteria andCollectPaymentIsNotNull() {
            addCriterion("collect_payment is not null");
            return (Criteria) this;
        }

        public Criteria andCollectPaymentEqualTo(BigDecimal value) {
            addCriterion("collect_payment =", value, "collectPayment");
            return (Criteria) this;
        }

        public Criteria andCollectPaymentNotEqualTo(BigDecimal value) {
            addCriterion("collect_payment <>", value, "collectPayment");
            return (Criteria) this;
        }

        public Criteria andCollectPaymentGreaterThan(BigDecimal value) {
            addCriterion("collect_payment >", value, "collectPayment");
            return (Criteria) this;
        }

        public Criteria andCollectPaymentGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("collect_payment >=", value, "collectPayment");
            return (Criteria) this;
        }

        public Criteria andCollectPaymentLessThan(BigDecimal value) {
            addCriterion("collect_payment <", value, "collectPayment");
            return (Criteria) this;
        }

        public Criteria andCollectPaymentLessThanOrEqualTo(BigDecimal value) {
            addCriterion("collect_payment <=", value, "collectPayment");
            return (Criteria) this;
        }

        public Criteria andCollectPaymentIn(List<BigDecimal> values) {
            addCriterion("collect_payment in", values, "collectPayment");
            return (Criteria) this;
        }

        public Criteria andCollectPaymentNotIn(List<BigDecimal> values) {
            addCriterion("collect_payment not in", values, "collectPayment");
            return (Criteria) this;
        }

        public Criteria andCollectPaymentBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("collect_payment between", value1, value2, "collectPayment");
            return (Criteria) this;
        }

        public Criteria andCollectPaymentNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("collect_payment not between", value1, value2, "collectPayment");
            return (Criteria) this;
        }

        public Criteria andLabourContractAmountIsNull() {
            addCriterion("labour_contract_amount is null");
            return (Criteria) this;
        }

        public Criteria andLabourContractAmountIsNotNull() {
            addCriterion("labour_contract_amount is not null");
            return (Criteria) this;
        }

        public Criteria andLabourContractAmountEqualTo(BigDecimal value) {
            addCriterion("labour_contract_amount =", value, "labourContractAmount");
            return (Criteria) this;
        }

        public Criteria andLabourContractAmountNotEqualTo(BigDecimal value) {
            addCriterion("labour_contract_amount <>", value, "labourContractAmount");
            return (Criteria) this;
        }

        public Criteria andLabourContractAmountGreaterThan(BigDecimal value) {
            addCriterion("labour_contract_amount >", value, "labourContractAmount");
            return (Criteria) this;
        }

        public Criteria andLabourContractAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("labour_contract_amount >=", value, "labourContractAmount");
            return (Criteria) this;
        }

        public Criteria andLabourContractAmountLessThan(BigDecimal value) {
            addCriterion("labour_contract_amount <", value, "labourContractAmount");
            return (Criteria) this;
        }

        public Criteria andLabourContractAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("labour_contract_amount <=", value, "labourContractAmount");
            return (Criteria) this;
        }

        public Criteria andLabourContractAmountIn(List<BigDecimal> values) {
            addCriterion("labour_contract_amount in", values, "labourContractAmount");
            return (Criteria) this;
        }

        public Criteria andLabourContractAmountNotIn(List<BigDecimal> values) {
            addCriterion("labour_contract_amount not in", values, "labourContractAmount");
            return (Criteria) this;
        }

        public Criteria andLabourContractAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("labour_contract_amount between", value1, value2, "labourContractAmount");
            return (Criteria) this;
        }

        public Criteria andLabourContractAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("labour_contract_amount not between", value1, value2, "labourContractAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialContractAmountIsNull() {
            addCriterion("material_contract_amount is null");
            return (Criteria) this;
        }

        public Criteria andMaterialContractAmountIsNotNull() {
            addCriterion("material_contract_amount is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialContractAmountEqualTo(BigDecimal value) {
            addCriterion("material_contract_amount =", value, "materialContractAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialContractAmountNotEqualTo(BigDecimal value) {
            addCriterion("material_contract_amount <>", value, "materialContractAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialContractAmountGreaterThan(BigDecimal value) {
            addCriterion("material_contract_amount >", value, "materialContractAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialContractAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("material_contract_amount >=", value, "materialContractAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialContractAmountLessThan(BigDecimal value) {
            addCriterion("material_contract_amount <", value, "materialContractAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialContractAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("material_contract_amount <=", value, "materialContractAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialContractAmountIn(List<BigDecimal> values) {
            addCriterion("material_contract_amount in", values, "materialContractAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialContractAmountNotIn(List<BigDecimal> values) {
            addCriterion("material_contract_amount not in", values, "materialContractAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialContractAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_contract_amount between", value1, value2, "materialContractAmount");
            return (Criteria) this;
        }

        public Criteria andMaterialContractAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("material_contract_amount not between", value1, value2, "materialContractAmount");
            return (Criteria) this;
        }

        public Criteria andInnerHrCostIsNull() {
            addCriterion("inner_hr_cost is null");
            return (Criteria) this;
        }

        public Criteria andInnerHrCostIsNotNull() {
            addCriterion("inner_hr_cost is not null");
            return (Criteria) this;
        }

        public Criteria andInnerHrCostEqualTo(BigDecimal value) {
            addCriterion("inner_hr_cost =", value, "innerHrCost");
            return (Criteria) this;
        }

        public Criteria andInnerHrCostNotEqualTo(BigDecimal value) {
            addCriterion("inner_hr_cost <>", value, "innerHrCost");
            return (Criteria) this;
        }

        public Criteria andInnerHrCostGreaterThan(BigDecimal value) {
            addCriterion("inner_hr_cost >", value, "innerHrCost");
            return (Criteria) this;
        }

        public Criteria andInnerHrCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_hr_cost >=", value, "innerHrCost");
            return (Criteria) this;
        }

        public Criteria andInnerHrCostLessThan(BigDecimal value) {
            addCriterion("inner_hr_cost <", value, "innerHrCost");
            return (Criteria) this;
        }

        public Criteria andInnerHrCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("inner_hr_cost <=", value, "innerHrCost");
            return (Criteria) this;
        }

        public Criteria andInnerHrCostIn(List<BigDecimal> values) {
            addCriterion("inner_hr_cost in", values, "innerHrCost");
            return (Criteria) this;
        }

        public Criteria andInnerHrCostNotIn(List<BigDecimal> values) {
            addCriterion("inner_hr_cost not in", values, "innerHrCost");
            return (Criteria) this;
        }

        public Criteria andInnerHrCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_hr_cost between", value1, value2, "innerHrCost");
            return (Criteria) this;
        }

        public Criteria andInnerHrCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inner_hr_cost not between", value1, value2, "innerHrCost");
            return (Criteria) this;
        }

        public Criteria andProjectCostIsNull() {
            addCriterion("project_cost is null");
            return (Criteria) this;
        }

        public Criteria andProjectCostIsNotNull() {
            addCriterion("project_cost is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCostEqualTo(BigDecimal value) {
            addCriterion("project_cost =", value, "projectCost");
            return (Criteria) this;
        }

        public Criteria andProjectCostNotEqualTo(BigDecimal value) {
            addCriterion("project_cost <>", value, "projectCost");
            return (Criteria) this;
        }

        public Criteria andProjectCostGreaterThan(BigDecimal value) {
            addCriterion("project_cost >", value, "projectCost");
            return (Criteria) this;
        }

        public Criteria andProjectCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_cost >=", value, "projectCost");
            return (Criteria) this;
        }

        public Criteria andProjectCostLessThan(BigDecimal value) {
            addCriterion("project_cost <", value, "projectCost");
            return (Criteria) this;
        }

        public Criteria andProjectCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_cost <=", value, "projectCost");
            return (Criteria) this;
        }

        public Criteria andProjectCostIn(List<BigDecimal> values) {
            addCriterion("project_cost in", values, "projectCost");
            return (Criteria) this;
        }

        public Criteria andProjectCostNotIn(List<BigDecimal> values) {
            addCriterion("project_cost not in", values, "projectCost");
            return (Criteria) this;
        }

        public Criteria andProjectCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_cost between", value1, value2, "projectCost");
            return (Criteria) this;
        }

        public Criteria andProjectCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_cost not between", value1, value2, "projectCost");
            return (Criteria) this;
        }

        public Criteria andProjectNetAmountIsNull() {
            addCriterion("project_net_amount is null");
            return (Criteria) this;
        }

        public Criteria andProjectNetAmountIsNotNull() {
            addCriterion("project_net_amount is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNetAmountEqualTo(BigDecimal value) {
            addCriterion("project_net_amount =", value, "projectNetAmount");
            return (Criteria) this;
        }

        public Criteria andProjectNetAmountNotEqualTo(BigDecimal value) {
            addCriterion("project_net_amount <>", value, "projectNetAmount");
            return (Criteria) this;
        }

        public Criteria andProjectNetAmountGreaterThan(BigDecimal value) {
            addCriterion("project_net_amount >", value, "projectNetAmount");
            return (Criteria) this;
        }

        public Criteria andProjectNetAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_net_amount >=", value, "projectNetAmount");
            return (Criteria) this;
        }

        public Criteria andProjectNetAmountLessThan(BigDecimal value) {
            addCriterion("project_net_amount <", value, "projectNetAmount");
            return (Criteria) this;
        }

        public Criteria andProjectNetAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_net_amount <=", value, "projectNetAmount");
            return (Criteria) this;
        }

        public Criteria andProjectNetAmountIn(List<BigDecimal> values) {
            addCriterion("project_net_amount in", values, "projectNetAmount");
            return (Criteria) this;
        }

        public Criteria andProjectNetAmountNotIn(List<BigDecimal> values) {
            addCriterion("project_net_amount not in", values, "projectNetAmount");
            return (Criteria) this;
        }

        public Criteria andProjectNetAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_net_amount between", value1, value2, "projectNetAmount");
            return (Criteria) this;
        }

        public Criteria andProjectNetAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_net_amount not between", value1, value2, "projectNetAmount");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNull() {
            addCriterion("customer_code is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNotNull() {
            addCriterion("customer_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeEqualTo(String value) {
            addCriterion("customer_code =", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotEqualTo(String value) {
            addCriterion("customer_code <>", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThan(String value) {
            addCriterion("customer_code >", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("customer_code >=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThan(String value) {
            addCriterion("customer_code <", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThanOrEqualTo(String value) {
            addCriterion("customer_code <=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLike(String value) {
            addCriterion("customer_code like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotLike(String value) {
            addCriterion("customer_code not like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIn(List<String> values) {
            addCriterion("customer_code in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotIn(List<String> values) {
            addCriterion("customer_code not in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBetween(String value1, String value2) {
            addCriterion("customer_code between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotBetween(String value1, String value2) {
            addCriterion("customer_code not between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andPriceTypeIsNull() {
            addCriterion("price_type is null");
            return (Criteria) this;
        }

        public Criteria andPriceTypeIsNotNull() {
            addCriterion("price_type is not null");
            return (Criteria) this;
        }

        public Criteria andPriceTypeEqualTo(String value) {
            addCriterion("price_type =", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeNotEqualTo(String value) {
            addCriterion("price_type <>", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeGreaterThan(String value) {
            addCriterion("price_type >", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeGreaterThanOrEqualTo(String value) {
            addCriterion("price_type >=", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeLessThan(String value) {
            addCriterion("price_type <", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeLessThanOrEqualTo(String value) {
            addCriterion("price_type <=", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeLike(String value) {
            addCriterion("price_type like", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeNotLike(String value) {
            addCriterion("price_type not like", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeIn(List<String> values) {
            addCriterion("price_type in", values, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeNotIn(List<String> values) {
            addCriterion("price_type not in", values, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeBetween(String value1, String value2) {
            addCriterion("price_type between", value1, value2, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeNotBetween(String value1, String value2) {
            addCriterion("price_type not between", value1, value2, "priceType");
            return (Criteria) this;
        }

        public Criteria andIndustryIsNull() {
            addCriterion("industry is null");
            return (Criteria) this;
        }

        public Criteria andIndustryIsNotNull() {
            addCriterion("industry is not null");
            return (Criteria) this;
        }

        public Criteria andIndustryEqualTo(String value) {
            addCriterion("industry =", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotEqualTo(String value) {
            addCriterion("industry <>", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryGreaterThan(String value) {
            addCriterion("industry >", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryGreaterThanOrEqualTo(String value) {
            addCriterion("industry >=", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLessThan(String value) {
            addCriterion("industry <", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLessThanOrEqualTo(String value) {
            addCriterion("industry <=", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryLike(String value) {
            addCriterion("industry like", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotLike(String value) {
            addCriterion("industry not like", value, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryIn(List<String> values) {
            addCriterion("industry in", values, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotIn(List<String> values) {
            addCriterion("industry not in", values, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryBetween(String value1, String value2) {
            addCriterion("industry between", value1, value2, "industry");
            return (Criteria) this;
        }

        public Criteria andIndustryNotBetween(String value1, String value2) {
            addCriterion("industry not between", value1, value2, "industry");
            return (Criteria) this;
        }

        public Criteria andSigningCenterIsNull() {
            addCriterion("signing_center is null");
            return (Criteria) this;
        }

        public Criteria andSigningCenterIsNotNull() {
            addCriterion("signing_center is not null");
            return (Criteria) this;
        }

        public Criteria andSigningCenterEqualTo(String value) {
            addCriterion("signing_center =", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterNotEqualTo(String value) {
            addCriterion("signing_center <>", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterGreaterThan(String value) {
            addCriterion("signing_center >", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterGreaterThanOrEqualTo(String value) {
            addCriterion("signing_center >=", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLessThan(String value) {
            addCriterion("signing_center <", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLessThanOrEqualTo(String value) {
            addCriterion("signing_center <=", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterLike(String value) {
            addCriterion("signing_center like", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterNotLike(String value) {
            addCriterion("signing_center not like", value, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterIn(List<String> values) {
            addCriterion("signing_center in", values, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterNotIn(List<String> values) {
            addCriterion("signing_center not in", values, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterBetween(String value1, String value2) {
            addCriterion("signing_center between", value1, value2, "signingCenter");
            return (Criteria) this;
        }

        public Criteria andSigningCenterNotBetween(String value1, String value2) {
            addCriterion("signing_center not between", value1, value2, "signingCenter");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}