package com.midea.pam.common.statistics.excelVo;

import com.midea.pam.common.util.DateUtils;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 **/
public class ReportBusinessWinExcelVO {
    @Excel(name = "序号",width = 10)
    private Integer num;

    @Excel(name = "商机编号",width = 20)
    private String businessCode;

    @Excel(name = "销售人员",width = 20)
    private String ownerName;

    @Excel(name = "赢单日期", width = 20, format = "yyyy-MM-dd")
    private Date checkAt;

    @Excel(name = "销售部门",width = 20)
    private String salesDepartmentName;

    @Excel(name = "行业",width = 20)
    private String industry;

    @Excel(name = "事业部",width = 20)
    private String divisionName;

    @Excel(name = "销售区域",width = 20)
    private String regionName;

    @Excel(name = "币种",width = 20)
    private String currencyCode;

    @Excel(name = "赢单金额(含税）",width = 20)
    private BigDecimal quoteWithTax;

    @Excel(name = "赢单金额(不含税）",width = 20)
    private BigDecimal quoteWithoutTax;

    @Excel(name = "报价硬件成本",width = 20)
    private BigDecimal quoteCostHardwareWithoutTax;

    @Excel(name = "报价人力成本",width = 20)
    private BigDecimal quoteCostLabourWithoutTax;

    @Excel(name = "报价差旅成本",width = 20)
    private BigDecimal quoteCostTravelWithoutTax;

    @Excel(name = "报价其他费用成本",width = 20)
    private BigDecimal quoteCostOtherWithoutTax;

    @Excel(name = "报价成本合计",width = 20)
    private BigDecimal quoteCostTotal;

    @Excel(name = "赢单毛利额",width = 20)
    private BigDecimal quoteProfitAmountWithoutTax;

    @Excel(name = "赢单毛利率",width = 20, suffix ="%")
    private BigDecimal quoteProfitPercentWithoutTax;

    @Excel(name = "客户编码",width = 20)
    private String customerPamCode;

    @Excel(name = "客户名称",width = 20)
    private String customerName;

    @Excel(name = "主合同编号",width = 20, replace = {"-_null"})
    private String contractCode;

    @Excel(name = "主合同名称",width = 20, replace = {"-_null"})
    private String contractName;

    @Excel(name = "主合同金额（含税）",width = 20, replace = {"-_null"})
    private BigDecimal contractAmount;

    @Excel(name = "主合同金额（不含税）",width = 20, replace = {"-_null"})
    private BigDecimal contractExcludingTaxAmount;

    @Excel(name = "合同日期", width = 20, format = "yyyy-MM-dd", replace = {"-_null"})
    private Date contractStartTime;

    @Excel(name = "项目总收入",width = 20, replace = {"-_null"})
    private BigDecimal contractTotalProjectIncome;

    @Excel(name = "项目总成本",width = 20, replace = {"-_null"})
    private BigDecimal contractTotalProjectCost;

    @Excel(name = "项目总毛利额",width = 20, replace = {"-_null"})
    private BigDecimal contractTotalProjectProfitAmount;

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Date getCheckAt() {
        return checkAt;
    }

    public void setCheckAt(Date checkAt) {
        this.checkAt = checkAt;
    }

    public String getSalesDepartmentName() {
        return salesDepartmentName;
    }

    public void setSalesDepartmentName(String salesDepartmentName) {
        this.salesDepartmentName = salesDepartmentName;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getDivisionName() {
        return divisionName;
    }

    public void setDivisionName(String divisionName) {
        this.divisionName = divisionName;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getQuoteWithTax() {
        return quoteWithTax;
    }

    public void setQuoteWithTax(BigDecimal quoteWithTax) {
        this.quoteWithTax = quoteWithTax;
    }

    public BigDecimal getQuoteWithoutTax() {
        return quoteWithoutTax;
    }

    public void setQuoteWithoutTax(BigDecimal quoteWithoutTax) {
        this.quoteWithoutTax = quoteWithoutTax;
    }

    public BigDecimal getQuoteCostHardwareWithoutTax() {
        return quoteCostHardwareWithoutTax;
    }

    public void setQuoteCostHardwareWithoutTax(BigDecimal quoteCostHardwareWithoutTax) {
        this.quoteCostHardwareWithoutTax = quoteCostHardwareWithoutTax;
    }

    public BigDecimal getQuoteCostLabourWithoutTax() {
        return quoteCostLabourWithoutTax;
    }

    public void setQuoteCostLabourWithoutTax(BigDecimal quoteCostLabourWithoutTax) {
        this.quoteCostLabourWithoutTax = quoteCostLabourWithoutTax;
    }

    public BigDecimal getQuoteCostTravelWithoutTax() {
        return quoteCostTravelWithoutTax;
    }

    public void setQuoteCostTravelWithoutTax(BigDecimal quoteCostTravelWithoutTax) {
        this.quoteCostTravelWithoutTax = quoteCostTravelWithoutTax;
    }

    public BigDecimal getQuoteCostOtherWithoutTax() {
        return quoteCostOtherWithoutTax;
    }

    public void setQuoteCostOtherWithoutTax(BigDecimal quoteCostOtherWithoutTax) {
        this.quoteCostOtherWithoutTax = quoteCostOtherWithoutTax;
    }

    public BigDecimal getQuoteCostTotal() {
        return quoteCostTotal;
    }

    public void setQuoteCostTotal(BigDecimal quoteCostTotal) {
        this.quoteCostTotal = quoteCostTotal;
    }

    public BigDecimal getQuoteProfitAmountWithoutTax() {
        return quoteProfitAmountWithoutTax;
    }

    public void setQuoteProfitAmountWithoutTax(BigDecimal quoteProfitAmountWithoutTax) {
        this.quoteProfitAmountWithoutTax = quoteProfitAmountWithoutTax;
    }

    public BigDecimal getQuoteProfitPercentWithoutTax() {
        return quoteProfitPercentWithoutTax;
    }

    public void setQuoteProfitPercentWithoutTax(BigDecimal quoteProfitPercentWithoutTax) {
        this.quoteProfitPercentWithoutTax = quoteProfitPercentWithoutTax;
    }

    public String getCustomerPamCode() {
        return customerPamCode;
    }

    public void setCustomerPamCode(String customerPamCode) {
        this.customerPamCode = customerPamCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    public BigDecimal getContractAmount() {
        return contractAmount;
    }

    public void setContractAmount(BigDecimal contractAmount) {
        this.contractAmount = contractAmount;
    }

    public BigDecimal getContractExcludingTaxAmount() {
        return contractExcludingTaxAmount;
    }

    public void setContractExcludingTaxAmount(BigDecimal contractExcludingTaxAmount) {
        this.contractExcludingTaxAmount = contractExcludingTaxAmount;
    }

    public Date getContractStartTime() {
        return contractStartTime;
    }

    public void setContractStartTime(Date contractStartTime) {
        this.contractStartTime = contractStartTime;
    }

    public BigDecimal getContractTotalProjectIncome() {
        return contractTotalProjectIncome;
    }

    public void setContractTotalProjectIncome(BigDecimal contractTotalProjectIncome) {
        this.contractTotalProjectIncome = contractTotalProjectIncome;
    }

    public BigDecimal getContractTotalProjectCost() {
        return contractTotalProjectCost;
    }

    public void setContractTotalProjectCost(BigDecimal contractTotalProjectCost) {
        this.contractTotalProjectCost = contractTotalProjectCost;
    }

    public BigDecimal getContractTotalProjectProfitAmount() {
        return contractTotalProjectProfitAmount;
    }

    public void setContractTotalProjectProfitAmount(BigDecimal contractTotalProjectProfitAmount) {
        this.contractTotalProjectProfitAmount = contractTotalProjectProfitAmount;
    }
}
