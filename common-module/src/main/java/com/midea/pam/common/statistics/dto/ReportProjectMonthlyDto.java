package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.ReportProjectMonthly;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ReportProjectMonthlyDto extends ReportProjectMonthly {
    private int number;
    private Long projectId;
    private String projectStatusName;
    private String priceTypeName;
    private Boolean deletedFlag;
    @ApiModelProperty(value = "基本信息：所属主体Id")
    private Long ouId;
}
