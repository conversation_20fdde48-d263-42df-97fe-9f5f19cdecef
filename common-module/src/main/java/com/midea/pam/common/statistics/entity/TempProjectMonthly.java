package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "工程月度大数据临时表")
public class TempProjectMonthly extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "基本信息：项目编号")
    private String projectCode;

    @ApiModelProperty(value = "基本信息：项目名称")
    private String projectName;

    @ApiModelProperty(value = "报表年月")
    private String theYearMonth;

    @ApiModelProperty(value = "基本信息：客户名称")
    private String customerName;

    @ApiModelProperty(value = "基本信息：所属主体Id")
    private Long ouId;

    @ApiModelProperty(value = "基本信息：所属主体")
    private String ouName;

    @ApiModelProperty(value = "基本信息：子合同编号")
    private String contractCode;

    @ApiModelProperty(value = "基本信息：项目经理")
    private String projectManager;

    @ApiModelProperty(value = "基本信息：所属区域")
    private String region;

    @ApiModelProperty(value = "基本信息：项目类型")
    private String projectType;

    @ApiModelProperty(value = "基本信息：项目状态")
    private Integer projectStatus;

    @ApiModelProperty(value = "基本信息：开工时间")
    private Date startTime;

    @ApiModelProperty(value = "基本信息：竣工时间")
    private Date endTime;

    @ApiModelProperty(value = "基本信息：合同金额（含税）")
    private BigDecimal contractAmount;

    @ApiModelProperty(value = "基本信息：初始利润额")
    private BigDecimal initialProfit;

    @ApiModelProperty(value = "项目营收情况-预计合同总收入（不含税）：总收入")
    private BigDecimal totalIncome;

    @ApiModelProperty(value = "项目营收情况-预计合同总收入（不含税）：合同初始收入")
    private BigDecimal initialIncome;

    @ApiModelProperty(value = "项目营收情况-预计合同总收入（不含税）：变更等追加收入")
    private BigDecimal appendIncome;

    @ApiModelProperty(value = "项目营收情况-项目预算：当前总预算")
    private BigDecimal projectTotalBudget;

    @ApiModelProperty(value = "项目营收情况-项目预算：项目初始预算")
    private BigDecimal projectInitialBudget;

    @ApiModelProperty(value = "项目营收情况-项目预算：项目变更预算")
    private BigDecimal projectChangeBudget;

    @ApiModelProperty(value = "项目营收情况:实际发生完工百分比")
    private String actualOccurCompletePercent;

    @ApiModelProperty(value = "项目营收情况:实际已结转完工百分比")
    private String actualCarryCompletePercent;

    @ApiModelProperty(value = "项目营收情况:累计实际工程成本")
    private BigDecimal collectProjectCost;

    @ApiModelProperty(value = "项目营收情况:实际已结转成本")
    private BigDecimal projectCarryCost;

    @ApiModelProperty(value = "项目营收情况-已确认合同收入:月初已确认收入")
    private BigDecimal monthEarlyIncome;

    @ApiModelProperty(value = "项目营收情况-已确认合同收入:本月确认收入")
    private BigDecimal monthIncome;

    @ApiModelProperty(value = "项目营收情况-已确认合同收入:累计确认收入")
    private BigDecimal collectIncome;

    @ApiModelProperty(value = "项目营收情况-已确认合同成本:月初已确认成本")
    private BigDecimal monthEarlyCost;

    @ApiModelProperty(value = "项目营收情况-已确认合同成本:本月确认成本")
    private BigDecimal monthCost;

    @ApiModelProperty(value = "项目营收情况-已确认合同成本:累计确认成本")
    private BigDecimal collectCost;

    @ApiModelProperty(value = "项目营收情况-累计已确认毛利:月初已确认毛利")
    private BigDecimal monthEarlyGrossProfit;

    @ApiModelProperty(value = "项目营收情况-累计已确认毛利:本月确认毛利")
    private BigDecimal monthGrossProfit;

    @ApiModelProperty(value = "项目营收情况-累计已确认毛利:累计毛利")
    private BigDecimal collectGrossProfit;

    @ApiModelProperty(value = "项目开票收款情况-已开票金额（含税）:本月初已开票")
    private BigDecimal monthEarlyInvoice;

    @ApiModelProperty(value = "项目开票收款情况-已开票金额（含税）:本月开票")
    private BigDecimal monthInvoice;

    @ApiModelProperty(value = "项目开票收款情况-已开票金额（含税）:月末累计已开票")
    private BigDecimal collectInvoice;

    @ApiModelProperty(value = "项目开票收款情况-已开票金额（含税）:月末累计已开票百分比")
    private String collectInvoicePercent;

    @ApiModelProperty(value = "项目开票收款情况:未开票应收账款（不含税）")
    private BigDecimal notTaxNotInvoiceReceivable;

    @ApiModelProperty(value = "项目开票收款情况:已开票应收账款（不含税）")
    private BigDecimal notTaxInvoiceReceivable;

    @ApiModelProperty(value = "项目开票收款情况-未开票情况:月初未开票")
    private BigDecimal monthEarlyNotInvoice;

    @ApiModelProperty(value = "项目开票收款情况-未开票情况:月末未开票")
    private BigDecimal collectNotInvoice;

    @ApiModelProperty(value = "累计收取工程款:月初已收款")
    private BigDecimal monthEarlyReceipt;

    @ApiModelProperty(value = "累计收取工程款:本月收款")
    private BigDecimal monthReceipt;

    @ApiModelProperty(value = "累计收取工程款:累计收款")
    private BigDecimal collectReceipt;

    @ApiModelProperty(value = "项目开票收款情况:月末累计已收款百分比")
    private String collectReceiptPercent;

    @ApiModelProperty(value = "项目开票收款情况:月初应收账款")
    private BigDecimal monthEarlyReceivable;

    @ApiModelProperty(value = "项目开票收款情况:月末应收账款")
    private BigDecimal monthEndReceivable;

    @ApiModelProperty(value = "项目资金使用情况-资金使用:月初已使用资金")
    private BigDecimal monthEarlyPayment;

    @ApiModelProperty(value = "项目资金使用情况-资金使用:本月使用资金")
    private BigDecimal monthPayment;

    @ApiModelProperty(value = "项目资金使用情况-资金使用:月末使用资金")
    private BigDecimal collectPayment;

    @ApiModelProperty(value = "项目资金使用情况-使用资金去向（累计）:劳务分包")
    private BigDecimal labourContractAmount;

    @ApiModelProperty(value = "项目资金使用情况-使用资金去向（累计）:材料设备")
    private BigDecimal materialContractAmount;

    @ApiModelProperty(value = "项目资金使用情况-使用资金去向（累计）:内部工时")
    private BigDecimal innerHrCost;

    @ApiModelProperty(value = "项目资金使用情况-使用资金去向（累计）:项目费用")
    private BigDecimal projectCost;

    @ApiModelProperty(value = "项目资金使用情况:项目资金净流量")
    private BigDecimal projectNetAmount;

    @ApiModelProperty(value = "基本信息：客户编码")
    private String customerCode;

    @ApiModelProperty(value = "基本信息：项目属性（ 1:内部;2:外部;3:研发）")
    private String priceType;

    @ApiModelProperty(value = "基本信息：行业")
    private String industry;

    @ApiModelProperty(value = "基本信息：签约中心")
    private String signingCenter;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getTheYearMonth() {
        return theYearMonth;
    }

    public void setTheYearMonth(String theYearMonth) {
        this.theYearMonth = theYearMonth == null ? null : theYearMonth.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode == null ? null : contractCode.trim();
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region == null ? null : region.trim();
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType == null ? null : projectType.trim();
    }

    public Integer getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(Integer projectStatus) {
        this.projectStatus = projectStatus;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public BigDecimal getContractAmount() {
        return contractAmount;
    }

    public void setContractAmount(BigDecimal contractAmount) {
        this.contractAmount = contractAmount;
    }

    public BigDecimal getInitialProfit() {
        return initialProfit;
    }

    public void setInitialProfit(BigDecimal initialProfit) {
        this.initialProfit = initialProfit;
    }

    public BigDecimal getTotalIncome() {
        return totalIncome;
    }

    public void setTotalIncome(BigDecimal totalIncome) {
        this.totalIncome = totalIncome;
    }

    public BigDecimal getInitialIncome() {
        return initialIncome;
    }

    public void setInitialIncome(BigDecimal initialIncome) {
        this.initialIncome = initialIncome;
    }

    public BigDecimal getAppendIncome() {
        return appendIncome;
    }

    public void setAppendIncome(BigDecimal appendIncome) {
        this.appendIncome = appendIncome;
    }

    public BigDecimal getProjectTotalBudget() {
        return projectTotalBudget;
    }

    public void setProjectTotalBudget(BigDecimal projectTotalBudget) {
        this.projectTotalBudget = projectTotalBudget;
    }

    public BigDecimal getProjectInitialBudget() {
        return projectInitialBudget;
    }

    public void setProjectInitialBudget(BigDecimal projectInitialBudget) {
        this.projectInitialBudget = projectInitialBudget;
    }

    public BigDecimal getProjectChangeBudget() {
        return projectChangeBudget;
    }

    public void setProjectChangeBudget(BigDecimal projectChangeBudget) {
        this.projectChangeBudget = projectChangeBudget;
    }

    public String getActualOccurCompletePercent() {
        return actualOccurCompletePercent;
    }

    public void setActualOccurCompletePercent(String actualOccurCompletePercent) {
        this.actualOccurCompletePercent = actualOccurCompletePercent == null ? null : actualOccurCompletePercent.trim();
    }

    public String getActualCarryCompletePercent() {
        return actualCarryCompletePercent;
    }

    public void setActualCarryCompletePercent(String actualCarryCompletePercent) {
        this.actualCarryCompletePercent = actualCarryCompletePercent == null ? null : actualCarryCompletePercent.trim();
    }

    public BigDecimal getCollectProjectCost() {
        return collectProjectCost;
    }

    public void setCollectProjectCost(BigDecimal collectProjectCost) {
        this.collectProjectCost = collectProjectCost;
    }

    public BigDecimal getProjectCarryCost() {
        return projectCarryCost;
    }

    public void setProjectCarryCost(BigDecimal projectCarryCost) {
        this.projectCarryCost = projectCarryCost;
    }

    public BigDecimal getMonthEarlyIncome() {
        return monthEarlyIncome;
    }

    public void setMonthEarlyIncome(BigDecimal monthEarlyIncome) {
        this.monthEarlyIncome = monthEarlyIncome;
    }

    public BigDecimal getMonthIncome() {
        return monthIncome;
    }

    public void setMonthIncome(BigDecimal monthIncome) {
        this.monthIncome = monthIncome;
    }

    public BigDecimal getCollectIncome() {
        return collectIncome;
    }

    public void setCollectIncome(BigDecimal collectIncome) {
        this.collectIncome = collectIncome;
    }

    public BigDecimal getMonthEarlyCost() {
        return monthEarlyCost;
    }

    public void setMonthEarlyCost(BigDecimal monthEarlyCost) {
        this.monthEarlyCost = monthEarlyCost;
    }

    public BigDecimal getMonthCost() {
        return monthCost;
    }

    public void setMonthCost(BigDecimal monthCost) {
        this.monthCost = monthCost;
    }

    public BigDecimal getCollectCost() {
        return collectCost;
    }

    public void setCollectCost(BigDecimal collectCost) {
        this.collectCost = collectCost;
    }

    public BigDecimal getMonthEarlyGrossProfit() {
        return monthEarlyGrossProfit;
    }

    public void setMonthEarlyGrossProfit(BigDecimal monthEarlyGrossProfit) {
        this.monthEarlyGrossProfit = monthEarlyGrossProfit;
    }

    public BigDecimal getMonthGrossProfit() {
        return monthGrossProfit;
    }

    public void setMonthGrossProfit(BigDecimal monthGrossProfit) {
        this.monthGrossProfit = monthGrossProfit;
    }

    public BigDecimal getCollectGrossProfit() {
        return collectGrossProfit;
    }

    public void setCollectGrossProfit(BigDecimal collectGrossProfit) {
        this.collectGrossProfit = collectGrossProfit;
    }

    public BigDecimal getMonthEarlyInvoice() {
        return monthEarlyInvoice;
    }

    public void setMonthEarlyInvoice(BigDecimal monthEarlyInvoice) {
        this.monthEarlyInvoice = monthEarlyInvoice;
    }

    public BigDecimal getMonthInvoice() {
        return monthInvoice;
    }

    public void setMonthInvoice(BigDecimal monthInvoice) {
        this.monthInvoice = monthInvoice;
    }

    public BigDecimal getCollectInvoice() {
        return collectInvoice;
    }

    public void setCollectInvoice(BigDecimal collectInvoice) {
        this.collectInvoice = collectInvoice;
    }

    public String getCollectInvoicePercent() {
        return collectInvoicePercent;
    }

    public void setCollectInvoicePercent(String collectInvoicePercent) {
        this.collectInvoicePercent = collectInvoicePercent == null ? null : collectInvoicePercent.trim();
    }

    public BigDecimal getNotTaxNotInvoiceReceivable() {
        return notTaxNotInvoiceReceivable;
    }

    public void setNotTaxNotInvoiceReceivable(BigDecimal notTaxNotInvoiceReceivable) {
        this.notTaxNotInvoiceReceivable = notTaxNotInvoiceReceivable;
    }

    public BigDecimal getNotTaxInvoiceReceivable() {
        return notTaxInvoiceReceivable;
    }

    public void setNotTaxInvoiceReceivable(BigDecimal notTaxInvoiceReceivable) {
        this.notTaxInvoiceReceivable = notTaxInvoiceReceivable;
    }

    public BigDecimal getMonthEarlyNotInvoice() {
        return monthEarlyNotInvoice;
    }

    public void setMonthEarlyNotInvoice(BigDecimal monthEarlyNotInvoice) {
        this.monthEarlyNotInvoice = monthEarlyNotInvoice;
    }

    public BigDecimal getCollectNotInvoice() {
        return collectNotInvoice;
    }

    public void setCollectNotInvoice(BigDecimal collectNotInvoice) {
        this.collectNotInvoice = collectNotInvoice;
    }

    public BigDecimal getMonthEarlyReceipt() {
        return monthEarlyReceipt;
    }

    public void setMonthEarlyReceipt(BigDecimal monthEarlyReceipt) {
        this.monthEarlyReceipt = monthEarlyReceipt;
    }

    public BigDecimal getMonthReceipt() {
        return monthReceipt;
    }

    public void setMonthReceipt(BigDecimal monthReceipt) {
        this.monthReceipt = monthReceipt;
    }

    public BigDecimal getCollectReceipt() {
        return collectReceipt;
    }

    public void setCollectReceipt(BigDecimal collectReceipt) {
        this.collectReceipt = collectReceipt;
    }

    public String getCollectReceiptPercent() {
        return collectReceiptPercent;
    }

    public void setCollectReceiptPercent(String collectReceiptPercent) {
        this.collectReceiptPercent = collectReceiptPercent == null ? null : collectReceiptPercent.trim();
    }

    public BigDecimal getMonthEarlyReceivable() {
        return monthEarlyReceivable;
    }

    public void setMonthEarlyReceivable(BigDecimal monthEarlyReceivable) {
        this.monthEarlyReceivable = monthEarlyReceivable;
    }

    public BigDecimal getMonthEndReceivable() {
        return monthEndReceivable;
    }

    public void setMonthEndReceivable(BigDecimal monthEndReceivable) {
        this.monthEndReceivable = monthEndReceivable;
    }

    public BigDecimal getMonthEarlyPayment() {
        return monthEarlyPayment;
    }

    public void setMonthEarlyPayment(BigDecimal monthEarlyPayment) {
        this.monthEarlyPayment = monthEarlyPayment;
    }

    public BigDecimal getMonthPayment() {
        return monthPayment;
    }

    public void setMonthPayment(BigDecimal monthPayment) {
        this.monthPayment = monthPayment;
    }

    public BigDecimal getCollectPayment() {
        return collectPayment;
    }

    public void setCollectPayment(BigDecimal collectPayment) {
        this.collectPayment = collectPayment;
    }

    public BigDecimal getLabourContractAmount() {
        return labourContractAmount;
    }

    public void setLabourContractAmount(BigDecimal labourContractAmount) {
        this.labourContractAmount = labourContractAmount;
    }

    public BigDecimal getMaterialContractAmount() {
        return materialContractAmount;
    }

    public void setMaterialContractAmount(BigDecimal materialContractAmount) {
        this.materialContractAmount = materialContractAmount;
    }

    public BigDecimal getInnerHrCost() {
        return innerHrCost;
    }

    public void setInnerHrCost(BigDecimal innerHrCost) {
        this.innerHrCost = innerHrCost;
    }

    public BigDecimal getProjectCost() {
        return projectCost;
    }

    public void setProjectCost(BigDecimal projectCost) {
        this.projectCost = projectCost;
    }

    public BigDecimal getProjectNetAmount() {
        return projectNetAmount;
    }

    public void setProjectNetAmount(BigDecimal projectNetAmount) {
        this.projectNetAmount = projectNetAmount;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode == null ? null : customerCode.trim();
    }

    public String getPriceType() {
        return priceType;
    }

    public void setPriceType(String priceType) {
        this.priceType = priceType == null ? null : priceType.trim();
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry == null ? null : industry.trim();
    }

    public String getSigningCenter() {
        return signingCenter;
    }

    public void setSigningCenter(String signingCenter) {
        this.signingCenter = signingCenter == null ? null : signingCenter.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", theYearMonth=").append(theYearMonth);
        sb.append(", customerName=").append(customerName);
        sb.append(", ouId=").append(ouId);
        sb.append(", ouName=").append(ouName);
        sb.append(", contractCode=").append(contractCode);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", region=").append(region);
        sb.append(", projectType=").append(projectType);
        sb.append(", projectStatus=").append(projectStatus);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", contractAmount=").append(contractAmount);
        sb.append(", initialProfit=").append(initialProfit);
        sb.append(", totalIncome=").append(totalIncome);
        sb.append(", initialIncome=").append(initialIncome);
        sb.append(", appendIncome=").append(appendIncome);
        sb.append(", projectTotalBudget=").append(projectTotalBudget);
        sb.append(", projectInitialBudget=").append(projectInitialBudget);
        sb.append(", projectChangeBudget=").append(projectChangeBudget);
        sb.append(", actualOccurCompletePercent=").append(actualOccurCompletePercent);
        sb.append(", actualCarryCompletePercent=").append(actualCarryCompletePercent);
        sb.append(", collectProjectCost=").append(collectProjectCost);
        sb.append(", projectCarryCost=").append(projectCarryCost);
        sb.append(", monthEarlyIncome=").append(monthEarlyIncome);
        sb.append(", monthIncome=").append(monthIncome);
        sb.append(", collectIncome=").append(collectIncome);
        sb.append(", monthEarlyCost=").append(monthEarlyCost);
        sb.append(", monthCost=").append(monthCost);
        sb.append(", collectCost=").append(collectCost);
        sb.append(", monthEarlyGrossProfit=").append(monthEarlyGrossProfit);
        sb.append(", monthGrossProfit=").append(monthGrossProfit);
        sb.append(", collectGrossProfit=").append(collectGrossProfit);
        sb.append(", monthEarlyInvoice=").append(monthEarlyInvoice);
        sb.append(", monthInvoice=").append(monthInvoice);
        sb.append(", collectInvoice=").append(collectInvoice);
        sb.append(", collectInvoicePercent=").append(collectInvoicePercent);
        sb.append(", notTaxNotInvoiceReceivable=").append(notTaxNotInvoiceReceivable);
        sb.append(", notTaxInvoiceReceivable=").append(notTaxInvoiceReceivable);
        sb.append(", monthEarlyNotInvoice=").append(monthEarlyNotInvoice);
        sb.append(", collectNotInvoice=").append(collectNotInvoice);
        sb.append(", monthEarlyReceipt=").append(monthEarlyReceipt);
        sb.append(", monthReceipt=").append(monthReceipt);
        sb.append(", collectReceipt=").append(collectReceipt);
        sb.append(", collectReceiptPercent=").append(collectReceiptPercent);
        sb.append(", monthEarlyReceivable=").append(monthEarlyReceivable);
        sb.append(", monthEndReceivable=").append(monthEndReceivable);
        sb.append(", monthEarlyPayment=").append(monthEarlyPayment);
        sb.append(", monthPayment=").append(monthPayment);
        sb.append(", collectPayment=").append(collectPayment);
        sb.append(", labourContractAmount=").append(labourContractAmount);
        sb.append(", materialContractAmount=").append(materialContractAmount);
        sb.append(", innerHrCost=").append(innerHrCost);
        sb.append(", projectCost=").append(projectCost);
        sb.append(", projectNetAmount=").append(projectNetAmount);
        sb.append(", customerCode=").append(customerCode);
        sb.append(", priceType=").append(priceType);
        sb.append(", industry=").append(industry);
        sb.append(", signingCenter=").append(signingCenter);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}