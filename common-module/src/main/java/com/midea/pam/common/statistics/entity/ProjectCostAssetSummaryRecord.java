package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目资产成本汇总")
public class ProjectCostAssetSummaryRecord extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "执行ID")
    private Long executeId;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "资产总预算")
    private BigDecimal budget;

    @ApiModelProperty(value = "已发生成本")
    private BigDecimal incurredCost;

    @ApiModelProperty(value = "剩余预算")
    private BigDecimal remainderBudget;

    @ApiModelProperty(value = "已发生成本比例")
    private BigDecimal incurredRatio;

    @ApiModelProperty(value = "是否被删除标志")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public BigDecimal getBudget() {
        return budget;
    }

    public void setBudget(BigDecimal budget) {
        this.budget = budget;
    }

    public BigDecimal getIncurredCost() {
        return incurredCost;
    }

    public void setIncurredCost(BigDecimal incurredCost) {
        this.incurredCost = incurredCost;
    }

    public BigDecimal getRemainderBudget() {
        return remainderBudget;
    }

    public void setRemainderBudget(BigDecimal remainderBudget) {
        this.remainderBudget = remainderBudget;
    }

    public BigDecimal getIncurredRatio() {
        return incurredRatio;
    }

    public void setIncurredRatio(BigDecimal incurredRatio) {
        this.incurredRatio = incurredRatio;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", budget=").append(budget);
        sb.append(", incurredCost=").append(incurredCost);
        sb.append(", remainderBudget=").append(remainderBudget);
        sb.append(", incurredRatio=").append(incurredRatio);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}