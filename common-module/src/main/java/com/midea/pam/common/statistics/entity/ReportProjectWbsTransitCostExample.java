package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportProjectWbsTransitCostExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportProjectWbsTransitCostExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeIsNull() {
            addCriterion("execute_time is null");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeIsNotNull() {
            addCriterion("execute_time is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeEqualTo(Date value) {
            addCriterion("execute_time =", value, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeNotEqualTo(Date value) {
            addCriterion("execute_time <>", value, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeGreaterThan(Date value) {
            addCriterion("execute_time >", value, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("execute_time >=", value, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeLessThan(Date value) {
            addCriterion("execute_time <", value, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeLessThanOrEqualTo(Date value) {
            addCriterion("execute_time <=", value, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeIn(List<Date> values) {
            addCriterion("execute_time in", values, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeNotIn(List<Date> values) {
            addCriterion("execute_time not in", values, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeBetween(Date value1, Date value2) {
            addCriterion("execute_time between", value1, value2, "executeTime");
            return (Criteria) this;
        }

        public Criteria andExecuteTimeNotBetween(Date value1, Date value2) {
            addCriterion("execute_time not between", value1, value2, "executeTime");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("type like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("type not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNull() {
            addCriterion("wbs_summary_code is null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNotNull() {
            addCriterion("wbs_summary_code is not null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeEqualTo(String value) {
            addCriterion("wbs_summary_code =", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotEqualTo(String value) {
            addCriterion("wbs_summary_code <>", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThan(String value) {
            addCriterion("wbs_summary_code >", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code >=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThan(String value) {
            addCriterion("wbs_summary_code <", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code <=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLike(String value) {
            addCriterion("wbs_summary_code like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotLike(String value) {
            addCriterion("wbs_summary_code not like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIn(List<String> values) {
            addCriterion("wbs_summary_code in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotIn(List<String> values) {
            addCriterion("wbs_summary_code not in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeBetween(String value1, String value2) {
            addCriterion("wbs_summary_code between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotBetween(String value1, String value2) {
            addCriterion("wbs_summary_code not between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNull() {
            addCriterion("activity_code is null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNotNull() {
            addCriterion("activity_code is not null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeEqualTo(String value) {
            addCriterion("activity_code =", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotEqualTo(String value) {
            addCriterion("activity_code <>", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThan(String value) {
            addCriterion("activity_code >", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("activity_code >=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThan(String value) {
            addCriterion("activity_code <", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThanOrEqualTo(String value) {
            addCriterion("activity_code <=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLike(String value) {
            addCriterion("activity_code like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotLike(String value) {
            addCriterion("activity_code not like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIn(List<String> values) {
            addCriterion("activity_code in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotIn(List<String> values) {
            addCriterion("activity_code not in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeBetween(String value1, String value2) {
            addCriterion("activity_code between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotBetween(String value1, String value2) {
            addCriterion("activity_code not between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andNumIsNull() {
            addCriterion("num is null");
            return (Criteria) this;
        }

        public Criteria andNumIsNotNull() {
            addCriterion("num is not null");
            return (Criteria) this;
        }

        public Criteria andNumEqualTo(String value) {
            addCriterion("num =", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotEqualTo(String value) {
            addCriterion("num <>", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThan(String value) {
            addCriterion("num >", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThanOrEqualTo(String value) {
            addCriterion("num >=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThan(String value) {
            addCriterion("num <", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThanOrEqualTo(String value) {
            addCriterion("num <=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLike(String value) {
            addCriterion("num like", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotLike(String value) {
            addCriterion("num not like", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumIn(List<String> values) {
            addCriterion("num in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotIn(List<String> values) {
            addCriterion("num not in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumBetween(String value1, String value2) {
            addCriterion("num between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotBetween(String value1, String value2) {
            addCriterion("num not between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractNameIsNull() {
            addCriterion("purchase_contract_name is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractNameIsNotNull() {
            addCriterion("purchase_contract_name is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractNameEqualTo(String value) {
            addCriterion("purchase_contract_name =", value, "purchaseContractName");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractNameNotEqualTo(String value) {
            addCriterion("purchase_contract_name <>", value, "purchaseContractName");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractNameGreaterThan(String value) {
            addCriterion("purchase_contract_name >", value, "purchaseContractName");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractNameGreaterThanOrEqualTo(String value) {
            addCriterion("purchase_contract_name >=", value, "purchaseContractName");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractNameLessThan(String value) {
            addCriterion("purchase_contract_name <", value, "purchaseContractName");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractNameLessThanOrEqualTo(String value) {
            addCriterion("purchase_contract_name <=", value, "purchaseContractName");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractNameLike(String value) {
            addCriterion("purchase_contract_name like", value, "purchaseContractName");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractNameNotLike(String value) {
            addCriterion("purchase_contract_name not like", value, "purchaseContractName");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractNameIn(List<String> values) {
            addCriterion("purchase_contract_name in", values, "purchaseContractName");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractNameNotIn(List<String> values) {
            addCriterion("purchase_contract_name not in", values, "purchaseContractName");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractNameBetween(String value1, String value2) {
            addCriterion("purchase_contract_name between", value1, value2, "purchaseContractName");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractNameNotBetween(String value1, String value2) {
            addCriterion("purchase_contract_name not between", value1, value2, "purchaseContractName");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNull() {
            addCriterion("vendor_name is null");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNotNull() {
            addCriterion("vendor_name is not null");
            return (Criteria) this;
        }

        public Criteria andVendorNameEqualTo(String value) {
            addCriterion("vendor_name =", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotEqualTo(String value) {
            addCriterion("vendor_name <>", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThan(String value) {
            addCriterion("vendor_name >", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_name >=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThan(String value) {
            addCriterion("vendor_name <", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThanOrEqualTo(String value) {
            addCriterion("vendor_name <=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLike(String value) {
            addCriterion("vendor_name like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotLike(String value) {
            addCriterion("vendor_name not like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameIn(List<String> values) {
            addCriterion("vendor_name in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotIn(List<String> values) {
            addCriterion("vendor_name not in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameBetween(String value1, String value2) {
            addCriterion("vendor_name between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotBetween(String value1, String value2) {
            addCriterion("vendor_name not between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNull() {
            addCriterion("pam_code is null");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNotNull() {
            addCriterion("pam_code is not null");
            return (Criteria) this;
        }

        public Criteria andPamCodeEqualTo(String value) {
            addCriterion("pam_code =", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotEqualTo(String value) {
            addCriterion("pam_code <>", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThan(String value) {
            addCriterion("pam_code >", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("pam_code >=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThan(String value) {
            addCriterion("pam_code <", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThanOrEqualTo(String value) {
            addCriterion("pam_code <=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLike(String value) {
            addCriterion("pam_code like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotLike(String value) {
            addCriterion("pam_code not like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeIn(List<String> values) {
            addCriterion("pam_code in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotIn(List<String> values) {
            addCriterion("pam_code not in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeBetween(String value1, String value2) {
            addCriterion("pam_code between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotBetween(String value1, String value2) {
            addCriterion("pam_code not between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNull() {
            addCriterion("erp_code is null");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNotNull() {
            addCriterion("erp_code is not null");
            return (Criteria) this;
        }

        public Criteria andErpCodeEqualTo(String value) {
            addCriterion("erp_code =", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotEqualTo(String value) {
            addCriterion("erp_code <>", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThan(String value) {
            addCriterion("erp_code >", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("erp_code >=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThan(String value) {
            addCriterion("erp_code <", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThanOrEqualTo(String value) {
            addCriterion("erp_code <=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLike(String value) {
            addCriterion("erp_code like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotLike(String value) {
            addCriterion("erp_code not like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIn(List<String> values) {
            addCriterion("erp_code in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotIn(List<String> values) {
            addCriterion("erp_code not in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeBetween(String value1, String value2) {
            addCriterion("erp_code between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotBetween(String value1, String value2) {
            addCriterion("erp_code not between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNull() {
            addCriterion("materiel_descr is null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNotNull() {
            addCriterion("materiel_descr is not null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrEqualTo(String value) {
            addCriterion("materiel_descr =", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotEqualTo(String value) {
            addCriterion("materiel_descr <>", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThan(String value) {
            addCriterion("materiel_descr >", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThanOrEqualTo(String value) {
            addCriterion("materiel_descr >=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThan(String value) {
            addCriterion("materiel_descr <", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThanOrEqualTo(String value) {
            addCriterion("materiel_descr <=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLike(String value) {
            addCriterion("materiel_descr like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotLike(String value) {
            addCriterion("materiel_descr not like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIn(List<String> values) {
            addCriterion("materiel_descr in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotIn(List<String> values) {
            addCriterion("materiel_descr not in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrBetween(String value1, String value2) {
            addCriterion("materiel_descr between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotBetween(String value1, String value2) {
            addCriterion("materiel_descr not between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andOrderNumIsNull() {
            addCriterion("order_num is null");
            return (Criteria) this;
        }

        public Criteria andOrderNumIsNotNull() {
            addCriterion("order_num is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNumEqualTo(BigDecimal value) {
            addCriterion("order_num =", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotEqualTo(BigDecimal value) {
            addCriterion("order_num <>", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumGreaterThan(BigDecimal value) {
            addCriterion("order_num >", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("order_num >=", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLessThan(BigDecimal value) {
            addCriterion("order_num <", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("order_num <=", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumIn(List<BigDecimal> values) {
            addCriterion("order_num in", values, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotIn(List<BigDecimal> values) {
            addCriterion("order_num not in", values, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("order_num between", value1, value2, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("order_num not between", value1, value2, "orderNum");
            return (Criteria) this;
        }

        public Criteria andStorageCountIsNull() {
            addCriterion("storage_count is null");
            return (Criteria) this;
        }

        public Criteria andStorageCountIsNotNull() {
            addCriterion("storage_count is not null");
            return (Criteria) this;
        }

        public Criteria andStorageCountEqualTo(BigDecimal value) {
            addCriterion("storage_count =", value, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountNotEqualTo(BigDecimal value) {
            addCriterion("storage_count <>", value, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountGreaterThan(BigDecimal value) {
            addCriterion("storage_count >", value, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("storage_count >=", value, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountLessThan(BigDecimal value) {
            addCriterion("storage_count <", value, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("storage_count <=", value, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountIn(List<BigDecimal> values) {
            addCriterion("storage_count in", values, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountNotIn(List<BigDecimal> values) {
            addCriterion("storage_count not in", values, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("storage_count between", value1, value2, "storageCount");
            return (Criteria) this;
        }

        public Criteria andStorageCountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("storage_count not between", value1, value2, "storageCount");
            return (Criteria) this;
        }

        public Criteria andCancelNumIsNull() {
            addCriterion("cancel_num is null");
            return (Criteria) this;
        }

        public Criteria andCancelNumIsNotNull() {
            addCriterion("cancel_num is not null");
            return (Criteria) this;
        }

        public Criteria andCancelNumEqualTo(BigDecimal value) {
            addCriterion("cancel_num =", value, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumNotEqualTo(BigDecimal value) {
            addCriterion("cancel_num <>", value, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumGreaterThan(BigDecimal value) {
            addCriterion("cancel_num >", value, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cancel_num >=", value, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumLessThan(BigDecimal value) {
            addCriterion("cancel_num <", value, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cancel_num <=", value, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumIn(List<BigDecimal> values) {
            addCriterion("cancel_num in", values, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumNotIn(List<BigDecimal> values) {
            addCriterion("cancel_num not in", values, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cancel_num between", value1, value2, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andCancelNumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cancel_num not between", value1, value2, "cancelNum");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceIsNull() {
            addCriterion("discount_price is null");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceIsNotNull() {
            addCriterion("discount_price is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceEqualTo(BigDecimal value) {
            addCriterion("discount_price =", value, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceNotEqualTo(BigDecimal value) {
            addCriterion("discount_price <>", value, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceGreaterThan(BigDecimal value) {
            addCriterion("discount_price >", value, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_price >=", value, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceLessThan(BigDecimal value) {
            addCriterion("discount_price <", value, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_price <=", value, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceIn(List<BigDecimal> values) {
            addCriterion("discount_price in", values, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceNotIn(List<BigDecimal> values) {
            addCriterion("discount_price not in", values, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_price between", value1, value2, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_price not between", value1, value2, "discountPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNull() {
            addCriterion("total_price is null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNotNull() {
            addCriterion("total_price is not null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceEqualTo(BigDecimal value) {
            addCriterion("total_price =", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotEqualTo(BigDecimal value) {
            addCriterion("total_price <>", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThan(BigDecimal value) {
            addCriterion("total_price >", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_price >=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThan(BigDecimal value) {
            addCriterion("total_price <", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_price <=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIn(List<BigDecimal> values) {
            addCriterion("total_price in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotIn(List<BigDecimal> values) {
            addCriterion("total_price not in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_price between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_price not between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andBudgetExecuteAmountTotalIsNull() {
            addCriterion("budget_execute_amount_total is null");
            return (Criteria) this;
        }

        public Criteria andBudgetExecuteAmountTotalIsNotNull() {
            addCriterion("budget_execute_amount_total is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetExecuteAmountTotalEqualTo(BigDecimal value) {
            addCriterion("budget_execute_amount_total =", value, "budgetExecuteAmountTotal");
            return (Criteria) this;
        }

        public Criteria andBudgetExecuteAmountTotalNotEqualTo(BigDecimal value) {
            addCriterion("budget_execute_amount_total <>", value, "budgetExecuteAmountTotal");
            return (Criteria) this;
        }

        public Criteria andBudgetExecuteAmountTotalGreaterThan(BigDecimal value) {
            addCriterion("budget_execute_amount_total >", value, "budgetExecuteAmountTotal");
            return (Criteria) this;
        }

        public Criteria andBudgetExecuteAmountTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_execute_amount_total >=", value, "budgetExecuteAmountTotal");
            return (Criteria) this;
        }

        public Criteria andBudgetExecuteAmountTotalLessThan(BigDecimal value) {
            addCriterion("budget_execute_amount_total <", value, "budgetExecuteAmountTotal");
            return (Criteria) this;
        }

        public Criteria andBudgetExecuteAmountTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_execute_amount_total <=", value, "budgetExecuteAmountTotal");
            return (Criteria) this;
        }

        public Criteria andBudgetExecuteAmountTotalIn(List<BigDecimal> values) {
            addCriterion("budget_execute_amount_total in", values, "budgetExecuteAmountTotal");
            return (Criteria) this;
        }

        public Criteria andBudgetExecuteAmountTotalNotIn(List<BigDecimal> values) {
            addCriterion("budget_execute_amount_total not in", values, "budgetExecuteAmountTotal");
            return (Criteria) this;
        }

        public Criteria andBudgetExecuteAmountTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_execute_amount_total between", value1, value2, "budgetExecuteAmountTotal");
            return (Criteria) this;
        }

        public Criteria andBudgetExecuteAmountTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_execute_amount_total not between", value1, value2, "budgetExecuteAmountTotal");
            return (Criteria) this;
        }

        public Criteria andBillMhAmountIsNull() {
            addCriterion("bill_mh_amount is null");
            return (Criteria) this;
        }

        public Criteria andBillMhAmountIsNotNull() {
            addCriterion("bill_mh_amount is not null");
            return (Criteria) this;
        }

        public Criteria andBillMhAmountEqualTo(BigDecimal value) {
            addCriterion("bill_mh_amount =", value, "billMhAmount");
            return (Criteria) this;
        }

        public Criteria andBillMhAmountNotEqualTo(BigDecimal value) {
            addCriterion("bill_mh_amount <>", value, "billMhAmount");
            return (Criteria) this;
        }

        public Criteria andBillMhAmountGreaterThan(BigDecimal value) {
            addCriterion("bill_mh_amount >", value, "billMhAmount");
            return (Criteria) this;
        }

        public Criteria andBillMhAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("bill_mh_amount >=", value, "billMhAmount");
            return (Criteria) this;
        }

        public Criteria andBillMhAmountLessThan(BigDecimal value) {
            addCriterion("bill_mh_amount <", value, "billMhAmount");
            return (Criteria) this;
        }

        public Criteria andBillMhAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("bill_mh_amount <=", value, "billMhAmount");
            return (Criteria) this;
        }

        public Criteria andBillMhAmountIn(List<BigDecimal> values) {
            addCriterion("bill_mh_amount in", values, "billMhAmount");
            return (Criteria) this;
        }

        public Criteria andBillMhAmountNotIn(List<BigDecimal> values) {
            addCriterion("bill_mh_amount not in", values, "billMhAmount");
            return (Criteria) this;
        }

        public Criteria andBillMhAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bill_mh_amount between", value1, value2, "billMhAmount");
            return (Criteria) this;
        }

        public Criteria andBillMhAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bill_mh_amount not between", value1, value2, "billMhAmount");
            return (Criteria) this;
        }

        public Criteria andBillCostAmountIsNull() {
            addCriterion("bill_cost_amount is null");
            return (Criteria) this;
        }

        public Criteria andBillCostAmountIsNotNull() {
            addCriterion("bill_cost_amount is not null");
            return (Criteria) this;
        }

        public Criteria andBillCostAmountEqualTo(BigDecimal value) {
            addCriterion("bill_cost_amount =", value, "billCostAmount");
            return (Criteria) this;
        }

        public Criteria andBillCostAmountNotEqualTo(BigDecimal value) {
            addCriterion("bill_cost_amount <>", value, "billCostAmount");
            return (Criteria) this;
        }

        public Criteria andBillCostAmountGreaterThan(BigDecimal value) {
            addCriterion("bill_cost_amount >", value, "billCostAmount");
            return (Criteria) this;
        }

        public Criteria andBillCostAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("bill_cost_amount >=", value, "billCostAmount");
            return (Criteria) this;
        }

        public Criteria andBillCostAmountLessThan(BigDecimal value) {
            addCriterion("bill_cost_amount <", value, "billCostAmount");
            return (Criteria) this;
        }

        public Criteria andBillCostAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("bill_cost_amount <=", value, "billCostAmount");
            return (Criteria) this;
        }

        public Criteria andBillCostAmountIn(List<BigDecimal> values) {
            addCriterion("bill_cost_amount in", values, "billCostAmount");
            return (Criteria) this;
        }

        public Criteria andBillCostAmountNotIn(List<BigDecimal> values) {
            addCriterion("bill_cost_amount not in", values, "billCostAmount");
            return (Criteria) this;
        }

        public Criteria andBillCostAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bill_cost_amount between", value1, value2, "billCostAmount");
            return (Criteria) this;
        }

        public Criteria andBillCostAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bill_cost_amount not between", value1, value2, "billCostAmount");
            return (Criteria) this;
        }

        public Criteria andOnTheWayCostIsNull() {
            addCriterion("on_the_way_cost is null");
            return (Criteria) this;
        }

        public Criteria andOnTheWayCostIsNotNull() {
            addCriterion("on_the_way_cost is not null");
            return (Criteria) this;
        }

        public Criteria andOnTheWayCostEqualTo(BigDecimal value) {
            addCriterion("on_the_way_cost =", value, "onTheWayCost");
            return (Criteria) this;
        }

        public Criteria andOnTheWayCostNotEqualTo(BigDecimal value) {
            addCriterion("on_the_way_cost <>", value, "onTheWayCost");
            return (Criteria) this;
        }

        public Criteria andOnTheWayCostGreaterThan(BigDecimal value) {
            addCriterion("on_the_way_cost >", value, "onTheWayCost");
            return (Criteria) this;
        }

        public Criteria andOnTheWayCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("on_the_way_cost >=", value, "onTheWayCost");
            return (Criteria) this;
        }

        public Criteria andOnTheWayCostLessThan(BigDecimal value) {
            addCriterion("on_the_way_cost <", value, "onTheWayCost");
            return (Criteria) this;
        }

        public Criteria andOnTheWayCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("on_the_way_cost <=", value, "onTheWayCost");
            return (Criteria) this;
        }

        public Criteria andOnTheWayCostIn(List<BigDecimal> values) {
            addCriterion("on_the_way_cost in", values, "onTheWayCost");
            return (Criteria) this;
        }

        public Criteria andOnTheWayCostNotIn(List<BigDecimal> values) {
            addCriterion("on_the_way_cost not in", values, "onTheWayCost");
            return (Criteria) this;
        }

        public Criteria andOnTheWayCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("on_the_way_cost between", value1, value2, "onTheWayCost");
            return (Criteria) this;
        }

        public Criteria andOnTheWayCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("on_the_way_cost not between", value1, value2, "onTheWayCost");
            return (Criteria) this;
        }

        public Criteria andDataTimeIsNull() {
            addCriterion("data_time is null");
            return (Criteria) this;
        }

        public Criteria andDataTimeIsNotNull() {
            addCriterion("data_time is not null");
            return (Criteria) this;
        }

        public Criteria andDataTimeEqualTo(Date value) {
            addCriterion("data_time =", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotEqualTo(Date value) {
            addCriterion("data_time <>", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeGreaterThan(Date value) {
            addCriterion("data_time >", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("data_time >=", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeLessThan(Date value) {
            addCriterion("data_time <", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeLessThanOrEqualTo(Date value) {
            addCriterion("data_time <=", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeIn(List<Date> values) {
            addCriterion("data_time in", values, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotIn(List<Date> values) {
            addCriterion("data_time not in", values, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeBetween(Date value1, Date value2) {
            addCriterion("data_time between", value1, value2, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotBetween(Date value1, Date value2) {
            addCriterion("data_time not between", value1, value2, "dataTime");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}