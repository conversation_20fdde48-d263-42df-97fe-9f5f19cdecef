package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProjectCostDetail extends LongIdEntity implements Serializable {
    private Long id;

    private Long executeId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目成本生成批次id")
    private Long costExecuteId;

    private String projectCode;

    private String projectName;

    private Long departmentId;

    private String departmentName;

    private Long unitId;

    private String unitName;

    private String projectType;

    private String projectAttribute;

    private String projectManager;

    private Long projectMilepostId;

    private String projectMilepostName;

    private Integer projectStatus;

    private Date projectStartDate;

    private Date projectEndDate;

    private BigDecimal noTaxAmount;

    private BigDecimal budgetAmount;

    private BigDecimal materialBudget;

    private BigDecimal humanBudget;

    private BigDecimal travelBudget;

    private BigDecimal noTravelBudget;

    private BigDecimal budgetProfit;

    private BigDecimal budgetProfitRate;

    private BigDecimal projectCostAmount;

    private BigDecimal materialCostActual;

    private BigDecimal materialCostRemaining;

    private BigDecimal inHumanCostActual;

    private BigDecimal inHumanCostRemaining;

    private BigDecimal outHumanCostActual;

    private BigDecimal outHumanCostRemaining;

    private BigDecimal travelCostActual;

    private BigDecimal travelCostRemaining;

    private BigDecimal travelEaAvailableAmount;

    private BigDecimal noTravelCostActual;

    private BigDecimal noTravelCostRemaining;

    private BigDecimal noTravelEaAvailableAmount;

    private BigDecimal shareCostActual;

    private BigDecimal actualCostProportion;

    private BigDecimal actualProfits;

    private BigDecimal actualProfitsRate;

    private String contractCode;

    private Long ouId;

    private String ouName;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName == null ? null : departmentName.trim();
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType == null ? null : projectType.trim();
    }

    public String getProjectAttribute() {
        return projectAttribute;
    }

    public void setProjectAttribute(String projectAttribute) {
        this.projectAttribute = projectAttribute == null ? null : projectAttribute.trim();
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager == null ? null : projectManager.trim();
    }

    public Long getProjectMilepostId() {
        return projectMilepostId;
    }

    public void setProjectMilepostId(Long projectMilepostId) {
        this.projectMilepostId = projectMilepostId;
    }

    public String getProjectMilepostName() {
        return projectMilepostName;
    }

    public void setProjectMilepostName(String projectMilepostName) {
        this.projectMilepostName = projectMilepostName == null ? null : projectMilepostName.trim();
    }

    public Integer getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(Integer projectStatus) {
        this.projectStatus = projectStatus;
    }

    public Date getProjectStartDate() {
        return projectStartDate;
    }

    public void setProjectStartDate(Date projectStartDate) {
        this.projectStartDate = projectStartDate;
    }

    public Date getProjectEndDate() {
        return projectEndDate;
    }

    public void setProjectEndDate(Date projectEndDate) {
        this.projectEndDate = projectEndDate;
    }

    public BigDecimal getNoTaxAmount() {
        return noTaxAmount;
    }

    public void setNoTaxAmount(BigDecimal noTaxAmount) {
        this.noTaxAmount = noTaxAmount;
    }

    public BigDecimal getBudgetAmount() {
        return budgetAmount;
    }

    public void setBudgetAmount(BigDecimal budgetAmount) {
        this.budgetAmount = budgetAmount;
    }

    public BigDecimal getMaterialBudget() {
        return materialBudget;
    }

    public void setMaterialBudget(BigDecimal materialBudget) {
        this.materialBudget = materialBudget;
    }

    public BigDecimal getHumanBudget() {
        return humanBudget;
    }

    public void setHumanBudget(BigDecimal humanBudget) {
        this.humanBudget = humanBudget;
    }

    public BigDecimal getTravelBudget() {
        return travelBudget;
    }

    public void setTravelBudget(BigDecimal travelBudget) {
        this.travelBudget = travelBudget;
    }

    public BigDecimal getNoTravelBudget() {
        return noTravelBudget;
    }

    public void setNoTravelBudget(BigDecimal noTravelBudget) {
        this.noTravelBudget = noTravelBudget;
    }

    public BigDecimal getBudgetProfit() {
        return budgetProfit;
    }

    public void setBudgetProfit(BigDecimal budgetProfit) {
        this.budgetProfit = budgetProfit;
    }

    public BigDecimal getBudgetProfitRate() {
        return budgetProfitRate;
    }

    public void setBudgetProfitRate(BigDecimal budgetProfitRate) {
        this.budgetProfitRate = budgetProfitRate;
    }

    public BigDecimal getProjectCostAmount() {
        return projectCostAmount;
    }

    public void setProjectCostAmount(BigDecimal projectCostAmount) {
        this.projectCostAmount = projectCostAmount;
    }

    public BigDecimal getMaterialCostActual() {
        return materialCostActual;
    }

    public void setMaterialCostActual(BigDecimal materialCostActual) {
        this.materialCostActual = materialCostActual;
    }

    public BigDecimal getMaterialCostRemaining() {
        return materialCostRemaining;
    }

    public void setMaterialCostRemaining(BigDecimal materialCostRemaining) {
        this.materialCostRemaining = materialCostRemaining;
    }

    public BigDecimal getInHumanCostActual() {
        return inHumanCostActual;
    }

    public void setInHumanCostActual(BigDecimal inHumanCostActual) {
        this.inHumanCostActual = inHumanCostActual;
    }

    public BigDecimal getInHumanCostRemaining() {
        return inHumanCostRemaining;
    }

    public void setInHumanCostRemaining(BigDecimal inHumanCostRemaining) {
        this.inHumanCostRemaining = inHumanCostRemaining;
    }

    public BigDecimal getOutHumanCostActual() {
        return outHumanCostActual;
    }

    public void setOutHumanCostActual(BigDecimal outHumanCostActual) {
        this.outHumanCostActual = outHumanCostActual;
    }

    public BigDecimal getOutHumanCostRemaining() {
        return outHumanCostRemaining;
    }

    public void setOutHumanCostRemaining(BigDecimal outHumanCostRemaining) {
        this.outHumanCostRemaining = outHumanCostRemaining;
    }

    public BigDecimal getTravelCostActual() {
        return travelCostActual;
    }

    public void setTravelCostActual(BigDecimal travelCostActual) {
        this.travelCostActual = travelCostActual;
    }

    public BigDecimal getTravelCostRemaining() {
        return travelCostRemaining;
    }

    public void setTravelCostRemaining(BigDecimal travelCostRemaining) {
        this.travelCostRemaining = travelCostRemaining;
    }

    public BigDecimal getTravelEaAvailableAmount() {
        return travelEaAvailableAmount;
    }

    public void setTravelEaAvailableAmount(BigDecimal travelEaAvailableAmount) {
        this.travelEaAvailableAmount = travelEaAvailableAmount;
    }

    public BigDecimal getNoTravelCostActual() {
        return noTravelCostActual;
    }

    public void setNoTravelCostActual(BigDecimal noTravelCostActual) {
        this.noTravelCostActual = noTravelCostActual;
    }

    public BigDecimal getNoTravelCostRemaining() {
        return noTravelCostRemaining;
    }

    public void setNoTravelCostRemaining(BigDecimal noTravelCostRemaining) {
        this.noTravelCostRemaining = noTravelCostRemaining;
    }

    public BigDecimal getNoTravelEaAvailableAmount() {
        return noTravelEaAvailableAmount;
    }

    public void setNoTravelEaAvailableAmount(BigDecimal noTravelEaAvailableAmount) {
        this.noTravelEaAvailableAmount = noTravelEaAvailableAmount;
    }

    public BigDecimal getShareCostActual() {
        return shareCostActual;
    }

    public void setShareCostActual(BigDecimal shareCostActual) {
        this.shareCostActual = shareCostActual;
    }

    public BigDecimal getActualCostProportion() {
        return actualCostProportion;
    }

    public void setActualCostProportion(BigDecimal actualCostProportion) {
        this.actualCostProportion = actualCostProportion;
    }

    public BigDecimal getActualProfits() {
        return actualProfits;
    }

    public void setActualProfits(BigDecimal actualProfits) {
        this.actualProfits = actualProfits;
    }

    public BigDecimal getActualProfitsRate() {
        return actualProfitsRate;
    }

    public void setActualProfitsRate(BigDecimal actualProfitsRate) {
        this.actualProfitsRate = actualProfitsRate;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode == null ? null : contractCode.trim();
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getCostExecuteId() {
        return costExecuteId;
    }

    public void setCostExecuteId(Long costExecuteId) {
        this.costExecuteId = costExecuteId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", executeId=").append(executeId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", departmentId=").append(departmentId);
        sb.append(", departmentName=").append(departmentName);
        sb.append(", unitId=").append(unitId);
        sb.append(", unitName=").append(unitName);
        sb.append(", projectType=").append(projectType);
        sb.append(", projectAttribute=").append(projectAttribute);
        sb.append(", projectManager=").append(projectManager);
        sb.append(", projectMilepostId=").append(projectMilepostId);
        sb.append(", projectMilepostName=").append(projectMilepostName);
        sb.append(", projectStatus=").append(projectStatus);
        sb.append(", projectStartDate=").append(projectStartDate);
        sb.append(", projectEndDate=").append(projectEndDate);
        sb.append(", noTaxAmount=").append(noTaxAmount);
        sb.append(", budgetAmount=").append(budgetAmount);
        sb.append(", materialBudget=").append(materialBudget);
        sb.append(", humanBudget=").append(humanBudget);
        sb.append(", travelBudget=").append(travelBudget);
        sb.append(", noTravelBudget=").append(noTravelBudget);
        sb.append(", budgetProfit=").append(budgetProfit);
        sb.append(", budgetProfitRate=").append(budgetProfitRate);
        sb.append(", projectCostAmount=").append(projectCostAmount);
        sb.append(", materialCostActual=").append(materialCostActual);
        sb.append(", materialCostRemaining=").append(materialCostRemaining);
        sb.append(", inHumanCostActual=").append(inHumanCostActual);
        sb.append(", inHumanCostRemaining=").append(inHumanCostRemaining);
        sb.append(", outHumanCostActual=").append(outHumanCostActual);
        sb.append(", outHumanCostRemaining=").append(outHumanCostRemaining);
        sb.append(", travelCostActual=").append(travelCostActual);
        sb.append(", travelCostRemaining=").append(travelCostRemaining);
        sb.append(", travelEaAvailableAmount=").append(travelEaAvailableAmount);
        sb.append(", noTravelCostActual=").append(noTravelCostActual);
        sb.append(", noTravelCostRemaining=").append(noTravelCostRemaining);
        sb.append(", noTravelEaAvailableAmount=").append(noTravelEaAvailableAmount);
        sb.append(", shareCostActual=").append(shareCostActual);
        sb.append(", actualCostProportion=").append(actualCostProportion);
        sb.append(", actualProfits=").append(actualProfits);
        sb.append(", actualProfitsRate=").append(actualProfitsRate);
        sb.append(", contractCode=").append(contractCode);
        sb.append(", ouId=").append(ouId);
        sb.append(", ouName=").append(ouName);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", costExecuteId=").append(costExecuteId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}