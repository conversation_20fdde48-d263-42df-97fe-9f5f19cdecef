package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.ProjectCostHumanItemRecord;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class ProjectCostHumanItemRecordDTO extends ProjectCostHumanItemRecord {

    @ApiModelProperty(value = "预算")
    private BigDecimal budget;

    @ApiModelProperty(value = "类型 1:内部员工,2:人力成本-费用,3:外部人员")
    private Integer type;

}