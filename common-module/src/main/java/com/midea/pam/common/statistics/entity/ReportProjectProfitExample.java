package com.midea.pam.common.statistics.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ReportProjectProfitExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReportProjectProfitExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Long value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Long value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Long value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Long value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Long> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Long> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Long value1, Long value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNull() {
            addCriterion("unit_name is null");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNotNull() {
            addCriterion("unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andUnitNameEqualTo(String value) {
            addCriterion("unit_name =", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotEqualTo(String value) {
            addCriterion("unit_name <>", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThan(String value) {
            addCriterion("unit_name >", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("unit_name >=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThan(String value) {
            addCriterion("unit_name <", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThanOrEqualTo(String value) {
            addCriterion("unit_name <=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLike(String value) {
            addCriterion("unit_name like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotLike(String value) {
            addCriterion("unit_name not like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIn(List<String> values) {
            addCriterion("unit_name in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotIn(List<String> values) {
            addCriterion("unit_name not in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameBetween(String value1, String value2) {
            addCriterion("unit_name between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotBetween(String value1, String value2) {
            addCriterion("unit_name not between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeIsNull() {
            addCriterion("division_code is null");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeIsNotNull() {
            addCriterion("division_code is not null");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeEqualTo(String value) {
            addCriterion("division_code =", value, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeNotEqualTo(String value) {
            addCriterion("division_code <>", value, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeGreaterThan(String value) {
            addCriterion("division_code >", value, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeGreaterThanOrEqualTo(String value) {
            addCriterion("division_code >=", value, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeLessThan(String value) {
            addCriterion("division_code <", value, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeLessThanOrEqualTo(String value) {
            addCriterion("division_code <=", value, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeLike(String value) {
            addCriterion("division_code like", value, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeNotLike(String value) {
            addCriterion("division_code not like", value, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeIn(List<String> values) {
            addCriterion("division_code in", values, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeNotIn(List<String> values) {
            addCriterion("division_code not in", values, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeBetween(String value1, String value2) {
            addCriterion("division_code between", value1, value2, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionCodeNotBetween(String value1, String value2) {
            addCriterion("division_code not between", value1, value2, "divisionCode");
            return (Criteria) this;
        }

        public Criteria andDivisionNameIsNull() {
            addCriterion("division_name is null");
            return (Criteria) this;
        }

        public Criteria andDivisionNameIsNotNull() {
            addCriterion("division_name is not null");
            return (Criteria) this;
        }

        public Criteria andDivisionNameEqualTo(String value) {
            addCriterion("division_name =", value, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameNotEqualTo(String value) {
            addCriterion("division_name <>", value, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameGreaterThan(String value) {
            addCriterion("division_name >", value, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameGreaterThanOrEqualTo(String value) {
            addCriterion("division_name >=", value, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameLessThan(String value) {
            addCriterion("division_name <", value, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameLessThanOrEqualTo(String value) {
            addCriterion("division_name <=", value, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameLike(String value) {
            addCriterion("division_name like", value, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameNotLike(String value) {
            addCriterion("division_name not like", value, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameIn(List<String> values) {
            addCriterion("division_name in", values, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameNotIn(List<String> values) {
            addCriterion("division_name not in", values, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameBetween(String value1, String value2) {
            addCriterion("division_name between", value1, value2, "divisionName");
            return (Criteria) this;
        }

        public Criteria andDivisionNameNotBetween(String value1, String value2) {
            addCriterion("division_name not between", value1, value2, "divisionName");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andPriceTypeIsNull() {
            addCriterion("price_type is null");
            return (Criteria) this;
        }

        public Criteria andPriceTypeIsNotNull() {
            addCriterion("price_type is not null");
            return (Criteria) this;
        }

        public Criteria andPriceTypeEqualTo(String value) {
            addCriterion("price_type =", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeNotEqualTo(String value) {
            addCriterion("price_type <>", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeGreaterThan(String value) {
            addCriterion("price_type >", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeGreaterThanOrEqualTo(String value) {
            addCriterion("price_type >=", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeLessThan(String value) {
            addCriterion("price_type <", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeLessThanOrEqualTo(String value) {
            addCriterion("price_type <=", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeLike(String value) {
            addCriterion("price_type like", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeNotLike(String value) {
            addCriterion("price_type not like", value, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeIn(List<String> values) {
            addCriterion("price_type in", values, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeNotIn(List<String> values) {
            addCriterion("price_type not in", values, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeBetween(String value1, String value2) {
            addCriterion("price_type between", value1, value2, "priceType");
            return (Criteria) this;
        }

        public Criteria andPriceTypeNotBetween(String value1, String value2) {
            addCriterion("price_type not between", value1, value2, "priceType");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andManagerNameIsNull() {
            addCriterion("manager_name is null");
            return (Criteria) this;
        }

        public Criteria andManagerNameIsNotNull() {
            addCriterion("manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andManagerNameEqualTo(String value) {
            addCriterion("manager_name =", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotEqualTo(String value) {
            addCriterion("manager_name <>", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameGreaterThan(String value) {
            addCriterion("manager_name >", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("manager_name >=", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameLessThan(String value) {
            addCriterion("manager_name <", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameLessThanOrEqualTo(String value) {
            addCriterion("manager_name <=", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameLike(String value) {
            addCriterion("manager_name like", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotLike(String value) {
            addCriterion("manager_name not like", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameIn(List<String> values) {
            addCriterion("manager_name in", values, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotIn(List<String> values) {
            addCriterion("manager_name not in", values, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameBetween(String value1, String value2) {
            addCriterion("manager_name between", value1, value2, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotBetween(String value1, String value2) {
            addCriterion("manager_name not between", value1, value2, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerIdIsNull() {
            addCriterion("manager_id is null");
            return (Criteria) this;
        }

        public Criteria andManagerIdIsNotNull() {
            addCriterion("manager_id is not null");
            return (Criteria) this;
        }

        public Criteria andManagerIdEqualTo(Long value) {
            addCriterion("manager_id =", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdNotEqualTo(Long value) {
            addCriterion("manager_id <>", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdGreaterThan(Long value) {
            addCriterion("manager_id >", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("manager_id >=", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdLessThan(Long value) {
            addCriterion("manager_id <", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdLessThanOrEqualTo(Long value) {
            addCriterion("manager_id <=", value, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdIn(List<Long> values) {
            addCriterion("manager_id in", values, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdNotIn(List<Long> values) {
            addCriterion("manager_id not in", values, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdBetween(Long value1, Long value2) {
            addCriterion("manager_id between", value1, value2, "managerId");
            return (Criteria) this;
        }

        public Criteria andManagerIdNotBetween(Long value1, Long value2) {
            addCriterion("manager_id not between", value1, value2, "managerId");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andPreviewFlagIsNull() {
            addCriterion("preview_flag is null");
            return (Criteria) this;
        }

        public Criteria andPreviewFlagIsNotNull() {
            addCriterion("preview_flag is not null");
            return (Criteria) this;
        }

        public Criteria andPreviewFlagEqualTo(Byte value) {
            addCriterion("preview_flag =", value, "previewFlag");
            return (Criteria) this;
        }

        public Criteria andPreviewFlagNotEqualTo(Byte value) {
            addCriterion("preview_flag <>", value, "previewFlag");
            return (Criteria) this;
        }

        public Criteria andPreviewFlagGreaterThan(Byte value) {
            addCriterion("preview_flag >", value, "previewFlag");
            return (Criteria) this;
        }

        public Criteria andPreviewFlagGreaterThanOrEqualTo(Byte value) {
            addCriterion("preview_flag >=", value, "previewFlag");
            return (Criteria) this;
        }

        public Criteria andPreviewFlagLessThan(Byte value) {
            addCriterion("preview_flag <", value, "previewFlag");
            return (Criteria) this;
        }

        public Criteria andPreviewFlagLessThanOrEqualTo(Byte value) {
            addCriterion("preview_flag <=", value, "previewFlag");
            return (Criteria) this;
        }

        public Criteria andPreviewFlagIn(List<Byte> values) {
            addCriterion("preview_flag in", values, "previewFlag");
            return (Criteria) this;
        }

        public Criteria andPreviewFlagNotIn(List<Byte> values) {
            addCriterion("preview_flag not in", values, "previewFlag");
            return (Criteria) this;
        }

        public Criteria andPreviewFlagBetween(Byte value1, Byte value2) {
            addCriterion("preview_flag between", value1, value2, "previewFlag");
            return (Criteria) this;
        }

        public Criteria andPreviewFlagNotBetween(Byte value1, Byte value2) {
            addCriterion("preview_flag not between", value1, value2, "previewFlag");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Long value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Long value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Long value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Long value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Long value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Long value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Long> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Long> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Long value1, Long value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Long value1, Long value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNameIsNull() {
            addCriterion("type_name is null");
            return (Criteria) this;
        }

        public Criteria andTypeNameIsNotNull() {
            addCriterion("type_name is not null");
            return (Criteria) this;
        }

        public Criteria andTypeNameEqualTo(String value) {
            addCriterion("type_name =", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameNotEqualTo(String value) {
            addCriterion("type_name <>", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameGreaterThan(String value) {
            addCriterion("type_name >", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("type_name >=", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameLessThan(String value) {
            addCriterion("type_name <", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameLessThanOrEqualTo(String value) {
            addCriterion("type_name <=", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameLike(String value) {
            addCriterion("type_name like", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameNotLike(String value) {
            addCriterion("type_name not like", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameIn(List<String> values) {
            addCriterion("type_name in", values, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameNotIn(List<String> values) {
            addCriterion("type_name not in", values, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameBetween(String value1, String value2) {
            addCriterion("type_name between", value1, value2, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameNotBetween(String value1, String value2) {
            addCriterion("type_name not between", value1, value2, "typeName");
            return (Criteria) this;
        }

        public Criteria andAmountIsNull() {
            addCriterion("amount is null");
            return (Criteria) this;
        }

        public Criteria andAmountIsNotNull() {
            addCriterion("amount is not null");
            return (Criteria) this;
        }

        public Criteria andAmountEqualTo(BigDecimal value) {
            addCriterion("amount =", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotEqualTo(BigDecimal value) {
            addCriterion("amount <>", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThan(BigDecimal value) {
            addCriterion("amount >", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("amount >=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThan(BigDecimal value) {
            addCriterion("amount <", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("amount <=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountIn(List<BigDecimal> values) {
            addCriterion("amount in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotIn(List<BigDecimal> values) {
            addCriterion("amount not in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount not between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalIsNull() {
            addCriterion("quote_cost_total is null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalIsNotNull() {
            addCriterion("quote_cost_total is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalEqualTo(BigDecimal value) {
            addCriterion("quote_cost_total =", value, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalNotEqualTo(BigDecimal value) {
            addCriterion("quote_cost_total <>", value, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalGreaterThan(BigDecimal value) {
            addCriterion("quote_cost_total >", value, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_total >=", value, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalLessThan(BigDecimal value) {
            addCriterion("quote_cost_total <", value, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_total <=", value, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalIn(List<BigDecimal> values) {
            addCriterion("quote_cost_total in", values, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalNotIn(List<BigDecimal> values) {
            addCriterion("quote_cost_total not in", values, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_total between", value1, value2, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_total not between", value1, value2, "quoteCostTotal");
            return (Criteria) this;
        }

        public Criteria andBudgetChangeNumIsNull() {
            addCriterion("budget_change_num is null");
            return (Criteria) this;
        }

        public Criteria andBudgetChangeNumIsNotNull() {
            addCriterion("budget_change_num is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetChangeNumEqualTo(Integer value) {
            addCriterion("budget_change_num =", value, "budgetChangeNum");
            return (Criteria) this;
        }

        public Criteria andBudgetChangeNumNotEqualTo(Integer value) {
            addCriterion("budget_change_num <>", value, "budgetChangeNum");
            return (Criteria) this;
        }

        public Criteria andBudgetChangeNumGreaterThan(Integer value) {
            addCriterion("budget_change_num >", value, "budgetChangeNum");
            return (Criteria) this;
        }

        public Criteria andBudgetChangeNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("budget_change_num >=", value, "budgetChangeNum");
            return (Criteria) this;
        }

        public Criteria andBudgetChangeNumLessThan(Integer value) {
            addCriterion("budget_change_num <", value, "budgetChangeNum");
            return (Criteria) this;
        }

        public Criteria andBudgetChangeNumLessThanOrEqualTo(Integer value) {
            addCriterion("budget_change_num <=", value, "budgetChangeNum");
            return (Criteria) this;
        }

        public Criteria andBudgetChangeNumIn(List<Integer> values) {
            addCriterion("budget_change_num in", values, "budgetChangeNum");
            return (Criteria) this;
        }

        public Criteria andBudgetChangeNumNotIn(List<Integer> values) {
            addCriterion("budget_change_num not in", values, "budgetChangeNum");
            return (Criteria) this;
        }

        public Criteria andBudgetChangeNumBetween(Integer value1, Integer value2) {
            addCriterion("budget_change_num between", value1, value2, "budgetChangeNum");
            return (Criteria) this;
        }

        public Criteria andBudgetChangeNumNotBetween(Integer value1, Integer value2) {
            addCriterion("budget_change_num not between", value1, value2, "budgetChangeNum");
            return (Criteria) this;
        }

        public Criteria andBudgetCostIsNull() {
            addCriterion("budget_cost is null");
            return (Criteria) this;
        }

        public Criteria andBudgetCostIsNotNull() {
            addCriterion("budget_cost is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetCostEqualTo(BigDecimal value) {
            addCriterion("budget_cost =", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostNotEqualTo(BigDecimal value) {
            addCriterion("budget_cost <>", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostGreaterThan(BigDecimal value) {
            addCriterion("budget_cost >", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_cost >=", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostLessThan(BigDecimal value) {
            addCriterion("budget_cost <", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_cost <=", value, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostIn(List<BigDecimal> values) {
            addCriterion("budget_cost in", values, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostNotIn(List<BigDecimal> values) {
            addCriterion("budget_cost not in", values, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_cost between", value1, value2, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andBudgetCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_cost not between", value1, value2, "budgetCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostIsNull() {
            addCriterion("incurred_cost is null");
            return (Criteria) this;
        }

        public Criteria andIncurredCostIsNotNull() {
            addCriterion("incurred_cost is not null");
            return (Criteria) this;
        }

        public Criteria andIncurredCostEqualTo(BigDecimal value) {
            addCriterion("incurred_cost =", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostNotEqualTo(BigDecimal value) {
            addCriterion("incurred_cost <>", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostGreaterThan(BigDecimal value) {
            addCriterion("incurred_cost >", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_cost >=", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLessThan(BigDecimal value) {
            addCriterion("incurred_cost <", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_cost <=", value, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostIn(List<BigDecimal> values) {
            addCriterion("incurred_cost in", values, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostNotIn(List<BigDecimal> values) {
            addCriterion("incurred_cost not in", values, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_cost between", value1, value2, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andIncurredCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_cost not between", value1, value2, "incurredCost");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountIsNull() {
            addCriterion("confirmed_cost_total_amount is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountIsNotNull() {
            addCriterion("confirmed_cost_total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount =", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount <>", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount >", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount >=", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountLessThan(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount <", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_total_amount <=", value, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountIn(List<BigDecimal> values) {
            addCriterion("confirmed_cost_total_amount in", values, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_cost_total_amount not in", values, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_cost_total_amount between", value1, value2, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_cost_total_amount not between", value1, value2, "confirmedCostTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountIsNull() {
            addCriterion("confirmed_income_total_amount is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountIsNotNull() {
            addCriterion("confirmed_income_total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountEqualTo(BigDecimal value) {
            addCriterion("confirmed_income_total_amount =", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_income_total_amount <>", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("confirmed_income_total_amount >", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_income_total_amount >=", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountLessThan(BigDecimal value) {
            addCriterion("confirmed_income_total_amount <", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_income_total_amount <=", value, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountIn(List<BigDecimal> values) {
            addCriterion("confirmed_income_total_amount in", values, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_income_total_amount not in", values, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_income_total_amount between", value1, value2, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_income_total_amount not between", value1, value2, "confirmedIncomeTotalAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeRatioIsNull() {
            addCriterion("confirmed_income_ratio is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeRatioIsNotNull() {
            addCriterion("confirmed_income_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeRatioEqualTo(BigDecimal value) {
            addCriterion("confirmed_income_ratio =", value, "confirmedIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeRatioNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_income_ratio <>", value, "confirmedIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeRatioGreaterThan(BigDecimal value) {
            addCriterion("confirmed_income_ratio >", value, "confirmedIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_income_ratio >=", value, "confirmedIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeRatioLessThan(BigDecimal value) {
            addCriterion("confirmed_income_ratio <", value, "confirmedIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_income_ratio <=", value, "confirmedIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeRatioIn(List<BigDecimal> values) {
            addCriterion("confirmed_income_ratio in", values, "confirmedIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeRatioNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_income_ratio not in", values, "confirmedIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_income_ratio between", value1, value2, "confirmedIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedIncomeRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_income_ratio not between", value1, value2, "confirmedIncomeRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitAmountIsNull() {
            addCriterion("confirmed_gross_profit_amount is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitAmountIsNotNull() {
            addCriterion("confirmed_gross_profit_amount is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitAmountEqualTo(BigDecimal value) {
            addCriterion("confirmed_gross_profit_amount =", value, "confirmedGrossProfitAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitAmountNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_gross_profit_amount <>", value, "confirmedGrossProfitAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitAmountGreaterThan(BigDecimal value) {
            addCriterion("confirmed_gross_profit_amount >", value, "confirmedGrossProfitAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_gross_profit_amount >=", value, "confirmedGrossProfitAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitAmountLessThan(BigDecimal value) {
            addCriterion("confirmed_gross_profit_amount <", value, "confirmedGrossProfitAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_gross_profit_amount <=", value, "confirmedGrossProfitAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitAmountIn(List<BigDecimal> values) {
            addCriterion("confirmed_gross_profit_amount in", values, "confirmedGrossProfitAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitAmountNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_gross_profit_amount not in", values, "confirmedGrossProfitAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_gross_profit_amount between", value1, value2, "confirmedGrossProfitAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_gross_profit_amount not between", value1, value2, "confirmedGrossProfitAmount");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioIsNull() {
            addCriterion("confirmed_gross_profit_ratio is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioIsNotNull() {
            addCriterion("confirmed_gross_profit_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioEqualTo(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio =", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio <>", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioGreaterThan(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio >", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio >=", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioLessThan(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio <", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_gross_profit_ratio <=", value, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioIn(List<BigDecimal> values) {
            addCriterion("confirmed_gross_profit_ratio in", values, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_gross_profit_ratio not in", values, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_gross_profit_ratio between", value1, value2, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andConfirmedGrossProfitRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_gross_profit_ratio not between", value1, value2, "confirmedGrossProfitRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractCodeIsNull() {
            addCriterion("child_contract_code is null");
            return (Criteria) this;
        }

        public Criteria andChildContractCodeIsNotNull() {
            addCriterion("child_contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andChildContractCodeEqualTo(String value) {
            addCriterion("child_contract_code =", value, "childContractCode");
            return (Criteria) this;
        }

        public Criteria andChildContractCodeNotEqualTo(String value) {
            addCriterion("child_contract_code <>", value, "childContractCode");
            return (Criteria) this;
        }

        public Criteria andChildContractCodeGreaterThan(String value) {
            addCriterion("child_contract_code >", value, "childContractCode");
            return (Criteria) this;
        }

        public Criteria andChildContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("child_contract_code >=", value, "childContractCode");
            return (Criteria) this;
        }

        public Criteria andChildContractCodeLessThan(String value) {
            addCriterion("child_contract_code <", value, "childContractCode");
            return (Criteria) this;
        }

        public Criteria andChildContractCodeLessThanOrEqualTo(String value) {
            addCriterion("child_contract_code <=", value, "childContractCode");
            return (Criteria) this;
        }

        public Criteria andChildContractCodeLike(String value) {
            addCriterion("child_contract_code like", value, "childContractCode");
            return (Criteria) this;
        }

        public Criteria andChildContractCodeNotLike(String value) {
            addCriterion("child_contract_code not like", value, "childContractCode");
            return (Criteria) this;
        }

        public Criteria andChildContractCodeIn(List<String> values) {
            addCriterion("child_contract_code in", values, "childContractCode");
            return (Criteria) this;
        }

        public Criteria andChildContractCodeNotIn(List<String> values) {
            addCriterion("child_contract_code not in", values, "childContractCode");
            return (Criteria) this;
        }

        public Criteria andChildContractCodeBetween(String value1, String value2) {
            addCriterion("child_contract_code between", value1, value2, "childContractCode");
            return (Criteria) this;
        }

        public Criteria andChildContractCodeNotBetween(String value1, String value2) {
            addCriterion("child_contract_code not between", value1, value2, "childContractCode");
            return (Criteria) this;
        }

        public Criteria andChildContractNameIsNull() {
            addCriterion("child_contract_name is null");
            return (Criteria) this;
        }

        public Criteria andChildContractNameIsNotNull() {
            addCriterion("child_contract_name is not null");
            return (Criteria) this;
        }

        public Criteria andChildContractNameEqualTo(String value) {
            addCriterion("child_contract_name =", value, "childContractName");
            return (Criteria) this;
        }

        public Criteria andChildContractNameNotEqualTo(String value) {
            addCriterion("child_contract_name <>", value, "childContractName");
            return (Criteria) this;
        }

        public Criteria andChildContractNameGreaterThan(String value) {
            addCriterion("child_contract_name >", value, "childContractName");
            return (Criteria) this;
        }

        public Criteria andChildContractNameGreaterThanOrEqualTo(String value) {
            addCriterion("child_contract_name >=", value, "childContractName");
            return (Criteria) this;
        }

        public Criteria andChildContractNameLessThan(String value) {
            addCriterion("child_contract_name <", value, "childContractName");
            return (Criteria) this;
        }

        public Criteria andChildContractNameLessThanOrEqualTo(String value) {
            addCriterion("child_contract_name <=", value, "childContractName");
            return (Criteria) this;
        }

        public Criteria andChildContractNameLike(String value) {
            addCriterion("child_contract_name like", value, "childContractName");
            return (Criteria) this;
        }

        public Criteria andChildContractNameNotLike(String value) {
            addCriterion("child_contract_name not like", value, "childContractName");
            return (Criteria) this;
        }

        public Criteria andChildContractNameIn(List<String> values) {
            addCriterion("child_contract_name in", values, "childContractName");
            return (Criteria) this;
        }

        public Criteria andChildContractNameNotIn(List<String> values) {
            addCriterion("child_contract_name not in", values, "childContractName");
            return (Criteria) this;
        }

        public Criteria andChildContractNameBetween(String value1, String value2) {
            addCriterion("child_contract_name between", value1, value2, "childContractName");
            return (Criteria) this;
        }

        public Criteria andChildContractNameNotBetween(String value1, String value2) {
            addCriterion("child_contract_name not between", value1, value2, "childContractName");
            return (Criteria) this;
        }

        public Criteria andChildContractStatusIsNull() {
            addCriterion("child_contract_status is null");
            return (Criteria) this;
        }

        public Criteria andChildContractStatusIsNotNull() {
            addCriterion("child_contract_status is not null");
            return (Criteria) this;
        }

        public Criteria andChildContractStatusEqualTo(Byte value) {
            addCriterion("child_contract_status =", value, "childContractStatus");
            return (Criteria) this;
        }

        public Criteria andChildContractStatusNotEqualTo(Byte value) {
            addCriterion("child_contract_status <>", value, "childContractStatus");
            return (Criteria) this;
        }

        public Criteria andChildContractStatusGreaterThan(Byte value) {
            addCriterion("child_contract_status >", value, "childContractStatus");
            return (Criteria) this;
        }

        public Criteria andChildContractStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("child_contract_status >=", value, "childContractStatus");
            return (Criteria) this;
        }

        public Criteria andChildContractStatusLessThan(Byte value) {
            addCriterion("child_contract_status <", value, "childContractStatus");
            return (Criteria) this;
        }

        public Criteria andChildContractStatusLessThanOrEqualTo(Byte value) {
            addCriterion("child_contract_status <=", value, "childContractStatus");
            return (Criteria) this;
        }

        public Criteria andChildContractStatusIn(List<Byte> values) {
            addCriterion("child_contract_status in", values, "childContractStatus");
            return (Criteria) this;
        }

        public Criteria andChildContractStatusNotIn(List<Byte> values) {
            addCriterion("child_contract_status not in", values, "childContractStatus");
            return (Criteria) this;
        }

        public Criteria andChildContractStatusBetween(Byte value1, Byte value2) {
            addCriterion("child_contract_status between", value1, value2, "childContractStatus");
            return (Criteria) this;
        }

        public Criteria andChildContractStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("child_contract_status not between", value1, value2, "childContractStatus");
            return (Criteria) this;
        }

        public Criteria andChildContractAmountIsNull() {
            addCriterion("child_contract_amount is null");
            return (Criteria) this;
        }

        public Criteria andChildContractAmountIsNotNull() {
            addCriterion("child_contract_amount is not null");
            return (Criteria) this;
        }

        public Criteria andChildContractAmountEqualTo(BigDecimal value) {
            addCriterion("child_contract_amount =", value, "childContractAmount");
            return (Criteria) this;
        }

        public Criteria andChildContractAmountNotEqualTo(BigDecimal value) {
            addCriterion("child_contract_amount <>", value, "childContractAmount");
            return (Criteria) this;
        }

        public Criteria andChildContractAmountGreaterThan(BigDecimal value) {
            addCriterion("child_contract_amount >", value, "childContractAmount");
            return (Criteria) this;
        }

        public Criteria andChildContractAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("child_contract_amount >=", value, "childContractAmount");
            return (Criteria) this;
        }

        public Criteria andChildContractAmountLessThan(BigDecimal value) {
            addCriterion("child_contract_amount <", value, "childContractAmount");
            return (Criteria) this;
        }

        public Criteria andChildContractAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("child_contract_amount <=", value, "childContractAmount");
            return (Criteria) this;
        }

        public Criteria andChildContractAmountIn(List<BigDecimal> values) {
            addCriterion("child_contract_amount in", values, "childContractAmount");
            return (Criteria) this;
        }

        public Criteria andChildContractAmountNotIn(List<BigDecimal> values) {
            addCriterion("child_contract_amount not in", values, "childContractAmount");
            return (Criteria) this;
        }

        public Criteria andChildContractAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("child_contract_amount between", value1, value2, "childContractAmount");
            return (Criteria) this;
        }

        public Criteria andChildContractAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("child_contract_amount not between", value1, value2, "childContractAmount");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceAmountWithtaxIsNull() {
            addCriterion("child_contract_actual_invoice_amount_withtax is null");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceAmountWithtaxIsNotNull() {
            addCriterion("child_contract_actual_invoice_amount_withtax is not null");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceAmountWithtaxEqualTo(BigDecimal value) {
            addCriterion("child_contract_actual_invoice_amount_withtax =", value, "childContractActualInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceAmountWithtaxNotEqualTo(BigDecimal value) {
            addCriterion("child_contract_actual_invoice_amount_withtax <>", value, "childContractActualInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceAmountWithtaxGreaterThan(BigDecimal value) {
            addCriterion("child_contract_actual_invoice_amount_withtax >", value, "childContractActualInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceAmountWithtaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("child_contract_actual_invoice_amount_withtax >=", value, "childContractActualInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceAmountWithtaxLessThan(BigDecimal value) {
            addCriterion("child_contract_actual_invoice_amount_withtax <", value, "childContractActualInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceAmountWithtaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("child_contract_actual_invoice_amount_withtax <=", value, "childContractActualInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceAmountWithtaxIn(List<BigDecimal> values) {
            addCriterion("child_contract_actual_invoice_amount_withtax in", values, "childContractActualInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceAmountWithtaxNotIn(List<BigDecimal> values) {
            addCriterion("child_contract_actual_invoice_amount_withtax not in", values, "childContractActualInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceAmountWithtaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("child_contract_actual_invoice_amount_withtax between", value1, value2, "childContractActualInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceAmountWithtaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("child_contract_actual_invoice_amount_withtax not between", value1, value2, "childContractActualInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainInvoiceAmountWithtaxIsNull() {
            addCriterion("child_contract_remain_invoice_amount_withtax is null");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainInvoiceAmountWithtaxIsNotNull() {
            addCriterion("child_contract_remain_invoice_amount_withtax is not null");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainInvoiceAmountWithtaxEqualTo(BigDecimal value) {
            addCriterion("child_contract_remain_invoice_amount_withtax =", value, "childContractRemainInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainInvoiceAmountWithtaxNotEqualTo(BigDecimal value) {
            addCriterion("child_contract_remain_invoice_amount_withtax <>", value, "childContractRemainInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainInvoiceAmountWithtaxGreaterThan(BigDecimal value) {
            addCriterion("child_contract_remain_invoice_amount_withtax >", value, "childContractRemainInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainInvoiceAmountWithtaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("child_contract_remain_invoice_amount_withtax >=", value, "childContractRemainInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainInvoiceAmountWithtaxLessThan(BigDecimal value) {
            addCriterion("child_contract_remain_invoice_amount_withtax <", value, "childContractRemainInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainInvoiceAmountWithtaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("child_contract_remain_invoice_amount_withtax <=", value, "childContractRemainInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainInvoiceAmountWithtaxIn(List<BigDecimal> values) {
            addCriterion("child_contract_remain_invoice_amount_withtax in", values, "childContractRemainInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainInvoiceAmountWithtaxNotIn(List<BigDecimal> values) {
            addCriterion("child_contract_remain_invoice_amount_withtax not in", values, "childContractRemainInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainInvoiceAmountWithtaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("child_contract_remain_invoice_amount_withtax between", value1, value2, "childContractRemainInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainInvoiceAmountWithtaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("child_contract_remain_invoice_amount_withtax not between", value1, value2, "childContractRemainInvoiceAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceRatioIsNull() {
            addCriterion("child_contract_actual_invoice_ratio is null");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceRatioIsNotNull() {
            addCriterion("child_contract_actual_invoice_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceRatioEqualTo(BigDecimal value) {
            addCriterion("child_contract_actual_invoice_ratio =", value, "childContractActualInvoiceRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceRatioNotEqualTo(BigDecimal value) {
            addCriterion("child_contract_actual_invoice_ratio <>", value, "childContractActualInvoiceRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceRatioGreaterThan(BigDecimal value) {
            addCriterion("child_contract_actual_invoice_ratio >", value, "childContractActualInvoiceRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("child_contract_actual_invoice_ratio >=", value, "childContractActualInvoiceRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceRatioLessThan(BigDecimal value) {
            addCriterion("child_contract_actual_invoice_ratio <", value, "childContractActualInvoiceRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("child_contract_actual_invoice_ratio <=", value, "childContractActualInvoiceRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceRatioIn(List<BigDecimal> values) {
            addCriterion("child_contract_actual_invoice_ratio in", values, "childContractActualInvoiceRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceRatioNotIn(List<BigDecimal> values) {
            addCriterion("child_contract_actual_invoice_ratio not in", values, "childContractActualInvoiceRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("child_contract_actual_invoice_ratio between", value1, value2, "childContractActualInvoiceRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualInvoiceRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("child_contract_actual_invoice_ratio not between", value1, value2, "childContractActualInvoiceRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptAmountWithtaxIsNull() {
            addCriterion("child_contract_actual_receipt_amount_withtax is null");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptAmountWithtaxIsNotNull() {
            addCriterion("child_contract_actual_receipt_amount_withtax is not null");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptAmountWithtaxEqualTo(BigDecimal value) {
            addCriterion("child_contract_actual_receipt_amount_withtax =", value, "childContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptAmountWithtaxNotEqualTo(BigDecimal value) {
            addCriterion("child_contract_actual_receipt_amount_withtax <>", value, "childContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptAmountWithtaxGreaterThan(BigDecimal value) {
            addCriterion("child_contract_actual_receipt_amount_withtax >", value, "childContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptAmountWithtaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("child_contract_actual_receipt_amount_withtax >=", value, "childContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptAmountWithtaxLessThan(BigDecimal value) {
            addCriterion("child_contract_actual_receipt_amount_withtax <", value, "childContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptAmountWithtaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("child_contract_actual_receipt_amount_withtax <=", value, "childContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptAmountWithtaxIn(List<BigDecimal> values) {
            addCriterion("child_contract_actual_receipt_amount_withtax in", values, "childContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptAmountWithtaxNotIn(List<BigDecimal> values) {
            addCriterion("child_contract_actual_receipt_amount_withtax not in", values, "childContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptAmountWithtaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("child_contract_actual_receipt_amount_withtax between", value1, value2, "childContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptAmountWithtaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("child_contract_actual_receipt_amount_withtax not between", value1, value2, "childContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainReceiptAmountWithtaxIsNull() {
            addCriterion("child_contract_remain_receipt_amount_withtax is null");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainReceiptAmountWithtaxIsNotNull() {
            addCriterion("child_contract_remain_receipt_amount_withtax is not null");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainReceiptAmountWithtaxEqualTo(BigDecimal value) {
            addCriterion("child_contract_remain_receipt_amount_withtax =", value, "childContractRemainReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainReceiptAmountWithtaxNotEqualTo(BigDecimal value) {
            addCriterion("child_contract_remain_receipt_amount_withtax <>", value, "childContractRemainReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainReceiptAmountWithtaxGreaterThan(BigDecimal value) {
            addCriterion("child_contract_remain_receipt_amount_withtax >", value, "childContractRemainReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainReceiptAmountWithtaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("child_contract_remain_receipt_amount_withtax >=", value, "childContractRemainReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainReceiptAmountWithtaxLessThan(BigDecimal value) {
            addCriterion("child_contract_remain_receipt_amount_withtax <", value, "childContractRemainReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainReceiptAmountWithtaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("child_contract_remain_receipt_amount_withtax <=", value, "childContractRemainReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainReceiptAmountWithtaxIn(List<BigDecimal> values) {
            addCriterion("child_contract_remain_receipt_amount_withtax in", values, "childContractRemainReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainReceiptAmountWithtaxNotIn(List<BigDecimal> values) {
            addCriterion("child_contract_remain_receipt_amount_withtax not in", values, "childContractRemainReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainReceiptAmountWithtaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("child_contract_remain_receipt_amount_withtax between", value1, value2, "childContractRemainReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractRemainReceiptAmountWithtaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("child_contract_remain_receipt_amount_withtax not between", value1, value2, "childContractRemainReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptRatioIsNull() {
            addCriterion("child_contract_actual_receipt_ratio is null");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptRatioIsNotNull() {
            addCriterion("child_contract_actual_receipt_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptRatioEqualTo(BigDecimal value) {
            addCriterion("child_contract_actual_receipt_ratio =", value, "childContractActualReceiptRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptRatioNotEqualTo(BigDecimal value) {
            addCriterion("child_contract_actual_receipt_ratio <>", value, "childContractActualReceiptRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptRatioGreaterThan(BigDecimal value) {
            addCriterion("child_contract_actual_receipt_ratio >", value, "childContractActualReceiptRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("child_contract_actual_receipt_ratio >=", value, "childContractActualReceiptRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptRatioLessThan(BigDecimal value) {
            addCriterion("child_contract_actual_receipt_ratio <", value, "childContractActualReceiptRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("child_contract_actual_receipt_ratio <=", value, "childContractActualReceiptRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptRatioIn(List<BigDecimal> values) {
            addCriterion("child_contract_actual_receipt_ratio in", values, "childContractActualReceiptRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptRatioNotIn(List<BigDecimal> values) {
            addCriterion("child_contract_actual_receipt_ratio not in", values, "childContractActualReceiptRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("child_contract_actual_receipt_ratio between", value1, value2, "childContractActualReceiptRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractActualReceiptRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("child_contract_actual_receipt_ratio not between", value1, value2, "childContractActualReceiptRatio");
            return (Criteria) this;
        }

        public Criteria andChildContractInvoiceSubReceiptAmountWithtaxIsNull() {
            addCriterion("child_contract_invoice_sub_receipt_amount_withtax is null");
            return (Criteria) this;
        }

        public Criteria andChildContractInvoiceSubReceiptAmountWithtaxIsNotNull() {
            addCriterion("child_contract_invoice_sub_receipt_amount_withtax is not null");
            return (Criteria) this;
        }

        public Criteria andChildContractInvoiceSubReceiptAmountWithtaxEqualTo(BigDecimal value) {
            addCriterion("child_contract_invoice_sub_receipt_amount_withtax =", value, "childContractInvoiceSubReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractInvoiceSubReceiptAmountWithtaxNotEqualTo(BigDecimal value) {
            addCriterion("child_contract_invoice_sub_receipt_amount_withtax <>", value, "childContractInvoiceSubReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractInvoiceSubReceiptAmountWithtaxGreaterThan(BigDecimal value) {
            addCriterion("child_contract_invoice_sub_receipt_amount_withtax >", value, "childContractInvoiceSubReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractInvoiceSubReceiptAmountWithtaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("child_contract_invoice_sub_receipt_amount_withtax >=", value, "childContractInvoiceSubReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractInvoiceSubReceiptAmountWithtaxLessThan(BigDecimal value) {
            addCriterion("child_contract_invoice_sub_receipt_amount_withtax <", value, "childContractInvoiceSubReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractInvoiceSubReceiptAmountWithtaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("child_contract_invoice_sub_receipt_amount_withtax <=", value, "childContractInvoiceSubReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractInvoiceSubReceiptAmountWithtaxIn(List<BigDecimal> values) {
            addCriterion("child_contract_invoice_sub_receipt_amount_withtax in", values, "childContractInvoiceSubReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractInvoiceSubReceiptAmountWithtaxNotIn(List<BigDecimal> values) {
            addCriterion("child_contract_invoice_sub_receipt_amount_withtax not in", values, "childContractInvoiceSubReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractInvoiceSubReceiptAmountWithtaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("child_contract_invoice_sub_receipt_amount_withtax between", value1, value2, "childContractInvoiceSubReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andChildContractInvoiceSubReceiptAmountWithtaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("child_contract_invoice_sub_receipt_amount_withtax not between", value1, value2, "childContractInvoiceSubReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andParentContractCodeIsNull() {
            addCriterion("parent_contract_code is null");
            return (Criteria) this;
        }

        public Criteria andParentContractCodeIsNotNull() {
            addCriterion("parent_contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andParentContractCodeEqualTo(String value) {
            addCriterion("parent_contract_code =", value, "parentContractCode");
            return (Criteria) this;
        }

        public Criteria andParentContractCodeNotEqualTo(String value) {
            addCriterion("parent_contract_code <>", value, "parentContractCode");
            return (Criteria) this;
        }

        public Criteria andParentContractCodeGreaterThan(String value) {
            addCriterion("parent_contract_code >", value, "parentContractCode");
            return (Criteria) this;
        }

        public Criteria andParentContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("parent_contract_code >=", value, "parentContractCode");
            return (Criteria) this;
        }

        public Criteria andParentContractCodeLessThan(String value) {
            addCriterion("parent_contract_code <", value, "parentContractCode");
            return (Criteria) this;
        }

        public Criteria andParentContractCodeLessThanOrEqualTo(String value) {
            addCriterion("parent_contract_code <=", value, "parentContractCode");
            return (Criteria) this;
        }

        public Criteria andParentContractCodeLike(String value) {
            addCriterion("parent_contract_code like", value, "parentContractCode");
            return (Criteria) this;
        }

        public Criteria andParentContractCodeNotLike(String value) {
            addCriterion("parent_contract_code not like", value, "parentContractCode");
            return (Criteria) this;
        }

        public Criteria andParentContractCodeIn(List<String> values) {
            addCriterion("parent_contract_code in", values, "parentContractCode");
            return (Criteria) this;
        }

        public Criteria andParentContractCodeNotIn(List<String> values) {
            addCriterion("parent_contract_code not in", values, "parentContractCode");
            return (Criteria) this;
        }

        public Criteria andParentContractCodeBetween(String value1, String value2) {
            addCriterion("parent_contract_code between", value1, value2, "parentContractCode");
            return (Criteria) this;
        }

        public Criteria andParentContractCodeNotBetween(String value1, String value2) {
            addCriterion("parent_contract_code not between", value1, value2, "parentContractCode");
            return (Criteria) this;
        }

        public Criteria andParentContractStatusIsNull() {
            addCriterion("parent_contract_status is null");
            return (Criteria) this;
        }

        public Criteria andParentContractStatusIsNotNull() {
            addCriterion("parent_contract_status is not null");
            return (Criteria) this;
        }

        public Criteria andParentContractStatusEqualTo(Byte value) {
            addCriterion("parent_contract_status =", value, "parentContractStatus");
            return (Criteria) this;
        }

        public Criteria andParentContractStatusNotEqualTo(Byte value) {
            addCriterion("parent_contract_status <>", value, "parentContractStatus");
            return (Criteria) this;
        }

        public Criteria andParentContractStatusGreaterThan(Byte value) {
            addCriterion("parent_contract_status >", value, "parentContractStatus");
            return (Criteria) this;
        }

        public Criteria andParentContractStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("parent_contract_status >=", value, "parentContractStatus");
            return (Criteria) this;
        }

        public Criteria andParentContractStatusLessThan(Byte value) {
            addCriterion("parent_contract_status <", value, "parentContractStatus");
            return (Criteria) this;
        }

        public Criteria andParentContractStatusLessThanOrEqualTo(Byte value) {
            addCriterion("parent_contract_status <=", value, "parentContractStatus");
            return (Criteria) this;
        }

        public Criteria andParentContractStatusIn(List<Byte> values) {
            addCriterion("parent_contract_status in", values, "parentContractStatus");
            return (Criteria) this;
        }

        public Criteria andParentContractStatusNotIn(List<Byte> values) {
            addCriterion("parent_contract_status not in", values, "parentContractStatus");
            return (Criteria) this;
        }

        public Criteria andParentContractStatusBetween(Byte value1, Byte value2) {
            addCriterion("parent_contract_status between", value1, value2, "parentContractStatus");
            return (Criteria) this;
        }

        public Criteria andParentContractStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("parent_contract_status not between", value1, value2, "parentContractStatus");
            return (Criteria) this;
        }

        public Criteria andParentContractAmountIsNull() {
            addCriterion("parent_contract_amount is null");
            return (Criteria) this;
        }

        public Criteria andParentContractAmountIsNotNull() {
            addCriterion("parent_contract_amount is not null");
            return (Criteria) this;
        }

        public Criteria andParentContractAmountEqualTo(BigDecimal value) {
            addCriterion("parent_contract_amount =", value, "parentContractAmount");
            return (Criteria) this;
        }

        public Criteria andParentContractAmountNotEqualTo(BigDecimal value) {
            addCriterion("parent_contract_amount <>", value, "parentContractAmount");
            return (Criteria) this;
        }

        public Criteria andParentContractAmountGreaterThan(BigDecimal value) {
            addCriterion("parent_contract_amount >", value, "parentContractAmount");
            return (Criteria) this;
        }

        public Criteria andParentContractAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("parent_contract_amount >=", value, "parentContractAmount");
            return (Criteria) this;
        }

        public Criteria andParentContractAmountLessThan(BigDecimal value) {
            addCriterion("parent_contract_amount <", value, "parentContractAmount");
            return (Criteria) this;
        }

        public Criteria andParentContractAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("parent_contract_amount <=", value, "parentContractAmount");
            return (Criteria) this;
        }

        public Criteria andParentContractAmountIn(List<BigDecimal> values) {
            addCriterion("parent_contract_amount in", values, "parentContractAmount");
            return (Criteria) this;
        }

        public Criteria andParentContractAmountNotIn(List<BigDecimal> values) {
            addCriterion("parent_contract_amount not in", values, "parentContractAmount");
            return (Criteria) this;
        }

        public Criteria andParentContractAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("parent_contract_amount between", value1, value2, "parentContractAmount");
            return (Criteria) this;
        }

        public Criteria andParentContractAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("parent_contract_amount not between", value1, value2, "parentContractAmount");
            return (Criteria) this;
        }

        public Criteria andParentContractActualReceiptAmountWithtaxIsNull() {
            addCriterion("parent_contract_actual_receipt_amount_withtax is null");
            return (Criteria) this;
        }

        public Criteria andParentContractActualReceiptAmountWithtaxIsNotNull() {
            addCriterion("parent_contract_actual_receipt_amount_withtax is not null");
            return (Criteria) this;
        }

        public Criteria andParentContractActualReceiptAmountWithtaxEqualTo(BigDecimal value) {
            addCriterion("parent_contract_actual_receipt_amount_withtax =", value, "parentContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andParentContractActualReceiptAmountWithtaxNotEqualTo(BigDecimal value) {
            addCriterion("parent_contract_actual_receipt_amount_withtax <>", value, "parentContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andParentContractActualReceiptAmountWithtaxGreaterThan(BigDecimal value) {
            addCriterion("parent_contract_actual_receipt_amount_withtax >", value, "parentContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andParentContractActualReceiptAmountWithtaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("parent_contract_actual_receipt_amount_withtax >=", value, "parentContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andParentContractActualReceiptAmountWithtaxLessThan(BigDecimal value) {
            addCriterion("parent_contract_actual_receipt_amount_withtax <", value, "parentContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andParentContractActualReceiptAmountWithtaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("parent_contract_actual_receipt_amount_withtax <=", value, "parentContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andParentContractActualReceiptAmountWithtaxIn(List<BigDecimal> values) {
            addCriterion("parent_contract_actual_receipt_amount_withtax in", values, "parentContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andParentContractActualReceiptAmountWithtaxNotIn(List<BigDecimal> values) {
            addCriterion("parent_contract_actual_receipt_amount_withtax not in", values, "parentContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andParentContractActualReceiptAmountWithtaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("parent_contract_actual_receipt_amount_withtax between", value1, value2, "parentContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andParentContractActualReceiptAmountWithtaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("parent_contract_actual_receipt_amount_withtax not between", value1, value2, "parentContractActualReceiptAmountWithtax");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerIsInnerIsNull() {
            addCriterion("customer_is_inner is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIsInnerIsNotNull() {
            addCriterion("customer_is_inner is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIsInnerEqualTo(Boolean value) {
            addCriterion("customer_is_inner =", value, "customerIsInner");
            return (Criteria) this;
        }

        public Criteria andCustomerIsInnerNotEqualTo(Boolean value) {
            addCriterion("customer_is_inner <>", value, "customerIsInner");
            return (Criteria) this;
        }

        public Criteria andCustomerIsInnerGreaterThan(Boolean value) {
            addCriterion("customer_is_inner >", value, "customerIsInner");
            return (Criteria) this;
        }

        public Criteria andCustomerIsInnerGreaterThanOrEqualTo(Boolean value) {
            addCriterion("customer_is_inner >=", value, "customerIsInner");
            return (Criteria) this;
        }

        public Criteria andCustomerIsInnerLessThan(Boolean value) {
            addCriterion("customer_is_inner <", value, "customerIsInner");
            return (Criteria) this;
        }

        public Criteria andCustomerIsInnerLessThanOrEqualTo(Boolean value) {
            addCriterion("customer_is_inner <=", value, "customerIsInner");
            return (Criteria) this;
        }

        public Criteria andCustomerIsInnerIn(List<Boolean> values) {
            addCriterion("customer_is_inner in", values, "customerIsInner");
            return (Criteria) this;
        }

        public Criteria andCustomerIsInnerNotIn(List<Boolean> values) {
            addCriterion("customer_is_inner not in", values, "customerIsInner");
            return (Criteria) this;
        }

        public Criteria andCustomerIsInnerBetween(Boolean value1, Boolean value2) {
            addCriterion("customer_is_inner between", value1, value2, "customerIsInner");
            return (Criteria) this;
        }

        public Criteria andCustomerIsInnerNotBetween(Boolean value1, Boolean value2) {
            addCriterion("customer_is_inner not between", value1, value2, "customerIsInner");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxIsNull() {
            addCriterion("quote_cost_hardware_without_tax is null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxIsNotNull() {
            addCriterion("quote_cost_hardware_without_tax is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxEqualTo(BigDecimal value) {
            addCriterion("quote_cost_hardware_without_tax =", value, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxNotEqualTo(BigDecimal value) {
            addCriterion("quote_cost_hardware_without_tax <>", value, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxGreaterThan(BigDecimal value) {
            addCriterion("quote_cost_hardware_without_tax >", value, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_hardware_without_tax >=", value, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxLessThan(BigDecimal value) {
            addCriterion("quote_cost_hardware_without_tax <", value, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_hardware_without_tax <=", value, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxIn(List<BigDecimal> values) {
            addCriterion("quote_cost_hardware_without_tax in", values, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxNotIn(List<BigDecimal> values) {
            addCriterion("quote_cost_hardware_without_tax not in", values, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_hardware_without_tax between", value1, value2, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostHardwareWithoutTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_hardware_without_tax not between", value1, value2, "quoteCostHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxIsNull() {
            addCriterion("quote_cost_labour_without_tax is null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxIsNotNull() {
            addCriterion("quote_cost_labour_without_tax is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxEqualTo(BigDecimal value) {
            addCriterion("quote_cost_labour_without_tax =", value, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxNotEqualTo(BigDecimal value) {
            addCriterion("quote_cost_labour_without_tax <>", value, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxGreaterThan(BigDecimal value) {
            addCriterion("quote_cost_labour_without_tax >", value, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_labour_without_tax >=", value, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxLessThan(BigDecimal value) {
            addCriterion("quote_cost_labour_without_tax <", value, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_labour_without_tax <=", value, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxIn(List<BigDecimal> values) {
            addCriterion("quote_cost_labour_without_tax in", values, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxNotIn(List<BigDecimal> values) {
            addCriterion("quote_cost_labour_without_tax not in", values, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_labour_without_tax between", value1, value2, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostLabourWithoutTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_labour_without_tax not between", value1, value2, "quoteCostLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxIsNull() {
            addCriterion("quote_cost_travel_without_tax is null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxIsNotNull() {
            addCriterion("quote_cost_travel_without_tax is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxEqualTo(BigDecimal value) {
            addCriterion("quote_cost_travel_without_tax =", value, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxNotEqualTo(BigDecimal value) {
            addCriterion("quote_cost_travel_without_tax <>", value, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxGreaterThan(BigDecimal value) {
            addCriterion("quote_cost_travel_without_tax >", value, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_travel_without_tax >=", value, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxLessThan(BigDecimal value) {
            addCriterion("quote_cost_travel_without_tax <", value, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_travel_without_tax <=", value, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxIn(List<BigDecimal> values) {
            addCriterion("quote_cost_travel_without_tax in", values, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxNotIn(List<BigDecimal> values) {
            addCriterion("quote_cost_travel_without_tax not in", values, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_travel_without_tax between", value1, value2, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostTravelWithoutTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_travel_without_tax not between", value1, value2, "quoteCostTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxIsNull() {
            addCriterion("quote_cost_other_without_tax is null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxIsNotNull() {
            addCriterion("quote_cost_other_without_tax is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxEqualTo(BigDecimal value) {
            addCriterion("quote_cost_other_without_tax =", value, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxNotEqualTo(BigDecimal value) {
            addCriterion("quote_cost_other_without_tax <>", value, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxGreaterThan(BigDecimal value) {
            addCriterion("quote_cost_other_without_tax >", value, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_other_without_tax >=", value, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxLessThan(BigDecimal value) {
            addCriterion("quote_cost_other_without_tax <", value, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("quote_cost_other_without_tax <=", value, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxIn(List<BigDecimal> values) {
            addCriterion("quote_cost_other_without_tax in", values, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxNotIn(List<BigDecimal> values) {
            addCriterion("quote_cost_other_without_tax not in", values, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_other_without_tax between", value1, value2, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andQuoteCostOtherWithoutTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("quote_cost_other_without_tax not between", value1, value2, "quoteCostOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetHardwareWithoutTaxIsNull() {
            addCriterion("project_budget_hardware_without_tax is null");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetHardwareWithoutTaxIsNotNull() {
            addCriterion("project_budget_hardware_without_tax is not null");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetHardwareWithoutTaxEqualTo(BigDecimal value) {
            addCriterion("project_budget_hardware_without_tax =", value, "projectBudgetHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetHardwareWithoutTaxNotEqualTo(BigDecimal value) {
            addCriterion("project_budget_hardware_without_tax <>", value, "projectBudgetHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetHardwareWithoutTaxGreaterThan(BigDecimal value) {
            addCriterion("project_budget_hardware_without_tax >", value, "projectBudgetHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetHardwareWithoutTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_budget_hardware_without_tax >=", value, "projectBudgetHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetHardwareWithoutTaxLessThan(BigDecimal value) {
            addCriterion("project_budget_hardware_without_tax <", value, "projectBudgetHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetHardwareWithoutTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_budget_hardware_without_tax <=", value, "projectBudgetHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetHardwareWithoutTaxIn(List<BigDecimal> values) {
            addCriterion("project_budget_hardware_without_tax in", values, "projectBudgetHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetHardwareWithoutTaxNotIn(List<BigDecimal> values) {
            addCriterion("project_budget_hardware_without_tax not in", values, "projectBudgetHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetHardwareWithoutTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_budget_hardware_without_tax between", value1, value2, "projectBudgetHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetHardwareWithoutTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_budget_hardware_without_tax not between", value1, value2, "projectBudgetHardwareWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetLabourWithoutTaxIsNull() {
            addCriterion("project_budget_labour_without_tax is null");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetLabourWithoutTaxIsNotNull() {
            addCriterion("project_budget_labour_without_tax is not null");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetLabourWithoutTaxEqualTo(BigDecimal value) {
            addCriterion("project_budget_labour_without_tax =", value, "projectBudgetLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetLabourWithoutTaxNotEqualTo(BigDecimal value) {
            addCriterion("project_budget_labour_without_tax <>", value, "projectBudgetLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetLabourWithoutTaxGreaterThan(BigDecimal value) {
            addCriterion("project_budget_labour_without_tax >", value, "projectBudgetLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetLabourWithoutTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_budget_labour_without_tax >=", value, "projectBudgetLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetLabourWithoutTaxLessThan(BigDecimal value) {
            addCriterion("project_budget_labour_without_tax <", value, "projectBudgetLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetLabourWithoutTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_budget_labour_without_tax <=", value, "projectBudgetLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetLabourWithoutTaxIn(List<BigDecimal> values) {
            addCriterion("project_budget_labour_without_tax in", values, "projectBudgetLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetLabourWithoutTaxNotIn(List<BigDecimal> values) {
            addCriterion("project_budget_labour_without_tax not in", values, "projectBudgetLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetLabourWithoutTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_budget_labour_without_tax between", value1, value2, "projectBudgetLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetLabourWithoutTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_budget_labour_without_tax not between", value1, value2, "projectBudgetLabourWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTravelWithoutTaxIsNull() {
            addCriterion("project_budget_travel_without_tax is null");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTravelWithoutTaxIsNotNull() {
            addCriterion("project_budget_travel_without_tax is not null");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTravelWithoutTaxEqualTo(BigDecimal value) {
            addCriterion("project_budget_travel_without_tax =", value, "projectBudgetTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTravelWithoutTaxNotEqualTo(BigDecimal value) {
            addCriterion("project_budget_travel_without_tax <>", value, "projectBudgetTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTravelWithoutTaxGreaterThan(BigDecimal value) {
            addCriterion("project_budget_travel_without_tax >", value, "projectBudgetTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTravelWithoutTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_budget_travel_without_tax >=", value, "projectBudgetTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTravelWithoutTaxLessThan(BigDecimal value) {
            addCriterion("project_budget_travel_without_tax <", value, "projectBudgetTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTravelWithoutTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_budget_travel_without_tax <=", value, "projectBudgetTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTravelWithoutTaxIn(List<BigDecimal> values) {
            addCriterion("project_budget_travel_without_tax in", values, "projectBudgetTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTravelWithoutTaxNotIn(List<BigDecimal> values) {
            addCriterion("project_budget_travel_without_tax not in", values, "projectBudgetTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTravelWithoutTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_budget_travel_without_tax between", value1, value2, "projectBudgetTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTravelWithoutTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_budget_travel_without_tax not between", value1, value2, "projectBudgetTravelWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetOtherWithoutTaxIsNull() {
            addCriterion("project_budget_other_without_tax is null");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetOtherWithoutTaxIsNotNull() {
            addCriterion("project_budget_other_without_tax is not null");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetOtherWithoutTaxEqualTo(BigDecimal value) {
            addCriterion("project_budget_other_without_tax =", value, "projectBudgetOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetOtherWithoutTaxNotEqualTo(BigDecimal value) {
            addCriterion("project_budget_other_without_tax <>", value, "projectBudgetOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetOtherWithoutTaxGreaterThan(BigDecimal value) {
            addCriterion("project_budget_other_without_tax >", value, "projectBudgetOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetOtherWithoutTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("project_budget_other_without_tax >=", value, "projectBudgetOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetOtherWithoutTaxLessThan(BigDecimal value) {
            addCriterion("project_budget_other_without_tax <", value, "projectBudgetOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetOtherWithoutTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("project_budget_other_without_tax <=", value, "projectBudgetOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetOtherWithoutTaxIn(List<BigDecimal> values) {
            addCriterion("project_budget_other_without_tax in", values, "projectBudgetOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetOtherWithoutTaxNotIn(List<BigDecimal> values) {
            addCriterion("project_budget_other_without_tax not in", values, "projectBudgetOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetOtherWithoutTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_budget_other_without_tax between", value1, value2, "projectBudgetOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetOtherWithoutTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("project_budget_other_without_tax not between", value1, value2, "projectBudgetOtherWithoutTax");
            return (Criteria) this;
        }

        public Criteria andIncurredCostHardwareIsNull() {
            addCriterion("incurred_cost_hardware is null");
            return (Criteria) this;
        }

        public Criteria andIncurredCostHardwareIsNotNull() {
            addCriterion("incurred_cost_hardware is not null");
            return (Criteria) this;
        }

        public Criteria andIncurredCostHardwareEqualTo(BigDecimal value) {
            addCriterion("incurred_cost_hardware =", value, "incurredCostHardware");
            return (Criteria) this;
        }

        public Criteria andIncurredCostHardwareNotEqualTo(BigDecimal value) {
            addCriterion("incurred_cost_hardware <>", value, "incurredCostHardware");
            return (Criteria) this;
        }

        public Criteria andIncurredCostHardwareGreaterThan(BigDecimal value) {
            addCriterion("incurred_cost_hardware >", value, "incurredCostHardware");
            return (Criteria) this;
        }

        public Criteria andIncurredCostHardwareGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_cost_hardware >=", value, "incurredCostHardware");
            return (Criteria) this;
        }

        public Criteria andIncurredCostHardwareLessThan(BigDecimal value) {
            addCriterion("incurred_cost_hardware <", value, "incurredCostHardware");
            return (Criteria) this;
        }

        public Criteria andIncurredCostHardwareLessThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_cost_hardware <=", value, "incurredCostHardware");
            return (Criteria) this;
        }

        public Criteria andIncurredCostHardwareIn(List<BigDecimal> values) {
            addCriterion("incurred_cost_hardware in", values, "incurredCostHardware");
            return (Criteria) this;
        }

        public Criteria andIncurredCostHardwareNotIn(List<BigDecimal> values) {
            addCriterion("incurred_cost_hardware not in", values, "incurredCostHardware");
            return (Criteria) this;
        }

        public Criteria andIncurredCostHardwareBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_cost_hardware between", value1, value2, "incurredCostHardware");
            return (Criteria) this;
        }

        public Criteria andIncurredCostHardwareNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_cost_hardware not between", value1, value2, "incurredCostHardware");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLabourIsNull() {
            addCriterion("incurred_cost_labour is null");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLabourIsNotNull() {
            addCriterion("incurred_cost_labour is not null");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLabourEqualTo(BigDecimal value) {
            addCriterion("incurred_cost_labour =", value, "incurredCostLabour");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLabourNotEqualTo(BigDecimal value) {
            addCriterion("incurred_cost_labour <>", value, "incurredCostLabour");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLabourGreaterThan(BigDecimal value) {
            addCriterion("incurred_cost_labour >", value, "incurredCostLabour");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLabourGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_cost_labour >=", value, "incurredCostLabour");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLabourLessThan(BigDecimal value) {
            addCriterion("incurred_cost_labour <", value, "incurredCostLabour");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLabourLessThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_cost_labour <=", value, "incurredCostLabour");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLabourIn(List<BigDecimal> values) {
            addCriterion("incurred_cost_labour in", values, "incurredCostLabour");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLabourNotIn(List<BigDecimal> values) {
            addCriterion("incurred_cost_labour not in", values, "incurredCostLabour");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLabourBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_cost_labour between", value1, value2, "incurredCostLabour");
            return (Criteria) this;
        }

        public Criteria andIncurredCostLabourNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_cost_labour not between", value1, value2, "incurredCostLabour");
            return (Criteria) this;
        }

        public Criteria andIncurredCostTravelIsNull() {
            addCriterion("incurred_cost_travel is null");
            return (Criteria) this;
        }

        public Criteria andIncurredCostTravelIsNotNull() {
            addCriterion("incurred_cost_travel is not null");
            return (Criteria) this;
        }

        public Criteria andIncurredCostTravelEqualTo(BigDecimal value) {
            addCriterion("incurred_cost_travel =", value, "incurredCostTravel");
            return (Criteria) this;
        }

        public Criteria andIncurredCostTravelNotEqualTo(BigDecimal value) {
            addCriterion("incurred_cost_travel <>", value, "incurredCostTravel");
            return (Criteria) this;
        }

        public Criteria andIncurredCostTravelGreaterThan(BigDecimal value) {
            addCriterion("incurred_cost_travel >", value, "incurredCostTravel");
            return (Criteria) this;
        }

        public Criteria andIncurredCostTravelGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_cost_travel >=", value, "incurredCostTravel");
            return (Criteria) this;
        }

        public Criteria andIncurredCostTravelLessThan(BigDecimal value) {
            addCriterion("incurred_cost_travel <", value, "incurredCostTravel");
            return (Criteria) this;
        }

        public Criteria andIncurredCostTravelLessThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_cost_travel <=", value, "incurredCostTravel");
            return (Criteria) this;
        }

        public Criteria andIncurredCostTravelIn(List<BigDecimal> values) {
            addCriterion("incurred_cost_travel in", values, "incurredCostTravel");
            return (Criteria) this;
        }

        public Criteria andIncurredCostTravelNotIn(List<BigDecimal> values) {
            addCriterion("incurred_cost_travel not in", values, "incurredCostTravel");
            return (Criteria) this;
        }

        public Criteria andIncurredCostTravelBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_cost_travel between", value1, value2, "incurredCostTravel");
            return (Criteria) this;
        }

        public Criteria andIncurredCostTravelNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_cost_travel not between", value1, value2, "incurredCostTravel");
            return (Criteria) this;
        }

        public Criteria andIncurredCostOtherIsNull() {
            addCriterion("incurred_cost_other is null");
            return (Criteria) this;
        }

        public Criteria andIncurredCostOtherIsNotNull() {
            addCriterion("incurred_cost_other is not null");
            return (Criteria) this;
        }

        public Criteria andIncurredCostOtherEqualTo(BigDecimal value) {
            addCriterion("incurred_cost_other =", value, "incurredCostOther");
            return (Criteria) this;
        }

        public Criteria andIncurredCostOtherNotEqualTo(BigDecimal value) {
            addCriterion("incurred_cost_other <>", value, "incurredCostOther");
            return (Criteria) this;
        }

        public Criteria andIncurredCostOtherGreaterThan(BigDecimal value) {
            addCriterion("incurred_cost_other >", value, "incurredCostOther");
            return (Criteria) this;
        }

        public Criteria andIncurredCostOtherGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_cost_other >=", value, "incurredCostOther");
            return (Criteria) this;
        }

        public Criteria andIncurredCostOtherLessThan(BigDecimal value) {
            addCriterion("incurred_cost_other <", value, "incurredCostOther");
            return (Criteria) this;
        }

        public Criteria andIncurredCostOtherLessThanOrEqualTo(BigDecimal value) {
            addCriterion("incurred_cost_other <=", value, "incurredCostOther");
            return (Criteria) this;
        }

        public Criteria andIncurredCostOtherIn(List<BigDecimal> values) {
            addCriterion("incurred_cost_other in", values, "incurredCostOther");
            return (Criteria) this;
        }

        public Criteria andIncurredCostOtherNotIn(List<BigDecimal> values) {
            addCriterion("incurred_cost_other not in", values, "incurredCostOther");
            return (Criteria) this;
        }

        public Criteria andIncurredCostOtherBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_cost_other between", value1, value2, "incurredCostOther");
            return (Criteria) this;
        }

        public Criteria andIncurredCostOtherNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("incurred_cost_other not between", value1, value2, "incurredCostOther");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostHardwareIsNull() {
            addCriterion("confirmed_cost_hardware is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostHardwareIsNotNull() {
            addCriterion("confirmed_cost_hardware is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostHardwareEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_hardware =", value, "confirmedCostHardware");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostHardwareNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_hardware <>", value, "confirmedCostHardware");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostHardwareGreaterThan(BigDecimal value) {
            addCriterion("confirmed_cost_hardware >", value, "confirmedCostHardware");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostHardwareGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_hardware >=", value, "confirmedCostHardware");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostHardwareLessThan(BigDecimal value) {
            addCriterion("confirmed_cost_hardware <", value, "confirmedCostHardware");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostHardwareLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_hardware <=", value, "confirmedCostHardware");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostHardwareIn(List<BigDecimal> values) {
            addCriterion("confirmed_cost_hardware in", values, "confirmedCostHardware");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostHardwareNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_cost_hardware not in", values, "confirmedCostHardware");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostHardwareBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_cost_hardware between", value1, value2, "confirmedCostHardware");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostHardwareNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_cost_hardware not between", value1, value2, "confirmedCostHardware");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostLabourIsNull() {
            addCriterion("confirmed_cost_labour is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostLabourIsNotNull() {
            addCriterion("confirmed_cost_labour is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostLabourEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_labour =", value, "confirmedCostLabour");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostLabourNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_labour <>", value, "confirmedCostLabour");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostLabourGreaterThan(BigDecimal value) {
            addCriterion("confirmed_cost_labour >", value, "confirmedCostLabour");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostLabourGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_labour >=", value, "confirmedCostLabour");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostLabourLessThan(BigDecimal value) {
            addCriterion("confirmed_cost_labour <", value, "confirmedCostLabour");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostLabourLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_labour <=", value, "confirmedCostLabour");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostLabourIn(List<BigDecimal> values) {
            addCriterion("confirmed_cost_labour in", values, "confirmedCostLabour");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostLabourNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_cost_labour not in", values, "confirmedCostLabour");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostLabourBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_cost_labour between", value1, value2, "confirmedCostLabour");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostLabourNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_cost_labour not between", value1, value2, "confirmedCostLabour");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTravelIsNull() {
            addCriterion("confirmed_cost_travel is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTravelIsNotNull() {
            addCriterion("confirmed_cost_travel is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTravelEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_travel =", value, "confirmedCostTravel");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTravelNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_travel <>", value, "confirmedCostTravel");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTravelGreaterThan(BigDecimal value) {
            addCriterion("confirmed_cost_travel >", value, "confirmedCostTravel");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTravelGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_travel >=", value, "confirmedCostTravel");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTravelLessThan(BigDecimal value) {
            addCriterion("confirmed_cost_travel <", value, "confirmedCostTravel");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTravelLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_travel <=", value, "confirmedCostTravel");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTravelIn(List<BigDecimal> values) {
            addCriterion("confirmed_cost_travel in", values, "confirmedCostTravel");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTravelNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_cost_travel not in", values, "confirmedCostTravel");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTravelBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_cost_travel between", value1, value2, "confirmedCostTravel");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostTravelNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_cost_travel not between", value1, value2, "confirmedCostTravel");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostOtherIsNull() {
            addCriterion("confirmed_cost_other is null");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostOtherIsNotNull() {
            addCriterion("confirmed_cost_other is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostOtherEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_other =", value, "confirmedCostOther");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostOtherNotEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_other <>", value, "confirmedCostOther");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostOtherGreaterThan(BigDecimal value) {
            addCriterion("confirmed_cost_other >", value, "confirmedCostOther");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostOtherGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_other >=", value, "confirmedCostOther");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostOtherLessThan(BigDecimal value) {
            addCriterion("confirmed_cost_other <", value, "confirmedCostOther");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostOtherLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confirmed_cost_other <=", value, "confirmedCostOther");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostOtherIn(List<BigDecimal> values) {
            addCriterion("confirmed_cost_other in", values, "confirmedCostOther");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostOtherNotIn(List<BigDecimal> values) {
            addCriterion("confirmed_cost_other not in", values, "confirmedCostOther");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostOtherBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_cost_other between", value1, value2, "confirmedCostOther");
            return (Criteria) this;
        }

        public Criteria andConfirmedCostOtherNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confirmed_cost_other not between", value1, value2, "confirmedCostOther");
            return (Criteria) this;
        }

        public Criteria andIsObjectiveProjectIsNull() {
            addCriterion("is_objective_project is null");
            return (Criteria) this;
        }

        public Criteria andIsObjectiveProjectIsNotNull() {
            addCriterion("is_objective_project is not null");
            return (Criteria) this;
        }

        public Criteria andIsObjectiveProjectEqualTo(Byte value) {
            addCriterion("is_objective_project =", value, "isObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andIsObjectiveProjectNotEqualTo(Byte value) {
            addCriterion("is_objective_project <>", value, "isObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andIsObjectiveProjectGreaterThan(Byte value) {
            addCriterion("is_objective_project >", value, "isObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andIsObjectiveProjectGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_objective_project >=", value, "isObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andIsObjectiveProjectLessThan(Byte value) {
            addCriterion("is_objective_project <", value, "isObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andIsObjectiveProjectLessThanOrEqualTo(Byte value) {
            addCriterion("is_objective_project <=", value, "isObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andIsObjectiveProjectIn(List<Byte> values) {
            addCriterion("is_objective_project in", values, "isObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andIsObjectiveProjectNotIn(List<Byte> values) {
            addCriterion("is_objective_project not in", values, "isObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andIsObjectiveProjectBetween(Byte value1, Byte value2) {
            addCriterion("is_objective_project between", value1, value2, "isObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andIsObjectiveProjectNotBetween(Byte value1, Byte value2) {
            addCriterion("is_objective_project not between", value1, value2, "isObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andProjectLevelIsNull() {
            addCriterion("project_level is null");
            return (Criteria) this;
        }

        public Criteria andProjectLevelIsNotNull() {
            addCriterion("project_level is not null");
            return (Criteria) this;
        }

        public Criteria andProjectLevelEqualTo(Byte value) {
            addCriterion("project_level =", value, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelNotEqualTo(Byte value) {
            addCriterion("project_level <>", value, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelGreaterThan(Byte value) {
            addCriterion("project_level >", value, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelGreaterThanOrEqualTo(Byte value) {
            addCriterion("project_level >=", value, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelLessThan(Byte value) {
            addCriterion("project_level <", value, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelLessThanOrEqualTo(Byte value) {
            addCriterion("project_level <=", value, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelIn(List<Byte> values) {
            addCriterion("project_level in", values, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelNotIn(List<Byte> values) {
            addCriterion("project_level not in", values, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelBetween(Byte value1, Byte value2) {
            addCriterion("project_level between", value1, value2, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andProjectLevelNotBetween(Byte value1, Byte value2) {
            addCriterion("project_level not between", value1, value2, "projectLevel");
            return (Criteria) this;
        }

        public Criteria andOunameIsNull() {
            addCriterion("ouname is null");
            return (Criteria) this;
        }

        public Criteria andOunameIsNotNull() {
            addCriterion("ouname is not null");
            return (Criteria) this;
        }

        public Criteria andOunameEqualTo(String value) {
            addCriterion("ouname =", value, "ouname");
            return (Criteria) this;
        }

        public Criteria andOunameNotEqualTo(String value) {
            addCriterion("ouname <>", value, "ouname");
            return (Criteria) this;
        }

        public Criteria andOunameGreaterThan(String value) {
            addCriterion("ouname >", value, "ouname");
            return (Criteria) this;
        }

        public Criteria andOunameGreaterThanOrEqualTo(String value) {
            addCriterion("ouname >=", value, "ouname");
            return (Criteria) this;
        }

        public Criteria andOunameLessThan(String value) {
            addCriterion("ouname <", value, "ouname");
            return (Criteria) this;
        }

        public Criteria andOunameLessThanOrEqualTo(String value) {
            addCriterion("ouname <=", value, "ouname");
            return (Criteria) this;
        }

        public Criteria andOunameLike(String value) {
            addCriterion("ouname like", value, "ouname");
            return (Criteria) this;
        }

        public Criteria andOunameNotLike(String value) {
            addCriterion("ouname not like", value, "ouname");
            return (Criteria) this;
        }

        public Criteria andOunameIn(List<String> values) {
            addCriterion("ouname in", values, "ouname");
            return (Criteria) this;
        }

        public Criteria andOunameNotIn(List<String> values) {
            addCriterion("ouname not in", values, "ouname");
            return (Criteria) this;
        }

        public Criteria andOunameBetween(String value1, String value2) {
            addCriterion("ouname between", value1, value2, "ouname");
            return (Criteria) this;
        }

        public Criteria andOunameNotBetween(String value1, String value2) {
            addCriterion("ouname not between", value1, value2, "ouname");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(Long value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(Long value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(Long value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(Long value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(Long value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<Long> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<Long> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(Long value1, Long value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(Long value1, Long value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Long value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Long value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Long value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Long value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Long> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Long> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Long value1, Long value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}