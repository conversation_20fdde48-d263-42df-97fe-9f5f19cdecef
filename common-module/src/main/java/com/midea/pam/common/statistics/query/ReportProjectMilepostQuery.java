package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Description
 * Created by l<PERSON>qing
 * Date 2021/11/5 16:07
 */
@ApiModel(value = "ReportProjectMilepostQuery", description = "项目里程碑报表")
public class ReportProjectMilepostQuery {

    @ApiModelProperty(value = "报表执行记录ID，用于传参")
    private Long executeId;

    @ApiModelProperty(value = "业务部门ID列表，不为空时，只查业务部门属于此列表中的项目")
    private List<Long> unitIds;

    private Long personal;

    private Long companyId;

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public List<Long> getUnitIds() {
        return unitIds;
    }

    public void setUnitIds(List<Long> unitIds) {
        this.unitIds = unitIds;
    }

    public Long getPersonal() {
        return personal;
    }

    public void setPersonal(Long personal) {
        this.personal = personal;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    @Override
    public String toString() {
        return "ReportProjectMilepostQuery{" +
                "executeId=" + executeId +
                ", unitIds=" + unitIds +
                ", personal=" + personal +
                ", companyId=" + companyId +
                '}';
    }
}
