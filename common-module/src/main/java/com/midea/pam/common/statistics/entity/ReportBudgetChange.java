package com.midea.pam.common.statistics.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "")
public class ReportBudgetChange extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "报表id")
    private Long reportId;

    @ApiModelProperty(value = "执行id")
    private Long executeId;

    @ApiModelProperty(value = "变更发起时间")
    private Date changeLaunchTime;

    @ApiModelProperty(value = "变更通过时间")
    private Date changePassTime;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "wbs编码")
    private String wbsCode;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "变更前金额")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "变更后金额")
    private BigDecimal changePrice;

    @ApiModelProperty(value = "变更额")
    private BigDecimal offsetPrice;

    @ApiModelProperty(value = "项目状态")
    private Integer projectStatus;

    @ApiModelProperty(value = "是否预立项：0:否，1:是")
    private Boolean previewFlag;

    @ApiModelProperty(value = "业务分类")
    private String unitName;

    @ApiModelProperty(value = "月份")
    private Integer month;

    private static final long serialVersionUID = 1L;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getExecuteId() {
        return executeId;
    }

    public void setExecuteId(Long executeId) {
        this.executeId = executeId;
    }

    public Date getChangeLaunchTime() {
        return changeLaunchTime;
    }

    public void setChangeLaunchTime(Date changeLaunchTime) {
        this.changeLaunchTime = changeLaunchTime;
    }

    public Date getChangePassTime() {
        return changePassTime;
    }

    public void setChangePassTime(Date changePassTime) {
        this.changePassTime = changePassTime;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getWbsCode() {
        return wbsCode;
    }

    public void setWbsCode(String wbsCode) {
        this.wbsCode = wbsCode == null ? null : wbsCode.trim();
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getChangePrice() {
        return changePrice;
    }

    public void setChangePrice(BigDecimal changePrice) {
        this.changePrice = changePrice;
    }

    public BigDecimal getOffsetPrice() {
        return offsetPrice;
    }

    public void setOffsetPrice(BigDecimal offsetPrice) {
        this.offsetPrice = offsetPrice;
    }

    public Integer getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(Integer projectStatus) {
        this.projectStatus = projectStatus;
    }

    public Boolean getPreviewFlag() {
        return previewFlag;
    }

    public void setPreviewFlag(Boolean previewFlag) {
        this.previewFlag = previewFlag;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportId=").append(reportId);
        sb.append(", executeId=").append(executeId);
        sb.append(", changeLaunchTime=").append(changeLaunchTime);
        sb.append(", changePassTime=").append(changePassTime);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", wbsCode=").append(wbsCode);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", originalPrice=").append(originalPrice);
        sb.append(", changePrice=").append(changePrice);
        sb.append(", offsetPrice=").append(offsetPrice);
        sb.append(", projectStatus=").append(projectStatus);
        sb.append(", previewFlag=").append(previewFlag);
        sb.append(", unitName=").append(unitName);
        sb.append(", month=").append(month);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}