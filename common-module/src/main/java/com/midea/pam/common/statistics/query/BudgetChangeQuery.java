package com.midea.pam.common.statistics.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class BudgetChangeQuery {
    @ApiModelProperty("审批通过起始时间")
    private Date startTime;

    @ApiModelProperty("审批通过截止时间")
    private Date endTime;

    @ApiModelProperty("业务实体id")
    private List<Long> manyOuId;

    @ApiModelProperty("数据权限：创建人id")
    private Long personalId;
}
