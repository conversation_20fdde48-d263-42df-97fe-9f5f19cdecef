package com.midea.pam.annotation;


import com.midea.pam.common.enums.SimpleCacheEnum;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 简单的分布式缓存 ex_panqj
 * 请考虑业务场景下谨慎使用!
 * 频繁更新的数据不建议使用!
 * 参数过多的方法,入参对象 不建议使用!
 * 暂不能使用在多层次嵌套的入参对象和返回值类型(譬如 PageInfo Response ...)
 */

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SimpleCache {

    /**
     * 入参的类型 为了方便组成唯一key
     */
    SimpleCacheEnum argType();

    /**
     * 缓存过期时间
     */
    long expireTime() default 16;

    /**
     * 过期时间单位
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;


}
