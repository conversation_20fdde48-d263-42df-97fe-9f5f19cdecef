package com.midea.pam.system;

import com.google.common.collect.Lists;
import com.midea.pam.common.basedata.dto.UserInfoDto;

import java.util.List;

/**
 * 获取系统当前上下文.
 * chengzy
 */
public class SystemContext {
    private static final ThreadLocal<UserInfoDto> CURRENT_USER = new InheritableThreadLocal<UserInfoDto>();

    public static void set(UserInfoDto userInfo) {
        CURRENT_USER.set(userInfo);
    }

    public static UserInfoDto get() {
        return CURRENT_USER.get();
    }

    public static void remove() {
        CURRENT_USER.remove();
    }

    public static String getUserName() {
        return CURRENT_USER.get().getName();
    }

    public static String getUserMip() {
        return CURRENT_USER.get().getUsername();
    }

    public static Long getUserId() {
        if (CURRENT_USER.get() == null) return -1l;

        return CURRENT_USER.get().getId();
    }

    public static String getLang() {
        return CURRENT_USER.get().getLang();
    }

    /**
     * mip账号
     *
     * @return
     */
    public static String getUid() {
        if (null == CURRENT_USER.get()) {
            return null;
        }
        return CURRENT_USER.get().getUsername();
    }

    /**
     * 当前登录单位id.
     *
     * @return 当前单位id
     */
    public static Long getUnitId() {
        if (null == CURRENT_USER.get()) {
            return null;
        }
        return CURRENT_USER.get().getUnitId();
    }

    /**
     * 当前登录单位名称.
     *
     * @return 当前单位名称
     */
    public static String getUnitName() {
        if (null == CURRENT_USER.get()) {
            return null;
        }
        return CURRENT_USER.get().getUnitName();
    }

    /**
     * 当前使用单位下登录用户所拥有的ou列表
     *
     * @return 用户对应的ou列表
     */
    public static List<Long> getOus() {
        if (null == CURRENT_USER.get()) {
            return Lists.newArrayList();
        }
        return CURRENT_USER.get().getOus();
    }

    /**
     *  获取授权给用户二级部门ID
     * @return
     */
    public static List<Long> getSecondUnits() {
        if (null == CURRENT_USER.get()) {
            return Lists.newArrayList();
        }
        return CURRENT_USER.get().getSecondUnitIds();
    }

}
