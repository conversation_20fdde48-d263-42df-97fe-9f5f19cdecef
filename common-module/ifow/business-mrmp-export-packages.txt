[{"attribute1": "complex", "attribute2": "2020-06-03", "availableFlag": 1, "createdBy": "u_8715206450", "createdName": "chenchao17", "creationDate": 1591147897000, "decisionList": [{"attribute1": "1267992302140731392", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"INPUT\",\"operator\":\"等于\",\"value\":\"3\"}", "fdGroup": "true,false,false,false,false", "fdId": "1267993568115896320", "fdType": "condition", "packageId": "1267992300790165504", "packageVersion": "1267992300978909184", "projectId": "1157571328510889984"}, {"attribute1": "1267992302140731392", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"\",\"operator\":\" \",\"value\":\"\"}", "fdGroup": "false,false,false,false,false", "fdId": "1267993568128479232", "fdType": "condition", "packageId": "1267992300790165504", "packageVersion": "1267992300978909184", "projectId": "1157571328510889984"}, {"attribute1": "1267992302140731392", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"\",\"operator\":\" \",\"value\":\"\"}", "fdGroup": "false,false,false,false,false", "fdId": "1267993568136867840", "fdType": "condition", "packageId": "1267992300790165504", "packageVersion": "1267992300978909184", "projectId": "1157571328510889984"}, {"attribute1": "1267992302140731392", "attribute3": "zh-CN", "fdGroup": "false,true,false,false,false", "fdId": "1267993568149450752", "fdType": "else", "packageId": "1267992300790165504", "packageVersion": "1267992300978909184", "projectId": "1157571328510889984"}, {"attribute1": "1267992302140731392", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"OUTPUT\",\"operator\":\"=\",\"value\":\"true\"}", "fdGroup": "true,false,false,false,false", "fdId": "1267993568153645056", "fdType": "then", "packageId": "1267992300790165504", "packageVersion": "1267992300978909184", "projectId": "1157571328510889984"}, {"attribute1": "1267992302140731392", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"OUTPUT\",\"operator\":\"=\",\"value\":\"false\"}", "fdGroup": "false,true,false,false,false", "fdId": "1267993568166227968", "fdType": "then", "packageId": "1267992300790165504", "packageVersion": "1267992300978909184", "projectId": "1157571328510889984"}, {"attribute1": "1267992302140731392", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"\",\"operator\":\"=\",\"value\":\"\"}", "fdGroup": "false,false,false,false,false", "fdId": "1267993568178810880", "fdType": "then", "packageId": "1267992300790165504", "packageVersion": "1267992300978909184", "projectId": "1157571328510889984"}], "defineList": [{"activeFlag": true, "attribute1": "0", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992301746466816", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": true, "packageId": "1267992300790165504", "ruleDefine": "如果\n INPUT = 3\n那么\n OUTPUT = true\n否则\n OUTPUT = false", "ruleName": "自定义", "rulePriority": 10, "versionId": "1267993639859466240"}, {"activeFlag": true, "attribute1": "0", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302090399744", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": false, "packageId": "1267992300790165504", "ruleDefine": "如果\n INPUT = 2\n那么\n OUTPUT = true\n", "ruleName": "2.商机来源=渠道 & 销售模式=美云销售", "rulePriority": 10, "versionId": "1267993639859466240"}, {"activeFlag": true, "attribute1": "0", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302102982656", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": false, "packageId": "1267992300790165504", "ruleDefine": "如果\n INPUT = 3\n那么\n OUTPUT = true\n", "ruleName": "3.商机来源=直接（直销） & 商机所在地=区域内", "rulePriority": 10, "versionId": "1267993639859466240"}, {"activeFlag": true, "attribute1": "0", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302128148480", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": false, "packageId": "1267992300790165504", "ruleDefine": "如果\n INPUT = 3\n那么\n OUTPUT = true", "ruleName": "4.商机来源=直接（直销） & 商机所在地=跨区域", "rulePriority": 10, "versionId": "1267993639859466240"}, {"activeFlag": true, "attribute1": "1", "deletedFlag": false, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302140731392", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": true, "packageId": "1267992300790165504", "ruleDefine": "如果\n INPUT = 3\n那么\n OUTPUT = true\n否则\n OUTPUT = false\n", "ruleName": "分支3", "rulePriority": 10, "versionId": "1267993639859466240"}], "fdId": "1267992300790165504", "fdName": "商机审批条件3", "fdType": 2, "isDeleted": 0, "lastUpdateDate": 1591147897000, "lastUpdatedBy": "u_8715206450", "lastUpdatedName": "chenchao17", "objectLibraryVo": [{"deletedFlag": 0, "fdCode": "INPUT", "fdId": "1267992301394145280", "fdName": "INPUT", "fdType": 1, "mapCast": "$cm.getDouble(\"INPUT\")", "packageId": "1267992300790165504", "paramType": 6, "serialNumber": 1, "versionId": "1267993639859466240"}, {"deletedFlag": 0, "fdCode": "OUTPUT", "fdId": "1267992301511585792", "fdName": "OUTPUT", "fdType": 2, "mapCast": "$cm.getBoolean(\"OUTPUT\")", "packageId": "1267992300790165504", "paramType": 5, "serialNumber": 1, "versionId": "1267993639859466240"}], "operationLock": 0, "packageCode": "P1267992300790165504", "packageInfo": "商机来源=直接（直销） & 商机所在地=区域内", "packageType": 2, "parentId": "1267992134793023488", "path": "/1267992134793023488/1267992300790165504", "projectId": "1157571328510889984", "treeNodeType": 3}, {"attribute1": "complex", "attribute2": "2020-06-03", "availableFlag": 1, "createdBy": "u_8715206450", "createdName": "chenchao17", "creationDate": 1591147897000, "decisionList": [{"attribute1": "1267992302497247232", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"INPUT\",\"operator\":\"等于\",\"value\":\"2\"}", "fdGroup": "true,false,false,false,false", "fdId": "1267993228515684352", "fdType": "condition", "packageId": "1267992302308503552", "packageVersion": "1267992302358835200", "projectId": "1157571328510889984"}, {"attribute1": "1267992302497247232", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"\",\"operator\":\" \",\"value\":\"\"}", "fdGroup": "false,false,false,false,false", "fdId": "1267993228528267264", "fdType": "condition", "packageId": "1267992302308503552", "packageVersion": "1267992302358835200", "projectId": "1157571328510889984"}, {"attribute1": "1267992302497247232", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"\",\"operator\":\" \",\"value\":\"\"}", "fdGroup": "false,false,false,false,false", "fdId": "1267993228536655872", "fdType": "condition", "packageId": "1267992302308503552", "packageVersion": "1267992302358835200", "projectId": "1157571328510889984"}, {"attribute1": "1267992302497247232", "attribute3": "zh-CN", "fdGroup": "false,true,false,false,false", "fdId": "1267993228545044480", "fdType": "else", "packageId": "1267992302308503552", "packageVersion": "1267992302358835200", "projectId": "1157571328510889984"}, {"attribute1": "1267992302497247232", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"OUTPUT\",\"operator\":\"=\",\"value\":\"true\"}", "fdGroup": "true,false,false,false,false", "fdId": "1267993228557627392", "fdType": "then", "packageId": "1267992302308503552", "packageVersion": "1267992302358835200", "projectId": "1157571328510889984"}, {"attribute1": "1267992302497247232", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"OUTPUT\",\"operator\":\"=\",\"value\":\"false\"}", "fdGroup": "false,true,false,false,false", "fdId": "1267993228566016000", "fdType": "then", "packageId": "1267992302308503552", "packageVersion": "1267992302358835200", "projectId": "1157571328510889984"}, {"attribute1": "1267992302497247232", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"\",\"operator\":\"=\",\"value\":\"\"}", "fdGroup": "false,false,false,false,false", "fdId": "1267993228578598912", "fdType": "then", "packageId": "1267992302308503552", "packageVersion": "1267992302358835200", "projectId": "1157571328510889984"}], "defineList": [{"activeFlag": true, "attribute1": "0", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302434332672", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": true, "packageId": "1267992302308503552", "ruleDefine": "如果\n INPUT = 2\n那么\n OUTPUT = true\n否则\n OUTPUT = false", "ruleName": "自定义", "rulePriority": 10, "versionId": "1267993445819207680"}, {"activeFlag": true, "attribute1": "0", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302451109888", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": false, "packageId": "1267992302308503552", "ruleDefine": "如果\n INPUT = 2\n那么\n OUTPUT = true\n", "ruleName": "2.商机来源=渠道 & 销售模式=美云销售", "rulePriority": 10, "versionId": "1267993445819207680"}, {"activeFlag": true, "attribute1": "0", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302467887104", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": false, "packageId": "1267992302308503552", "ruleDefine": "如果\n INPUT = 3\n那么\n OUTPUT = true\n", "ruleName": "3.商机来源=直接（直销） & 商机所在地=区域内", "rulePriority": 10, "versionId": "1267993445819207680"}, {"activeFlag": true, "attribute1": "0", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302480470016", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": false, "packageId": "1267992302308503552", "ruleDefine": "如果\n INPUT = 3\n那么\n OUTPUT = true", "ruleName": "4.商机来源=直接（直销） & 商机所在地=跨区域", "rulePriority": 10, "versionId": "1267993445819207680"}, {"activeFlag": true, "attribute1": "1", "deletedFlag": false, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302497247232", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": true, "packageId": "1267992302308503552", "ruleDefine": "如果\n INPUT = 2\n那么\n OUTPUT = true\n否则\n OUTPUT = false\n", "ruleName": "计划2", "rulePriority": 10, "versionId": "1267993445819207680"}], "fdId": "1267992302308503552", "fdName": "商机审批条件2", "fdType": 2, "isDeleted": 0, "lastUpdateDate": 1591147897000, "lastUpdatedBy": "u_8715206450", "lastUpdatedName": "chenchao17", "objectLibraryVo": [{"deletedFlag": 0, "fdCode": "INPUT", "fdId": "1267992302396583936", "fdName": "INPUT", "fdType": 1, "mapCast": "$cm.getDouble(\"INPUT\")", "packageId": "1267992302308503552", "paramType": 6, "serialNumber": 1, "versionId": "1267993445819207680"}, {"deletedFlag": 0, "fdCode": "OUTPUT", "fdId": "1267992302409166848", "fdName": "OUTPUT", "fdType": 2, "mapCast": "$cm.getBoolean(\"OUTPUT\")", "packageId": "1267992302308503552", "paramType": 5, "serialNumber": 1, "versionId": "1267993445819207680"}], "operationLock": 0, "packageCode": "P1267992302308503552", "packageInfo": "商机来源=渠道 & 销售模式=美云销售", "packageType": 2, "parentId": "1267992134793023488", "path": "/1267992134793023488/1267992302308503552", "projectId": "1157571328510889984", "treeNodeType": 3}, {"attribute1": "complex", "attribute2": "2020-06-03", "availableFlag": 1, "createdBy": "u_8715206450", "createdName": "chenchao17", "creationDate": 1591147897000, "decisionList": [{"attribute1": "1267992302727933952", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"INPUT\",\"operator\":\"等于\",\"value\":\"4\"}", "fdGroup": "true,false,false,false,false", "fdId": "1267993772412055552", "fdType": "condition", "packageId": "1267992302576939008", "packageVersion": "1267992302623076352", "projectId": "1157571328510889984"}, {"attribute1": "1267992302727933952", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"\",\"operator\":\" \",\"value\":\"\"}", "fdGroup": "false,false,false,false,false", "fdId": "1267993772416249856", "fdType": "condition", "packageId": "1267992302576939008", "packageVersion": "1267992302623076352", "projectId": "1157571328510889984"}, {"attribute1": "1267992302727933952", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"INPUT\",\"operator\":\"等于\",\"value\":\"\"}", "fdGroup": "false,false,false,false,false", "fdId": "1267993772424638464", "fdType": "condition", "packageId": "1267992302576939008", "packageVersion": "1267992302623076352", "projectId": "1157571328510889984"}, {"attribute1": "1267992302727933952", "attribute3": "zh-CN", "fdGroup": "false,true,false,false,false", "fdId": "1267993772433027072", "fdType": "else", "packageId": "1267992302576939008", "packageVersion": "1267992302623076352", "projectId": "1157571328510889984"}, {"attribute1": "1267992302727933952", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"OUTPUT\",\"operator\":\"=\",\"value\":\"true\"}", "fdGroup": "true,false,false,false,false", "fdId": "1267993772441415680", "fdType": "then", "packageId": "1267992302576939008", "packageVersion": "1267992302623076352", "projectId": "1157571328510889984"}, {"attribute1": "1267992302727933952", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"OUTPUT\",\"operator\":\"=\",\"value\":\"false\"}", "fdGroup": "false,true,false,false,false", "fdId": "1267993772445609984", "fdType": "then", "packageId": "1267992302576939008", "packageVersion": "1267992302623076352", "projectId": "1157571328510889984"}, {"attribute1": "1267992302727933952", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"\",\"operator\":\"=\",\"value\":\"\"}", "fdGroup": "false,false,false,false,false", "fdId": "1267993772453998592", "fdType": "then", "packageId": "1267992302576939008", "packageVersion": "1267992302623076352", "projectId": "1157571328510889984"}], "defineList": [{"activeFlag": true, "attribute1": "0", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302685990912", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": true, "packageId": "1267992302576939008", "ruleDefine": "如果\n INPUT = 4\n那么\n OUTPUT = true\n否则\n OUTPUT = false\n", "ruleName": "自定义", "rulePriority": 10, "versionId": "1267993837063057408"}, {"activeFlag": true, "attribute1": "0", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302694379520", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": false, "packageId": "1267992302576939008", "ruleDefine": "如果\n INPUT = 2\n那么\n OUTPUT = true\n", "ruleName": "2.商机来源=渠道 & 销售模式=美云销售", "rulePriority": 10, "versionId": "1267993837063057408"}, {"activeFlag": true, "attribute1": "0", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302706962432", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": false, "packageId": "1267992302576939008", "ruleDefine": "如果\n INPUT = 3\n那么\n OUTPUT = true\n", "ruleName": "3.商机来源=直接（直销） & 商机所在地=区域内", "rulePriority": 10, "versionId": "1267993837063057408"}, {"activeFlag": true, "attribute1": "0", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302715351040", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": false, "packageId": "1267992302576939008", "ruleDefine": "如果\n INPUT = 3\n那么\n OUTPUT = true", "ruleName": "4.商机来源=直接（直销） & 商机所在地=跨区域", "rulePriority": 10, "versionId": "1267993837063057408"}, {"activeFlag": true, "attribute1": "1", "deletedFlag": false, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302727933952", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": true, "packageId": "1267992302576939008", "ruleDefine": "如果\n INPUT = 4\n那么\n OUTPUT = true\n否则\n OUTPUT = false\n", "ruleName": "分支4", "rulePriority": 10, "versionId": "1267993837063057408"}], "fdId": "1267992302576939008", "fdName": "商机审批条件4", "fdType": 2, "isDeleted": 0, "lastUpdateDate": 1591147897000, "lastUpdatedBy": "u_8715206450", "lastUpdatedName": "chenchao17", "objectLibraryVo": [{"deletedFlag": 0, "fdCode": "INPUT", "fdId": "1267992302656630784", "fdName": "INPUT", "fdType": 1, "mapCast": "$cm.getDouble(\"INPUT\")", "packageId": "1267992302576939008", "paramType": 6, "serialNumber": 1, "versionId": "1267993837063057408"}, {"deletedFlag": 0, "fdCode": "OUTPUT", "fdId": "1267992302669213696", "fdName": "OUTPUT", "fdType": 2, "mapCast": "$cm.getBoolean(\"OUTPUT\")", "packageId": "1267992302576939008", "paramType": 5, "serialNumber": 1, "versionId": "1267993837063057408"}], "operationLock": 0, "packageCode": "P1267992302576939008", "packageInfo": "商机来源=直接（直销） & 商机所在地=跨区域", "packageType": 2, "parentId": "1267992134793023488", "path": "/1267992134793023488/1267992302576939008", "projectId": "1157571328510889984", "treeNodeType": 3}, {"attribute1": "complex", "attribute2": "2020-06-03", "availableFlag": 1, "createdBy": "u_8715206450", "createdName": "chenchao17", "creationDate": 1591147897000, "decisionList": [{"attribute1": "1267992303004758016", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"INPUT\",\"operator\":\"等于\",\"value\":\"1\"}", "fdGroup": "true,false,false,false,false", "fdId": "1267992625504792576", "fdType": "condition", "packageId": "1267992302824402944", "packageVersion": "1267992302878928896", "projectId": "1157571328510889984"}, {"attribute1": "1267992303004758016", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"\",\"operator\":\" \",\"value\":\"\"}", "fdGroup": "false,false,false,false,false", "fdId": "1267992625546735616", "fdType": "condition", "packageId": "1267992302824402944", "packageVersion": "1267992302878928896", "projectId": "1157571328510889984"}, {"attribute1": "1267992303004758016", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"\",\"operator\":\" \",\"value\":\"\"}", "fdGroup": "false,false,false,false,false", "fdId": "1267992625559318528", "fdType": "condition", "packageId": "1267992302824402944", "packageVersion": "1267992302878928896", "projectId": "1157571328510889984"}, {"attribute1": "1267992303004758016", "attribute3": "zh-CN", "fdGroup": "false,true,false,false,false", "fdId": "1267992625567707136", "fdType": "else", "packageId": "1267992302824402944", "packageVersion": "1267992302878928896", "projectId": "1157571328510889984"}, {"attribute1": "1267992303004758016", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"OUTPUT\",\"operator\":\"=\",\"value\":\"true\"}", "fdGroup": "true,false,false,false,false", "fdId": "1267992625576095744", "fdType": "then", "packageId": "1267992302824402944", "packageVersion": "1267992302878928896", "projectId": "1157571328510889984"}, {"attribute1": "1267992303004758016", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"OUTPUT\",\"operator\":\"=\",\"value\":\"false\"}", "fdGroup": "false,true,false,false,false", "fdId": "1267992625584484352", "fdType": "then", "packageId": "1267992302824402944", "packageVersion": "1267992302878928896", "projectId": "1157571328510889984"}, {"attribute1": "1267992303004758016", "attribute3": "zh-CN", "fdContent": "{\"select\":false,\"obj\":\"\",\"operator\":\"=\",\"value\":\"\"}", "fdGroup": "false,false,false,false,false", "fdId": "1267992625592872960", "fdType": "then", "packageId": "1267992302824402944", "packageVersion": "1267992302878928896", "projectId": "1157571328510889984"}], "defineList": [{"activeFlag": true, "attribute1": "0", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302954426368", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": true, "packageId": "1267992302824402944", "ruleDefine": "如果\n INPUT = 1\n那么\n OUTPUT = true\n否则\n OUTPUT = false", "ruleName": "自定义", "rulePriority": 10, "versionId": "1267992978263363584"}, {"activeFlag": true, "attribute1": "0", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302962814976", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": false, "packageId": "1267992302824402944", "ruleDefine": "如果\n INPUT = 2\n那么\n OUTPUT = true\n", "ruleName": "2.商机来源=渠道 & 销售模式=美云销售", "rulePriority": 10, "versionId": "1267992978263363584"}, {"activeFlag": true, "attribute1": "0", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302975397888", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": false, "packageId": "1267992302824402944", "ruleDefine": "如果\n INPUT = 3\n那么\n OUTPUT = true\n", "ruleName": "3.商机来源=直接（直销） & 商机所在地=区域内", "rulePriority": 10, "versionId": "1267992978263363584"}, {"activeFlag": true, "attribute1": "0", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302983786496", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": false, "packageId": "1267992302824402944", "ruleDefine": "如果\n INPUT = 3\n那么\n OUTPUT = true", "ruleName": "4.商机来源=直接（直销） & 商机所在地=跨区域", "rulePriority": 10, "versionId": "1267992978263363584"}, {"activeFlag": true, "attribute1": "1", "deletedFlag": true, "exceptionFlag": false, "executionType": 1, "fdId": "1267992302996369408", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": true, "packageId": "1267992302824402944", "ruleDefine": "如果\n\n那么\n\n否则\n", "ruleName": "自定义1", "rulePriority": 10, "versionId": "1267992978263363584"}, {"activeFlag": true, "attribute1": "1", "deletedFlag": false, "exceptionFlag": false, "executionType": 1, "fdId": "1267992303004758016", "initializeFlag": false, "loopLimit": 100, "otherwiseFlag": true, "packageId": "1267992302824402944", "ruleDefine": "如果\n INPUT = 1\n那么\n OUTPUT = true\n否则\n OUTPUT = false\n", "ruleName": "决策表", "rulePriority": 10, "versionId": "1267992978263363584"}], "fdId": "1267992302824402944", "fdName": "商机审批条件1", "fdType": 2, "isDeleted": 0, "lastUpdateDate": 1591147897000, "lastUpdatedBy": "u_8715206450", "lastUpdatedName": "chenchao17", "objectLibraryVo": [{"deletedFlag": 0, "fdCode": "INPUT", "fdId": "1267992302925066240", "fdName": "INPUT", "fdType": 1, "mapCast": "$cm.getInteger(\"INPUT\")", "packageId": "1267992302824402944", "paramType": 2, "serialNumber": 1, "versionId": "1267992978263363584"}, {"deletedFlag": 0, "fdCode": "OUTPUT", "fdId": "1267992302937649152", "fdName": "OUTPUT", "fdType": 2, "mapCast": "$cm.getBoolean(\"OUTPUT\")", "packageId": "1267992302824402944", "paramType": 5, "serialNumber": 1, "versionId": "1267992978263363584"}], "operationLock": 0, "packageCode": "P1267992302824402944", "packageInfo": "商机来源=渠道 & 销售模式=自主销售", "packageType": 2, "parentId": "1267992134793023488", "path": "/1267992134793023488/1267992302824402944", "projectId": "1157571328510889984", "treeNodeType": 3}]